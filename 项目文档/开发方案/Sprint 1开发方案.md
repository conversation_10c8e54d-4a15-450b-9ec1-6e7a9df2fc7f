# **柴管家 Sprint 1 开发方案**

**版本:** 1.0  
**创建日期:** 2025-08-03  
**Sprint周期:** 2025-08-11 至 2025-08-25 (2周)  
**开发模式:** 独立全栈工程师  
**史诗范围:** 核心渠道管理 (Epic: Core Channel Management)

## **1. Sprint 1 概述**

### **1.1 Sprint目标**

Sprint 1是柴管家项目的第一个功能开发冲刺，基于Sprint 0已完成的BDD剧本，实现**史诗1：核心渠道管理**的完整功能。

**核心目标：**
- ✅ 基于已完成的Gherkin剧本实现自动化测试
- ✅ 实现闲鱼平台连接器和渠道管理功能
- ✅ 建立Cookie安全管理和状态监控机制
- ✅ 严格遵循BDD三步走开发流程
- ✅ 为后续史诗奠定坚实的技术基础

### **1.2 已完成的BDD剧本分析**

基于`features/core_channel_management/`目录中的剧本，Sprint 1需要实现：

#### **Feature 1: 渠道连接管理** (11个场景)
- 闲鱼账号Cookie连接流程
- Cookie输入方式和验证
- 连接失败处理和重试机制
- 重复连接防护
- Cookie权限验证和安全管理

#### **Feature 2: 渠道管理功能** (15个场景)
- 渠道CRUD操作（创建、查看、编辑、删除）
- 软删除机制和历史渠道管理
- 渠道恢复和彻底删除
- 渠道状态切换和排序筛选

#### **Feature 3: 渠道状态监控** (6个场景)
- 实时连接状态监控
- Cookie失效检测和自动重连
- 手动重连和Cookie更新
- 监控数据统计和报告

### **1.3 技术实现重点**

- **Cookie安全管理**: AES加密存储，脱敏显示
- **软删除机制**: 数据保留但隐藏，支持恢复
- **实时状态监控**: WebSocket连接状态检测
- **闲鱼平台集成**: 基于现有连接器代码适配

## **2. BDD开发计划**

### **2.1 阶段二：自动化测试实现 (Day 1-6)**

基于已完成的Gherkin剧本，实现对应的自动化测试。

#### **任务组 2.1：单元测试开发** ⏱️ 12小时

**渠道连接测试模块：**
- [ ] `tests/unit/connectors/test_base_connector.py` - 连接器基类测试
- [ ] `tests/unit/connectors/test_xianyu_connector.py` - 闲鱼连接器测试
- [ ] `tests/unit/services/test_channel_service.py` - 渠道服务测试
- [ ] `tests/unit/utils/test_cookie_manager.py` - Cookie管理工具测试

**渠道管理测试模块：**
- [ ] `tests/unit/models/test_channel_model.py` - 渠道数据模型测试
- [ ] `tests/unit/services/test_channel_crud.py` - 渠道CRUD操作测试
- [ ] `tests/unit/services/test_soft_delete.py` - 软删除机制测试

**状态监控测试模块：**
- [ ] `tests/unit/monitoring/test_status_monitor.py` - 状态监控服务测试
- [ ] `tests/unit/monitoring/test_reconnection.py` - 重连机制测试

#### **任务组 2.2：集成测试开发** ⏱️ 10小时

**API集成测试：**
- [ ] `tests/integration/api/test_channel_connection_api.py` - 渠道连接API测试
- [ ] `tests/integration/api/test_channel_management_api.py` - 渠道管理API测试
- [ ] `tests/integration/api/test_channel_monitoring_api.py` - 状态监控API测试

**数据库集成测试：**
- [ ] `tests/integration/db/test_channel_operations.py` - 渠道数据库操作测试
- [ ] `tests/integration/db/test_soft_delete_flow.py` - 软删除流程测试

**端到端流程测试：**
- [ ] `tests/integration/flows/test_connection_flow.py` - 完整连接流程测试
- [ ] `tests/integration/flows/test_management_flow.py` - 管理流程测试

#### **任务组 2.3：前端测试开发** ⏱️ 6小时

**组件测试：**
- [ ] `src/frontend/src/components/__tests__/ChannelList.test.tsx`
- [ ] `src/frontend/src/components/__tests__/ChannelConnection.test.tsx`
- [ ] `src/frontend/src/components/__tests__/ChannelMonitor.test.tsx`

**页面测试：**
- [ ] `src/frontend/src/pages/__tests__/ChannelManagement.test.tsx`

**E2E测试：**
- [ ] `tests/e2e/channel_management.spec.ts` - 端到端用户流程测试

### **2.2 阶段三：产品代码实现 (Day 7-14)**

#### **任务组 3.1：后端核心实现** ⏱️ 20小时

**数据模型设计：**
- [ ] `src/backend/app/models/channel.py` - 渠道数据模型
- [ ] `src/backend/app/models/channel_connection_log.py` - 连接日志模型
- [ ] 数据库迁移脚本创建

**平台连接器架构：**
- [ ] `src/backend/app/connectors/base_connector.py` - 连接器基类
- [ ] `src/backend/app/connectors/xianyu_connector.py` - 闲鱼连接器适配
- [ ] `src/backend/app/connectors/connector_manager.py` - 连接器管理器
- [ ] `src/backend/app/utils/cookie_manager.py` - Cookie安全管理工具

**业务服务层：**
- [ ] `src/backend/app/services/channel_service.py` - 渠道管理服务
- [ ] `src/backend/app/services/connection_service.py` - 连接管理服务
- [ ] `src/backend/app/services/monitoring_service.py` - 状态监控服务

**API端点实现：**
- [ ] `src/backend/app/api/v1/channels.py` - 渠道管理API
- [ ] `src/backend/app/api/v1/connections.py` - 连接管理API
- [ ] `src/backend/app/api/v1/monitoring.py` - 状态监控API

#### **任务组 3.2：前端界面实现** ⏱️ 16小时

**页面组件：**
- [ ] `src/frontend/src/pages/ChannelManagement.tsx` - 渠道管理主页面
- [ ] `src/frontend/src/pages/HistoryChannels.tsx` - 历史渠道页面

**功能组件：**
- [ ] `src/frontend/src/components/ChannelList.tsx` - 渠道列表组件
- [ ] `src/frontend/src/components/ChannelConnection.tsx` - 连接对话框组件
- [ ] `src/frontend/src/components/ChannelMonitor.tsx` - 状态监控组件
- [ ] `src/frontend/src/components/CookieInput.tsx` - Cookie输入组件
- [ ] `src/frontend/src/components/ChannelDetails.tsx` - 渠道详情组件

**状态管理和API集成：**
- [ ] `src/frontend/src/services/channelApi.ts` - 渠道API服务
- [ ] `src/frontend/src/hooks/useChannels.ts` - 渠道状态管理Hook
- [ ] `src/frontend/src/hooks/useChannelMonitor.ts` - 监控状态Hook

#### **任务组 3.3：验证界面创建** ⏱️ 4小时

- [ ] `src/verification/channel_management/index.html` - 渠道管理验证页面
- [ ] `src/verification/channel_management/connection_test.html` - 连接测试页面
- [ ] `src/verification/channel_management/monitoring_test.html` - 监控测试页面

## **3. 详细技术实现方案**

### **3.1 数据库设计**

#### **渠道表 (channels)**
```sql
CREATE TABLE channels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    platform VARCHAR(50) NOT NULL,           -- 平台类型 (xianyu, douyin, etc.)
    platform_account_id VARCHAR(255) NOT NULL, -- 平台账号ID
    display_name VARCHAR(255),                -- 用户设置的别名
    avatar_url VARCHAR(500),                  -- 头像URL
    encrypted_config TEXT,                    -- 加密的连接配置(Cookie等)
    status VARCHAR(20) DEFAULT 'disconnected', -- connected, disconnected, error, paused
    is_deleted BOOLEAN DEFAULT FALSE,         -- 软删除标记
    last_connected_at TIMESTAMP,             -- 最后连接时间
    last_active_at TIMESTAMP,                -- 最后活跃时间
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    deleted_at TIMESTAMP,                    -- 软删除时间

    UNIQUE(platform, platform_account_id, is_deleted)
);
```

#### **连接状态日志表 (channel_connection_logs)**
```sql
CREATE TABLE channel_connection_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    channel_id UUID REFERENCES channels(id),
    status VARCHAR(20) NOT NULL,             -- connected, disconnected, error, cookie_expired
    error_message TEXT,                      -- 错误信息
    operation_type VARCHAR(50),              -- connect, disconnect, reconnect, cookie_update
    metadata JSONB,                          -- 额外信息
    created_at TIMESTAMP DEFAULT NOW()
);
```

### **3.2 Cookie安全管理方案**

#### **加密存储机制**
```python
# src/backend/app/utils/cookie_manager.py
from cryptography.fernet import Fernet
import base64
import json

class CookieManager:
    def __init__(self, encryption_key: str):
        self.cipher = Fernet(encryption_key.encode())

    def encrypt_cookie(self, cookie_data: dict) -> str:
        """加密Cookie数据"""
        json_data = json.dumps(cookie_data)
        encrypted = self.cipher.encrypt(json_data.encode())
        return base64.b64encode(encrypted).decode()

    def decrypt_cookie(self, encrypted_data: str) -> dict:
        """解密Cookie数据"""
        encrypted_bytes = base64.b64decode(encrypted_data.encode())
        decrypted = self.cipher.decrypt(encrypted_bytes)
        return json.loads(decrypted.decode())

    def mask_cookie_for_display(self, cookie_str: str) -> str:
        """脱敏显示Cookie"""
        if len(cookie_str) <= 20:
            return "*" * len(cookie_str)
        return cookie_str[:10] + "*" * (len(cookie_str) - 20) + cookie_str[-10:]
```

### **3.3 软删除机制实现**

#### **服务层实现**
```python
# src/backend/app/services/channel_service.py
class ChannelService:
    async def soft_delete_channel(self, channel_id: str, user_id: str) -> bool:
        """软删除渠道"""
        channel = await self.get_channel(channel_id)
        if not channel:
            raise ChannelNotFoundError()

        # 更新删除标记
        channel.is_deleted = True
        channel.deleted_at = datetime.utcnow()

        # 停止监控服务
        await self.monitoring_service.stop_monitoring(channel_id)

        # 记录操作日志
        await self.log_operation(channel_id, "soft_delete", user_id)

        return await self.repository.update(channel)

    async def restore_channel(self, channel_id: str, user_id: str) -> bool:
        """恢复已删除渠道"""
        channel = await self.get_deleted_channel(channel_id)
        if not channel:
            raise ChannelNotFoundError()

        # 恢复渠道
        channel.is_deleted = False
        channel.deleted_at = None

        # 重新启动监控
        await self.monitoring_service.start_monitoring(channel_id)

        # 记录操作日志
        await self.log_operation(channel_id, "restore", user_id)

        return await self.repository.update(channel)
```

### **3.4 实时状态监控架构**

#### **监控服务设计**
```python
# src/backend/app/services/monitoring_service.py
class ChannelMonitoringService:
    def __init__(self):
        self.active_monitors = {}
        self.reconnection_manager = ReconnectionManager()

    async def start_monitoring(self, channel_id: str):
        """启动渠道监控"""
        if channel_id in self.active_monitors:
            return

        monitor = ChannelMonitor(channel_id)
        self.active_monitors[channel_id] = monitor
        await monitor.start()

    async def handle_connection_error(self, channel_id: str, error: Exception):
        """处理连接错误"""
        if isinstance(error, CookieExpiredError):
            await self.handle_cookie_expired(channel_id)
        else:
            await self.reconnection_manager.schedule_reconnection(channel_id)

    async def handle_cookie_expired(self, channel_id: str):
        """处理Cookie过期"""
        # 更新渠道状态
        await self.channel_service.update_status(channel_id, "cookie_expired")

        # 发送通知
        await self.notification_service.send_cookie_expired_notification(channel_id)

        # 暂停自动化操作
        await self.automation_service.pause_channel(channel_id)
```

## **4. 时间规划与里程碑**

### **4.1 详细时间线**

```mermaid
gantt
    title Sprint 1 开发时间线
    dateFormat  YYYY-MM-DD
    axisFormat  %m-%d

    section 阶段2: 自动化测试
    单元测试开发        :s2-1, 2025-08-11, 3d
    集成测试开发        :s2-2, after s2-1, 2d
    前端测试开发        :s2-3, after s2-2, 1d

    section 阶段3: 产品代码
    后端核心实现        :s3-1, after s2-3, 5d
    前端界面实现        :s3-2, after s3-1, 4d
    验证界面创建        :s3-3, after s3-2, 1d

    section 阶段4: 集成验收
    系统集成测试        :s4-1, after s3-3, 1d
    用户验收测试        :s4-2, after s4-1, 1d
```

### **4.2 关键里程碑**

| 里程碑 | 日期 | 交付物 | 验收标准 |
|--------|------|--------|----------|
| **M1: 测试套件完成** | 2025-08-17 | 完整自动化测试 | 所有Gherkin场景有对应测试，覆盖率≥85% |
| **M2: 后端功能完成** | 2025-08-22 | 后端API和服务 | 所有API端点正常响应，业务逻辑正确 |
| **M3: 前端界面完成** | 2025-08-24 | 前端管理界面 | 界面交互流畅，功能完整 |
| **M4: Sprint 1交付** | 2025-08-25 | 完整功能模块 | 所有Gherkin场景验收通过 |

## **5. 验收标准与质量保证**

### **5.1 基于BDD剧本的验收标准**

#### **渠道连接功能验收**
- [ ] 所有11个连接场景通过验证
- [ ] Cookie加密存储和脱敏显示正常
- [ ] 连接失败处理和重试机制有效
- [ ] 重复连接防护机制工作正常

#### **渠道管理功能验收**
- [ ] 所有15个管理场景通过验证
- [ ] 软删除机制完整实现
- [ ] 历史渠道管理功能正常
- [ ] 数据统计正确排除已删除渠道

#### **渠道监控功能验收**
- [ ] 所有6个监控场景通过验证
- [ ] 实时状态监控准确
- [ ] Cookie失效检测和通知及时
- [ ] 自动重连和手动重连正常

### **5.2 技术质量标准**

#### **代码质量**
- [ ] 后端代码通过 `black`、`isort`、`flake8` 检查
- [ ] 前端代码通过 `ESLint`、`Prettier` 检查
- [ ] 单元测试覆盖率 ≥ 85%
- [ ] 所有公共函数有完整文档字符串

#### **安全标准**
- [ ] Cookie信息AES加密存储
- [ ] 敏感信息不出现在日志中
- [ ] API端点有适当的权限验证
- [ ] 输入数据有完整的验证和清理

#### **性能标准**
- [ ] API响应时间 ≤ 200ms
- [ ] 前端页面加载时间 ≤ 2秒
- [ ] 支持至少10个并发渠道连接
- [ ] 状态监控延迟 ≤ 5秒

## **6. 风险评估与应对策略**

### **6.1 技术风险**

| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| **闲鱼Cookie机制变化** | 中 | 连接器失效 | 建立Cookie格式监控，准备适配方案 |
| **软删除数据一致性** | 中 | 数据统计错误 | 完善数据库约束，增加一致性检查 |
| **加密性能问题** | 低 | 响应速度慢 | 优化加密算法，使用缓存机制 |

### **6.2 进度风险**

| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| **测试编写复杂度高** | 中 | 延期交付 | 优先核心场景，并行开发测试 |
| **前后端集成问题** | 中 | 功能无法使用 | 提前定义API契约，使用Mock数据 |

## **7. 成功标准与交付清单**

### **7.1 Sprint 1成功标准**

#### **BDD验收标准**
- [ ] 32个Gherkin场景全部通过自动化验证
- [ ] 所有Given-When-Then步骤有对应的测试实现
- [ ] 验收测试覆盖所有业务规则和边界条件

#### **功能完整性标准**
- [ ] 闲鱼平台连接器完整实现
- [ ] 渠道管理CRUD操作正常
- [ ] 软删除和恢复机制工作正常
- [ ] 实时状态监控准确有效

### **7.2 最终交付清单**

#### **代码交付物**
- [ ] 完整的渠道管理功能模块
- [ ] 基于闲鱼连接器的平台架构
- [ ] Cookie安全管理系统
- [ ] 软删除和状态监控机制
- [ ] 完整的自动化测试套件

#### **文档交付物**
- [ ] 更新的API文档（OpenAPI规范）
- [ ] Cookie管理和安全指南
- [ ] 渠道管理用户操作手册
- [ ] 技术架构文档更新

#### **验证交付物**
- [ ] 功能验证界面
- [ ] BDD测试执行报告
- [ ] 性能测试结果
- [ ] 安全检查报告

---

**📋 Sprint 1 Ready Definition:**
- [x] BDD剧本已完成并评审通过
- [ ] 技术方案已确认
- [ ] 开发环境已准备就绪
- [ ] 测试数据和Mock服务已准备

**🎯 Sprint 1 Done Definition:**
- [ ] 所有32个Gherkin场景通过自动化验证
- [ ] 代码质量检查通过
- [ ] 功能验证界面测试通过
- [ ] 部署到测试环境成功
- [ ] 产品负责人验收通过

---

**下一步行动：**
1. 确认Sprint 1开发方案
2. 开始实现第一个Feature的自动化测试
3. 建立开发分支和CI/CD流程
4. 启动BDD测试驱动开发流程
