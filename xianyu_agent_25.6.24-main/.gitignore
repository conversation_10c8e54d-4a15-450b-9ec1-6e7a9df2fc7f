# 环境变量文件（包含敏感信息）
.env
# 提示词文件
*_prompt.txt
# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
ENV/
env/
.venv/

# 日志文件
logs/
*.log

# 数据库文件
*.db
*.sqlite3
*.sqlite

# 其他临时文件
.DS_Store
.idea/
.vscode/
*.swp
*.swo
.coverage
htmlcov/
.pytest_cache/
.tox/
.nox/
.hypothesis/
.coverage.* 