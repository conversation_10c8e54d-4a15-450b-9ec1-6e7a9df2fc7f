{"price_negotiation": {"name": "价格议价", "description": "用户想要咨询价格、讨价还价、询问优惠等价格相关问题", "example": "JSON：[{'name': 'target_price', 'desc': '用户期望的价格', 'value': ''}]\n输入：能不能便宜点，100元怎么样\n答：{'name': 'target_price', 'value': '100'}", "parameters": [{"name": "target_price", "desc": "用户期望的价格", "type": "string", "required": false}, {"name": "reason", "desc": "砍价的理由", "type": "string", "required": false}]}, "technical_inquiry": {"name": "技术咨询", "description": "用户询问商品的技术参数、规格、兼容性、使用方法等技术问题", "example": "JSON：[{'name': 'tech_question', 'desc': '具体的技术问题', 'value': ''}]\n输入：这个支持Type-C接口吗\n答：{'name': 'tech_question', 'value': '是否支持Type-C接口'}", "parameters": [{"name": "tech_question", "desc": "具体的技术问题", "type": "string", "required": true}, {"name": "product_spec", "desc": "询问的具体规格参数", "type": "string", "required": false}]}, "general_service": {"name": "通用客服", "description": "一般性的咨询、问候、物流查询等其他问题", "example": "JSON：[{'name': 'service_type', 'desc': '服务类型', 'value': ''}]\n输入：什么时候发货\n答：{'name': 'service_type', 'value': '物流查询'}", "parameters": [{"name": "service_type", "desc": "服务类型：物流查询、售后咨询、一般问候等", "type": "string", "required": false}]}, "human_handover": {"name": "人工介入", "description": "需要人工客服介入的场景：大批量采购(≥10件)、高价值议价(≥1000元)、用户投诉情绪、代理商合作、定制需求、复杂技术问题等", "example": "JSON：[{'name': 'handover_reason', 'desc': '需要人工介入的原因', 'value': ''}]\n输入：我要采购50台iPhone，能给批发价吗\n答：{'name': 'handover_reason', 'value': '大批量采购需求', 'urgency_level': '高'}", "parameters": [{"name": "handover_reason", "desc": "需要人工介入的原因：大批量采购、高价值议价、投诉处理、代理合作、定制需求、复杂技术问题等", "type": "string", "required": true}, {"name": "urgency_level", "desc": "紧急程度：高(批量采购/投诉)、中(议价/定制/代理)、低", "type": "string", "required": true}, {"name": "purchase_intent", "desc": "购买意向强度：强、中、弱", "type": "string", "required": false}]}}