Defaulting to user installation because normal site-packages is not writeable
Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple/
Requirement already satisfied: pytest in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (7.4.3)
Requirement already satisfied: pytest-asyncio in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (0.21.1)
Requirement already satisfied: psutil in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (7.0.0)
Collecting aiofiles
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/a5/45/30bb92d442636f570cb5651bc661f52b610e2eec3f891a5dc3a4c3667db0/aiofiles-24.1.0-py3-none-any.whl (15 kB)
Requirement already satisfied: exceptiongroup>=1.0.0rc8 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from pytest) (1.3.0)
Requirement already satisfied: iniconfig in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from pytest) (2.1.0)
Requirement already satisfied: tomli>=1.0.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from pytest) (2.2.1)
Requirement already satisfied: pluggy<2.0,>=0.12 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from pytest) (1.6.0)
Requirement already satisfied: packaging in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from pytest) (25.0)
Requirement already satisfied: typing-extensions>=4.6.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from exceptiongroup>=1.0.0rc8->pytest) (4.14.0)
Installing collected packages: aiofiles
Successfully installed aiofiles-24.1.0
