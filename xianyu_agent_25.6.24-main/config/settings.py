"""
系统配置管理模块

提供统一的配置管理，支持环境变量和配置文件
"""

import os
from typing import Optional, Dict, Any
from dotenv import load_dotenv
from loguru import logger


class Settings:
    """系统配置管理器"""
    
    def __init__(self, env_file: str = ".env"):
        """
        初始化配置管理器
        
        Args:
            env_file: 环境变量文件路径
        """
        self.env_file = env_file
        self._config_cache: Dict[str, Any] = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        # 加载环境变量
        if os.path.exists(self.env_file):
            load_dotenv(self.env_file)

        # 缓存常用配置
        self._config_cache = {
            # API配置
            'api_key': os.getenv("API_KEY"),
            'model_base_url': os.getenv("MODEL_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
            'model_name': os.getenv("MODEL_NAME", "qwen-max"),
            
            # 平台配置
            'cookies_str': os.getenv("COOKIES_STR"),
            
            # 系统配置
            'debug_mode': os.getenv("DEBUG_MODE", "false").lower() == "true",
            'log_level': os.getenv("LOG_LEVEL", "INFO").upper(),
            'related_intent_threshold': float(os.getenv("RELATED_INTENT_THRESHOLD", "0.6")),
            
            # 数据库配置
            'db_path': os.getenv("DB_PATH", "data/chat_history.db"),
            'max_history': int(os.getenv("MAX_HISTORY", "100")),
            
            # WebSocket配置
            'ws_url': os.getenv("WS_URL", "wss://h5api.m.goofish.com/websocket"),
            'message_expire_time': int(os.getenv("MESSAGE_EXPIRE_TIME", "300000")),  # 5分钟
            'token_refresh_interval': int(os.getenv("TOKEN_REFRESH_INTERVAL", "3600")),  # 1小时
            
            # 通知配置
            'notification_enabled': os.getenv("NOTIFICATION_ENABLED", "false").lower() == "true",
            'dingtalk_webhook': os.getenv("DINGTALK_WEBHOOK"),
            'wechat_webhook': os.getenv("WECHAT_WEBHOOK"),
        }
        
        logger.info("配置加载完成")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        return self._config_cache.get(key, default)
    
    def get_api_config(self) -> Dict[str, str]:
        """获取API配置"""
        return {
            'api_key': self.get('api_key'),
            'base_url': self.get('model_base_url'),
            'model_name': self.get('model_name')
        }
    
    def get_db_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return {
            'db_path': self.get('db_path'),
            'max_history': self.get('max_history')
        }
    
    def get_ws_config(self) -> Dict[str, Any]:
        """获取WebSocket配置"""
        return {
            'ws_url': self.get('ws_url'),
            'message_expire_time': self.get('message_expire_time'),
            'token_refresh_interval': self.get('token_refresh_interval')
        }
    
    def get_notification_config(self) -> Dict[str, Any]:
        """获取通知配置"""
        return {
            'enabled': self.get('notification_enabled'),
            'dingtalk_webhook': self.get('dingtalk_webhook'),
            'wechat_webhook': self.get('wechat_webhook')
        }
    
    def is_debug_mode(self) -> bool:
        """是否为调试模式"""
        return self.get('debug_mode', False)
    
    def get_log_level(self) -> str:
        """获取日志级别"""
        return self.get('log_level', 'INFO')
    
    def reload(self):
        """重新加载配置"""
        logger.info("重新加载配置...")
        self._load_config()
        logger.info("配置重新加载完成")
    
    def validate_required_config(self) -> bool:
        """
        验证必需的配置项
        
        Returns:
            bool: 配置是否有效
        """
        required_keys = ['api_key', 'cookies_str']
        missing_keys = []
        
        for key in required_keys:
            if not self.get(key):
                missing_keys.append(key)
        
        if missing_keys:
            logger.error(f"缺少必需的配置项: {missing_keys}")
            return False
        
        logger.info("配置验证通过")
        return True


# 全局配置实例
settings = Settings()
