"""
日志配置模块

提供统一的日志配置和管理
"""

import sys
import os
from pathlib import Path
from loguru import logger
from typing import Optional


def setup_logger(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    rotation: str = "10 MB",
    retention: str = "7 days",
    format_string: Optional[str] = None
) -> None:
    """
    设置全局日志配置
    
    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 日志文件路径，如果为None则只输出到控制台
        rotation: 日志轮转大小
        retention: 日志保留时间
        format_string: 自定义日志格式
    """
    
    # 移除默认的handler
    logger.remove()
    
    # 默认日志格式
    if format_string is None:
        format_string = (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
    
    # 添加控制台输出
    logger.add(
        sys.stderr,
        level=log_level,
        format=format_string,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 如果指定了日志文件，添加文件输出
    if log_file:
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_file,
            level=log_level,
            format=format_string,
            rotation=rotation,
            retention=retention,
            encoding="utf-8",
            backtrace=True,
            diagnose=True
        )
        
        logger.info(f"日志文件已配置: {log_file}")
    
    logger.info(f"日志系统初始化完成，级别: {log_level}")


def get_module_logger(module_name: str):
    """
    获取模块专用的logger
    
    Args:
        module_name: 模块名称
        
    Returns:
        logger实例
    """
    return logger.bind(module=module_name)


def setup_debug_logger():
    """设置调试模式的日志配置"""
    setup_logger(
        log_level="DEBUG",
        log_file="logs/debug.log",
        format_string=(
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{extra[module]}</cyan> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
    )


def setup_production_logger():
    """设置生产环境的日志配置"""
    setup_logger(
        log_level="INFO",
        log_file="logs/app.log",
        rotation="50 MB",
        retention="30 days"
    )


class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        self._module_loggers = {}
    
    def get_logger(self, module_name: str):
        """
        获取或创建模块logger
        
        Args:
            module_name: 模块名称
            
        Returns:
            logger实例
        """
        if module_name not in self._module_loggers:
            self._module_loggers[module_name] = logger.bind(module=module_name)
        
        return self._module_loggers[module_name]
    
    def log_system_info(self):
        """记录系统信息"""
        import platform
        import psutil
        
        logger.info("=== 系统信息 ===")
        logger.info(f"操作系统: {platform.system()} {platform.release()}")
        logger.info(f"Python版本: {platform.python_version()}")
        logger.info(f"CPU核心数: {psutil.cpu_count()}")
        logger.info(f"内存总量: {psutil.virtual_memory().total / (1024**3):.2f} GB")
        logger.info("===============")
    
    def log_module_startup(self, module_name: str, version: str = "1.0.0"):
        """
        记录模块启动信息
        
        Args:
            module_name: 模块名称
            version: 模块版本
        """
        module_logger = self.get_logger(module_name)
        module_logger.info(f"模块 {module_name} v{version} 启动")
    
    def log_module_shutdown(self, module_name: str):
        """
        记录模块关闭信息
        
        Args:
            module_name: 模块名称
        """
        module_logger = self.get_logger(module_name)
        module_logger.info(f"模块 {module_name} 关闭")


# 全局日志管理器实例
logger_manager = LoggerManager()
