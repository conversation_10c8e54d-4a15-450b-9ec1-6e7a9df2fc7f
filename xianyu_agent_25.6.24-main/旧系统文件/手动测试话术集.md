# 闲鱼智能客服系统 - 手动测试话术集

## 🎯 测试目标
验证系统能够正确识别需要人工介入的场景，并准确区分AI可以处理的普通咨询。

## 📋 测试方式
1. 启动系统：`python main.py`
2. 依次输入以下测试话术
3. 观察系统响应，验证是否符合预期结果

---

## ✅ 应该触发人工介入的场景

### 1. 大批量采购场景
**测试目标**：验证系统能识别批量采购需求

**测试话术**：
```
我要采购50台iPhone，能给批发价吗？
```
**预期结果**：触发人工介入，显示"检测到您的需求比较复杂，正在为您转接人工客服..."

**其他测试话术**：
```
我们公司需要批量采购100台电脑，有优惠吗？
想要20个这样的产品，批发价多少？
我要买15台，能便宜点吗？
```

### 2. 投诉退款场景
**测试目标**：验证系统能识别用户投诉情绪

**测试话术**：
```
这个手机质量太差了，我要投诉退款！
```
**预期结果**：触发人工介入

**其他测试话术**：
```
产品有问题，我要退货退款
这东西太垃圾了，要求赔偿
卖家态度太差，我要投诉
收到的货不对，强烈要求退款
```

### 3. 代理商合作场景
**测试目标**：验证系统能识别商业合作意图

**测试话术**：
```
我想做你们的代理商，怎么联系？
```
**预期结果**：触发人工介入

**其他测试话术**：
```
有代理加盟的机会吗？
想成为你们的经销商
可以做区域代理吗？
怎么申请代理权？
```

### 4. 定制需求场景
**测试目标**：验证系统能识别定制化需求

**测试话术**：
```
能定制LOGO吗？我需要100个
```
**预期结果**：触发人工介入

**其他测试话术**：
```
可以按我的要求定制吗？
能不能印上我们公司的标志？
需要特殊规格的产品，能定做吗？
想要个性化定制，怎么办？
```

### 5. 复杂技术场景
**测试目标**：验证系统能识别超出AI能力的复杂问题

**测试话术**：
```
这个产品的详细技术参数文档在哪里下载？需要专业技术支持
```
**预期结果**：触发人工介入

**其他测试话术**：
```
需要详细的技术对接文档
这个设备的API接口怎么调用？
产品的专业认证证书能提供吗？
需要技术工程师现场指导
```

### 6. 高价值议价场景
**测试目标**：验证系统能识别高价值订单的深度议价

**测试话术**：
```
这个2000元的产品，1500元能卖吗？我要5台
```
**预期结果**：触发人工介入

**其他测试话术**：
```
5000块的设备，能给个4000的友情价吗？
这批货总价3万，能便宜3000吗？
大单优惠，10000元的订单打几折？
```

---

## ❌ 不应该触发人工介入的场景

### 1. 普通价格咨询
**测试目标**：验证普通议价由AI处理

**测试话术**：
```
这个价格能便宜点吗？
```
**预期结果**：价格议价场景(price_negotiation)，AI正常处理

**其他测试话术**：
```
有优惠吗？
能打折吗？
最低多少钱？
100块钱怎么样？
```

### 2. 普通技术咨询
**测试目标**：验证基础技术问题由AI处理

**测试话术**：
```
支持Type-C接口吗？
```
**预期结果**：技术咨询场景(technical_inquiry)，AI正常处理

**其他测试话术**：
```
这个是什么材质的？
有哪些颜色可选？
尺寸是多少？
重量多重？
保修期多久？
```

### 3. 一般服务咨询
**测试目标**：验证常规服务问题由AI处理

**测试话术**：
```
什么时候发货？
```
**预期结果**：通用客服场景(general_service)，AI正常处理

**其他测试话术**：
```
包邮吗？
怎么付款？
可以货到付款吗？
物流用什么快递？
```

---

## 🔍 边界测试场景

### 1. 模糊数量表达
**测试目标**：验证系统对模糊大量表达的处理

**测试话术**：
```
我要很多这个产品，大概几十个
```
**观察结果**：看是否触发人工介入

### 2. 轻微不满情绪
**测试目标**：验证系统对轻微负面情绪的处理

**测试话术**：
```
这个产品一般般，不太满意
```
**观察结果**：看是否触发人工介入

### 3. 价格敏感边界
**测试目标**：验证1000元价格边界的处理

**测试话术**：
```
这个999元的商品，能便宜100吗？
```
**观察结果**：看是否触发人工介入

---

## 📊 测试记录表

建议您在测试过程中记录结果：

| 序号 | 测试话术 | 预期结果 | 实际结果 | 状态 | 备注 |
|------|---------|----------|----------|------|------|
| 1 | 我要采购50台iPhone，能给批发价吗？ | 人工介入 |  |  |  |
| 2 | 这个手机质量太差了，我要投诉退款！ | 人工介入 |  |  |  |
| 3 | 我想做你们的代理商，怎么联系？ | 人工介入 |  |  |  |
| 4 | 能定制LOGO吗？我需要100个 | 人工介入 |  |  |  |
| 5 | 这个价格能便宜点吗？ | AI处理 |  |  |  |
| 6 | 支持Type-C接口吗？ | AI处理 |  |  |  |
| 7 | 什么时候发货？ | AI处理 |  |  |  |

---

## 🚨 注意事项

1. **测试环境**：确保已配置好环境变量和API密钥
2. **网络状况**：保证网络稳定，避免API调用失败影响测试结果
3. **重启测试**：每次测试新话术前建议重启系统，避免上下文干扰
4. **记录详细**：记录具体的系统响应内容，便于分析
5. **多次验证**：对于边界场景，建议多次测试确认一致性

## 🎯 成功标准

- ✅ 所有"应该触发人工介入"的场景都正确识别
- ✅ 所有"不应该触发人工介入"的场景都由AI正常处理  
- ✅ 人工介入时显示合适的转接提示信息
- ✅ 系统响应时间在合理范围内（通常3-8秒）

开始测试吧！有任何问题随时告诉我。 🚀 