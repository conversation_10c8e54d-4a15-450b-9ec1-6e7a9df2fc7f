#!/usr/bin/env python3
"""
重构系统测试运行器

运行模块化单体架构重构后的系统测试
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """主函数"""
    print("🚀 闲鱼智能客服系统 - 重构测试")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查项目结构
    required_dirs = [
        'config',
        'core', 
        'modules',
        'tests'
    ]
    
    missing_dirs = []
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            missing_dirs.append(dir_name)
    
    if missing_dirs:
        print(f"❌ 缺少必要目录: {missing_dirs}")
        return False
    
    print("✅ 项目结构检查通过")
    
    # 检查依赖
    try:
        import loguru
        import openai
        print("✅ 核心依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    # 运行测试
    print("\n🧪 开始运行测试...")
    print("-" * 30)
    
    try:
        # 运行重构系统测试
        result = subprocess.run([
            sys.executable, 
            'tests/test_refactored_system.py'
        ], capture_output=True, text=True)
        
        print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n🎉 所有测试通过！")
            print("重构后的模块化单体架构系统功能正常。")
            return True
        else:
            print(f"\n❌ 测试失败，退出码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
