import time
import os
import re
import sys

import requests
from loguru import logger
from utils.xianyu_utils import generate_sign


class XianyuApis:
    def __init__(self):
        self.url = 'https://h5api.m.goofish.com/h5/mtop.taobao.idlemessage.pc.login.token/1.0/'
        self.session = requests.Session()
        self.session.headers.update({
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'origin': 'https://www.goofish.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://www.goofish.com/',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        })
        
    def clear_duplicate_cookies(self):
        """清理重复的cookies"""
        # 创建一个新的CookieJar
        new_jar = requests.cookies.RequestsCookieJar()
        
        # 记录已经添加过的cookie名称
        added_cookies = set()
        
        # 按照cookies列表的逆序遍历（最新的通常在后面）
        cookie_list = list(self.session.cookies)
        cookie_list.reverse()
        
        for cookie in cookie_list:
            # 如果这个cookie名称还没有添加过，就添加到新jar中
            if cookie.name not in added_cookies:
                new_jar.set_cookie(cookie)
                added_cookies.add(cookie.name)
                
        # 替换session的cookies
        self.session.cookies = new_jar
        
        # 更新完cookies后，更新.env文件
        self.update_env_cookies()
        
    def update_env_cookies(self):
        """更新.env文件中的COOKIES_STR"""
        try:
            # 获取当前cookies的字符串形式
            cookie_str = '; '.join([f"{cookie.name}={cookie.value}" for cookie in self.session.cookies])
            
            # 读取.env文件
            env_path = os.path.join(os.getcwd(), '.env')
            if not os.path.exists(env_path):
                logger.warning(".env文件不存在，无法更新COOKIES_STR")
                return
                
            with open(env_path, 'r', encoding='utf-8') as f:
                env_content = f.read()
                
            # 使用正则表达式替换COOKIES_STR的值
            if 'COOKIES_STR=' in env_content:
                new_env_content = re.sub(
                    r'COOKIES_STR=.*', 
                    f'COOKIES_STR={cookie_str}',
                    env_content
                )
                
                # 写回.env文件
                with open(env_path, 'w', encoding='utf-8') as f:
                    f.write(new_env_content)
                    
                logger.debug("已更新.env文件中的COOKIES_STR")
            else:
                logger.warning(".env文件中未找到COOKIES_STR配置项")
        except Exception as e:
            logger.warning(f"更新.env文件失败: {str(e)}")
        
    def hasLogin(self, retry_count=0):
        """调用hasLogin.do接口进行登录状态检查"""
        if retry_count >= 2:
            logger.error("Login检查失败，重试次数过多")
            return False
            
        try:
            url = 'https://passport.goofish.com/newlogin/hasLogin.do'
            params = {
                'appName': 'xianyu',
                'fromSite': '77'
            }
            data = {
                'hid': self.session.cookies.get('unb', ''),
                'ltl': 'true',
                'appName': 'xianyu',
                'appEntrance': 'web',
                '_csrf_token': self.session.cookies.get('XSRF-TOKEN', ''),
                'umidToken': '',
                'hsiz': self.session.cookies.get('cookie2', ''),
                'bizParams': 'taobaoBizLoginFrom=web',
                'mainPage': 'false',
                'isMobile': 'false',
                'lang': 'zh_CN',
                'returnUrl': '',
                'fromSite': '77',
                'isIframe': 'true',
                'documentReferer': 'https://www.goofish.com/',
                'defaultView': 'hasLogin',
                'umidTag': 'SERVER',
                'deviceId': self.session.cookies.get('cna', '')
            }
            
            response = self.session.post(url, params=params, data=data)
            res_json = response.json()
            
            if res_json.get('content', {}).get('success'):
                logger.debug("Login成功")
                # 清理和更新cookies
                self.clear_duplicate_cookies()
                return True
            else:
                logger.warning(f"Login失败: {res_json}")
                time.sleep(0.5)
                return self.hasLogin(retry_count + 1)
                
        except Exception as e:
            logger.error(f"Login请求异常: {str(e)}")
            time.sleep(0.5)
            return self.hasLogin(retry_count + 1)

    def get_token(self, device_id, retry_count=0):
        if retry_count >= 2:  # 最多重试3次
            logger.warning("获取token失败，尝试重新登陆")
            # 尝试通过hasLogin重新登录
            if self.hasLogin():
                logger.info("重新登录成功，重新尝试获取token")
                return self.get_token(device_id, 0)  # 重置重试次数
            else:
                logger.error("重新登录失败，Cookie已失效")
                logger.error("🔴 程序即将退出，请更新.env文件中的COOKIES_STR后重新启动")
                sys.exit(1)  # 直接退出程序
            
        params = {
            'jsv': '2.7.2',
            'appKey': '********',
            't': str(int(time.time()) * 1000),
            'sign': '',
            'v': '1.0',
            'type': 'originaljson',
            'accountSite': 'xianyu',
            'dataType': 'json',
            'timeout': '20000',
            'api': 'mtop.taobao.idlemessage.pc.login.token',
            'sessionOption': 'AutoLoginOnly',
            'spm_cnt': 'a21ybx.im.0.0',
        }
        data_val = '{"appKey":"444e9908a51d1cb236a27862abc769c9","deviceId":"' + device_id + '"}'
        data = {
            'data': data_val,
        }
        
        # 简单获取token，信任cookies已清理干净
        token = self.session.cookies.get('_m_h5_tk', '').split('_')[0]
        
        sign = generate_sign(params['t'], token, data_val)
        params['sign'] = sign
        
        try:
            response = self.session.post('https://h5api.m.goofish.com/h5/mtop.taobao.idlemessage.pc.login.token/1.0/', params=params, data=data)
            res_json = response.json()
            
            if isinstance(res_json, dict):
                ret_value = res_json.get('ret', [])
                # 检查ret是否包含成功信息
                if not any('SUCCESS::调用成功' in ret for ret in ret_value):
                    logger.warning(f"Token API调用失败，错误信息: {ret_value}")
                    # 处理响应中的Set-Cookie
                    if 'Set-Cookie' in response.headers:
                        logger.debug("检测到Set-Cookie，更新cookie")  # 降级为DEBUG并简化
                        self.clear_duplicate_cookies()
                    time.sleep(0.5)
                    return self.get_token(device_id, retry_count + 1)
                else:
                    logger.info("Token获取成功")
                    return res_json
            else:
                logger.error(f"Token API返回格式异常: {res_json}")
                return self.get_token(device_id, retry_count + 1)
                
        except Exception as e:
            logger.error(f"Token API请求异常: {str(e)}")
            time.sleep(0.5)
            return self.get_token(device_id, retry_count + 1)

    def get_item_info(self, item_id, retry_count=0):
        """获取商品信息，自动处理token失效的情况"""
        if retry_count >= 3:  # 最多重试3次
            logger.error("获取商品信息失败，重试次数过多")
            return {"error": "获取商品信息失败，重试次数过多"}
            
        params = {
            'jsv': '2.7.2',
            'appKey': '********',
            't': str(int(time.time()) * 1000),
            'sign': '',
            'v': '1.0',
            'type': 'originaljson',
            'accountSite': 'xianyu',
            'dataType': 'json',
            'timeout': '20000',
            'api': 'mtop.taobao.idle.pc.detail',
            'sessionOption': 'AutoLoginOnly',
            'spm_cnt': 'a21ybx.im.0.0',
        }
        
        data_val = '{"itemId":"' + item_id + '"}'
        data = {
            'data': data_val,
        }
        
        # 简单获取token，信任cookies已清理干净
        token = self.session.cookies.get('_m_h5_tk', '').split('_')[0]
        
        sign = generate_sign(params['t'], token, data_val)
        params['sign'] = sign
        
        try:
            response = self.session.post(
                'https://h5api.m.goofish.com/h5/mtop.taobao.idle.pc.detail/1.0/', 
                params=params, 
                data=data
            )
            
            res_json = response.json()
            # 检查返回状态
            if isinstance(res_json, dict):
                ret_value = res_json.get('ret', [])
                # 检查ret是否包含成功信息
                if not any('SUCCESS::调用成功' in ret for ret in ret_value):
                    logger.warning(f"商品信息API调用失败，错误信息: {ret_value}")
                    # 处理响应中的Set-Cookie
                    if 'Set-Cookie' in response.headers:
                        logger.debug("检测到Set-Cookie，更新cookie")
                        self.clear_duplicate_cookies()
                    time.sleep(0.5)
                    return self.get_item_info(item_id, retry_count + 1)
                else:
                    logger.debug(f"商品信息获取成功: {item_id}")
                    return res_json
            else:
                logger.error(f"商品信息API返回格式异常: {res_json}")
                return self.get_item_info(item_id, retry_count + 1)
                
        except Exception as e:
            logger.error(f"商品信息API请求异常: {str(e)}")
            time.sleep(0.5)
            return self.get_item_info(item_id, retry_count + 1)

    def parse_item_info(self, response_data):
        """
        解析商品信息响应数据，提取关键字段
        
        Args:
            response_data: get_item_info方法返回的原始响应数据
            
        Returns:
            dict: 解析后的结构化商品信息
        """
        try:
            # 检查响应是否成功
            if not isinstance(response_data, dict) or 'data' not in response_data:
                return {"error": "无效的响应数据"}
            
            ret_codes = response_data.get('ret', [])
            if not any('SUCCESS::调用成功' in ret for ret in ret_codes):
                return {"error": f"API调用失败: {ret_codes}"}
            
            data = response_data['data']
            item_do = data.get('itemDO', {})
            seller_do = data.get('sellerDO', {})
            
            # 提取商品基本信息
            item_info = {
                # 商品基本信息
                "item_id": item_do.get('itemId'),
                "title": item_do.get('title', ''),
                "description": item_do.get('desc', ''),
                "price": item_do.get('soldPrice', ''),
                "original_price": item_do.get('originalPrice', '0'),
                "transport_fee": item_do.get('transportFee', '0.00'),
                "create_time": item_do.get('GMT_CREATE_DATE_KEY', ''),
                
                # 商品状态
                "item_status": item_do.get('itemStatusStr', ''),
                "browse_count": item_do.get('browseCnt', 0),
                "collect_count": item_do.get('collectCnt', 0),
                "want_count": item_do.get('wantCnt', 0),
                "favor_count": item_do.get('favorCnt', 0),
                
                # 商品图片
                "images": self._extract_images(item_do.get('imageInfos', [])),
                "main_image": self._get_main_image(item_do.get('imageInfos', [])),
                
                # 商品标签和分类
                "price_tags": [tag.get('text', '') for tag in item_do.get('priceRelativeTags', [])],
                "common_tags": [tag.get('text', '') for tag in item_do.get('commonTags', [])],
                "item_labels": [label.get('text', '') for label in item_do.get('itemLabelExtList', [])],
                "recommend_tags": [tag.get('text', '') for tag in item_do.get('recommendTagList', [])],
                
                # 商品属性
                "properties": self._extract_properties(item_do.get('cpvLabels', [])),
                
                # 卖家信息
                "seller": {
                    "seller_id": seller_do.get('sellerId'),
                    "nickname": seller_do.get('nick', ''),
                    "city": seller_do.get('city', ''),
                    "avatar": seller_do.get('portraitUrl', ''),
                    "last_visit": seller_do.get('lastVisitTime', ''),
                    "reply_ratio_24h": seller_do.get('replyRatio24h', ''),
                    "reply_interval": seller_do.get('replyInterval', ''),
                    "sold_items_count": seller_do.get('hasSoldNumInteger', 0),
                    "total_items_count": seller_do.get('itemCount', 0),
                    "register_days": seller_do.get('userRegDay', 0),
                    "zhima_auth": seller_do.get('zhimaAuth', False),
                    "credit_level": seller_do.get('zhimaLevelInfo', {}).get('levelName', ''),
                    "seller_tags": [tag.get('text', '') for tag in seller_do.get('sellerInfoTags', [])],
                    "identity_tags": [tag.get('text', '') for tag in seller_do.get('identityTags', [])]
                },
                
                # 交易相关
                "support_trade": item_do.get('pcSupportTrade', False),
                "trade_access_type": item_do.get('tradeAccessType', 0),
                "bargained": item_do.get('bargained', False),
                
                # 其他信息
                "quality_url": item_do.get('qualityUrl', ''),
                "report_url": item_do.get('reportUrl', ''),
                "share_data": item_do.get('shareData', {}),
                "rich_text_desc": item_do.get('richTextDesc', ''),
                
                # 解析时间
                "parsed_at": data.get('serverTime', '')
            }
            
            logger.info(f"成功解析商品信息 - ID: {item_info['item_id']}, 标题: {item_info['title'][:50]}...")
            return item_info
            
        except Exception as e:
            logger.error(f"解析商品信息时出错: {str(e)}")
            return {"error": f"解析失败: {str(e)}"}
    
    def _extract_images(self, image_infos):
        """提取商品图片信息"""
        images = []
        for img_info in image_infos:
            if isinstance(img_info, dict) and 'url' in img_info:
                images.append({
                    "url": img_info['url'],
                    "width": img_info.get('widthSize', 0),
                    "height": img_info.get('heightSize', 0),
                    "is_major": img_info.get('major', False),
                    "type": img_info.get('type', 0)
                })
        return images
    
    def _get_main_image(self, image_infos):
        """获取主图片URL"""
        for img_info in image_infos:
            if isinstance(img_info, dict) and img_info.get('major', False):
                return img_info.get('url', '')
        # 如果没有主图，返回第一张图片
        if image_infos and isinstance(image_infos[0], dict):
            return image_infos[0].get('url', '')
        return ''
    
    def _extract_properties(self, cpv_labels):
        """提取商品属性"""
        properties = {}
        for prop in cpv_labels:
            if isinstance(prop, dict):
                prop_name = prop.get('propertyName', '')
                prop_value = prop.get('valueName', '')
                if prop_name and prop_value:
                    properties[prop_name] = prop_value
        return properties

    def get_item_info_parsed(self, item_id):
        """便捷方法：获取商品信息并自动解析"""
        raw_data = self.get_item_info(item_id)
        return self.parse_item_info(raw_data)

    def get_and_save_item_info(self, item_id):
        """
        获取商品信息并保存到数据库
        
        Args:
            item_id: 商品ID
            
        Returns:
            dict: 解析后的商品信息
        """
        try:
            # 从数据库导入上下文管理器
            from 旧系统文件.context_manager import ChatContextManager
            
            # 首先尝试从数据库获取解析后的商品信息
            context_manager = ChatContextManager()
            parsed_item = context_manager.get_parsed_item_info(item_id)
            
            if parsed_item:
                logger.debug(f"从数据库获取到解析后的商品信息: {item_id}")
                return parsed_item
            
            # 数据库中没有解析后的信息，从API获取
            logger.info(f"从API获取商品信息并解析: {item_id}")
            raw_data = self.get_item_info(item_id)
            
            if "error" in raw_data:
                logger.error(f"获取商品信息失败: {raw_data}")
                return raw_data
            
            # 解析商品信息
            parsed_item = self.parse_item_info(raw_data)
            
            if "error" in parsed_item:
                logger.error(f"解析商品信息失败: {parsed_item}")
                return parsed_item
            
            # 保存原始数据到items表
            if 'data' in raw_data and 'itemDO' in raw_data['data']:
                item_info = raw_data['data']['itemDO']
                context_manager.save_item_info(item_id, item_info)
            
            # 保存解析后的数据到详细表
            success = context_manager.save_parsed_item_info(parsed_item)
            
            if success:
                logger.info(f"商品信息已保存到数据库: {item_id}")
            else:
                logger.warning(f"保存商品信息到数据库失败: {item_id}")
            
            return parsed_item
            
        except Exception as e:
            logger.error(f"获取并保存商品信息时出错: {e}")
            return {"error": f"处理失败: {str(e)}"}
