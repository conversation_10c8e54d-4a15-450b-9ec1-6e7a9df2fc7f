import sqlite3
import os
import json
from datetime import datetime
from loguru import logger


class ChatContextManager:
    """
    聊天上下文管理器
    
    负责存储和检索用户与商品之间的对话历史，使用SQLite数据库进行持久化存储。
    支持按会话ID检索对话历史，以及议价次数统计。
    """
    
    def __init__(self, max_history=100, db_path="data/chat_history.db"):
        """
        初始化聊天上下文管理器
        
        Args:
            max_history: 每个对话保留的最大消息数
            db_path: SQLite数据库文件路径
        """
        self.max_history = max_history
        self.db_path = db_path
        self._init_db()
        
    def _init_db(self):
        """初始化数据库表结构"""
        # 确保数据库目录存在
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
            
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建消息表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT NOT NULL,
            item_id TEXT NOT NULL,
            role TEXT NOT NULL,
            content TEXT NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            chat_id TEXT
        )
        ''')
        
        # 检查是否需要添加chat_id字段（兼容旧数据库）
        cursor.execute("PRAGMA table_info(messages)")
        columns = [column[1] for column in cursor.fetchall()]
        if 'chat_id' not in columns:
            cursor.execute('ALTER TABLE messages ADD COLUMN chat_id TEXT')
            logger.info("已为messages表添加chat_id字段")
        
        # 创建索引以加速查询
        cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_user_item ON messages (user_id, item_id)
        ''')
        
        cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_chat_id ON messages (chat_id)
        ''')
        
        cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_timestamp ON messages (timestamp)
        ''')
        
        # 创建基于会话ID的议价次数表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS chat_bargain_counts (
            chat_id TEXT PRIMARY KEY,
            count INTEGER DEFAULT 0,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建商品信息表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS items (
            item_id TEXT PRIMARY KEY,
            data TEXT NOT NULL,
            price REAL,
            description TEXT,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建详细商品信息表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS item_details (
            item_id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            description TEXT,
            price REAL,
            original_price REAL,
            transport_fee REAL,
            create_time TEXT,
            item_status TEXT,
            browse_count INTEGER DEFAULT 0,
            collect_count INTEGER DEFAULT 0,
            want_count INTEGER DEFAULT 0,
            favor_count INTEGER DEFAULT 0,
            main_image TEXT,
            quality_url TEXT,
            report_url TEXT,
            rich_text_desc TEXT,
            support_trade BOOLEAN DEFAULT FALSE,
            trade_access_type INTEGER DEFAULT 0,
            bargained BOOLEAN DEFAULT FALSE,
            seller_id TEXT,
            parsed_at TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (seller_id) REFERENCES sellers(seller_id)
        )
        ''')
        
        # 创建商品图片表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS item_images (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_id TEXT NOT NULL,
            url TEXT NOT NULL,
            width INTEGER DEFAULT 0,
            height INTEGER DEFAULT 0,
            is_major BOOLEAN DEFAULT FALSE,
            image_type INTEGER DEFAULT 0,
            sort_order INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (item_id) REFERENCES item_details(item_id)
        )
        ''')
        
        # 创建商品标签表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS item_tags (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_id TEXT NOT NULL,
            tag_text TEXT NOT NULL,
            tag_type TEXT NOT NULL, -- price_tags, common_tags, item_labels, recommend_tags
            tag_id TEXT,
            bg_color TEXT,
            text_color TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (item_id) REFERENCES item_details(item_id)
        )
        ''')
        
        # 创建商品属性表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS item_properties (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_id TEXT NOT NULL,
            property_name TEXT NOT NULL,
            property_value TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (item_id) REFERENCES item_details(item_id)
        )
        ''')
        
        # 创建卖家信息表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS sellers (
            seller_id TEXT PRIMARY KEY,
            nickname TEXT,
            city TEXT,
            avatar TEXT,
            last_visit TEXT,
            reply_ratio_24h TEXT,
            reply_interval TEXT,
            sold_items_count INTEGER DEFAULT 0,
            total_items_count INTEGER DEFAULT 0,
            register_days INTEGER DEFAULT 0,
            zhima_auth BOOLEAN DEFAULT FALSE,
            credit_level TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建卖家标签表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS seller_tags (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            seller_id TEXT NOT NULL,
            tag_text TEXT NOT NULL,
            tag_type TEXT NOT NULL, -- seller_tags, identity_tags
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (seller_id) REFERENCES sellers(seller_id)
        )
        ''')
        
        # 创建相关索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_item_details_seller ON item_details (seller_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_item_images_item ON item_images (item_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_item_tags_item ON item_tags (item_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_item_properties_item ON item_properties (item_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_seller_tags_seller ON seller_tags (seller_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_item_details_status ON item_details (item_status)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_item_details_price ON item_details (price)')
        
        conn.commit()
        conn.close()
        logger.info(f"聊天历史数据库初始化完成: {self.db_path}")
        

            
    def save_item_info(self, item_id, item_data):
        """
        保存商品信息到数据库
        
        Args:
            item_id: 商品ID
            item_data: 商品信息字典
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 从商品数据中提取有用信息
            price = float(item_data.get('soldPrice', 0))
            description = item_data.get('desc', '')
            
            # 将整个商品数据转换为JSON字符串
            data_json = json.dumps(item_data, ensure_ascii=False)
            
            cursor.execute(
                """
                INSERT INTO items (item_id, data, price, description, last_updated) 
                VALUES (?, ?, ?, ?, ?)
                ON CONFLICT(item_id) 
                DO UPDATE SET data = ?, price = ?, description = ?, last_updated = ?
                """,
                (
                    item_id, data_json, price, description, datetime.now().isoformat(),
                    data_json, price, description, datetime.now().isoformat()
                )
            )
            
            conn.commit()
            logger.debug(f"商品信息已保存: {item_id}")
        except Exception as e:
            logger.error(f"保存商品信息时出错: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def save_parsed_item_info(self, parsed_item_info):
        """
        保存解析后的详细商品信息到数据库
        
        Args:
            parsed_item_info: 解析后的商品信息字典
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            item_id = parsed_item_info.get('item_id')
            if not item_id:
                logger.error("商品ID不能为空")
                return False
                
            # 1. 保存商品详细信息
            cursor.execute('''
                INSERT OR REPLACE INTO item_details (
                    item_id, title, description, price, original_price, transport_fee,
                    create_time, item_status, browse_count, collect_count, want_count,
                    favor_count, main_image, quality_url, report_url, rich_text_desc,
                    support_trade, trade_access_type, bargained, seller_id, parsed_at,
                    updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                item_id,
                parsed_item_info.get('title', ''),
                parsed_item_info.get('description', ''),
                float(parsed_item_info.get('price', 0)) if parsed_item_info.get('price') else 0,
                float(parsed_item_info.get('original_price', 0)) if parsed_item_info.get('original_price') else 0,
                float(parsed_item_info.get('transport_fee', 0)) if parsed_item_info.get('transport_fee') else 0,
                parsed_item_info.get('create_time', ''),
                parsed_item_info.get('item_status', ''),
                parsed_item_info.get('browse_count', 0),
                parsed_item_info.get('collect_count', 0),
                parsed_item_info.get('want_count', 0),
                parsed_item_info.get('favor_count', 0),
                parsed_item_info.get('main_image', ''),
                parsed_item_info.get('quality_url', ''),
                parsed_item_info.get('report_url', ''),
                parsed_item_info.get('rich_text_desc', ''),
                parsed_item_info.get('support_trade', False),
                parsed_item_info.get('trade_access_type', 0),
                parsed_item_info.get('bargained', False),
                parsed_item_info.get('seller', {}).get('seller_id'),
                parsed_item_info.get('parsed_at', ''),
                datetime.now().isoformat()
            ))
            
            # 2. 删除旧的图片信息，保存新的图片信息
            cursor.execute('DELETE FROM item_images WHERE item_id = ?', (item_id,))
            images = parsed_item_info.get('images', [])
            for idx, image in enumerate(images):
                cursor.execute('''
                    INSERT INTO item_images (
                        item_id, url, width, height, is_major, image_type, sort_order
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    item_id,
                    image.get('url', ''),
                    image.get('width', 0),
                    image.get('height', 0),
                    image.get('is_major', False),
                    image.get('type', 0),
                    idx
                ))
            
            # 3. 删除旧的标签信息，保存新的标签信息
            cursor.execute('DELETE FROM item_tags WHERE item_id = ?', (item_id,))
            
            # 价格标签
            for tag in parsed_item_info.get('price_tags', []):
                cursor.execute('''
                    INSERT INTO item_tags (item_id, tag_text, tag_type) VALUES (?, ?, ?)
                ''', (item_id, tag, 'price_tags'))
            
            # 通用标签
            for tag in parsed_item_info.get('common_tags', []):
                cursor.execute('''
                    INSERT INTO item_tags (item_id, tag_text, tag_type) VALUES (?, ?, ?)
                ''', (item_id, tag, 'common_tags'))
            
            # 商品标签
            for tag in parsed_item_info.get('item_labels', []):
                cursor.execute('''
                    INSERT INTO item_tags (item_id, tag_text, tag_type) VALUES (?, ?, ?)
                ''', (item_id, tag, 'item_labels'))
            
            # 推荐标签
            for tag in parsed_item_info.get('recommend_tags', []):
                cursor.execute('''
                    INSERT INTO item_tags (item_id, tag_text, tag_type) VALUES (?, ?, ?)
                ''', (item_id, tag, 'recommend_tags'))
            
            # 4. 删除旧的属性信息，保存新的属性信息
            cursor.execute('DELETE FROM item_properties WHERE item_id = ?', (item_id,))
            properties = parsed_item_info.get('properties', {})
            for prop_name, prop_value in properties.items():
                cursor.execute('''
                    INSERT INTO item_properties (item_id, property_name, property_value)
                    VALUES (?, ?, ?)
                ''', (item_id, prop_name, prop_value))
            
            # 5. 保存卖家信息
            seller_info = parsed_item_info.get('seller', {})
            if seller_info and seller_info.get('seller_id'):
                seller_id = seller_info.get('seller_id')
                cursor.execute('''
                    INSERT OR REPLACE INTO sellers (
                        seller_id, nickname, city, avatar, last_visit, reply_ratio_24h,
                        reply_interval, sold_items_count, total_items_count, register_days,
                        zhima_auth, credit_level, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    seller_id,
                    seller_info.get('nickname', ''),
                    seller_info.get('city', ''),
                    seller_info.get('avatar', ''),
                    seller_info.get('last_visit', ''),
                    seller_info.get('reply_ratio_24h', ''),
                    seller_info.get('reply_interval', ''),
                    seller_info.get('sold_items_count', 0),
                    seller_info.get('total_items_count', 0),
                    seller_info.get('register_days', 0),
                    seller_info.get('zhima_auth', False),
                    seller_info.get('credit_level', ''),
                    datetime.now().isoformat()
                ))
                
                # 6. 删除旧的卖家标签，保存新的卖家标签
                cursor.execute('DELETE FROM seller_tags WHERE seller_id = ?', (seller_id,))
                
                # 卖家标签
                for tag in seller_info.get('seller_tags', []):
                    cursor.execute('''
                        INSERT INTO seller_tags (seller_id, tag_text, tag_type) VALUES (?, ?, ?)
                    ''', (seller_id, tag, 'seller_tags'))
                
                # 身份标签
                for tag in seller_info.get('identity_tags', []):
                    cursor.execute('''
                        INSERT INTO seller_tags (seller_id, tag_text, tag_type) VALUES (?, ?, ?)
                    ''', (seller_id, tag, 'identity_tags'))
            
            conn.commit()
            logger.info(f"解析后的商品详细信息已保存: {item_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存解析后的商品信息时出错: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def get_parsed_item_info(self, item_id):
        """
        从数据库获取解析后的详细商品信息
        
        Args:
            item_id: 商品ID
            
        Returns:
            dict: 商品详细信息字典，如果不存在返回None
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 获取商品基本信息
            cursor.execute('''
                SELECT * FROM item_details WHERE item_id = ?
            ''', (item_id,))
            
            item_row = cursor.fetchone()
            if not item_row:
                return None
            
            # 将行数据转换为字典
            columns = [description[0] for description in cursor.description]
            item_info = dict(zip(columns, item_row))
            
            # 获取商品图片
            cursor.execute('''
                SELECT url, width, height, is_major, image_type, sort_order
                FROM item_images WHERE item_id = ? ORDER BY sort_order
            ''', (item_id,))
            
            images = []
            for img_row in cursor.fetchall():
                images.append({
                    'url': img_row[0],
                    'width': img_row[1],
                    'height': img_row[2],
                    'is_major': bool(img_row[3]),
                    'type': img_row[4]
                })
            item_info['images'] = images
            
            # 获取商品标签
            cursor.execute('''
                SELECT tag_text, tag_type FROM item_tags WHERE item_id = ?
            ''', (item_id,))
            
            price_tags = []
            common_tags = []
            item_labels = []
            recommend_tags = []
            
            for tag_row in cursor.fetchall():
                tag_text, tag_type = tag_row
                if tag_type == 'price_tags':
                    price_tags.append(tag_text)
                elif tag_type == 'common_tags':
                    common_tags.append(tag_text)
                elif tag_type == 'item_labels':
                    item_labels.append(tag_text)
                elif tag_type == 'recommend_tags':
                    recommend_tags.append(tag_text)
            
            item_info['price_tags'] = price_tags
            item_info['common_tags'] = common_tags
            item_info['item_labels'] = item_labels
            item_info['recommend_tags'] = recommend_tags
            
            # 获取商品属性
            cursor.execute('''
                SELECT property_name, property_value FROM item_properties WHERE item_id = ?
            ''', (item_id,))
            
            properties = {}
            for prop_row in cursor.fetchall():
                properties[prop_row[0]] = prop_row[1]
            item_info['properties'] = properties
            
            # 获取卖家信息
            if item_info.get('seller_id'):
                cursor.execute('''
                    SELECT * FROM sellers WHERE seller_id = ?
                ''', (item_info['seller_id'],))
                
                seller_row = cursor.fetchone()
                if seller_row:
                    seller_columns = [description[0] for description in cursor.description]
                    seller_info = dict(zip(seller_columns, seller_row))
                    
                    # 获取卖家标签
                    cursor.execute('''
                        SELECT tag_text, tag_type FROM seller_tags WHERE seller_id = ?
                    ''', (item_info['seller_id'],))
                    
                    seller_tags = []
                    identity_tags = []
                    
                    for tag_row in cursor.fetchall():
                        tag_text, tag_type = tag_row
                        if tag_type == 'seller_tags':
                            seller_tags.append(tag_text)
                        elif tag_type == 'identity_tags':
                            identity_tags.append(tag_text)
                    
                    seller_info['seller_tags'] = seller_tags
                    seller_info['identity_tags'] = identity_tags
                    item_info['seller'] = seller_info
            
            return item_info
            
        except Exception as e:
            logger.error(f"获取解析后的商品信息时出错: {e}")
            return None
        finally:
            conn.close()

    def get_item_info(self, item_id):
        """
        从数据库获取商品信息（原有方法）
        
        Args:
            item_id: 商品ID
            
        Returns:
            dict: 商品信息字典，如果不存在返回None
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "SELECT data FROM items WHERE item_id = ?",
                (item_id,)
            )
            
            result = cursor.fetchone()
            if result:
                return json.loads(result[0])
            return None
        except Exception as e:
            logger.error(f"获取商品信息时出错: {e}")
            return None
        finally:
            conn.close()

    def add_message_by_chat(self, chat_id, user_id, item_id, role, content):
        """
        基于会话ID添加新消息到对话历史
        
        Args:
            chat_id: 会话ID
            user_id: 用户ID (用户消息存真实user_id，助手消息存卖家ID)
            item_id: 商品ID
            role: 消息角色 (user/assistant)
            content: 消息内容
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 插入新消息，使用chat_id作为额外标识
            cursor.execute(
                "INSERT INTO messages (user_id, item_id, role, content, timestamp, chat_id) VALUES (?, ?, ?, ?, ?, ?)",
                (user_id, item_id, role, content, datetime.now().isoformat(), chat_id)
            )
            
            # 检查是否需要清理旧消息（基于chat_id）
            cursor.execute(
                """
                SELECT id FROM messages 
                WHERE chat_id = ? 
                ORDER BY timestamp DESC 
                LIMIT ?, 1
                """, 
                (chat_id, self.max_history)
            )
            
            oldest_to_keep = cursor.fetchone()
            if oldest_to_keep:
                cursor.execute(
                    "DELETE FROM messages WHERE chat_id = ? AND id < ?",
                    (chat_id, oldest_to_keep[0])
                )
            
            conn.commit()
        except Exception as e:
            logger.error(f"添加消息到数据库时出错: {e}")
            conn.rollback()
        finally:
            conn.close()

    def get_context_by_chat(self, chat_id):
        """
        基于会话ID获取对话历史
        
        Args:
            chat_id: 会话ID
            
        Returns:
            list: 包含对话历史的列表
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                """
                SELECT role, content FROM messages 
                WHERE chat_id = ? 
                ORDER BY timestamp ASC
                LIMIT ?
                """, 
                (chat_id, self.max_history)
            )
            
            messages = [{"role": role, "content": content} for role, content in cursor.fetchall()]
            
            # 获取议价次数并添加到上下文中
            bargain_count = self.get_bargain_count_by_chat(chat_id)
            if bargain_count > 0:
                messages.append({
                    "role": "system", 
                    "content": f"议价次数: {bargain_count}"
                })
            
        except Exception as e:
            logger.error(f"获取对话历史时出错: {e}")
            messages = []
        finally:
            conn.close()
        
        return messages

    def increment_bargain_count_by_chat(self, chat_id):
        """
        基于会话ID增加议价次数
        
        Args:
            chat_id: 会话ID
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 使用UPSERT语法直接基于chat_id增加议价次数
            cursor.execute(
                """
                INSERT INTO chat_bargain_counts (chat_id, count, last_updated)
                VALUES (?, 1, ?)
                ON CONFLICT(chat_id) 
                DO UPDATE SET count = count + 1, last_updated = ?
                """,
                (chat_id, datetime.now().isoformat(), datetime.now().isoformat())
            )
            
            conn.commit()
            logger.debug(f"会话 {chat_id} 议价次数已增加")
        except Exception as e:
            logger.error(f"增加议价次数时出错: {e}")
            conn.rollback()
        finally:
            conn.close()

    def get_bargain_count_by_chat(self, chat_id):
        """
        基于会话ID获取议价次数
        
        Args:
            chat_id: 会话ID
            
        Returns:
            int: 议价次数
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "SELECT count FROM chat_bargain_counts WHERE chat_id = ?",
                (chat_id,)
            )
            
            result = cursor.fetchone()
            return result[0] if result else 0
        except Exception as e:
            logger.error(f"获取议价次数时出错: {e}")
            return 0
        finally:
            conn.close() 