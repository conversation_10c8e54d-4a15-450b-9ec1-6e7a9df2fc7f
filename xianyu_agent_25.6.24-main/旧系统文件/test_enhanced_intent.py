#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版意图识别引擎测试脚本
"""

import os
import sys
from dotenv import load_dotenv
from loguru import logger
from enhanced_intent_engine import EnhancedIntentEngine

# 加载环境变量
load_dotenv()

def test_intent_recognition():
    """测试意图识别功能"""
    
    # 检查环境变量
    if not os.getenv("API_KEY") or os.getenv("API_KEY") == "your_api_key_here":
        logger.error("请先配置API_KEY环境变量")
        return False
    
    try:
        # 初始化引擎
        logger.info("正在初始化增强版意图识别引擎...")
        engine = EnhancedIntentEngine()
        
        # 测试用例
        test_cases = [
            {
                "input": "这个价格能便宜点吗？", 
                "item_desc": "iPhone 13 128GB",
                "expected": "price_negotiation"
            },
            {
                "input": "支持Type-C接口吗？",
                "item_desc": "小米充电器", 
                "expected": "technical_inquiry"
            },
            {
                "input": "什么时候发货？",
                "item_desc": "笔记本电脑",
                "expected": "general_service"
            },
            {
                "input": "100块钱怎么样？",
                "item_desc": "二手键盘",
                "expected": "price_negotiation"
            },
            {
                "input": "这个和MacBook Air比怎么样？",
                "item_desc": "华为MateBook",
                "expected": "technical_inquiry"
            },
            {
                "input": "你好",
                "item_desc": "随便什么商品",
                "expected": "general_service"
            }
        ]
        
        logger.info("开始测试意图识别...")
        print("\n" + "="*80)
        print("🧠 增强版意图识别测试结果")
        print("="*80)
        
        success_count = 0
        total_count = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            user_input = test_case["input"]
            item_desc = test_case["item_desc"]
            expected = test_case["expected"]
            
            print(f"\n📝 测试案例 {i}:")
            print(f"   用户输入: {user_input}")
            print(f"   商品描述: {item_desc}")
            print(f"   预期意图: {expected}")
            
            # 进行意图识别
            try:
                detected_intent = engine.recognize_intent(user_input, item_desc)
                legacy_intent = engine.map_to_legacy_intent(detected_intent)
                
                print(f"   识别结果: {detected_intent}")
                print(f"   映射结果: {legacy_intent}")
                
                # 检查结果
                if detected_intent == expected:
                    print(f"   ✅ 测试通过")
                    success_count += 1
                else:
                    print(f"   ❌ 测试失败")
                
                # 提取参数信息
                params = engine.extract_intent_parameters(user_input, detected_intent)
                if params.get("raw_response"):
                    print(f"   📊 参数提取: {params['raw_response'][:100]}...")
                    
            except Exception as e:
                print(f"   💥 识别异常: {e}")
            
            # 重置上下文以避免影响下一个测试
            engine.reset_context()
        
        # 输出总结
        print(f"\n" + "="*80)
        print(f"📊 测试总结:")
        print(f"   总计测试: {total_count}")
        print(f"   成功案例: {success_count}")
        print(f"   成功率: {success_count/total_count*100:.1f}%")
        print("="*80)
        
        # 测试多轮对话相关性
        print(f"\n🔄 测试多轮对话相关性判断...")
        test_multi_turn_conversation(engine)
        
        return success_count == total_count
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        return False


def test_multi_turn_conversation(engine):
    """测试多轮对话相关性判断"""
    
    print(f"\n多轮对话测试案例:")
    
    # 第一轮：价格咨询
    user_input1 = "这个键盘多少钱？"
    intent1 = engine.recognize_intent(user_input1, "机械键盘")
    print(f"第1轮 - 输入: {user_input1}")
    print(f"       识别意图: {intent1}")
    
    # 第二轮：继续讨论价格（应该被识别为相关）
    user_input2 = "能不能便宜50块？"
    intent2 = engine.recognize_intent(user_input2, "机械键盘")
    print(f"第2轮 - 输入: {user_input2}")
    print(f"       识别意图: {intent2}")
    print(f"       相关性: {'✅ 相关' if intent2 == intent1 else '❌ 不相关'}")
    
    # 第三轮：切换到技术问题（应该被识别为新意图）
    user_input3 = "这个支持RGB背光吗？"
    intent3 = engine.recognize_intent(user_input3, "机械键盘")
    print(f"第3轮 - 输入: {user_input3}")
    print(f"       识别意图: {intent3}")
    print(f"       意图切换: {'✅ 正确切换' if intent3 != intent2 else '❌ 未切换'}")


def test_scene_template_loading():
    """测试场景模板加载"""
    
    try:
        from enhanced_intent_engine import SceneTemplateLoader
        
        print(f"\n🔧 测试场景模板加载...")
        configs = SceneTemplateLoader.load_all_scene_configs()
        
        print(f"已加载场景数量: {len(configs)}")
        for scene_name, scene_info in configs.items():
            print(f"  - {scene_name}: {scene_info.get('name', 'Unknown')}")
        
        return len(configs) > 0
        
    except Exception as e:
        logger.error(f"场景模板加载测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🚀 启动增强版意图识别引擎测试")
    print("="*80)
    
    # 设置日志级别
    logger.remove()
    logger.add(sys.stderr, level="INFO")
    
    # 执行测试
    try:
        # 测试场景模板加载
        scene_test_passed = test_scene_template_loading()
        
        if scene_test_passed:
            # 测试意图识别
            intent_test_passed = test_intent_recognition()
            
            if intent_test_passed:
                print(f"\n🎉 所有测试通过！增强版意图识别引擎工作正常。")
                sys.exit(0)
            else:
                print(f"\n⚠️  部分测试失败，请检查配置和模型响应。")
                sys.exit(1)
        else:
            print(f"\n❌ 场景模板加载失败，请检查配置文件。")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n\n🛑 测试被用户中断")
        sys.exit(130)
    except Exception as e:
        logger.error(f"测试异常终止: {e}")
        sys.exit(1) 