<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲鱼智能客服</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .app-window {
            width: 1400px;
            height: 900px;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .app-titlebar {
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            -webkit-app-region: drag;
        }

        .app-title {
            color: white;
            font-size: 14px;
            font-weight: 500;
            user-select: none;
        }

        .app-controls {
            display: flex;
            gap: 8px;
            -webkit-app-region: no-drag;
        }

        .control-btn {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .control-btn.close {
            background: #ff5f56;
        }

        .control-btn.minimize {
            background: #ffbd2e;
        }

        .control-btn.maximize {
            background: #27ca3f;
        }

        .control-btn:hover {
            opacity: 0.8;
        }

        .container {
            display: flex;
            height: calc(100% - 40px);
            backdrop-filter: blur(20px);
        }

        /* 左侧菜单栏 */
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            padding: 24px;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow-y: auto;
        }

        .logo {
            display: flex;
            align-items: center;
            margin-bottom: 32px;
            color: white;
            font-size: 18px;
            font-weight: 600;
        }

        .logo-icon {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 14px 18px;
            margin-bottom: 6px;
            border-radius: 12px;
            color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            font-size: 15px;
        }

        .menu-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .menu-item.active {
            background: rgba(255, 255, 255, 0.15);
            color: white;
        }

        .menu-item.has-submenu::after {
            content: '▸';
            margin-left: auto;
            transition: transform 0.3s ease;
        }

        .menu-item.has-submenu.expanded::after {
            transform: rotate(90deg);
        }

        .menu-icon {
            width: 16px;
            height: 16px;
            margin-right: 12px;
        }

        .submenu {
            margin-left: 20px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .submenu.expanded {
            max-height: 200px;
        }

        .submenu .menu-item {
            padding: 8px 12px;
            font-size: 14px;
        }

        .start-button {
            margin-top: auto;
            padding: 12px;
            background: linear-gradient(135deg, #00ff7f, #32cd32);
            border: none;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .start-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 255, 127, 0.3);
        }

        .status-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            margin-top: 10px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .status-indicator {
            display: flex;
            align-items: center;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            margin-right: 6px;
        }

        .status-stopped {
            background: #ff4757;
        }

        .status-running {
            background: #00ff7f;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
        }

        .top-bar {
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 16px;
        }

        .content-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        /* 通用样式 */
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px;
            margin-bottom: 20px;
        }

        .tab-bar {
            display: flex;
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 4px;
        }

        .tab-item {
            flex: 1;
            padding: 10px 16px;
            text-align: center;
            border-radius: 8px;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-item.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: white;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            font-size: 14px;
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #00ff7f, #32cd32);
            color: white;
        }

        .alert {
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .alert-success {
            background: rgba(0, 255, 127, 0.2);
            border: 1px solid rgba(0, 255, 127, 0.3);
            color: #00ff7f;
        }

        .page-content {
            display: none;
        }

        .page-content.active {
            display: block;
        }

        /* 商品卡片 */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .product-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 16px;
            color: white;
        }

        .product-image {
            width: 100%;
            height: 150px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .product-title {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .product-price {
            color: #ff6b6b;
            font-size: 18px;
            font-weight: 700;
        }

        /* 表格样式 */
        .table-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            color: white;
        }

        .table th,
        .table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .table th {
            background: rgba(255, 255, 255, 0.1);
            font-weight: 600;
        }

        /* 聊天界面 */
        .chat-layout {
            display: flex;
            height: calc(100% - 160px);
            gap: 20px;
        }

        .chat-sidebar {
            width: 300px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 16px;
        }

        .chat-main {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 16px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
            font-weight: 600;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .message {
            margin-bottom: 16px;
            padding: 12px 16px;
            border-radius: 16px;
            max-width: 80%;
        }

        .message.user {
            background: rgba(102, 126, 234, 0.3);
            margin-left: auto;
            color: white;
        }

        .message.bot {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
        }

        .chat-input {
            padding: 16px 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            gap: 12px;
        }

        .chat-input input {
            flex: 1;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
        }

        /* 统计图表 */
        .stats-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px;
            color: white;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
        }

        .chart-placeholder {
            height: 200px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.5);
        }

        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="app-window">
        <!-- 应用标题栏 -->
        <div class="app-titlebar">
            <div class="app-title">闲鱼智能客服系统</div>
            <div class="app-controls">
                <div class="control-btn minimize"></div>
                <div class="control-btn maximize"></div>
                <div class="control-btn close"></div>
            </div>
        </div>
        
        <div class="container">
            <!-- 左侧菜单栏 -->
            <div class="sidebar">
            <div class="logo">
                <div class="logo-icon">💬</div>
                <span>闲鱼智能客服</span>
            </div>

            <div class="menu-item" data-page="message-monitor">
                <span class="menu-icon">📊</span>
                <span>消息监控</span>
            </div>

            <div class="menu-item" data-page="product-management">
                <span class="menu-icon">📦</span>
                <span>商品管理</span>
            </div>

            <div class="menu-item has-submenu" data-page="config-management">
                <span class="menu-icon">⚙️</span>
                <span>配置管理</span>
            </div>
            <div class="submenu">
                <div class="menu-item" data-page="basic-config">
                    <span>基础配置</span>
                </div>
                <div class="menu-item active" data-page="cookie-management">
                    <span>Cookie管理</span>
                </div>
                <div class="menu-item" data-page="api-config">
                    <span>API配置</span>
                </div>
            </div>

            <div class="menu-item" data-page="agent-management">
                <span class="menu-icon">🤖</span>
                <span>Agent管理</span>
            </div>

            <div class="menu-item" data-page="sensitive-words">
                <span class="menu-icon">🔍</span>
                <span>敏感词管理</span>
            </div>

            <div class="menu-item" data-page="statistics">
                <span class="menu-icon">📈</span>
                <span>数据统计</span>
            </div>

            <div class="menu-item" data-page="system-logs">
                <span class="menu-icon">📝</span>
                <span>系统日志</span>
            </div>

            <div class="menu-item" data-page="about">
                <span class="menu-icon">ℹ️</span>
                <span>关于</span>
            </div>

            <button class="start-button">🟢 启动</button>
            
            <div class="status-bar">
                <div class="status-indicator">
                    <div class="status-dot status-stopped"></div>
                    <span>服务已停止</span>
                </div>
                <span>WebSocket未连接</span>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="top-bar" id="page-title">Cookie管理 - 闲鱼智能客服</div>

            <div class="content-area">
                <!-- Cookie管理页面 -->
                <div class="page-content active" id="cookie-management">
                    <div class="tab-bar">
                        <div class="tab-item">基础配置</div>
                        <div class="tab-item active">Cookie管理</div>
                        <div class="tab-item">API配置</div>
                    </div>

                    <div class="alert alert-success">
                        <span>✅ Cookie状态正常</span>
                        <span style="margin-left: 16px;">当前Cookie配置有效，可以正常使用闲鱼服务</span>
                        <div style="margin-left: auto; display: flex; gap: 10px;">
                            <button class="btn btn-success">🔍 验证Cookie</button>
                            <button class="btn btn-primary">保存配置</button>
                        </div>
                    </div>

                    <div class="glass-card">
                        <h3 style="color: white; margin-bottom: 16px;">📝 Cookie配置</h3>
                        <div class="form-group">
                            <label class="form-label">* Cookie字符</label>
                            <textarea class="form-input" rows="6" placeholder="请粘贴闲鱼网站的Cookie字符串..."></textarea>
                            <small style="color: rgba(255,255,255,0.6); margin-top: 8px; display: block;">
                                💡 从浏览器开发者工具中获取完整的Cookie信息，格式类似：sessionid=xxx; ctoken=xxx; ...
                            </small>
                        </div>
                    </div>
                </div>

                <!-- 商品管理页面 -->
                <div class="page-content" id="product-management">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2 style="color: white;">商品管理</h2>
                        <div style="display: flex; gap: 12px;">
                            <button class="btn btn-primary">📥 导出数据</button>
                            <button class="btn btn-success">🔄 刷新商品</button>
                        </div>
                    </div>

                    <div class="tab-bar">
                        <div class="tab-item active">商品列表</div>
                        <div class="tab-item">知识库管理</div>
                        <div class="tab-item">虚拟发货管理</div>
                    </div>

                    <div style="display: flex; gap: 16px; margin-bottom: 20px;">
                        <input class="form-input" placeholder="搜索商品标题、ID或描述..." style="flex: 1;">
                        <select class="form-input" style="width: 150px;">
                            <option>全部状态</option>
                        </select>
                        <select class="form-input" style="width: 150px;">
                            <option>全部类型</option>
                        </select>
                        <button class="btn btn-primary">搜索</button>
                    </div>

                    <div class="product-grid">
                        <div class="product-card">
                            <div class="product-image">📱</div>
                            <div class="product-title">超值套餐</div>
                            <div class="product-price">¥0.01</div>
                            <div style="margin-top: 12px; display: flex; gap: 8px;">
                                <button class="btn btn-primary" style="font-size: 12px; padding: 6px 12px;">设置</button>
                                <button class="btn" style="font-size: 12px; padding: 6px 12px; background: #ff4757; color: white;">删除</button>
                            </div>
                        </div>
                        <div class="product-card">
                            <div class="product-image">🧪</div>
                            <div class="product-title">测试商品-状态流程验证</div>
                            <div class="product-price">¥0</div>
                            <div style="margin-top: 12px; display: flex; gap: 8px;">
                                <button class="btn btn-primary" style="font-size: 12px; padding: 6px 12px;">设置</button>
                                <button class="btn" style="font-size: 12px; padding: 6px 12px; background: #ff4757; color: white;">删除</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 敏感词管理页面 -->
                <div class="page-content" id="sensitive-words">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2 style="color: white;">敏感词管理</h2>
                        <button class="btn btn-success">➕ 添加敏感词</button>
                    </div>

                    <div style="display: flex; gap: 16px; margin-bottom: 20px;">
                        <input class="form-input" placeholder="搜索敏感词..." style="flex: 1;">
                        <select class="form-input" style="width: 120px;">
                            <option>选择分类</option>
                        </select>
                        <select class="form-input" style="width: 120px;">
                            <option>选择状态</option>
                        </select>
                        <button class="btn" style="background: #6c757d; color: white;">清除筛选</button>
                        <button class="btn btn-primary">🔄 刷新</button>
                    </div>

                    <div style="color: white; margin-bottom: 16px;">敏感词列表 (19)</div>

                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>敏感词</th>
                                    <th>分类</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>微信</td>
                                    <td><span style="background: #ffc107; color: #000; padding: 2px 8px; border-radius: 4px; font-size: 12px;">系统预设</span></td>
                                    <td><span style="background: #28a745; color: #fff; padding: 2px 8px; border-radius: 4px; font-size: 12px;">已启用</span></td>
                                    <td>2025/5/27 11:21:31</td>
                                    <td>
                                        <button class="btn" style="background: #ffc107; color: #000; font-size: 12px; padding: 4px 8px;">禁用</button>
                                        <button class="btn" style="background: #6c757d; color: #fff; font-size: 12px; padding: 4px 8px;">系统词</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>QQ</td>
                                    <td><span style="background: #ffc107; color: #000; padding: 2px 8px; border-radius: 4px; font-size: 12px;">系统预设</span></td>
                                    <td><span style="background: #28a745; color: #fff; padding: 2px 8px; border-radius: 4px; font-size: 12px;">已启用</span></td>
                                    <td>2025/5/27 11:21:31</td>
                                    <td>
                                        <button class="btn" style="background: #ffc107; color: #000; font-size: 12px; padding: 4px 8px;">禁用</button>
                                        <button class="btn" style="background: #6c757d; color: #fff; font-size: 12px; padding: 4px 8px;">系统词</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>支付宝</td>
                                    <td><span style="background: #ffc107; color: #000; padding: 2px 8px; border-radius: 4px; font-size: 12px;">系统预设</span></td>
                                    <td><span style="background: #28a745; color: #fff; padding: 2px 8px; border-radius: 4px; font-size: 12px;">已启用</span></td>
                                    <td>2025/5/27 11:21:31</td>
                                    <td>
                                        <button class="btn" style="background: #ffc107; color: #000; font-size: 12px; padding: 4px 8px;">禁用</button>
                                        <button class="btn" style="background: #6c757d; color: #fff; font-size: 12px; padding: 4px 8px;">系统词</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Agent管理页面 -->
                <div class="page-content" id="agent-management">
                    <div style="display: flex; gap: 20px; height: calc(100% - 180px);">
                        <div style="width: 320px;">
                            <h3 style="color: white; margin-bottom: 16px;">👨‍💼 Agent管理</h3>
                            <button class="btn btn-success" style="width: 100%; margin-bottom: 16px;">➕ 新建Agent</button>
                            
                            <div class="glass-card" style="background: rgba(255, 215, 0, 0.2); border-color: rgba(255, 215, 0, 0.3);">
                                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                    <div style="width: 8px; height: 8px; background: #00ff7f; border-radius: 50%; margin-right: 8px;"></div>
                                    <span style="color: white; font-weight: 600;">系统提示词</span>
                                </div>
                                <p style="color: rgba(255,255,255,0.8); font-size: 14px;">处理所有类型的用户咨询，可以调用其他专业Agent</p>
                                <div style="margin-top: 12px; display: flex; gap: 8px;">
                                    <button class="btn" style="background: transparent; border: 1px solid rgba(255,255,255,0.3); color: white; font-size: 12px; padding: 4px 8px;">已启用</button>
                                    <button class="btn" style="background: transparent; border: 1px solid rgba(255,255,255,0.3); color: white; font-size: 12px; padding: 4px 8px;">📝</button>
                                    <button class="btn" style="background: transparent; border: 1px solid rgba(255,255,255,0.3); color: white; font-size: 12px; padding: 4px 8px;">🗑️</button>
                                </div>
                            </div>
                        </div>

                        <div style="flex: 1;">
                            <h3 style="color: white; margin-bottom: 16px;">👤 系统提示词 - Agent配置</h3>
                            
                            <div style="color: rgba(255,255,255,0.7); margin-bottom: 20px; padding: 12px; background: rgba(255,255,255,0.05); border-radius: 8px;">
                                ℹ️ 所有Agent默认可以访问全部模块识别（商品库、FAQ库、政策库），保存后立即生效。
                            </div>

                            <div class="form-group">
                                <label class="form-label">* Agent名称</label>
                                <input class="form-input" value="系统提示词" placeholder="系统提示词">
                            </div>

                            <div class="form-group">
                                <label class="form-label">* 功能描述</label>
                                <input class="form-input" placeholder="处理所有类型的用户咨询，可以调用其他专业Agent">
                            </div>

                            <div class="form-group">
                                <label class="form-label">启用状态</label>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="color: rgba(255,255,255,0.7);">已禁用</span>
                                    <label style="position: relative; display: inline-block; width: 50px; height: 24px;">
                                        <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;">
                                        <span style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background: #667eea; transition: 0.4s; border-radius: 24px;"></span>
                                        <span style="position: absolute; content: ''; height: 18px; width: 18px; left: 3px; bottom: 3px; background: white; transition: 0.4s; border-radius: 50%;"></span>
                                    </label>
                                    <span style="color: white;">已启用（默认Agent不可禁用）</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">* Agent提示词</label>
                                <textarea class="form-input" rows="12" style="font-family: monospace; line-height: 1.6;">你是一个专业的虚拟商品引流转化专家，专门销售虚拟教程、团购券以及引流型虚拟商品。你的核心目标是通过低价（0.01-0.1元）商品进行引流到指定渠道，我们CPA转化收益。

## 核心商品逻辑销售 (无需同买家核)

- 商品定价较低 (0.01-0.1元)，主要目标是引流到微信非接转利
- 真正收益来源：用户添加的策源使用优惠券更到微店CPA收成
- 商品本质：获取优惠券的方法教程，而非优惠券本身
- 转化路径：闲鱼商品 → 微信公众号 → CPA转化

## 沟通策略与技巧

### 1. 意图识别与应对
**商品使用咨询（高转化意向）**</textarea>
                                <div style="text-align: right; margin-top: 8px; color: rgba(255,255,255,0.5); font-size: 12px;">字符数: 1826</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 消息监控页面 -->
                <div class="page-content" id="message-monitor">
                    <div class="chat-layout">
                        <div class="chat-sidebar">
                            <h3 style="color: white; margin-bottom: 16px;">消息 27</h3>
                            <input class="form-input" placeholder="搜索对话..." style="margin-bottom: 16px;">
                            
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <div style="padding: 12px; background: rgba(255,255,255,0.1); border-radius: 8px; cursor: pointer;">
                                    <div style="display: flex; align-items: center; margin-bottom: 4px;">
                                        <div style="width: 32px; height: 32px; background: #17a2b8; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; margin-right: 8px; font-size: 14px;">22</div>
                                        <div>
                                            <div style="color: white; font-size: 14px;">用户227952...</div>
                                            <div style="color: rgba(255,255,255,0.6); font-size: 12px;">2025/6/17 22:08:04</div>
                                        </div>
                                    </div>
                                    <div style="color: rgba(255,255,255,0.7); font-size: 12px;">[助手] 快给ta一...</div>
                                </div>

                                <div style="padding: 12px; background: rgba(255,255,255,0.1); border-radius: 8px; cursor: pointer;">
                                    <div style="display: flex; align-items: center; margin-bottom: 4px;">
                                        <div style="width: 32px; height: 32px; background: #17a2b8; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; margin-right: 8px; font-size: 14px;">29</div>
                                        <div>
                                            <div style="color: white; font-size: 14px;">用户297491...</div>
                                            <div style="color: rgba(255,255,255,0.6); font-size: 12px;">2025/6/17 21:48:20</div>
                                        </div>
                                    </div>
                                    <div style="color: rgba(255,255,255,0.7); font-size: 12px;">[助手] 发来一条...</div>
                                </div>
                            </div>
                        </div>

                        <div class="chat-main">
                            <div class="chat-header">
                                <div style="display: flex; align-items: center;">
                                    <div style="width: 40px; height: 40px; background: #17a2b8; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; margin-right: 12px; font-size: 16px;">22</div>
                                    <div>
                                        <div>用户227952...</div>
                                        <div style="font-size: 12px; color: rgba(255,255,255,0.7);">用户ID: 2279520684</div>
                                    </div>
                                </div>
                            </div>

                            <div class="chat-messages">
                                <div style="text-align: center; color: rgba(255,255,255,0.5); margin-bottom: 16px;">发来一条新消息</div>
                                <div style="text-align: center; color: rgba(255,255,255,0.5); font-size: 12px; margin-bottom: 20px;">2025/06/17 21:48</div>

                                <div class="message user">在吗</div>
                                <div style="text-align: right; color: rgba(255,255,255,0.5); font-size: 11px; margin-bottom: 16px;">2025/06/17 22:06</div>

                                <div class="message bot">亲我在呢 有什么可以帮忙的吗</div>
                                <div style="color: rgba(255,255,255,0.5); font-size: 11px; margin-bottom: 16px;">2025/06/17 22:06</div>

                                <div style="text-align: center; color: rgba(255,255,255,0.5); background: rgba(255,255,255,0.1); padding: 8px; border-radius: 8px; margin: 16px 0;">买家确认收货，交易成功</div>
                                <div style="text-align: center; color: rgba(255,255,255,0.5); font-size: 12px; margin-bottom: 16px;">2025/06/17 22:07</div>

                                <div style="text-align: center; color: rgba(255,255,255,0.5); margin-bottom: 16px;">快给ta一个评价呢～</div>
                            </div>

                            <div class="chat-input">
                                <input placeholder="输入回复内容... (Ctrl+Enter 发送)">
                                <button class="btn btn-primary">发送</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据统计页面 -->
                <div class="page-content" id="statistics">
                    <div class="stats-grid">
                        <div class="chart-container">
                            <div class="chart-title">📊 消息趋势分析</div>
                            <div class="chart-placeholder">消息趋势图表</div>
                        </div>
                        <div class="chart-container">
                            <div class="chart-title">🎯 意图分布</div>
                            <div class="chart-placeholder">
                                <div style="text-align: center;">
                                    <div style="margin-bottom: 8px;">意图分布饼图</div>
                                    <div style="font-size: 12px;">
                                        <span style="color: #ff6b6b;">■ 价格咨询</span><br>
                                        <span style="color: #4ecdc4;">■ 技术咨询</span><br>
                                        <span style="color: #45b7d1;">■ 通用咨询</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="border-left: 4px solid #667eea; padding-left: 16px; margin-bottom: 30px;">
                        <h3 style="color: white; margin-bottom: 16px;">📈 详细统计</h3>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div class="chart-container">
                            <div class="chart-title">⏰ 24小时活跃度分析</div>
                            <div class="chart-placeholder">24小时活跃度图表</div>
                        </div>
                        <div class="chart-container">
                            <div class="chart-title">👥 用户行为分析</div>
                            <div class="chart-placeholder">用户行为分析图表</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script>
        // 菜单展开/收起功能
        document.querySelectorAll('.menu-item.has-submenu').forEach(item => {
            item.addEventListener('click', function(e) {
                e.stopPropagation();
                this.classList.toggle('expanded');
                const submenu = this.nextElementSibling;
                submenu.classList.toggle('expanded');
            });
        });

        // 默认展开配置管理
        document.querySelector('[data-page="config-management"]').classList.add('expanded');
        document.querySelector('.submenu').classList.add('expanded');

        // 菜单切换功能
        document.querySelectorAll('.menu-item:not(.has-submenu)').forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有active状态
                document.querySelectorAll('.menu-item').forEach(mi => {
                    mi.classList.remove('active');
                });
                
                // 隐藏所有页面
                document.querySelectorAll('.page-content').forEach(page => {
                    page.classList.remove('active');
                });
                
                // 激活当前菜单项
                this.classList.add('active');
                
                // 显示对应页面
                const pageId = this.getAttribute('data-page');
                const targetPage = document.getElementById(pageId);
                if (targetPage) {
                    targetPage.classList.add('active');
                    
                    // 更新标题
                    const pageTitle = this.textContent + ' - 闲鱼智能客服';
                    document.getElementById('page-title').textContent = pageTitle;
                }
            });
        });

        // 启动按钮功能
        document.querySelector('.start-button').addEventListener('click', function() {
            const statusDot = document.querySelector('.status-dot');
            const statusText = document.querySelector('.status-indicator span');
            const websocketStatus = document.querySelector('.status-bar span:last-child');
            
            if (statusDot.classList.contains('status-stopped')) {
                // 启动服务
                statusDot.classList.remove('status-stopped');
                statusDot.classList.add('status-running');
                statusText.textContent = '服务运行中';
                websocketStatus.textContent = 'WebSocket已连接';
                this.textContent = '🔴 停止';
                this.style.background = 'linear-gradient(135deg, #ff4757, #ff3742)';
            } else {
                // 停止服务
                statusDot.classList.remove('status-running');
                statusDot.classList.add('status-stopped');
                statusText.textContent = '服务已停止';
                websocketStatus.textContent = 'WebSocket未连接';
                this.textContent = '🟢 启动';
                this.style.background = 'linear-gradient(135deg, #00ff7f, #32cd32)';
            }
        });

        // 标签页切换
        document.querySelectorAll('.tab-item').forEach(item => {
            item.addEventListener('click', function() {
                const parent = this.parentElement;
                parent.querySelectorAll('.tab-item').forEach(tab => {
                    tab.classList.remove('active');
                });
                this.classList.add('active');
            });
        });
    </script>
</body>
</html> 