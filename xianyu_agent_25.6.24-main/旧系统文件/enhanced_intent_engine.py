# -*- coding: utf-8 -*-
"""
增强版意图识别引擎
集成Cornucopia-Agent-main项目的智能意图识别机制
"""

import json
import os
import glob
import re
from typing import Dict, List, Optional, Tuple
from loguru import logger
from openai import OpenAI


class SceneTemplateLoader:
    """场景模板加载器"""
    
    @staticmethod
    def load_scene_templates(file_path: str) -> Dict:
        """从JSON文件加载场景模板"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return json.load(file)
        except Exception as e:
            logger.error(f"加载场景模板失败: {file_path}, 错误: {e}")
            return {}

    @staticmethod
    def load_all_scene_configs() -> Dict:
        """加载所有场景配置"""
        all_scene_configs = {}
        
        # 搜索scene_config目录下的所有json文件
        pattern = "scene_config/**/*.json"
        for file_path in glob.glob(pattern, recursive=True):
            logger.debug(f"正在加载场景配置: {file_path}")
            current_config = SceneTemplateLoader.load_scene_templates(file_path)
            
            for key, value in current_config.items():
                if key not in all_scene_configs:
                    all_scene_configs[key] = value
                    logger.debug(f"已加载场景: {key}")
                else:
                    logger.warning(f"场景 {key} 已存在，跳过重复加载")
        
        logger.info(f"总共加载了 {len(all_scene_configs)} 个场景配置")
        return all_scene_configs


class IntentExtractor:
    """意图提取器"""
    
    def __init__(self, client: OpenAI):
        self.client = client

    def extract_float_from_text(self, text: str) -> float:
        """从文本中提取浮点数"""
        try:
            # 查找第一个浮点数
            match = re.search(r'\d+\.?\d*', text)
            return float(match.group()) if match else 0.0
        except:
            return 0.0

    def extract_continuous_digits(self, text: str) -> List[str]:
        """提取文本中的连续数字"""
        return re.findall(r'\d+', text)


class EnhancedIntentEngine:
    """增强版意图识别引擎"""
    
    def __init__(self):
        self.client = OpenAI(
            api_key=os.getenv("API_KEY"),
            base_url=os.getenv("MODEL_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
        )
        
        # 加载场景配置
        self.scene_templates = SceneTemplateLoader.load_all_scene_configs()
        self.current_intent = None
        self.intent_context = {}  # 存储意图上下文信息
        self.related_threshold = float(os.getenv("RELATED_INTENT_THRESHOLD", "0.6"))
        
        # 初始化意图提取器
        self.extractor = IntentExtractor(self.client)
        
        logger.info(f"意图引擎初始化完成，加载了 {len(self.scene_templates)} 个场景")

    def is_related_to_last_intent(self, user_input: str) -> bool:
        """判断当前输入是否与上一次意图相关"""
        if not self.current_intent or self.current_intent not in self.scene_templates:
            return False
            
        scene_desc = self.scene_templates[self.current_intent]['description']
        
        # 构建相关性判断的提示词
        prompt = f"""判断当前用户输入内容与当前对话场景的关联性:

当前对话场景: {scene_desc}
当前用户输入: {user_input}

请仅用小数回答关联度，得分范围0.0至1.0，例如：0.8"""

        try:
            response = self._call_llm(prompt)
            relevance_score = self.extractor.extract_float_from_text(response)
            
            logger.debug(f"意图相关性得分: {relevance_score}, 阈值: {self.related_threshold}")
            return relevance_score > self.related_threshold
            
        except Exception as e:
            logger.error(f"判断意图相关性时出错: {e}")
            return False

    def recognize_intent(self, user_input: str, item_desc: str = "", context: str = "") -> str:
        """
        智能意图识别
        
        Args:
            user_input: 用户输入
            item_desc: 商品描述
            context: 对话上下文
            
        Returns:
            识别到的意图名称
        """
        # 1. 检查是否与上次意图相关
        if self.is_related_to_last_intent(user_input):
            logger.info(f"用户输入与当前意图 '{self.current_intent}' 相关，继续当前场景")
            return self.current_intent

        # 2. 动态构建场景选项
        purpose_options = {}
        purpose_descriptions = {}
        index = 1
        
        for template_key, template_info in self.scene_templates.items():
            purpose_options[str(index)] = template_key
            purpose_descriptions[str(index)] = f"{template_info['name']} - {template_info['description']}"
            index += 1
        
        # 3. 构建选项提示
        options_prompt = "\n".join([
            f"{key}. {value}" for key, value in purpose_descriptions.items()
        ])
        options_prompt += "\n0. 其他场景"

        # 4. 构建完整的意图识别提示
        full_prompt = f"""你是一个智能意图识别助手，需要根据用户输入判断用户的真实意图。

商品信息: {item_desc}
对话历史: {context}
用户当前输入: {user_input}

可选择的场景：
{options_prompt}

请根据用户输入的内容，选择最匹配的场景序号。只需要回复数字，如：1、2、3等。
如果没有合适的场景，请回复：0"""

        try:
            # 5. 调用LLM进行意图识别
            response = self._call_llm(full_prompt)
            user_choices = self.extractor.extract_continuous_digits(response)
            
            logger.debug(f"LLM意图识别响应: {response}")
            logger.debug(f"提取的选择: {user_choices}")
            
            # 6. 解析识别结果
            if user_choices and user_choices[0] != '0':
                choice_index = user_choices[0]
                if choice_index in purpose_options:
                    detected_intent = purpose_options[choice_index]
                    self.current_intent = detected_intent
                    
                    # 记录意图切换
                    scene_info = self.scene_templates[detected_intent]
                    logger.info(f"识别到新意图: {detected_intent} ({scene_info['name']})")
                    
                    return detected_intent
            
            # 7. 未识别到有效意图，返回通用服务
            logger.info("未识别到特定意图，使用通用服务场景")
            self.current_intent = "general_service"
            return "general_service"
            
        except Exception as e:
            logger.error(f"意图识别过程中出错: {e}")
            self.current_intent = "general_service"
            return "general_service"

    def extract_intent_parameters(self, user_input: str, intent: str) -> Dict:
        """
        提取意图相关的参数信息
        
        Args:
            user_input: 用户输入
            intent: 意图名称
            
        Returns:
            提取的参数字典
        """
        if intent not in self.scene_templates:
            return {}
            
        scene_config = self.scene_templates[intent]
        parameters = scene_config.get('parameters', [])
        
        if not parameters:
            return {}
        
        # 构建参数提取提示
        param_descriptions = []
        for param in parameters:
            param_descriptions.append(f"- {param['name']}: {param['desc']}")
        
        example = scene_config.get('example', '')
        
        extract_prompt = f"""从用户输入中提取相关参数信息。

场景: {scene_config['name']}
需要提取的参数:
{chr(10).join(param_descriptions)}

示例格式:
{example}

用户输入: {user_input}

请以JSON格式返回提取的参数，如果某个参数没有找到，value设为空字符串。"""

        try:
            response = self._call_llm(extract_prompt)
            # 这里可以添加JSON解析逻辑
            logger.debug(f"参数提取响应: {response}")
            return {"raw_response": response}  # 简化版本，返回原始响应
            
        except Exception as e:
            logger.error(f"参数提取时出错: {e}")
            return {}



    def get_intent_mapping(self) -> Dict[str, str]:
        """
        获取新旧意图的映射关系
        
        Returns:
            意图映射字典 {新意图: 旧意图}
        """
        return {
            "price_negotiation": "price",
            "technical_inquiry": "tech", 
            "general_service": "default",
            "human_handover": "handover"  # 新增人工介入映射
        }

    def map_to_legacy_intent(self, enhanced_intent: str) -> str:
        """
        将增强意图映射到原有系统的意图
        
        Args:
            enhanced_intent: 增强意图识别的结果
            
        Returns:
            原有系统的意图标识
        """
        mapping = self.get_intent_mapping()
        legacy_intent = mapping.get(enhanced_intent, "default")
        
        logger.debug(f"意图映射: {enhanced_intent} -> {legacy_intent}")
        return legacy_intent

    def _call_llm(self, prompt: str, temperature: float = 0.1) -> str:
        """调用LLM"""
        try:
            response = self.client.chat.completions.create(
                model=os.getenv("MODEL_NAME", "qwen-turbo"),
                messages=[
                    {"role": "system", "content": "你是一个专业的意图识别助手，请准确分析用户意图。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=temperature,
                max_tokens=1000
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"调用LLM时出错: {e}")
            raise

    def reset_context(self):
        """重置意图上下文"""
        self.current_intent = None
        self.intent_context = {}
        logger.debug("意图上下文已重置")

    def get_current_intent_info(self) -> Optional[Dict]:
        """获取当前意图的详细信息"""
        if self.current_intent and self.current_intent in self.scene_templates:
            return self.scene_templates[self.current_intent]
        return None


if __name__ == "__main__":
    # 测试代码
    from dotenv import load_dotenv
    load_dotenv()
    
    engine = EnhancedIntentEngine()
    
    # 测试意图识别
    test_inputs = [
        "这个价格能不能便宜点？",
        "支持Type-C接口吗？",
        "什么时候发货？",
        "100块钱怎么样？"
    ]
    
    for user_input in test_inputs:
        print(f"\n用户输入: {user_input}")
        intent = engine.recognize_intent(user_input)
        legacy_intent = engine.map_to_legacy_intent(intent)
        print(f"识别意图: {intent} -> {legacy_intent}")
        
        # 提取参数
        params = engine.extract_intent_parameters(user_input, intent)
        if params:
            print(f"提取参数: {params}") 