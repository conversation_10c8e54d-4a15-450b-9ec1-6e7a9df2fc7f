import json
import time
import os
import re
from datetime import datetime

import requests
from loguru import logger
from utils.xianyu_utils import generate_sign, trans_cookies, generate_device_id


class XianyuApis:
    def __init__(self):
        self.url = 'https://h5api.m.goofish.com/h5/mtop.taobao.idlemessage.pc.login.token/1.0/'
        self.session = requests.Session()
        self.session.headers.update({
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'origin': 'https://www.goofish.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://www.goofish.com/',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        })
        
    def clear_duplicate_cookies(self):
        """清理重复的cookies"""
        # 创建一个新的CookieJar
        new_jar = requests.cookies.RequestsCookieJar()
        
        # 记录已经添加过的cookie名称
        added_cookies = set()
        
        # 按照cookies列表的逆序遍历（最新的通常在后面）
        cookie_list = list(self.session.cookies)
        cookie_list.reverse()
        
        for cookie in cookie_list:
            # 如果这个cookie名称还没有添加过，就添加到新jar中
            if cookie.name not in added_cookies:
                new_jar.set_cookie(cookie)
                added_cookies.add(cookie.name)
                
        # 替换session的cookies
        self.session.cookies = new_jar
        
        # 更新完cookies后，更新.env文件
        self.update_env_cookies()
        
    def update_env_cookies(self):
        """更新.env文件中的COOKIES_STR"""
        try:
            # 获取当前cookies的字符串形式
            cookie_str = '; '.join([f"{cookie.name}={cookie.value}" for cookie in self.session.cookies])
            
            # 读取.env文件
            env_path = os.path.join(os.getcwd(), '.env')
            if not os.path.exists(env_path):
                logger.warning(".env文件不存在，无法更新COOKIES_STR")
                return
                
            with open(env_path, 'r', encoding='utf-8') as f:
                env_content = f.read()
                
            # 使用正则表达式替换COOKIES_STR的值
            if 'COOKIES_STR=' in env_content:
                new_env_content = re.sub(
                    r'COOKIES_STR=.*', 
                    f'COOKIES_STR={cookie_str}',
                    env_content
                )
                
                # 写回.env文件
                with open(env_path, 'w', encoding='utf-8') as f:
                    f.write(new_env_content)
                    
                logger.info("已更新.env文件中的COOKIES_STR")
            else:
                logger.warning(".env文件中未找到COOKIES_STR配置项")
        except Exception as e:
            logger.warning(f"更新.env文件失败: {str(e)}")
        
    def get_token(self, device_id, retry_count=0):
        if retry_count >= 3:  # 最多重试3次
            logger.error("获取token失败，重试次数过多")
            return {"error": "获取token失败，重试次数过多"}
            
        params = {
            'jsv': '2.7.2',
            'appKey': '********',
            't': str(int(time.time()) * 1000),
            'sign': '',
            'v': '1.0',
            'type': 'originaljson',
            'accountSite': 'xianyu',
            'dataType': 'json',
            'timeout': '20000',
            'api': 'mtop.taobao.idlemessage.pc.login.token',
            'sessionOption': 'AutoLoginOnly',
            'spm_cnt': 'a21ybx.im.0.0',
        }
        data_val = '{"appKey":"444e9908a51d1cb236a27862abc769c9","deviceId":"' + device_id + '"}'
        data = {
            'data': data_val,
        }
        
        # 简单获取token，信任cookies已清理干净
        token = self.session.cookies.get('_m_h5_tk', '').split('_')[0]
        
        sign = generate_sign(params['t'], token, data_val)
        params['sign'] = sign
        response = self.session.post('https://h5api.m.goofish.com/h5/mtop.taobao.idlemessage.pc.login.token/1.0/', params=params, data=data)
        res_json = response.json()
        if isinstance(res_json, dict):
            ret_value = res_json.get('ret', [])
            # 检查ret是否包含成功信息
            if not any('SUCCESS::调用成功' in ret for ret in ret_value):
                logger.warning(f"API调用失败，错误信息: {ret_value}")
                # 处理响应中的Set-Cookie
                if 'Set-Cookie' in response.headers:
                    logger.info("检测到Set-Cookie，等待cookie更新")
                    self.clear_duplicate_cookies()
                time.sleep(0.5)
                return self.get_token(device_id, retry_count + 1)
        return res_json

    def get_item_info(self, item_id, retry_count=0):
        """获取商品信息，自动处理token失效的情况"""
        if retry_count >= 3:  # 最多重试3次
            logger.error("获取商品信息失败，重试次数过多")
            return {"error": "获取商品信息失败，重试次数过多"}
            
        params = {
            'jsv': '2.7.2',
            'appKey': '********',
            't': str(int(time.time()) * 1000),
            'sign': '',
            'v': '1.0',
            'type': 'originaljson',
            'accountSite': 'xianyu',
            'dataType': 'json',
            'timeout': '20000',
            'api': 'mtop.taobao.idle.pc.detail',
            'sessionOption': 'AutoLoginOnly',
            'spm_cnt': 'a21ybx.im.0.0',
        }
        
        data_val = '{"itemId":"' + item_id + '"}'
        data = {
            'data': data_val,
        }
        
        # 简单获取token，信任cookies已清理干净
        token = self.session.cookies.get('_m_h5_tk', '').split('_')[0]
        
        sign = generate_sign(params['t'], token, data_val)
        params['sign'] = sign
        
        response = self.session.post(
            'https://h5api.m.goofish.com/h5/mtop.taobao.idle.pc.detail/1.0/', 
            params=params, 
            data=data
        )
        
        res_json = response.json()
        # 检查返回状态
        if isinstance(res_json, dict):
            ret_value = res_json.get('ret', [])
            # 检查ret是否包含成功信息
            if not any('SUCCESS::调用成功' in ret for ret in ret_value):
                logger.warning(f"API调用失败，错误信息: {ret_value}")
                # 处理响应中的Set-Cookie
                if 'Set-Cookie' in response.headers:
                    logger.info("检测到Set-Cookie，等待cookie更新")
                    self.clear_duplicate_cookies()
                time.sleep(0.5)
                return self.get_item_info(item_id, retry_count + 1)
        return res_json

    def parse_item_info(self, response_data):
        """
        解析商品信息响应数据，提取关键字段
        
        Args:
            response_data: get_item_info方法返回的原始响应数据
            
        Returns:
            dict: 解析后的结构化商品信息
        """
        try:
            # 检查响应是否成功
            if not isinstance(response_data, dict) or 'data' not in response_data:
                return {"error": "无效的响应数据"}
            
            ret_codes = response_data.get('ret', [])
            if not any('SUCCESS::调用成功' in ret for ret in ret_codes):
                return {"error": f"API调用失败: {ret_codes}"}
            
            data = response_data['data']
            item_do = data.get('itemDO', {})
            seller_do = data.get('sellerDO', {})
            
            # 提取商品基本信息
            item_info = {
                # 商品基本信息
                "item_id": item_do.get('itemId'),
                "title": item_do.get('title', ''),
                "description": item_do.get('desc', ''),
                "price": item_do.get('soldPrice', ''),
                "original_price": item_do.get('originalPrice', '0'),
                "transport_fee": item_do.get('transportFee', '0.00'),
                "create_time": item_do.get('GMT_CREATE_DATE_KEY', ''),
                
                # 商品状态
                "item_status": item_do.get('itemStatusStr', ''),
                "browse_count": item_do.get('browseCnt', 0),
                "collect_count": item_do.get('collectCnt', 0),
                "want_count": item_do.get('wantCnt', 0),
                "favor_count": item_do.get('favorCnt', 0),
                
                # 商品图片
                "images": self._extract_images(item_do.get('imageInfos', [])),
                "main_image": self._get_main_image(item_do.get('imageInfos', [])),
                
                # 商品标签和分类
                "price_tags": [tag.get('text', '') for tag in item_do.get('priceRelativeTags', [])],
                "common_tags": [tag.get('text', '') for tag in item_do.get('commonTags', [])],
                "item_labels": [label.get('text', '') for label in item_do.get('itemLabelExtList', [])],
                "recommend_tags": [tag.get('text', '') for tag in item_do.get('recommendTagList', [])],
                
                # 商品属性
                "properties": self._extract_properties(item_do.get('cpvLabels', [])),
                
                # 卖家信息
                "seller": {
                    "seller_id": seller_do.get('sellerId'),
                    "nickname": seller_do.get('nick', ''),
                    "city": seller_do.get('city', ''),
                    "avatar": seller_do.get('portraitUrl', ''),
                    "last_visit": seller_do.get('lastVisitTime', ''),
                    "reply_ratio_24h": seller_do.get('replyRatio24h', ''),
                    "reply_interval": seller_do.get('replyInterval', ''),
                    "sold_items_count": seller_do.get('hasSoldNumInteger', 0),
                    "total_items_count": seller_do.get('itemCount', 0),
                    "register_days": seller_do.get('userRegDay', 0),
                    "zhima_auth": seller_do.get('zhimaAuth', False),
                    "credit_level": seller_do.get('zhimaLevelInfo', {}).get('levelName', ''),
                    "seller_tags": [tag.get('text', '') for tag in seller_do.get('sellerInfoTags', [])],
                    "identity_tags": [tag.get('text', '') for tag in seller_do.get('identityTags', [])]
                },
                
                # 交易相关
                "support_trade": item_do.get('pcSupportTrade', False),
                "trade_access_type": item_do.get('tradeAccessType', 0),
                "bargained": item_do.get('bargained', False),
                
                # 其他信息
                "quality_url": item_do.get('qualityUrl', ''),
                "report_url": item_do.get('reportUrl', ''),
                "share_data": item_do.get('shareData', {}),
                "rich_text_desc": item_do.get('richTextDesc', ''),
                
                # 解析时间
                "parsed_at": data.get('serverTime', '')
            }
            
            logger.info(f"成功解析商品信息 - ID: {item_info['item_id']}, 标题: {item_info['title'][:50]}...")
            return item_info
            
        except Exception as e:
            logger.error(f"解析商品信息时出错: {str(e)}")
            return {"error": f"解析失败: {str(e)}"}
    
    def _extract_images(self, image_infos):
        """提取商品图片信息"""
        images = []
        for img_info in image_infos:
            if isinstance(img_info, dict) and 'url' in img_info:
                images.append({
                    "url": img_info['url'],
                    "width": img_info.get('widthSize', 0),
                    "height": img_info.get('heightSize', 0),
                    "is_major": img_info.get('major', False),
                    "type": img_info.get('type', 0)
                })
        return images
    
    def _get_main_image(self, image_infos):
        """获取主图片URL"""
        for img_info in image_infos:
            if isinstance(img_info, dict) and img_info.get('major', False):
                return img_info.get('url', '')
        # 如果没有主图，返回第一张图片
        if image_infos and isinstance(image_infos[0], dict):
            return image_infos[0].get('url', '')
        return ''
    
    def _extract_properties(self, cpv_labels):
        """提取商品属性"""
        properties = {}
        for prop in cpv_labels:
            if isinstance(prop, dict):
                prop_name = prop.get('propertyName', '')
                prop_value = prop.get('valueName', '')
                if prop_name and prop_value:
                    properties[prop_name] = prop_value
        return properties

    def get_item_info_parsed(self, item_id):
        """便捷方法：获取商品信息并自动解析"""
        raw_data = self.get_item_info(item_id)
        return self.parse_item_info(raw_data)

    def get_user_card_info(self, user_id, retry_count=0):
        """
        获取用户卡片信息（包含用户基本信息和交易状态相关数据）
        
        Args:
            user_id (str): 用户ID
            retry_count (int): 重试次数
            
        Returns:
            dict: 用户卡片信息，包含可能的交易状态指标
        """
        if retry_count >= 3:
            logger.error("获取用户卡片信息失败，重试次数过多")
            return {"error": "获取用户卡片信息失败，重试次数过多"}
            
        params = {
            'jsv': '2.7.2',
            'appKey': '********',
            't': str(int(time.time()) * 1000),
            'sign': '',
            'v': '1.0',
            'type': 'originaljson',
            'accountSite': 'xianyu',
            'dataType': 'json',
            'timeout': '20000',
            'api': 'mtop.taobao.idle.user.page.my.info',
            'sessionOption': 'AutoLoginOnly',
        }
        
        data_val = f'{{"userId":"{user_id}"}}'
        data = {'data': data_val}
        
        token = self.session.cookies.get('_m_h5_tk', '').split('_')[0]
        sign = generate_sign(params['t'], token, data_val)
        params['sign'] = sign
        
        response = self.session.post(
            'https://h5api.m.goofish.com/h5/mtop.taobao.idle.user.page.my.info/1.0/',
            params=params,
            data=data
        )
        
        res_json = response.json()
        if isinstance(res_json, dict):
            ret_value = res_json.get('ret', [])
            if not any('SUCCESS::调用成功' in ret for ret in ret_value):
                logger.warning(f"获取用户卡片信息失败: {ret_value}")
                if 'Set-Cookie' in response.headers:
                    self.clear_duplicate_cookies()
                time.sleep(0.5)
                return self.get_user_card_info(user_id, retry_count + 1)
        return res_json

    def get_conversation_status(self, user_id, item_id, retry_count=0):
        """
        获取特定用户与商品的对话状态信息
        
        Args:
            user_id (str): 买家用户ID
            item_id (str): 商品ID
            retry_count (int): 重试次数
            
        Returns:
            dict: 对话状态信息，包含可能的交易状态标识
        """
        if retry_count >= 3:
            logger.error("获取对话状态失败，重试次数过多")
            return {"error": "获取对话状态失败，重试次数过多"}
            
        params = {
            'jsv': '2.7.2',
            'appKey': '********',
            't': str(int(time.time()) * 1000),
            'sign': '',
            'v': '1.0',
            'type': 'originaljson',
            'accountSite': 'xianyu',
            'dataType': 'json',
            'timeout': '20000',
            'api': 'mtop.taobao.idle.conversation.list',
            'sessionOption': 'AutoLoginOnly',
        }
        
        data_val = f'{{"itemId":"{item_id}","userId":"{user_id}","pageSize":1}}'
        data = {'data': data_val}
        
        token = self.session.cookies.get('_m_h5_tk', '').split('_')[0]
        sign = generate_sign(params['t'], token, data_val)
        params['sign'] = sign
        
        response = self.session.post(
            'https://h5api.m.goofish.com/h5/mtop.taobao.idle.conversation.list/1.0/',
            params=params,
            data=data
        )
        
        res_json = response.json()
        if isinstance(res_json, dict):
            ret_value = res_json.get('ret', [])
            if not any('SUCCESS::调用成功' in ret for ret in ret_value):
                logger.warning(f"获取对话状态失败: {ret_value}")
                if 'Set-Cookie' in response.headers:
                    self.clear_duplicate_cookies()
                time.sleep(0.5)
                return self.get_conversation_status(user_id, item_id, retry_count + 1)
        return res_json

    def parse_transaction_status_from_conversation(self, conversation_data):
        """
        从对话数据中解析交易状态信息
        
        Args:
            conversation_data (dict): get_conversation_status返回的对话数据
            
        Returns:
            dict: 解析出的交易状态信息
        """
        try:
            if not isinstance(conversation_data, dict) or 'data' not in conversation_data:
                return {"error": "无效的对话数据"}
            
            data = conversation_data['data']
            if not data or 'conversationList' not in data:
                return {"transaction_status": "unknown", "status_indicators": []}
            
            conversations = data['conversationList']
            if not conversations:
                return {"transaction_status": "no_conversation", "status_indicators": []}
            
            # 分析第一个对话（最相关的对话）
            conversation = conversations[0]
            status_indicators = []
            transaction_status = "inquiry"  # 默认状态：咨询中
            
            # 1. 检查redReminder字段 - 系统状态消息
            if 'redReminder' in conversation:
                red_reminder = conversation['redReminder']
                if red_reminder:
                    status_indicators.append({
                        "type": "system_status",
                        "content": red_reminder,
                        "priority": "high"
                    })
                    
                    # 根据系统消息判断交易状态
                    if any(keyword in red_reminder for keyword in ['等待买家付款', '买家已拍下']):
                        transaction_status = "ordered_unpaid"
                    elif any(keyword in red_reminder for keyword in ['等待卖家发货', '买家已付款']):
                        transaction_status = "paid"
                    elif any(keyword in red_reminder for keyword in ['等待买家确认收货', '卖家已发货']):
                        transaction_status = "pending_delivery"
                    elif any(keyword in red_reminder for keyword in ['买家已确认收货', '交易完成']):
                        transaction_status = "delivered"
                    elif any(keyword in red_reminder for keyword in ['交易关闭', '交易取消']):
                        transaction_status = "transaction_closed"
            
            # 2. 检查最近消息内容
            if 'lastMsgContent' in conversation:
                last_msg = conversation['lastMsgContent']
                if last_msg:
                    status_indicators.append({
                        "type": "last_message",
                        "content": last_msg,
                        "priority": "medium"
                    })
                    
                    # 从最后消息推断状态（如果系统状态未明确的话）
                    if transaction_status == "inquiry":
                        if any(keyword in last_msg for keyword in ['能便宜', '议价', '价格']):
                            transaction_status = "bargaining"
                        elif any(keyword in last_msg for keyword in ['拍下', '买了', '下单']):
                            transaction_status = "ordered_unpaid"
                        elif any(keyword in last_msg for keyword in ['付款', '付了', '支付']):
                            transaction_status = "paid"
                        elif any(keyword in last_msg for keyword in ['收到', '确认收货']):
                            transaction_status = "delivered"
            
            # 3. 检查对话状态字段
            conversation_status_fields = ['status', 'orderStatus', 'tradeStatus']
            for field in conversation_status_fields:
                if field in conversation and conversation[field]:
                    status_indicators.append({
                        "type": f"conversation_{field}",
                        "content": str(conversation[field]),
                        "priority": "high"
                    })
            
            # 4. 检查是否有订单相关信息
            order_fields = ['orderId', 'tradeId', 'orderInfo']
            order_info = {}
            for field in order_fields:
                if field in conversation and conversation[field]:
                    order_info[field] = conversation[field]
                    status_indicators.append({
                        "type": "order_info",
                        "content": f"{field}: {conversation[field]}",
                        "priority": "high"
                    })
            
            # 5. 时间分析
            if 'lastMsgTime' in conversation:
                last_msg_time = conversation['lastMsgTime']
                status_indicators.append({
                    "type": "timing",
                    "content": f"最后消息时间: {last_msg_time}",
                    "priority": "low"
                })
            
            return {
                "transaction_status": transaction_status,
                "status_indicators": status_indicators,
                "order_info": order_info,
                "conversation_summary": {
                    "user_id": conversation.get('userId', ''),
                    "item_id": conversation.get('itemId', ''),
                    "last_message": conversation.get('lastMsgContent', ''),
                    "red_reminder": conversation.get('redReminder', ''),
                    "last_time": conversation.get('lastMsgTime', '')
                }
            }
            
        except Exception as e:
            logger.error(f"解析交易状态时出错: {e}")
            return {"error": f"解析失败: {str(e)}"}

    def get_transaction_status_comprehensive(self, user_id, item_id):
        """
        综合获取买家交易状态信息（整合多个API的结果）
        
        Args:
            user_id (str): 买家用户ID
            item_id (str): 商品ID
            
        Returns:
            dict: 综合的交易状态信息
        """
        try:
            # 1. 获取用户卡片信息
            user_info = self.get_user_card_info(user_id)
            
            # 2. 获取对话状态
            conversation_data = self.get_conversation_status(user_id, item_id)
            
            # 3. 解析交易状态
            transaction_status = self.parse_transaction_status_from_conversation(conversation_data)
            
            # 4. 获取商品信息（用于上下文）
            item_info = self.get_item_info_parsed(item_id)
            
            # 5. 整合结果
            comprehensive_result = {
                "user_id": user_id,
                "item_id": item_id,
                "transaction_status": transaction_status.get("transaction_status", "unknown"),
                "status_confidence": self._calculate_status_confidence(transaction_status),
                "status_indicators": transaction_status.get("status_indicators", []),
                "order_info": transaction_status.get("order_info", {}),
                "user_info_available": "data" in user_info and not "error" in user_info,
                "conversation_summary": transaction_status.get("conversation_summary", {}),
                "item_context": {
                    "title": item_info.get("title", ""),
                    "price": item_info.get("price", 0),
                    "status": item_info.get("status", "")
                },
                "api_call_status": {
                    "user_info_success": "data" in user_info,
                    "conversation_success": "data" in conversation_data,
                    "item_info_success": "title" in item_info
                },
                "timestamp": int(time.time())
            }
            
            return comprehensive_result
            
        except Exception as e:
            logger.error(f"综合获取交易状态时出错: {e}")
            return {
                "error": f"获取失败: {str(e)}",
                "user_id": user_id,
                "item_id": item_id,
                "timestamp": int(time.time())
            }

    def _calculate_status_confidence(self, transaction_status_data):
        """
        计算交易状态的置信度
        
        Args:
            transaction_status_data (dict): 交易状态解析结果
            
        Returns:
            float: 置信度分数 (0-1)
        """
        if "error" in transaction_status_data:
            return 0.0
        
        confidence = 0.0
        indicators = transaction_status_data.get("status_indicators", [])
        
        for indicator in indicators:
            if indicator["priority"] == "high":
                confidence += 0.4
            elif indicator["priority"] == "medium":
                confidence += 0.2
            elif indicator["priority"] == "low":
                confidence += 0.1
        
        # 限制在0-1范围内
        return min(confidence, 1.0)

    def batch_get_transaction_status(self, user_item_pairs):
        """
        批量获取多个用户-商品对的交易状态
        
        Args:
            user_item_pairs (list): [(user_id, item_id), ...] 的列表
            
        Returns:
            list: 每个用户-商品对的交易状态信息列表
        """
        results = []
        
        for user_id, item_id in user_item_pairs:
            try:
                status_info = self.get_transaction_status_comprehensive(user_id, item_id)
                results.append(status_info)
                
                # 添加请求间隔，避免过于频繁的API调用
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"批量获取状态失败 ({user_id}, {item_id}): {e}")
                results.append({
                    "error": str(e),
                    "user_id": user_id,
                    "item_id": item_id,
                    "timestamp": int(time.time())
                })
        
        return results

    def get_transaction_status_from_database(self, user_id, item_id):
        """
        从项目数据库中获取买家交易状态信息
        这是一个新增的方法，用于整合数据库中已有的交易状态数据
        
        Args:
            user_id (str): 买家用户ID
            item_id (str): 商品ID
            
        Returns:
            dict: 数据库中的交易状态信息
        """
        try:
            # 导入项目的上下文管理器
            from 旧系统文件.context_manager import ChatContextManager
            
            # 初始化数据库连接
            context_manager = ChatContextManager()
            
            # 1. 获取买家状态
            buyer_status = context_manager.get_buyer_status(user_id, item_id)
            
            # 2. 获取最近的消息记录
            recent_messages = context_manager.get_recent_messages(
                limit=10, user_id=user_id, item_id=item_id
            )
            
            # 3. 获取商品信息
            item_info = context_manager.get_item_info(item_id)
            
            # 4. 获取议价次数
            bargain_count = context_manager.get_bargain_count(user_id, item_id)
            
            # 5. 整合数据
            database_status = {
                "data_source": "database",
                "user_id": user_id,
                "item_id": item_id,
                "buyer_status": buyer_status,
                "recent_messages": recent_messages[:5] if recent_messages else [],  # 只取最近5条
                "item_info": item_info,
                "bargain_count": bargain_count,
                "has_conversation_data": len(recent_messages) > 0 if recent_messages else False,
                "timestamp": int(time.time())
            }
            
            # 6. 从数据库状态推断交易状态
            if buyer_status:
                database_status["inferred_status"] = buyer_status.get("current_status", "unknown")
                database_status["status_category"] = buyer_status.get("status_category", "unknown")
                database_status["is_old_customer"] = buyer_status.get("is_old_customer", False)
                database_status["status_confidence"] = 0.9  # 数据库数据置信度较高
            else:
                # 从消息记录中推断状态
                inferred_status = self._infer_status_from_messages(recent_messages)
                database_status["inferred_status"] = inferred_status["status"]
                database_status["status_confidence"] = inferred_status["confidence"]
            
            return database_status
            
        except Exception as e:
            logger.error(f"从数据库获取交易状态时出错: {e}")
            return {
                "error": f"数据库访问失败: {str(e)}",
                "data_source": "database",
                "user_id": user_id,
                "item_id": item_id,
                "timestamp": int(time.time())
            }

    def _infer_status_from_messages(self, messages):
        """
        从消息记录中推断交易状态
        
        Args:
            messages (list): 消息记录列表
            
        Returns:
            dict: 推断的状态信息
        """
        if not messages:
            return {"status": "no_conversation", "confidence": 0.0}
        
        # 分析最近的消息内容
        status_keywords = {
            "bargaining": ["便宜", "议价", "价格", "优惠", "折扣"],
            "ordered_unpaid": ["拍下", "下单", "购买", "要了"],
            "paid": ["付款", "付了", "支付", "转账"],
            "pending_delivery": ["发货", "快递", "物流"],
            "delivered": ["收到", "确认收货", "收货"]
        }
        
        # 检查最近的消息
        for message in messages[:3]:  # 检查最近3条消息
            content = message.get("content", "").lower()
            for status, keywords in status_keywords.items():
                if any(keyword in content for keyword in keywords):
                    return {"status": status, "confidence": 0.6}
        
        # 如果有消息但无法确定状态，则为咨询状态
        return {"status": "product_inquiry", "confidence": 0.3}

    def get_transaction_status_enhanced(self, user_id, item_id):
        """
        增强版交易状态获取方法
        整合API数据和数据库数据，提供更准确的交易状态信息
        
        Args:
            user_id (str): 买家用户ID
            item_id (str): 商品ID
            
        Returns:
            dict: 增强的交易状态信息
        """
        try:
            # 1. 从数据库获取状态（优先级最高，因为包含实时更新的状态）
            database_status = self.get_transaction_status_from_database(user_id, item_id)
            
            # 2. 尝试从API获取补充信息（如果cookies有效）
            api_status = {}
            try:
                # 获取商品信息（这个API通常可用）
                item_info = self.get_item_info_parsed(item_id)
                if "error" not in item_info:
                    api_status["item_context"] = {
                        "title": item_info.get("title", ""),
                        "price": item_info.get("price", 0),
                        "status": item_info.get("status", ""),
                        "seller_info": item_info.get("seller", {})
                    }
            except Exception as e:
                logger.warning(f"API获取商品信息失败: {e}")
            
            # 3. 整合数据源
            enhanced_result = {
                "user_id": user_id,
                "item_id": item_id,
                "transaction_status": database_status.get("inferred_status", "unknown"),
                "status_confidence": database_status.get("status_confidence", 0.0),
                "status_source": "database_primary",
                
                # 数据库信息
                "database_info": {
                    "buyer_status": database_status.get("buyer_status"),
                    "recent_messages_count": len(database_status.get("recent_messages", [])),
                    "has_conversation": database_status.get("has_conversation_data", False),
                    "bargain_count": database_status.get("bargain_count", 0),
                    "is_old_customer": database_status.get("is_old_customer", False)
                },
                
                # API信息
                "api_info": api_status,
                
                # 状态历史
                "status_history": database_status.get("buyer_status", {}).get("status_history", []) if database_status.get("buyer_status") else [],
                
                # 推荐的下一步操作
                "recommended_actions": self._get_recommended_actions(
                    database_status.get("inferred_status", "unknown"),
                    database_status.get("recent_messages", [])
                ),
                
                "timestamp": int(time.time())
            }
            
            # 4. 如果数据库中没有状态信息，则使用API推断
            if enhanced_result["transaction_status"] == "unknown" and api_status:
                # 从商品状态推断
                if api_status.get("item_context", {}).get("status") == "在线":
                    enhanced_result["transaction_status"] = "product_inquiry"
                    enhanced_result["status_confidence"] = 0.3
                    enhanced_result["status_source"] = "api_fallback"
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"增强版交易状态获取失败: {e}")
            return {
                "error": f"获取失败: {str(e)}",
                "user_id": user_id,
                "item_id": item_id,
                "transaction_status": "error",
                "timestamp": int(time.time())
            }

    def _get_recommended_actions(self, status, recent_messages):
        """
        根据当前状态和消息历史推荐下一步操作
        
        Args:
            status (str): 当前交易状态
            recent_messages (list): 最近的消息记录
            
        Returns:
            list: 推荐操作列表
        """
        actions = []
        
        if status == "bargaining":
            actions.extend([
                "关注价格谈判进展",
                "适时提供优惠或促销信息",
                "引导买家下单"
            ])
        elif status == "ordered_unpaid":
            actions.extend([
                "提醒买家及时付款",
                "发送付款链接",
                "设置付款超时提醒"
            ])
        elif status == "paid":
            actions.extend([
                "准备发货",
                "更新物流信息",
                "发送发货通知"
            ])
        elif status == "pending_delivery":
            actions.extend([
                "跟踪物流状态",
                "主动询问收货情况",
                "准备售后服务"
            ])
        elif status == "delivered":
            actions.extend([
                "请求买家评价",
                "提供售后支持",
                "推荐其他商品"
            ])
        elif status == "product_inquiry":
            actions.extend([
                "详细介绍商品特性",
                "解答买家疑问",
                "引导买家购买"
            ])
        else:
            actions.extend([
                "主动联系买家了解需求",
                "提供商品详细信息",
                "建立良好沟通"
            ])
        
        return actions

    def analyze_conversation_patterns(self, user_id, item_id):
        """
        分析对话模式，提供更深入的买家行为洞察
        
        Args:
            user_id (str): 买家用户ID
            item_id (str): 商品ID
            
        Returns:
            dict: 对话模式分析结果
        """
        try:
            database_status = self.get_transaction_status_from_database(user_id, item_id)
            
            if "error" in database_status:
                return database_status
            
            messages = database_status.get("recent_messages", [])
            
            analysis = {
                "user_id": user_id,
                "item_id": item_id,
                "conversation_analysis": {
                    "total_messages": len(messages),
                    "user_messages": len([m for m in messages if m.get("role") == "user"]),
                    "assistant_messages": len([m for m in messages if m.get("role") == "assistant"]),
                    "message_frequency": self._calculate_message_frequency(messages),
                    "conversation_duration": self._calculate_conversation_duration(messages),
                    "intent_analysis": self._analyze_message_intents(messages),
                    "engagement_level": self._calculate_engagement_level(messages),
                    "urgency_indicators": self._detect_urgency_indicators(messages)
                },
                "buyer_profile": {
                    "bargain_count": database_status.get("bargain_count", 0),
                    "is_active_bargainer": database_status.get("bargain_count", 0) > 2,
                    "is_old_customer": database_status.get("is_old_customer", False),
                    "communication_style": self._analyze_communication_style(messages)
                },
                "timestamp": int(time.time())
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"对话模式分析失败: {e}")
            return {
                "error": f"分析失败: {str(e)}",
                "user_id": user_id,
                "item_id": item_id,
                "timestamp": int(time.time())
            }

    def _calculate_message_frequency(self, messages):
        """计算消息频率"""
        if len(messages) < 2:
            return 0
        
        timestamps = [msg.get("timestamp") for msg in messages if msg.get("timestamp")]
        if len(timestamps) < 2:
            return 0
        
        # 计算平均消息间隔（分钟）
        intervals = []
        for i in range(1, len(timestamps)):
            try:
                if isinstance(timestamps[i], str):
                    time1 = datetime.fromisoformat(timestamps[i].replace('Z', '+00:00'))
                    time2 = datetime.fromisoformat(timestamps[i-1].replace('Z', '+00:00'))
                else:
                    time1 = datetime.fromtimestamp(timestamps[i] / 1000)
                    time2 = datetime.fromtimestamp(timestamps[i-1] / 1000)
                
                interval = abs((time1 - time2).total_seconds() / 60)
                intervals.append(interval)
            except:
                continue
        
        return sum(intervals) / len(intervals) if intervals else 0

    def _calculate_conversation_duration(self, messages):
        """计算对话持续时间"""
        if len(messages) < 2:
            return 0
        
        timestamps = [msg.get("timestamp") for msg in messages if msg.get("timestamp")]
        if len(timestamps) < 2:
            return 0
        
        try:
            first_time = timestamps[-1]  # 最早的消息
            last_time = timestamps[0]    # 最新的消息
            
            if isinstance(first_time, str):
                first_dt = datetime.fromisoformat(first_time.replace('Z', '+00:00'))
                last_dt = datetime.fromisoformat(last_time.replace('Z', '+00:00'))
            else:
                first_dt = datetime.fromtimestamp(first_time / 1000)
                last_dt = datetime.fromtimestamp(last_time / 1000)
            
            duration = (last_dt - first_dt).total_seconds() / 3600  # 小时
            return duration
        except:
            return 0

    def _analyze_message_intents(self, messages):
        """分析消息意图"""
        intent_keywords = {
            "price_inquiry": ["价格", "多少钱", "便宜", "优惠"],
            "product_details": ["功能", "参数", "配置", "怎么样"],
            "availability": ["有货", "现货", "库存"],
            "shipping": ["快递", "发货", "物流", "邮费"],
            "negotiation": ["议价", "便宜点", "能不能", "最低"],
            "purchase_intent": ["要了", "买了", "拍下", "下单"]
        }
        
        intent_counts = {intent: 0 for intent in intent_keywords.keys()}
        
        for message in messages:
            if message.get("role") == "user":
                content = message.get("content", "").lower()
                for intent, keywords in intent_keywords.items():
                    if any(keyword in content for keyword in keywords):
                        intent_counts[intent] += 1
        
        return intent_counts

    def _calculate_engagement_level(self, messages):
        """计算参与度水平"""
        if not messages:
            return "low"
        
        user_messages = [m for m in messages if m.get("role") == "user"]
        
        if len(user_messages) >= 5:
            return "high"
        elif len(user_messages) >= 2:
            return "medium"
        else:
            return "low"

    def _detect_urgency_indicators(self, messages):
        """检测紧急性指标"""
        urgency_keywords = [
            "急", "马上", "立即", "现在", "今天", "明天",
            "快点", "赶紧", "时间", "等不及"
        ]
        
        urgency_count = 0
        for message in messages:
            if message.get("role") == "user":
                content = message.get("content", "").lower()
                urgency_count += sum(1 for keyword in urgency_keywords if keyword in content)
        
        if urgency_count >= 2:
            return "high"
        elif urgency_count >= 1:
            return "medium"
        else:
            return "low"

    def _analyze_communication_style(self, messages):
        """分析沟通风格"""
        if not messages:
            return "unknown"
        
        user_messages = [m for m in messages if m.get("role") == "user"]
        
        if not user_messages:
            return "unknown"
        
        total_length = sum(len(m.get("content", "")) for m in user_messages)
        avg_length = total_length / len(user_messages)
        
        # 检查礼貌用词
        polite_words = ["谢谢", "请问", "麻烦", "不好意思", "打扰"]
        polite_count = sum(
            1 for message in user_messages 
            for word in polite_words 
            if word in message.get("content", "")
        )
        
        if avg_length > 20 and polite_count > 0:
            return "polite_detailed"
        elif avg_length > 20:
            return "detailed"
        elif polite_count > 0:
            return "polite_brief"
        else:
            return "brief"

    def get_official_transaction_status(self, user_id, item_id, retry_count=0):
        """
        从闲鱼官方API获取交易状态 - 重点利用对话中的redReminder字段
        这是获取官方维护交易状态的直接方法，无需复杂的状态推断
        
        Args:
            user_id (str): 买家用户ID  
            item_id (str): 商品ID
            retry_count (int): 重试次数
            
        Returns:
            dict: 官方交易状态信息
        """
        if retry_count >= 3:
            logger.error("获取官方交易状态失败，重试次数过多")
            return {"error": "获取官方交易状态失败，重试次数过多"}
            
        try:
            # 1. 获取对话状态 - 包含redReminder字段
            conversation_data = self.get_conversation_status(user_id, item_id)
            
            if "error" in conversation_data:
                return conversation_data
                
            # 2. 提取官方状态信息
            official_status = self._extract_official_status_from_conversation(conversation_data)
            
            # 3. 获取商品信息作为上下文
            item_info = self.get_item_info_parsed(item_id)
            
            # 4. 构建完整的状态信息
            result = {
                "data_source": "official_api",
                "user_id": user_id,
                "item_id": item_id,
                "official_status": official_status,
                "item_context": {
                    "title": item_info.get("title", "") if "error" not in item_info else "",
                    "price": item_info.get("price", "") if "error" not in item_info else "",
                    "seller_id": item_info.get("seller", {}).get("seller_id", "") if "error" not in item_info else ""
                },
                "timestamp": int(time.time()),
                "api_success": "error" not in conversation_data and "error" not in item_info
            }
            
            return result
            
        except Exception as e:
            logger.error(f"获取官方交易状态时出错: {e}")
            return {
                "error": f"获取失败: {str(e)}",
                "user_id": user_id,
                "item_id": item_id,
                "timestamp": int(time.time())
            }

    def _extract_official_status_from_conversation(self, conversation_data):
        """
        从对话数据中提取闲鱼官方维护的交易状态信息
        
        Args:
            conversation_data (dict): 对话API返回的数据
            
        Returns:
            dict: 提取的官方状态信息
        """
        try:
            if not isinstance(conversation_data, dict) or 'data' not in conversation_data:
                return {
                    "red_reminder": None,
                    "mapped_status": "unknown",
                    "confidence": 0.0,
                    "error": "无效的对话数据"
                }
            
            data = conversation_data['data']
            if not data or 'conversationList' not in data:
                return {
                    "red_reminder": None,
                    "mapped_status": "no_conversation", 
                    "confidence": 0.0,
                    "conversation_count": 0
                }
            
            conversations = data['conversationList']
            if not conversations:
                return {
                    "red_reminder": None,
                    "mapped_status": "no_conversation",
                    "confidence": 0.0,
                    "conversation_count": 0
                }
            
            # 分析第一个对话（最相关的对话）
            conversation = conversations[0]
            
            # 核心：提取redReminder字段 - 这是闲鱼官方的状态字段
            red_reminder = conversation.get('redReminder', '')
            
            # 闲鱼官方状态到标准状态的映射
            official_status_mapping = {
                # 官方状态 -> 我们的标准状态
                '等待买家付款': 'ordered_unpaid',
                '买家已拍下，待付款': 'ordered_unpaid', 
                '等待卖家发货': 'paid',
                '买家已付款': 'paid',
                '我已付款，等待你发货': 'paid',
                '等待买家确认收货': 'pending_delivery',
                '卖家已发货': 'pending_delivery',
                '买家已确认收货': 'delivered',
                '确认收货': 'delivered',
                '交易完成': 'delivered',
                '交易关闭': 'transaction_closed',
                '交易取消': 'transaction_closed'
            }
            
            # 映射官方状态
            mapped_status = "unknown"
            confidence = 0.0
            
            if red_reminder:
                # 直接匹配
                if red_reminder in official_status_mapping:
                    mapped_status = official_status_mapping[red_reminder]
                    confidence = 0.95  # 官方状态高置信度
                else:
                    # 模糊匹配
                    for official_status, standard_status in official_status_mapping.items():
                        if official_status in red_reminder or red_reminder in official_status:
                            mapped_status = standard_status
                            confidence = 0.85  # 模糊匹配稍低置信度
                            break
                
                if mapped_status == "unknown":
                    mapped_status = "inquiry"  # 有对话但状态不明，默认为咨询
                    confidence = 0.3
            else:
                # 没有redReminder字段，尝试从其他字段推断
                last_message = conversation.get('lastMsgContent', '')
                if last_message:
                    mapped_status = "inquiry"  # 有对话记录
                    confidence = 0.2
                else:
                    mapped_status = "no_activity"
                    confidence = 0.1
            
            # 提取其他有用信息
            additional_info = {
                "last_message": conversation.get('lastMsgContent', ''),
                "last_message_time": conversation.get('lastMsgTime', ''),
                "conversation_id": conversation.get('conversationId', ''),
                "item_title": conversation.get('itemTitle', ''),
                "seller_nick": conversation.get('sellerNick', '')
            }
            
            return {
                "red_reminder": red_reminder,
                "mapped_status": mapped_status,
                "confidence": confidence,
                "conversation_count": len(conversations),
                "official_mapping_used": red_reminder in official_status_mapping,
                "additional_info": additional_info,
                "all_possible_reminders": [conv.get('redReminder', '') for conv in conversations if conv.get('redReminder')]
            }
            
        except Exception as e:
            logger.error(f"提取官方状态时出错: {e}")
            return {
                "red_reminder": None,
                "mapped_status": "error",
                "confidence": 0.0,
                "error": str(e)
            }

    def batch_get_official_status(self, user_item_pairs, include_context=True):
        """
        批量获取多个用户-商品对的官方交易状态
        
        Args:
            user_item_pairs (list): [(user_id, item_id), ...] 的列表
            include_context (bool): 是否包含商品上下文信息
            
        Returns:
            list: 每个用户-商品对的官方状态信息列表
        """
        results = []
        
        logger.info(f"开始批量获取 {len(user_item_pairs)} 个用户-商品对的官方状态")
        
        for i, (user_id, item_id) in enumerate(user_item_pairs, 1):
            try:
                logger.info(f"处理第 {i}/{len(user_item_pairs)} 个: user_id={user_id}, item_id={item_id}")
                
                status_info = self.get_official_transaction_status(user_id, item_id)
                
                # 添加批次信息
                status_info.update({
                    "batch_index": i,
                    "batch_total": len(user_item_pairs)
                })
                
                results.append(status_info)
                
                # 添加请求间隔，避免过于频繁的API调用
                if i < len(user_item_pairs):  # 最后一个不需要等待
                    time.sleep(0.2)
                
            except Exception as e:
                logger.error(f"批量获取状态失败 ({user_id}, {item_id}): {e}")
                results.append({
                    "error": str(e),
                    "user_id": user_id,
                    "item_id": item_id,
                    "batch_index": i,
                    "batch_total": len(user_item_pairs),
                    "timestamp": int(time.time())
                })
        
        # 统计结果
        success_count = len([r for r in results if "error" not in r])
        logger.info(f"批量获取完成: 成功 {success_count}/{len(user_item_pairs)}")
        
        return results

    def get_status_summary_report(self, user_item_pairs=None):
        """
        生成交易状态摘要报告 - 基于官方API数据
        
        Args:
            user_item_pairs (list, optional): 指定的用户-商品对列表，如果不提供则从数据库获取
            
        Returns:
            dict: 状态分布报告
        """
        try:
            # 如果没有提供用户-商品对，尝试从数据库获取
            if not user_item_pairs:
                try:
                    from 旧系统文件.context_manager import ChatContextManager
                    context_manager = ChatContextManager()
                    
                    # 获取最近活跃的用户-商品对
                    recent_conversations = context_manager.get_recent_conversations(limit=20)
                    user_item_pairs = [(conv['user_id'], conv['item_id']) for conv in recent_conversations]
                except Exception as e:
                    logger.warning(f"从数据库获取用户-商品对失败: {e}")
                    return {"error": "无法获取用户-商品对数据"}
            
            if not user_item_pairs:
                return {"error": "没有可分析的用户-商品对"}
            
            # 批量获取官方状态
            status_results = self.batch_get_official_status(user_item_pairs)
            
            # 统计分析
            status_distribution = {}
            confidence_stats = []
            api_success_count = 0
            red_reminder_examples = {}
            
            for result in status_results:
                if "error" in result:
                    continue
                    
                api_success_count += 1
                
                official_status = result.get("official_status", {})
                mapped_status = official_status.get("mapped_status", "unknown")
                confidence = official_status.get("confidence", 0.0)
                red_reminder = official_status.get("red_reminder", "")
                
                # 状态分布统计
                if mapped_status not in status_distribution:
                    status_distribution[mapped_status] = 0
                status_distribution[mapped_status] += 1
                
                # 置信度统计
                confidence_stats.append(confidence)
                
                # redReminder示例收集
                if red_reminder and mapped_status not in red_reminder_examples:
                    red_reminder_examples[mapped_status] = red_reminder
            
            # 计算统计指标
            total_valid = len([r for r in status_results if "error" not in r])
            avg_confidence = sum(confidence_stats) / len(confidence_stats) if confidence_stats else 0
            
            return {
                "summary": {
                    "total_pairs": len(user_item_pairs),
                    "api_success_count": api_success_count,
                    "api_success_rate": api_success_count / len(user_item_pairs) if user_item_pairs else 0,
                    "avg_confidence": avg_confidence,
                    "unique_statuses": len(status_distribution)
                },
                "status_distribution": status_distribution,
                "red_reminder_examples": red_reminder_examples,
                "confidence_distribution": {
                    "high_confidence": len([c for c in confidence_stats if c >= 0.8]),
                    "medium_confidence": len([c for c in confidence_stats if 0.5 <= c < 0.8]),
                    "low_confidence": len([c for c in confidence_stats if c < 0.5])
                },
                "timestamp": int(time.time())
            }
            
        except Exception as e:
            logger.error(f"生成状态摘要报告时出错: {e}")
            return {"error": f"报告生成失败: {str(e)}"}

    def get_official_status_from_websocket_messages(self, user_id, item_id):
        """
        从WebSocket消息记录中提取官方交易状态
        利用已存储的系统消息(redReminder字段)获取官方状态
        
        Args:
            user_id (str): 买家用户ID
            item_id (str): 商品ID
            
        Returns:
            dict: 官方交易状态信息
        """
        try:
            from 旧系统文件.context_manager import ChatContextManager
            
            context_manager = ChatContextManager()
            
            # 1. 获取最近的消息记录，特别关注系统消息
            recent_messages = context_manager.get_recent_messages(
                limit=50, user_id=user_id, item_id=item_id
            )
            
            if not recent_messages:
                return {
                    "data_source": "websocket_messages",
                    "official_status": "no_conversation",
                    "red_reminder": None,
                    "confidence": 0.0,
                    "user_id": user_id,
                    "item_id": item_id,
                    "timestamp": int(time.time())
                }
            
            # 2. 查找最新的系统状态消息
            system_status_keywords = [
                '等待买家付款', '买家已拍下，待付款', '买家已付款',
                '我已付款，等待你发货', '等待卖家发货', '卖家已发货',
                '等待买家确认收货', '买家已确认收货', '交易关闭',
                '交易完成', '交易取消'
            ]
            
            latest_system_message = None
            latest_system_time = None
            
            # 3. 从最新消息向前搜索系统状态消息
            for message in recent_messages:
                content = message.get("content", "")
                
                # 检查是否包含官方状态关键词
                for keyword in system_status_keywords:
                    if keyword in content:
                        # 检查是否是系统消息或包含官方状态标识
                        if (message.get("role") == "system" or 
                            any(indicator in content for indicator in ["[", "]", "系统消息", "官方"])):
                            
                            latest_system_message = keyword
                            latest_system_time = message.get("timestamp")
                            break
                
                if latest_system_message:
                    break
            
            # 4. 映射官方状态到标准状态
            official_status_mapping = {
                '等待买家付款': 'ordered_unpaid',
                '买家已拍下，待付款': 'ordered_unpaid',
                '买家已付款': 'paid',
                '我已付款，等待你发货': 'paid',
                '等待卖家发货': 'paid',
                '卖家已发货': 'pending_delivery',
                '等待买家确认收货': 'pending_delivery',
                '买家已确认收货': 'delivered',
                '交易完成': 'delivered',
                '交易关闭': 'transaction_closed',
                '交易取消': 'transaction_closed'
            }
            
            if latest_system_message:
                mapped_status = official_status_mapping.get(latest_system_message, "unknown")
                confidence = 0.9  # 来自WebSocket的官方消息，高置信度
            else:
                # 没有找到系统状态消息，从用户消息推断
                mapped_status = self._infer_status_from_messages(recent_messages)["status"]
                confidence = 0.3  # 推断状态，低置信度
            
            # 5. 获取对话统计信息
            user_messages = [m for m in recent_messages if m.get("role") == "user"]
            assistant_messages = [m for m in recent_messages if m.get("role") == "assistant"]
            
            return {
                "data_source": "websocket_messages",
                "official_status": mapped_status,
                "red_reminder": latest_system_message,
                "confidence": confidence,
                "user_id": user_id,
                "item_id": item_id,
                "conversation_stats": {
                    "total_messages": len(recent_messages),
                    "user_messages": len(user_messages),
                    "assistant_messages": len(assistant_messages),
                    "latest_system_time": latest_system_time,
                    "has_recent_activity": len(recent_messages) > 0
                },
                "recent_message_sample": recent_messages[:3] if recent_messages else [],
                "timestamp": int(time.time())
            }
            
        except Exception as e:
            logger.error(f"从WebSocket消息提取官方状态时出错: {e}")
            return {
                "error": f"提取失败: {str(e)}",
                "data_source": "websocket_messages",
                "user_id": user_id,
                "item_id": item_id,
                "timestamp": int(time.time())
            }

    def get_official_transaction_status_v2(self, user_id, item_id):
        """
        获取官方交易状态 - 版本2：基于WebSocket消息和数据库
        这是对get_official_transaction_status的改进版本，当API不可用时使用WebSocket消息作为数据源
        
        Args:
            user_id (str): 买家用户ID
            item_id (str): 商品ID
            
        Returns:
            dict: 官方交易状态信息
        """
        try:
            # 1. 尝试从WebSocket消息中获取官方状态
            websocket_status = self.get_official_status_from_websocket_messages(user_id, item_id)
            
            # 2. 获取数据库中的买家状态作为补充
            database_status = self.get_transaction_status_from_database(user_id, item_id)
            
            # 3. 尝试获取商品信息作为上下文
            item_context = {}
            try:
                item_info = self.get_item_info_parsed(item_id)
                if "error" not in item_info:
                    item_context = {
                        "title": item_info.get("title", ""),
                        "price": item_info.get("price", ""),
                        "status": item_info.get("item_status", "")
                    }
            except Exception as e:
                logger.warning(f"获取商品信息失败: {e}")
            
            # 4. 整合结果
            primary_status = websocket_status.get("official_status", "unknown")
            primary_confidence = websocket_status.get("confidence", 0.0)
            
            # 如果WebSocket消息没有找到有效状态，使用数据库状态
            if primary_status == "unknown" and not websocket_status.get("error"):
                db_inferred_status = database_status.get("inferred_status")
                if db_inferred_status and db_inferred_status != "unknown":
                    primary_status = db_inferred_status
                    primary_confidence = database_status.get("status_confidence", 0.0)
            
            result = {
                "data_source": "websocket_and_database",
                "user_id": user_id,
                "item_id": item_id,
                "official_status": primary_status,
                "confidence": primary_confidence,
                "red_reminder": websocket_status.get("red_reminder"),
                
                # WebSocket数据
                "websocket_data": {
                    "status": websocket_status.get("official_status"),
                    "confidence": websocket_status.get("confidence"),
                    "conversation_stats": websocket_status.get("conversation_stats", {}),
                    "error": websocket_status.get("error")
                },
                
                # 数据库数据
                "database_data": {
                    "buyer_status": database_status.get("buyer_status"),
                    "inferred_status": database_status.get("inferred_status"),
                    "is_old_customer": database_status.get("is_old_customer", False),
                    "bargain_count": database_status.get("bargain_count", 0),
                    "error": database_status.get("error")
                },
                
                # 商品上下文
                "item_context": item_context,
                
                # 推荐操作
                "recommended_actions": self._get_recommended_actions(
                    primary_status, 
                    websocket_status.get("recent_message_sample", [])
                ),
                
                "timestamp": int(time.time())
            }
            
            return result
            
        except Exception as e:
            logger.error(f"获取官方交易状态v2时出错: {e}")
            return {
                "error": f"获取失败: {str(e)}",
                "data_source": "websocket_and_database",
                "user_id": user_id,
                "item_id": item_id,
                "timestamp": int(time.time())
            }
