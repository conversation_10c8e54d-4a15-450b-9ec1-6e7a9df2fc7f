import re
from typing import List, Dict
import os
from openai import OpenAI
from loguru import logger
# 导入增强版意图识别引擎
from enhanced_intent_engine import EnhancedIntentEngine


class XianyuReplyBot:
    def __init__(self):
        # 初始化OpenAI客户端
        self.client = OpenAI(
            api_key=os.getenv("API_KEY"),
            base_url=os.getenv("MODEL_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
        )
        self._init_system_prompts()
        self._init_agents()
        
        # 初始化增强版意图识别引擎
        self.enhanced_intent_engine = EnhancedIntentEngine()
        self.last_intent = None  # 记录最后一次意图

    def _init_agents(self):
        """初始化各领域Agent"""
        self.agents = {
            'price': PriceAgent(self.client, self.price_prompt, self._safe_filter),
            'tech': TechAgent(self.client, self.tech_prompt, self._safe_filter),
            'default': DefaultAgent(self.client, self.default_prompt, self._safe_filter),
        }

    def _init_system_prompts(self):
        """初始化各Agent专用提示词，直接从文件中加载"""
        prompt_dir = "prompts"
        
        try:
            # 加载价格提示词
            with open(os.path.join(prompt_dir, "price_prompt.txt"), "r", encoding="utf-8") as f:
                self.price_prompt = f.read()
                logger.debug(f"已加载价格提示词，长度: {len(self.price_prompt)} 字符")
            
            # 加载技术提示词
            with open(os.path.join(prompt_dir, "tech_prompt.txt"), "r", encoding="utf-8") as f:
                self.tech_prompt = f.read()
                logger.debug(f"已加载技术提示词，长度: {len(self.tech_prompt)} 字符")
            
            # 加载默认提示词
            with open(os.path.join(prompt_dir, "default_prompt.txt"), "r", encoding="utf-8") as f:
                self.default_prompt = f.read()
                logger.debug(f"已加载默认提示词，长度: {len(self.default_prompt)} 字符")
                
            logger.info("成功加载所有提示词")
        except Exception as e:
            logger.error(f"加载提示词时出错: {e}")
            raise

    def _safe_filter(self, text: str) -> str:
        """安全过滤模块"""
        blocked_phrases = ["微信", "QQ", "支付宝", "银行卡", "线下"]
        return "[安全提醒]请通过平台沟通" if any(p in text for p in blocked_phrases) else text

    def format_history(self, context: List[Dict]) -> str:
        """格式化对话历史，返回完整的对话记录"""
        # 过滤掉系统消息，只保留用户和助手的对话
        user_assistant_msgs = [msg for msg in context if msg['role'] in ['user', 'assistant']]
        return "\n".join([f"{msg['role']}: {msg['content']}" for msg in user_assistant_msgs])

    def generate_reply(self, user_msg: str, item_desc: str, context: List[Dict]) -> str:
        """
        使用增强版意图识别引擎生成回复
        
        Args:
            user_msg: 用户消息
            item_desc: 商品描述
            context: 对话历史
            
        Returns:
            str: 生成的回复
        """
        try:
            # 格式化对话历史用于意图识别
            formatted_context = self.format_history(context)
            
            # 使用增强版意图识别引擎
            intent_result = self.enhanced_intent_engine.recognize_intent(user_msg, item_desc, formatted_context)
            
            # 处理意图识别结果
            if isinstance(intent_result, dict):
                intent = intent_result.get("intent", "general_service")
                mapped_intent = intent_result.get("mapped_intent", "default")
                parameters = intent_result.get("parameters", {})
                confidence = intent_result.get("confidence", 0.0)
            else:
                # 兼容字符串格式返回
                intent = str(intent_result)
                mapped_intent = self._map_intent_to_legacy(intent)
                parameters = {}
                confidence = 1.0
            
            # 检查是否需要人工介入
            if intent == "human_handover":
                logger.warning(f"🚨 触发人工介入场景，等待人工客服接管")
                
                # 生成简单的转接提示，不调用任何Agent
                handover_message = "检测到您的需求比较复杂，正在为您转接人工客服，请稍等片刻。专业客服将为您提供更好的服务！"
                
                self.last_intent = "handover"
                return handover_message
            
            self.last_intent = mapped_intent
            
            logger.info(f"🧠 增强意图识别: {intent} -> {mapped_intent} (置信度: {confidence:.2f})")
            
            # 提取议价次数
            bargain_count = self._extract_bargain_count(context)
            
            # 根据映射的意图选择对应的Agent
            if mapped_intent == 'price':
                response = self.agents['price'].generate(
                    user_msg=user_msg,
                    item_desc=item_desc,
                    context=formatted_context,
                    bargain_count=bargain_count,
                    parameters=parameters  # 传递参数
                )
            elif mapped_intent == 'tech':
                response = self.agents['tech'].generate(
                    user_msg=user_msg,
                    item_desc=item_desc,
                    context=formatted_context,
                    parameters=parameters  # 传递参数
                )
            else:  # default
                response = self.agents['default'].generate(
                    user_msg=user_msg,
                    item_desc=item_desc,
                    context=formatted_context,
                    parameters=parameters  # 传递参数
                )
            
            logger.info(f"💬 Agent回复: {response}")
            return response
            
        except Exception as e:
            logger.error(f"生成回复时发生错误: {str(e)}")
            return "抱歉，系统暂时繁忙，请稍后再试"
    
    def _map_intent_to_legacy(self, intent: str) -> str:
        """将新意图映射到旧系统的意图格式"""
        mapping = {
            'price_negotiation': 'price',
            'technical_inquiry': 'tech',
            'general_service': 'default'
        }
        return mapping.get(intent, 'default')
    


    def _extract_bargain_count(self, context: List[Dict]) -> int:
        """
        从上下文中提取议价次数信息
        
        Args:
            context: 对话历史
            
        Returns:
            int: 议价次数，如果没有找到则返回0
        """
        # 查找系统消息中的议价次数信息
        for msg in context:
            if msg['role'] == 'system' and '议价次数' in msg['content']:
                try:
                    # 提取议价次数
                    match = re.search(r'议价次数[:：]\s*(\d+)', msg['content'])
                    if match:
                        return int(match.group(1))
                except Exception:
                    pass
        return 0

    def reload_prompts(self):
        """重新加载所有提示词"""
        logger.info("正在重新加载提示词...")
        self._init_system_prompts()
        self._init_agents()
        # 重新初始化增强版意图识别引擎
        self.enhanced_intent_engine = EnhancedIntentEngine()
        logger.info("提示词重新加载完成")

    def reset_intent_context(self):
        """重置意图上下文"""
        self.enhanced_intent_engine.reset_context()
        self.last_intent = None
        logger.info("意图上下文已重置")

    def get_intent_debug_info(self) -> Dict:
        """获取意图识别的调试信息"""
        current_intent_info = self.enhanced_intent_engine.get_current_intent_info()
        return {
            "current_intent": self.enhanced_intent_engine.current_intent,
            "legacy_intent": self.last_intent,
            "intent_info": current_intent_info,
            "available_scenes": list(self.enhanced_intent_engine.scene_templates.keys())
        }



class BaseAgent:
    """Agent基类，支持参数化回复"""

    def __init__(self, client, system_prompt, safety_filter):
        self.client = client
        self.system_prompt = system_prompt
        self.safety_filter = safety_filter

    def generate(self, user_msg: str, item_desc: str, context: str, bargain_count: int = 0, parameters: dict = None) -> str:
        """生成回复模板方法，支持参数化"""
        parameters = parameters or {}
        messages = self._build_messages(user_msg, item_desc, context, parameters)
        response = self._call_llm(messages)
        return self.safety_filter(response)

    def _build_messages(self, user_msg: str, item_desc: str, context: str, parameters: dict = None) -> List[Dict]:
        """构建消息链，包含参数信息"""
        parameters = parameters or {}
        
        # 构建参数信息字符串
        param_info = ""
        if parameters:
            param_info = "\n【智能参数提取】\n"
            for param in parameters:
                if isinstance(param, dict) and 'name' in param and 'value' in param:
                    name = param.get('name', '')
                    value = param.get('value', '')
                    desc = param.get('desc', '')
                    if value:
                        param_info += f"- {name}: {value}"
                        if desc:
                            param_info += f" ({desc})"
                        param_info += "\n"
        
        system_content = f"【商品信息】{item_desc}\n【你与客户对话历史】{context}{param_info}\n{self.system_prompt}"
        
        return [
            {"role": "system", "content": system_content},
            {"role": "user", "content": user_msg}
        ]

    def _call_llm(self, messages: List[Dict], temperature: float = 0.4) -> str:
        """调用大模型"""
        response = self.client.chat.completions.create(
            model=os.getenv("MODEL_NAME", "qwen-max"),
            messages=messages,
            temperature=temperature,
            max_tokens=500,
            top_p=0.8
        )
        return response.choices[0].message.content


class PriceAgent(BaseAgent):
    """议价处理Agent，支持参数化决策"""

    def generate(self, user_msg: str, item_desc: str, context: str, bargain_count: int=0, parameters: dict = None) -> str:
        """重写生成逻辑，支持参数化议价策略"""
        parameters = parameters or {}
        dynamic_temp = self._calc_temperature(bargain_count)
        
        # 分析议价参数
        price_analysis = self._analyze_price_parameters(parameters)
        
        messages = self._build_messages(user_msg, item_desc, context, parameters)
        messages[0]['content'] += f"\n▲当前议价轮次：{bargain_count}"
        if price_analysis:
            messages[0]['content'] += f"\n▲议价分析：{price_analysis}"

        response = self.client.chat.completions.create(
            model=os.getenv("MODEL_NAME", "qwen-max"),
            messages=messages,
            temperature=dynamic_temp,
            max_tokens=500,
            top_p=0.8
        )
        return self.safety_filter(response.choices[0].message.content)

    def _calc_temperature(self, bargain_count: int) -> float:
        """动态温度策略"""
        return min(0.3 + bargain_count * 0.15, 0.9)
    
    def _analyze_price_parameters(self, parameters: dict) -> str:
        """分析价格参数，提供决策建议"""
        analysis = []
        for param in parameters:
            if isinstance(param, dict):
                name = param.get('name', '')
                value = param.get('value', '')
                
                if name == 'target_price' and value:
                    analysis.append(f"用户期望价格: {value}")
                elif name == 'reason' and value:
                    analysis.append(f"砍价理由: {value}")
        
        return "; ".join(analysis) if analysis else ""


class TechAgent(BaseAgent):
    """技术咨询Agent，支持参数化技术服务"""
    
    def generate(self, user_msg: str, item_desc: str, context: str, bargain_count: int=0, parameters: dict = None) -> str:
        """重写生成逻辑，支持参数化技术回复"""
        parameters = parameters or {}
        
        # 分析技术参数
        tech_analysis = self._analyze_tech_parameters(parameters)
        
        messages = self._build_messages(user_msg, item_desc, context, parameters)
        if tech_analysis:
            messages[0]['content'] += f"\n▲技术重点：{tech_analysis}"

        response = self.client.chat.completions.create(
            model=os.getenv("MODEL_NAME", "qwen-max"),
            messages=messages,
            temperature=0.4,
            max_tokens=500,
            top_p=0.8,
            extra_body={
                "enable_search": True,
            }
        )

        return self.safety_filter(response.choices[0].message.content)
    
    def _analyze_tech_parameters(self, parameters: dict) -> str:
        """分析技术参数，提供回复重点"""
        analysis = []
        for param in parameters:
            if isinstance(param, dict):
                name = param.get('name', '')
                value = param.get('value', '')
                
                if name == 'tech_question' and value:
                    analysis.append(f"核心问题: {value}")
                elif name == 'comparison_target' and value:
                    analysis.append(f"对比目标: {value}")
                elif name == 'usage_scenario' and value:
                    analysis.append(f"使用场景: {value}")
        
        return "; ".join(analysis) if analysis else ""


class DefaultAgent(BaseAgent):
    """默认处理Agent，支持参数化服务"""

    def generate(self, user_msg: str, item_desc: str, context: str, bargain_count: int=0, parameters: dict = None) -> str:
        """重写生成逻辑，支持参数化通用服务"""
        parameters = parameters or {}
        
        # 分析服务参数
        service_analysis = self._analyze_service_parameters(parameters)
        
        messages = self._build_messages(user_msg, item_desc, context, parameters)
        if service_analysis:
            messages[0]['content'] += f"\n▲服务重点：{service_analysis}"
        
        response = self._call_llm(messages, temperature=0.7)
        return response
    
    def _analyze_service_parameters(self, parameters: dict) -> str:
        """分析服务参数，提供服务重点"""
        analysis = []
        for param in parameters:
            if isinstance(param, dict):
                name = param.get('name', '')
                value = param.get('value', '')
                
                if name == 'service_type' and value:
                    analysis.append(f"服务类型: {value}")
                elif name == 'urgency_level' and value:
                    analysis.append(f"紧急程度: {value}")
        
        return "; ".join(analysis) if analysis else ""