# 闲鱼智能客服系统 - 模块化单体架构版本

## 🎯 项目概述

这是闲鱼智能客服系统的模块化单体架构重构版本，采用现代化的模块化设计，具备高可维护性、可扩展性和可测试性。

### ✨ 核心特性

- 🏗️ **模块化架构**: 清晰的模块划分，职责明确
- 🔌 **可插拔设计**: 支持策略模式，易于扩展新功能
- 🧪 **测试驱动**: 完整的测试覆盖，确保代码质量
- ⚙️ **配置统一**: 统一的配置管理和日志系统
- 🚀 **高性能**: 异步处理，支持高并发
- 👥 **人工介入**: 智能的人工客服转接机制

## 📁 项目结构

```
xianyu-agent-refactored/
├── main_refactored.py         # 新架构主程序入口
├── start.py                   # 系统启动脚本
├── dev_tools.py              # 开发工具脚本
├── run_tests.py              # 测试运行脚本
├── .env.template             # 配置文件模板
├── requirements.txt          # 依赖管理
├── README_NEW_ARCHITECTURE.md # 新架构说明文档
│
├── core/                     # 核心编排逻辑
│   ├── message_router.py     # 消息路由与分发
│   └── reply_generator.py    # 回复生成与策略调度
│
├── modules/                  # 可插拔功能模块
│   ├── platform_connector/   # 平台连接器
│   │   ├── base_connector.py # 抽象基类
│   │   └── xianyu_connector.py # 闲鱼平台实现
│   ├── context_manager/      # 上下文管理
│   │   └── db_handler.py     # 数据库操作
│   ├── intent_engine/        # 意图识别
│   │   └── engine.py         # 意图识别引擎
│   ├── reply_strategies/     # 回复策略
│   │   ├── base_strategy.py  # 策略基类
│   │   ├── price_strategy.py # 价格议价策略
│   │   ├── tech_strategy.py  # 技术咨询策略
│   │   └── default_strategy.py # 默认策略
│   └── human_handover/       # 人工介入
│       └── notifier.py       # 通知管理
│
├── config/                   # 系统配置
│   ├── settings.py          # 配置管理
│   └── logger_config.py     # 日志配置
│
├── tests/                    # 测试文件
│   └── test_refactored_system.py # 系统测试
│
├── scene_config/             # 场景配置
├── data/                     # 数据存储
├── logs/                     # 日志文件
└── review/                   # 开发记录
```

## 🚀 快速开始

### 1. 环境准备

确保你的系统满足以下要求：
- Python 3.8+
- 必要的Python包（见requirements.txt）

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置系统

```bash
# 复制配置模板
cp .env.template .env

# 编辑配置文件，填入真实配置
nano .env
```

必填配置项：
- `API_KEY`: 大模型API密钥
- `COOKIES_STR`: 闲鱼平台Cookies

### 4. 初始化环境

```bash
# 使用开发工具初始化
python dev_tools.py setup
```

### 5. 运行测试

```bash
# 运行系统测试
python dev_tools.py test
```

### 6. 启动系统

```bash
# 启动新架构系统
python start.py
```

## 🛠️ 开发工具

项目提供了便捷的开发工具：

```bash
# 运行测试
python dev_tools.py test

# 检查代码质量
python dev_tools.py check

# 查看系统状态
python dev_tools.py status

# 清理缓存文件
python dev_tools.py clean

# 设置开发环境
python dev_tools.py setup
```

## 🔄 系统工作流程

1. **消息接收**: XianyuConnector接收平台消息
2. **消息路由**: MessageRouter进行消息分类和验证
3. **意图识别**: IntentEngine识别用户意图
4. **策略选择**: ReplyGenerator选择合适的回复策略
5. **回复生成**: 对应策略生成个性化回复
6. **消息发送**: 通过平台连接器发送回复
7. **人工介入**: 必要时转接人工客服

## 🧪 测试

系统包含完整的测试套件：

- **单元测试**: 每个模块的独立功能测试
- **集成测试**: 模块间协作测试
- **系统测试**: 端到端功能验证

运行测试：
```bash
python run_tests.py
```

## 📊 监控与日志

- **日志系统**: 基于loguru的结构化日志
- **配置管理**: 统一的环境变量管理
- **错误追踪**: 详细的错误信息和堆栈跟踪

## 🔧 扩展开发

### 添加新的回复策略

1. 继承`BaseStrategy`类
2. 实现必要的方法
3. 在`ReplyGenerator`中注册策略

### 添加新的平台支持

1. 继承`BasePlatformConnector`类
2. 实现平台特定的连接逻辑
3. 在主程序中配置新连接器

### 添加新的通知方式

1. 扩展`HumanHandoverManager`
2. 实现新的通知发送方法
3. 在配置中添加相关设置

## 📝 配置说明

详细的配置项说明请参考`.env.template`文件中的注释。

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 运行测试确保通过
5. 提交Pull Request

## 📄 许可证

本项目采用MIT许可证。

## 🆘 故障排除

### 常见问题

1. **配置文件错误**: 检查`.env`文件格式和必填项
2. **依赖缺失**: 运行`pip install -r requirements.txt`
3. **权限问题**: 确保有数据目录的读写权限
4. **网络连接**: 检查API和WebSocket连接

### 获取帮助

- 查看日志文件：`logs/app.log`
- 运行系统检查：`python dev_tools.py status`
- 查看测试结果：`python dev_tools.py test`

---

🎉 **恭喜！你现在拥有了一个现代化、可维护的智能客服系统！**
