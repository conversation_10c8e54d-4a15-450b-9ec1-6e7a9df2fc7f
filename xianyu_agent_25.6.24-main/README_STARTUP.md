# 闲鱼智能客服系统 - 启动指南

## 📋 目录
- [系统启动前的准备工作](#系统启动前的准备工作)
- [详细的启动步骤](#详细的启动步骤)
- [启动后的验证方法](#启动后的验证方法)
- [常见启动问题的排查](#常见启动问题的排查)
- [开发和调试模式](#开发和调试模式)

---

## 🛠️ 系统启动前的准备工作

### 1. 环境要求检查

#### Python版本要求
```bash
# 检查Python版本（需要3.8+）
python3 --version
# 输出示例：Python 3.9.6
```

#### 必需依赖包
```bash
# 安装依赖包
pip install -r requirements.txt

# 或使用国内镜像源（推荐）
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

**核心依赖列表**：
- `loguru>=0.7.0` - 日志系统
- `openai>=1.0.0` - AI模型客户端
- `python-dotenv>=1.0.0` - 环境变量管理
- `requests>=2.31.0` - HTTP请求
- `websockets>=11.0.0` - WebSocket客户端

### 2. 配置文件设置

#### 创建配置文件
```bash
# 复制配置模板
cp .env.template .env

# 编辑配置文件
nano .env  # 或使用其他编辑器
```

#### 必填配置项

**🔑 API配置（必填）**
```env
# 大模型API密钥 - 通过阿里云百炼平台获取
API_KEY=sk-your-api-key-here

# API基础URL（通常不需要修改）
MODEL_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# 模型名称
MODEL_NAME=qwen-max
```

**🍪 平台配置（必填）**
```env
# 闲鱼平台Cookies - 从浏览器开发者工具获取
COOKIES_STR=your_complete_cookies_string_here
```

**获取Cookies的方法**：
1. 打开浏览器，登录闲鱼网页版
2. 按F12打开开发者工具
3. 切换到Network标签
4. 刷新页面，找到任意请求
5. 复制Request Headers中的完整Cookie字符串

#### 可选配置项

**⚙️ 系统配置**
```env
# 调试模式（开发时建议设为true）
DEBUG_MODE=false

# 日志级别
LOG_LEVEL=INFO

# 意图识别阈值
RELATED_INTENT_THRESHOLD=0.6
```

**📊 数据库配置**
```env
# 数据库文件路径
DB_PATH=data/chat_history.db

# 最大历史消息数
MAX_HISTORY=100
```

**🔔 通知配置（可选）**
```env
# 是否启用通知
NOTIFICATION_ENABLED=false

# 钉钉Webhook地址
DINGTALK_WEBHOOK=

# 微信Webhook地址
WECHAT_WEBHOOK=
```

### 3. 数据目录初始化

```bash
# 使用开发工具自动初始化
python3 dev_tools.py setup

# 或手动创建目录
mkdir -p data logs
```

---

## 🚀 详细的启动步骤

### 1. 项目目录定位

确保在项目根目录下执行启动命令：
```bash
# 进入项目目录
cd /path/to/xianyu_agent_25.6.20-main

# 确认目录结构
ls -la
# 应该看到：main_refactored.py, start.py, config/, core/, modules/ 等
```

### 2. 启动系统

#### 方法一：使用启动脚本（推荐）
```bash
# 标准启动
python3 start.py
```

#### 方法二：直接启动主程序
```bash
# 直接启动（不推荐，缺少环境检查）
python3 main_refactored.py
```

### 3. 启动过程关键日志

**✅ 正常启动日志示例**：
```
🎯 闲鱼智能客服系统 - 模块化单体架构版本
============================================================
🔍 检查运行环境...
✅ Python版本: 3.9.6
✅ 目录结构检查通过
✅ 配置文件检查通过
✅ 核心依赖检查通过
✅ 数据目录已创建

🎉 环境检查通过，正在启动系统...
----------------------------------------
INFO     | 配置加载完成
INFO     | 🚀 闲鱼智能客服系统启动中...
INFO     | 📊 初始化上下文管理器...
INFO     | 🧠 初始化意图识别引擎...
INFO     | 👥 初始化人工介入管理器...
INFO     | 💬 初始化回复生成器...
INFO     | 🚦 初始化消息路由器...
INFO     | 🔌 初始化平台连接器...
DEBUG    | 用户ID: 2218802903208, 设备ID: xxx-xxx-xxx
INFO     | 🌐 连接到闲鱼平台...
DEBUG    | Login成功
INFO     | Token获取成功
INFO     | 闲鱼平台连接成功
INFO     | ✅ 系统初始化完成
INFO     | 🎯 开始监听消息...
INFO     | WebSocket连接已建立
DEBUG    | WebSocket注册消息已发送
INFO     | WebSocket连接注册完成
```

### 4. 启动成功标志

系统启动成功的关键标志：
- ✅ `✅ 系统初始化完成`
- ✅ `🎯 开始监听消息...`
- ✅ `WebSocket连接已建立`
- ✅ `WebSocket连接注册完成`
- ✅ 开始出现心跳日志：`心跳包已发送` 和 `收到心跳响应`

---

## ✅ 启动后的验证方法

### 1. 检查WebSocket连接状态

**正常连接标志**：
```
INFO     | WebSocket连接已建立
INFO     | WebSocket连接注册完成
DEBUG    | 心跳包已发送
DEBUG    | 收到心跳响应
```

**连接异常标志**：
```
ERROR    | WebSocket连接失败: HTTP 404
ERROR    | 连接闲鱼平台失败: generate_device_id() missing...
```

### 2. 验证各模块工作状态

```bash
# 使用开发工具检查系统状态
python3 dev_tools.py status
```

**正常输出示例**：
```
📋 配置文件:
  ✅ .env
  ✅ .env.template

📁 数据目录:
  ✅ data (X 个文件)
  ✅ logs (X 个文件)
  ✅ scene_config (X 个文件)

🏗️ 模块结构:
  ✅ config (X 个Python文件)
  ✅ core (X 个Python文件)
  ✅ modules (X 个Python文件)
  ✅ tests (X 个Python文件)
```

### 3. 测试消息收发功能

#### 方法一：运行系统测试
```bash
# 运行完整测试套件
python3 dev_tools.py test

# 或直接运行测试文件
python3 tests/test_refactored_system.py
```

#### 方法二：观察实时日志
启动系统后，观察日志中是否出现：
```
🔍 [DEBUG] 收到WebSocket原始消息: ...
🔍 [PARSE] 开始解析消息...
🔍 [ROUTER] 收到消息处理请求: ...
```

---

## 🔧 常见启动问题的排查

### 1. 认证失败（401错误）

**错误现象**：
```
ERROR    | 连接闲鱼平台失败: HTTP 401
ERROR    | {"reason": "device id or appkey is not equal", "code": "4000001"}
```

**解决方案**：
1. **检查Cookies有效性**：
   ```bash
   # 重新获取Cookies
   # 1. 清除浏览器缓存
   # 2. 重新登录闲鱼
   # 3. 复制新的Cookies到.env文件
   ```

2. **验证用户ID**：
   ```bash
   # 检查日志中的用户ID是否正确
   grep "用户ID:" logs/app.log
   ```

3. **重新生成配置**：
   ```bash
   # 备份当前配置
   cp .env .env.backup
   
   # 重新配置
   cp .env.template .env
   # 重新填入正确的配置
   ```

### 2. WebSocket连接失败

**错误现象**：
```
ERROR    | WebSocket连接失败: HTTP 404
ERROR    | WebSocket连接失败: Connection refused
```

**解决方案**：
1. **检查网络连接**：
   ```bash
   # 测试网络连通性
   ping wss-goofish.dingtalk.com
   ```

2. **检查防火墙设置**：
   ```bash
   # 确保WebSocket端口未被阻止
   # 检查企业网络是否限制WebSocket连接
   ```

3. **重启系统**：
   ```bash
   # 停止当前进程（Ctrl+C）
   # 等待5秒后重新启动
   python3 start.py
   ```

### 3. 配置文件错误

**错误现象**：
```
ERROR    | 配置验证失败: 缺少必需配置项
ERROR    | 无法获取用户ID (unb)，请检查cookies配置
```

**诊断步骤**：
1. **检查配置文件格式**：
   ```bash
   # 检查.env文件是否存在
   ls -la .env
   
   # 检查配置文件内容
   cat .env | grep -E "API_KEY|COOKIES_STR"
   ```

2. **验证必填项**：
   ```bash
   # 使用开发工具检查配置
   python3 dev_tools.py check
   ```

3. **重置配置**：
   ```bash
   # 使用模板重新配置
   cp .env.template .env
   nano .env  # 重新填入配置
   ```

---

## 🔍 开发和调试模式

### 1. 启用调试日志

**方法一：修改配置文件**
```env
# 在.env文件中设置
DEBUG_MODE=true
LOG_LEVEL=DEBUG
```

**方法二：临时启用**
```bash
# 设置环境变量
export DEBUG_MODE=true
export LOG_LEVEL=DEBUG
python3 start.py
```

### 2. 使用开发工具

```bash
# 检查系统状态
python3 dev_tools.py status

# 运行测试
python3 dev_tools.py test

# 检查代码质量
python3 dev_tools.py check

# 清理缓存
python3 dev_tools.py clean

# 设置开发环境
python3 dev_tools.py setup
```

### 3. 调试日志说明

**关键调试标识**：
- `🔍 [DEBUG]` - WebSocket消息接收
- `🔍 [PARSE]` - 消息解析过程
- `🔍 [ROUTER]` - 消息路由处理
- `🔍 [MAIN]` - 主程序流程

**调试日志示例**：
```
🔍 [DEBUG] 收到WebSocket原始消息: {"headers":...}
🔍 [PARSE] 开始解析消息，消息键: ['headers', 'lwp', 'body']
🔍 [PARSE] 确认为同步包消息，开始处理
🔍 [ROUTER] 收到消息处理请求: ID=xxx, 类型=USER_MESSAGE
🔍 [MAIN] ✅ 收到标准化消息: 用户=张三, 内容='你好'
```

### 4. 性能监控

```bash
# 查看系统资源使用
top -p $(pgrep -f "python3.*start.py")

# 查看日志文件大小
ls -lh logs/

# 监控实时日志
tail -f logs/app.log
```

---

## 🎯 快速启动检查清单

- [ ] Python 3.8+ 已安装
- [ ] 依赖包已安装 (`pip install -r requirements.txt`)
- [ ] `.env` 文件已配置（API_KEY 和 COOKIES_STR）
- [ ] 在项目根目录下执行启动命令
- [ ] 观察启动日志确认无错误
- [ ] 确认看到 "WebSocket连接注册完成" 消息
- [ ] 确认心跳机制正常工作

**🎉 启动成功！系统现在已准备好处理用户消息并提供智能回复服务！**

---

## 📚 附录

### A. 完整的配置文件示例

```env
# 闲鱼智能客服系统 - 完整配置示例

# ===================
# API 配置 (必填)
# ===================
API_KEY=sk-1234567890abcdef1234567890abcdef
MODEL_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
MODEL_NAME=qwen-max

# ===================
# 平台配置 (必填)
# ===================
COOKIES_STR=unb=2218802903208; cookie2=1234567890abcdef; t=1234567890; _tb_token_=abcdef123456; xlly_s=1; _m_h5_tk=abcdef_1234567890; _m_h5_tk_enc=1234567890abcdef

# ===================
# 系统配置
# ===================
DEBUG_MODE=false
LOG_LEVEL=INFO
RELATED_INTENT_THRESHOLD=0.6

# ===================
# 数据库配置
# ===================
DB_PATH=data/chat_history.db
MAX_HISTORY=100

# ===================
# WebSocket配置
# ===================
WS_URL=wss://wss-goofish.dingtalk.com/
MESSAGE_EXPIRE_TIME=300000
TOKEN_REFRESH_INTERVAL=3600

# ===================
# 通知配置 (可选)
# ===================
NOTIFICATION_ENABLED=false
DINGTALK_WEBHOOK=
WECHAT_WEBHOOK=
```

### B. 系统架构概览

```
闲鱼智能客服系统架构
├── 启动层 (start.py)
├── 主程序 (main_refactored.py)
├── 核心层 (core/)
│   ├── 消息路由器 (message_router.py)
│   └── 回复生成器 (reply_generator.py)
├── 功能模块层 (modules/)
│   ├── 平台连接器 (platform_connector/)
│   ├── 上下文管理 (context_manager/)
│   ├── 意图识别 (intent_engine/)
│   ├── 回复策略 (reply_strategies/)
│   └── 人工介入 (human_handover/)
├── 配置层 (config/)
│   ├── 设置管理 (settings.py)
│   └── 日志配置 (logger_config.py)
└── 工具层 (dev_tools.py, tests/)
```

### C. 日志文件说明

**日志文件位置**：
- 主日志：`logs/app.log`
- 错误日志：`logs/error.log`
- 调试日志：`logs/debug.log`

**日志级别说明**：
- `DEBUG`: 详细的调试信息
- `INFO`: 一般信息
- `WARNING`: 警告信息
- `ERROR`: 错误信息

### D. 端口和网络要求

**出站连接要求**：
- HTTPS (443): 用于API调用和Token获取
- WebSocket (443): 用于实时消息连接
- DNS (53): 用于域名解析

**防火墙配置**：
```bash
# 允许出站HTTPS连接
iptables -A OUTPUT -p tcp --dport 443 -j ACCEPT

# 允许DNS查询
iptables -A OUTPUT -p udp --dport 53 -j ACCEPT
```

### E. 性能优化建议

**系统资源**：
- 最小内存：512MB
- 推荐内存：1GB+
- CPU：单核即可，多核更佳
- 磁盘：至少100MB可用空间

**优化配置**：
```env
# 生产环境推荐配置
DEBUG_MODE=false
LOG_LEVEL=INFO
MAX_HISTORY=50
MESSAGE_EXPIRE_TIME=180000
```

### F. 安全注意事项

1. **配置文件安全**：
   ```bash
   # 设置配置文件权限
   chmod 600 .env

   # 确保不提交到版本控制
   echo ".env" >> .gitignore
   ```

2. **日志安全**：
   ```bash
   # 定期清理日志
   find logs/ -name "*.log" -mtime +7 -delete

   # 设置日志轮转
   logrotate /etc/logrotate.d/xianyu-agent
   ```

3. **网络安全**：
   - 使用HTTPS连接
   - 定期更新Cookies
   - 监控异常连接

---

## 🆘 技术支持

### 问题反馈
如果遇到启动问题，请提供以下信息：
1. 操作系统版本
2. Python版本
3. 完整的错误日志
4. 配置文件（隐藏敏感信息）

### 常用命令速查

```bash
# 快速启动
python3 start.py

# 检查状态
python3 dev_tools.py status

# 运行测试
python3 dev_tools.py test

# 查看日志
tail -f logs/app.log

# 重置环境
python3 dev_tools.py setup

# 清理缓存
python3 dev_tools.py clean
```

---

**📞 需要帮助？**
- 查看 `README_NEW_ARCHITECTURE.md` 了解系统架构
- 查看 `review/` 目录了解开发历程
- 运行 `python3 dev_tools.py --help` 查看工具帮助
