#!/usr/bin/env python3
"""
闲鱼智能客服系统 - 开发工具

提供开发和维护相关的工具命令
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def run_tests():
    """运行测试"""
    print("🧪 运行系统测试...")
    try:
        result = subprocess.run([
            sys.executable, 'tests/test_refactored_system.py'
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ 所有测试通过！")
            return True
        else:
            print("❌ 测试失败")
            return False
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False

def check_code_quality():
    """检查代码质量"""
    print("🔍 检查代码质量...")
    
    # 检查Python语法
    python_files = []
    for root, dirs, files in os.walk('.'):
        # 跳过隐藏目录和虚拟环境
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    print(f"📁 找到 {len(python_files)} 个Python文件")
    
    syntax_errors = []
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                compile(f.read(), file_path, 'exec')
        except SyntaxError as e:
            syntax_errors.append(f"{file_path}: {e}")
    
    if syntax_errors:
        print("❌ 发现语法错误:")
        for error in syntax_errors:
            print(f"  {error}")
        return False
    else:
        print("✅ 语法检查通过")
        return True

def show_system_status():
    """显示系统状态"""
    print("📊 系统状态检查...")
    
    # 检查配置文件
    config_files = ['.env', '.env.template', '.env.example']
    print("\n📋 配置文件:")
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"  ✅ {config_file}")
        else:
            print(f"  ❌ {config_file}")
    
    # 检查数据目录
    data_dirs = ['data', 'logs', 'scene_config']
    print("\n📁 数据目录:")
    for data_dir in data_dirs:
        if os.path.exists(data_dir):
            file_count = len(list(Path(data_dir).rglob('*')))
            print(f"  ✅ {data_dir} ({file_count} 个文件)")
        else:
            print(f"  ❌ {data_dir}")
    
    # 检查模块结构
    modules = ['config', 'core', 'modules', 'tests']
    print("\n🏗️ 模块结构:")
    for module in modules:
        if os.path.exists(module):
            py_files = list(Path(module).rglob('*.py'))
            print(f"  ✅ {module} ({len(py_files)} 个Python文件)")
        else:
            print(f"  ❌ {module}")

def clean_cache():
    """清理缓存文件"""
    print("🧹 清理缓存文件...")
    
    cache_patterns = [
        '**/__pycache__',
        '**/*.pyc',
        '**/*.pyo',
        '**/.pytest_cache',
        '**/logs/*.log'
    ]
    
    removed_count = 0
    for pattern in cache_patterns:
        for path in Path('.').glob(pattern):
            if path.is_file():
                path.unlink()
                removed_count += 1
            elif path.is_dir():
                import shutil
                shutil.rmtree(path)
                removed_count += 1
    
    print(f"✅ 已清理 {removed_count} 个缓存文件/目录")

def setup_environment():
    """设置开发环境"""
    print("⚙️ 设置开发环境...")
    
    # 创建必要目录
    dirs_to_create = ['data', 'logs', 'tests']
    for dir_name in dirs_to_create:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"✅ 创建目录: {dir_name}")
    
    # 复制配置文件模板
    if not os.path.exists('.env') and os.path.exists('.env.template'):
        import shutil
        shutil.copy('.env.template', '.env')
        print("✅ 已复制配置文件模板")
        print("💡 请编辑 .env 文件填入真实配置")
    
    print("✅ 开发环境设置完成")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='闲鱼智能客服系统开发工具')
    parser.add_argument('command', choices=[
        'test', 'check', 'status', 'clean', 'setup'
    ], help='要执行的命令')
    
    args = parser.parse_args()
    
    print("🛠️ 闲鱼智能客服系统 - 开发工具")
    print("=" * 50)
    
    if args.command == 'test':
        success = run_tests()
        sys.exit(0 if success else 1)
    elif args.command == 'check':
        success = check_code_quality()
        sys.exit(0 if success else 1)
    elif args.command == 'status':
        show_system_status()
    elif args.command == 'clean':
        clean_cache()
    elif args.command == 'setup':
        setup_environment()

if __name__ == "__main__":
    main()
