# 闲鱼智能客服系统 - 环境配置模板
# 复制此文件为 .env 并填入真实配置

# ===================
# API 配置 (必填)
# ===================
# 大模型API密钥 - 通过百炼模型平台获取
API_KEY=your_api_key_here

# 大模型API基础URL
MODEL_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# 使用的模型名称
MODEL_NAME=qwen-max

# ===================
# 平台配置 (必填)
# ===================
# 闲鱼平台Cookies - 从浏览器开发者工具获取
COOKIES_STR=your_cookies_string_here

# WebSocket连接URL
WS_URL=wss://h5api.m.goofish.com/websocket

# 消息过期时间 (毫秒，默认5分钟)
MESSAGE_EXPIRE_TIME=300000

# Token刷新间隔 (秒，默认1小时)
TOKEN_REFRESH_INTERVAL=3600

# ===================
# 系统配置
# ===================
# 调试模式 (true/false)
DEBUG_MODE=false

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# 意图相关性阈值 (0.0-1.0)
RELATED_INTENT_THRESHOLD=0.6

# ===================
# 数据库配置
# ===================
# 数据库文件路径
DB_PATH=data/chat_history.db

# 最大历史消息数
MAX_HISTORY=100

# ===================
# 通知配置 (可选)
# ===================
# 是否启用通知 (true/false)
NOTIFICATION_ENABLED=false

# 钉钉Webhook地址 (可选)
DINGTALK_WEBHOOK=

# 微信Webhook地址 (可选)
WECHAT_WEBHOOK=

# ===================
# 其他配置
# ===================
# 切换关键词
TOGGLE_KEYWORDS=。
