#!/usr/bin/env python3
"""
闲鱼智能客服系统 - 启动脚本

新架构版本的系统启动入口
"""

import os
import sys
import asyncio
from pathlib import Path

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必要的目录
    required_dirs = ['config', 'core', 'modules', 'scene_config']
    missing_dirs = []
    
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            missing_dirs.append(dir_name)
    
    if missing_dirs:
        print(f"❌ 缺少必要目录: {missing_dirs}")
        return False
    
    print("✅ 目录结构检查通过")
    
    # 检查配置文件
    if not os.path.exists('.env'):
        print("⚠️  未找到 .env 配置文件")
        if os.path.exists('.env.template'):
            print("💡 请复制 .env.template 为 .env 并填入配置")
        elif os.path.exists('.env.example'):
            print("💡 请复制 .env.example 为 .env 并填入配置")
        return False
    
    print("✅ 配置文件检查通过")
    
    # 检查依赖
    try:
        import loguru
        import openai
        import websockets
        import requests
        print("✅ 核心依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("💡 请运行: pip install -r requirements.txt")
        return False
    
    return True

def create_data_directory():
    """创建数据目录"""
    data_dir = Path("data")
    logs_dir = Path("logs")
    
    data_dir.mkdir(exist_ok=True)
    logs_dir.mkdir(exist_ok=True)
    
    print("✅ 数据目录已创建")

async def start_system():
    """启动系统"""
    try:
        # 导入主系统
        from main_refactored import XianyuAgentSystem
        
        print("🚀 启动闲鱼智能客服系统...")
        print("=" * 50)
        
        # 创建并启动系统
        system = XianyuAgentSystem()
        await system.start()
        
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🎯 闲鱼智能客服系统 - 模块化单体架构版本")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请修复后重试")
        sys.exit(1)
    
    # 创建必要目录
    create_data_directory()
    
    print("\n🎉 环境检查通过，正在启动系统...")
    print("-" * 40)
    
    # 启动系统
    try:
        asyncio.run(start_system())
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
