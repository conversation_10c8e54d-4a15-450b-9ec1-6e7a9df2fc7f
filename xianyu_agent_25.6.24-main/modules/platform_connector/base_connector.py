"""
平台连接器基类

定义所有平台连接器的标准接口，支持多平台扩展
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum


class MessageType(Enum):
    """消息类型枚举"""
    USER_MESSAGE = "user_message"
    SYSTEM_EVENT = "system_event"
    TYPING_STATUS = "typing_status"
    OTHER = "other"


@dataclass
class StandardMessage:
    """标准化消息格式"""
    message_id: str
    chat_id: str
    user_id: str
    item_id: str
    content: str
    message_type: MessageType
    timestamp: int
    sender_name: str
    platform_data: Dict[str, Any]  # 平台特有数据


@dataclass
class ItemInfo:
    """标准化商品信息格式"""
    item_id: str
    title: str
    price: float
    description: str
    images: List[str]
    seller_id: str
    platform_data: Dict[str, Any]  # 平台特有数据


class BasePlatformConnector(ABC):
    """平台连接器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化平台连接器
        
        Args:
            config: 平台配置信息
        """
        self.config = config
        self.is_connected = False
        self._setup_connector()
    
    @abstractmethod
    def _setup_connector(self):
        """设置连接器，子类实现具体逻辑"""
        pass
    
    @abstractmethod
    async def connect(self) -> bool:
        """
        连接到平台
        
        Returns:
            bool: 连接是否成功
        """
        pass
    
    @abstractmethod
    async def disconnect(self):
        """断开平台连接"""
        pass
    
    @abstractmethod
    def authenticate(self) -> bool:
        """
        平台认证
        
        Returns:
            bool: 认证是否成功
        """
        pass
    
    @abstractmethod
    def get_token(self) -> Optional[str]:
        """
        获取访问令牌
        
        Returns:
            str: 访问令牌，失败返回None
        """
        pass
    
    @abstractmethod
    def refresh_token(self) -> bool:
        """
        刷新访问令牌
        
        Returns:
            bool: 刷新是否成功
        """
        pass
    
    @abstractmethod
    async def listen_messages(self, callback):
        """
        监听平台消息
        
        Args:
            callback: 消息回调函数，接收StandardMessage对象
        """
        pass
    
    @abstractmethod
    def parse_message(self, raw_message: Dict[str, Any]) -> Optional[StandardMessage]:
        """
        解析原始消息为标准格式
        
        Args:
            raw_message: 平台原始消息
            
        Returns:
            StandardMessage: 标准化消息，解析失败返回None
        """
        pass
    
    @abstractmethod
    def get_item_info(self, item_id: str) -> Optional[ItemInfo]:
        """
        获取商品信息
        
        Args:
            item_id: 商品ID
            
        Returns:
            ItemInfo: 商品信息，获取失败返回None
        """
        pass
    
    @abstractmethod
    def send_message(self, chat_id: str, content: str) -> bool:
        """
        发送消息
        
        Args:
            chat_id: 会话ID
            content: 消息内容
            
        Returns:
            bool: 发送是否成功
        """
        pass
    
    def is_chat_message(self, raw_message: Dict[str, Any]) -> bool:
        """
        判断是否为聊天消息
        
        Args:
            raw_message: 原始消息
            
        Returns:
            bool: 是否为聊天消息
        """
        # 默认实现，子类可以重写
        return True
    
    def is_system_message(self, raw_message: Dict[str, Any]) -> bool:
        """
        判断是否为系统消息
        
        Args:
            raw_message: 原始消息
            
        Returns:
            bool: 是否为系统消息
        """
        # 默认实现，子类可以重写
        return False
    
    def is_typing_status(self, raw_message: Dict[str, Any]) -> bool:
        """
        判断是否为输入状态消息
        
        Args:
            raw_message: 原始消息
            
        Returns:
            bool: 是否为输入状态消息
        """
        # 默认实现，子类可以重写
        return False
    
    def get_platform_name(self) -> str:
        """
        获取平台名称
        
        Returns:
            str: 平台名称
        """
        return self.__class__.__name__.replace('Connector', '').lower()
    
    def get_connection_status(self) -> Dict[str, Any]:
        """
        获取连接状态信息
        
        Returns:
            Dict: 连接状态信息
        """
        return {
            'platform': self.get_platform_name(),
            'connected': self.is_connected,
            'config_valid': self._validate_config()
        }
    
    def _validate_config(self) -> bool:
        """
        验证配置是否有效
        
        Returns:
            bool: 配置是否有效
        """
        # 默认实现，子类可以重写
        return bool(self.config)
    
    def __str__(self) -> str:
        return f"{self.get_platform_name()}Connector(connected={self.is_connected})"
