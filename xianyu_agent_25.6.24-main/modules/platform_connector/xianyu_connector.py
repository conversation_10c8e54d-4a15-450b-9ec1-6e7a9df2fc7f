"""
闲鱼平台连接器

实现闲鱼平台的具体连接逻辑，继承自BasePlatformConnector
"""

import time
import json
import re
import sys
import asyncio
import websockets
from typing import Dict, Any, Optional, List
import requests
from loguru import logger

from .base_connector import BasePlatformConnector, StandardMessage, ItemInfo, MessageType
from config.settings import settings
from utils.xianyu_utils import generate_sign, generate_mid, generate_uuid, trans_cookies, generate_device_id, decrypt


class XianyuConnector(BasePlatformConnector):
    """闲鱼平台连接器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化闲鱼连接器
        
        Args:
            config: 闲鱼平台配置
        """
        self.session = None
        self.websocket = None
        self.current_token = None
        self.last_token_refresh_time = 0
        self.token_refresh_interval = config.get('token_refresh_interval', 3600)
        self.message_expire_time = config.get('message_expire_time', 300000)
        
        super().__init__(config)
    
    def _setup_connector(self):
        """设置闲鱼连接器"""
        self.session = requests.Session()
        self.session.headers.update({
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'origin': 'https://www.goofish.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://www.goofish.com/',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        })
        
        # 设置cookies
        cookies_str = self.config.get('cookies_str')
        if cookies_str:
            self._set_cookies(cookies_str)

        # 获取用户ID并生成device ID
        self.user_id = self.session.cookies.get('unb')
        if not self.user_id:
            raise Exception("无法获取用户ID (unb)，请检查cookies配置")

        self.device_id = generate_device_id(self.user_id)
        logger.debug(f"用户ID: {self.user_id}, 设备ID: {self.device_id}")

        logger.info("闲鱼连接器初始化完成")
    
    def _set_cookies(self, cookies_str: str):
        """设置cookies"""
        try:
            cookies_dict = trans_cookies(cookies_str)
            for name, value in cookies_dict.items():
                self.session.cookies.set(name, value)
            logger.debug("Cookies设置完成")
        except Exception as e:
            logger.error(f"设置cookies失败: {e}")
    
    def clear_duplicate_cookies(self):
        """清理重复的cookies"""
        try:
            # 获取所有cookies
            cookies = list(self.session.cookies)
            
            # 按名称分组
            cookie_groups = {}
            for cookie in cookies:
                if cookie.name not in cookie_groups:
                    cookie_groups[cookie.name] = []
                cookie_groups[cookie.name].append(cookie)
            
            # 清理重复项，保留最新的
            for name, group in cookie_groups.items():
                if len(group) > 1:
                    # 按创建时间排序，保留最新的
                    group.sort(key=lambda x: getattr(x, 'expires', 0) or 0, reverse=True)
                    # 删除旧的cookies
                    for old_cookie in group[1:]:
                        self.session.cookies.clear(old_cookie.domain, old_cookie.path, old_cookie.name)
                        
            logger.debug("重复cookies清理完成")
        except Exception as e:
            logger.warning(f"清理cookies时出错: {e}")
    
    async def connect(self) -> bool:
        """连接到闲鱼平台"""
        try:
            # 首先进行认证
            if not self.authenticate():
                logger.error("闲鱼平台认证失败")
                return False
            
            # 获取token
            if not self.get_token():
                logger.error("获取闲鱼token失败")
                return False
            
            self.is_connected = True
            logger.info("闲鱼平台连接成功")
            return True
            
        except Exception as e:
            logger.error(f"连接闲鱼平台失败: {e}")
            return False
    
    async def disconnect(self):
        """断开闲鱼平台连接"""
        try:
            if self.websocket:
                await self.websocket.close()
                self.websocket = None
            
            self.is_connected = False
            logger.info("闲鱼平台连接已断开")
            
        except Exception as e:
            logger.error(f"断开闲鱼平台连接失败: {e}")
    
    def authenticate(self) -> bool:
        """闲鱼平台认证"""
        return self.has_login()
    
    def has_login(self, retry_count=0) -> bool:
        """检查登录状态"""
        if retry_count >= 2:
            logger.error("Login检查失败，重试次数过多")
            return False
            
        try:
            url = 'https://passport.goofish.com/newlogin/hasLogin.do'
            params = {
                'appName': 'xianyu',
                'fromSite': '77'
            }
            data = {
                'hid': self.session.cookies.get('unb', ''),
                'ltl': 'true',
                'appName': 'xianyu',
                'appEntrance': 'web',
                '_csrf_token': self.session.cookies.get('XSRF-TOKEN', ''),
                'umidToken': '',
                'hsiz': self.session.cookies.get('cookie2', ''),
                'bizParams': 'taobaoBizLoginFrom=web',
                'mainPage': 'false',
                'isMobile': 'false',
                'lang': 'zh_CN',
                'returnUrl': '',
                'fromSite': '77',
                'isIframe': 'true',
                'documentReferer': 'https://www.goofish.com/',
                'defaultView': 'hasLogin',
                'umidTag': 'SERVER',
                'deviceId': self.session.cookies.get('cna', '')
            }
            
            response = self.session.post(url, params=params, data=data)
            res_json = response.json()
            
            if res_json.get('content', {}).get('success'):
                logger.debug("Login成功")
                self.clear_duplicate_cookies()
                return True
            else:
                logger.warning(f"Login失败: {res_json}")
                time.sleep(0.5)
                return self.has_login(retry_count + 1)
                
        except Exception as e:
            logger.error(f"Login请求异常: {str(e)}")
            time.sleep(0.5)
            return self.has_login(retry_count + 1)

    def get_token(self) -> Optional[str]:
        """获取访问令牌"""
        # 使用已初始化的device_id
        return self._get_token_with_device_id(self.device_id)

    def _get_token_with_device_id(self, device_id: str, retry_count=0) -> Optional[str]:
        """使用设备ID获取token"""
        if retry_count >= 2:
            logger.warning("获取token失败，尝试重新登陆")
            if self.has_login():
                logger.info("重新登录成功，重新尝试获取token")
                return self._get_token_with_device_id(device_id, 0)
            else:
                logger.error("重新登录失败，Cookie已失效")
                return None

        params = {
            'jsv': '2.7.2',
            'appKey': '********',
            't': str(int(time.time()) * 1000),
            'sign': '',
            'v': '1.0',
            'type': 'originaljson',
            'accountSite': 'xianyu',
            'dataType': 'json',
            'timeout': '20000',
            'api': 'mtop.taobao.idlemessage.pc.login.token',
            'sessionOption': 'AutoLoginOnly',
            'spm_cnt': 'a21ybx.im.0.0',
        }
        data_val = '{"appKey":"444e9908a51d1cb236a27862abc769c9","deviceId":"' + device_id + '"}'
        data = {'data': data_val}

        # 获取token
        token = self.session.cookies.get('_m_h5_tk', '').split('_')[0]
        sign = generate_sign(params['t'], token, data_val)
        params['sign'] = sign

        try:
            response = self.session.post(
                'https://h5api.m.goofish.com/h5/mtop.taobao.idlemessage.pc.login.token/1.0/',
                params=params,
                data=data
            )
            res_json = response.json()

            if isinstance(res_json, dict):
                ret_value = res_json.get('ret', [])
                if not any('SUCCESS::调用成功' in ret for ret in ret_value):
                    logger.warning(f"Token API调用失败，错误信息: {ret_value}")
                    if 'Set-Cookie' in response.headers:
                        logger.debug("检测到Set-Cookie，更新cookie")
                        self.clear_duplicate_cookies()
                    time.sleep(0.5)
                    return self._get_token_with_device_id(device_id, retry_count + 1)
                else:
                    logger.info("Token获取成功")
                    # 提取accessToken字符串，与原版保持一致
                    if 'data' in res_json and 'accessToken' in res_json['data']:
                        access_token = res_json['data']['accessToken']
                        self.current_token = access_token  # 只保存accessToken字符串
                        self.last_token_refresh_time = time.time()
                        return access_token
                    else:
                        logger.error(f"Token响应格式异常: {res_json}")
                        return None
            else:
                logger.error(f"Token API返回格式异常: {res_json}")
                return self._get_token_with_device_id(device_id, retry_count + 1)

        except Exception as e:
            logger.error(f"Token API请求异常: {str(e)}")
            time.sleep(0.5)
            return self._get_token_with_device_id(device_id, retry_count + 1)

    def refresh_token(self) -> bool:
        """刷新访问令牌"""
        try:
            new_token = self.get_token()
            if new_token:
                self.current_token = new_token
                self.last_token_refresh_time = time.time()
                logger.info("Token刷新成功")
                return True
            else:
                logger.error("Token刷新失败")
                return False
        except Exception as e:
            logger.error(f"刷新token时出错: {e}")
            return False

    async def listen_messages(self, callback):
        """监听平台消息"""
        # 使用正确的WebSocket URL
        ws_url = 'wss://wss-goofish.dingtalk.com/'

        # 重连循环
        while True:
            try:
                # 构建连接头部
                headers = {
                    "Cookie": self.config.get('cookies_str', ''),
                    "Host": "wss-goofish.dingtalk.com",
                    "Connection": "Upgrade",
                    "Pragma": "no-cache",
                    "Cache-Control": "no-cache",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "Origin": "https://www.goofish.com",
                    "Accept-Encoding": "gzip, deflate, br, zstd",
                    "Accept-Language": "zh-CN,zh;q=0.9",
                }

                async with websockets.connect(ws_url, extra_headers=headers) as websocket:
                    self.websocket = websocket
                    logger.info("WebSocket连接已建立")

                    # 发送初始化消息
                    await self._send_init_message()

                    # 启动心跳任务
                    heartbeat_task = asyncio.create_task(self._heartbeat_loop())

                    try:
                        # 监听消息
                        async for message in websocket:
                            try:
                                # 🔍 调试日志：记录所有接收到的原始消息
                                logger.debug(f"🔍 [DEBUG] 收到WebSocket原始消息: {message[:200]}...")

                                raw_data = json.loads(message)

                                # 🔍 调试日志：记录解析后的数据结构
                                logger.debug(f"🔍 [DEBUG] 解析后的消息数据: {json.dumps(raw_data, ensure_ascii=False)[:300]}...")

                                # 处理心跳响应
                                if await self._handle_heartbeat_response(raw_data):
                                    logger.debug("🔍 [DEBUG] 心跳响应已处理，跳过")
                                    continue

                                # 发送ACK响应
                                await self._send_ack_response(raw_data)

                                # 🔍 调试日志：开始消息解析
                                logger.debug("🔍 [DEBUG] 开始解析消息...")
                                standard_message = self.parse_message(raw_data)

                                if standard_message:
                                    logger.info(f"🔍 [CONNECTOR] ✅ 成功解析用户消息: {standard_message.content}")
                                    logger.info(f"🔍 [CONNECTOR] 📞 准备调用消息处理回调")
                                    await callback(standard_message)
                                    logger.info(f"🔍 [CONNECTOR] ✅ 消息处理回调完成")
                                else:
                                    logger.debug("🔍 [CONNECTOR] 消息解析结果为None，可能不是用户消息")

                            except json.JSONDecodeError:
                                logger.warning(f"无法解析WebSocket消息: {message}")
                            except Exception as e:
                                logger.error(f"处理WebSocket消息时出错: {e}")

                    finally:
                        # 清理心跳任务
                        heartbeat_task.cancel()
                        try:
                            await heartbeat_task
                        except asyncio.CancelledError:
                            pass

            except websockets.exceptions.ConnectionClosed:
                logger.warning("WebSocket连接已关闭，5秒后重连...")
                await asyncio.sleep(5)
            except Exception as e:
                logger.error(f"WebSocket连接失败: {e}")
                logger.info("5秒后重试连接...")
                await asyncio.sleep(5)

    async def _send_init_message(self):
        """发送WebSocket初始化消息"""
        if not self.websocket:
            return

        try:
            # 确保有有效的token
            if not self.current_token:
                logger.error("无法获取有效token，初始化失败")
                raise Exception("Token获取失败")

            # 使用已初始化的用户ID和设备ID
            user_id = self.user_id
            device_id = self.device_id

            # 发送注册消息
            register_msg = {
                "lwp": "/reg",
                "headers": {
                    "cache-header": "app-key token ua wv",
                    "app-key": "444e9908a51d1cb236a27862abc769c9",
                    "token": self.current_token,  # 直接使用accessToken字符串
                    "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 DingTalk(2.1.5) OS(Windows/10) Browser(Chrome/*********) DingWeb/2.1.5 IMPaaS DingWeb/2.1.5",
                    "dt": "j",
                    "wv": "im:3,au:3,sy:6",
                    "sync": "0,0;0;0;",
                    "did": device_id,
                    "mid": generate_mid()
                }
            }

            await self.websocket.send(json.dumps(register_msg))
            logger.debug("WebSocket注册消息已发送")

            # 等待注册完成
            await asyncio.sleep(1)

            # 发送同步状态消息
            sync_msg = {
                "lwp": "/r/SyncStatus/ackDiff",
                "headers": {"mid": "5701741704675979 0"},
                "body": [{
                    "pipeline": "sync",
                    "tooLong2Tag": "PNM,1",
                    "channel": "sync",
                    "topic": "sync",
                    "highPts": 0,
                    "pts": int(time.time() * 1000) * 1000,
                    "seq": 0,
                    "timestamp": int(time.time() * 1000)
                }]
            }

            await self.websocket.send(json.dumps(sync_msg))
            logger.info('WebSocket连接注册完成')

        except Exception as e:
            logger.error(f"发送WebSocket初始化消息失败: {e}")
            raise

    async def _send_ack_response(self, message_data):
        """发送ACK响应"""
        try:
            if "headers" in message_data and "mid" in message_data["headers"]:
                ack = {
                    "code": 200,
                    "headers": {
                        "mid": message_data["headers"]["mid"],
                        "sid": message_data["headers"].get("sid", "")
                    }
                }
                # 复制其他可能的header字段
                for key in ["app-key", "ua", "dt"]:
                    if key in message_data["headers"]:
                        ack["headers"][key] = message_data["headers"][key]

                await self.websocket.send(json.dumps(ack))
                logger.debug("ACK响应已发送")
        except Exception as e:
            logger.debug(f"发送ACK响应失败: {e}")

    async def _heartbeat_loop(self):
        """心跳维护循环"""
        heartbeat_interval = 15  # 15秒心跳间隔
        last_heartbeat_time = time.time()

        while True:
            try:
                current_time = time.time()

                # 检查是否需要发送心跳
                if current_time - last_heartbeat_time >= heartbeat_interval:
                    await self._send_heartbeat()
                    last_heartbeat_time = current_time

                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"心跳循环出错: {e}")
                break

    async def _send_heartbeat(self):
        """发送心跳包"""
        try:
            heartbeat_msg = {
                "lwp": "/!",
                "headers": {
                    "mid": generate_mid()
                }
            }
            await self.websocket.send(json.dumps(heartbeat_msg))
            logger.debug("心跳包已发送")
        except Exception as e:
            logger.error(f"发送心跳包失败: {e}")

    async def _handle_heartbeat_response(self, message_data):
        """处理心跳响应"""
        try:
            if (
                isinstance(message_data, dict)
                and "headers" in message_data
                and "mid" in message_data["headers"]
                and "code" in message_data
                and message_data["code"] == 200
            ):
                logger.debug("收到心跳响应")
                return True
        except Exception as e:
            logger.debug(f"处理心跳响应出错: {e}")
        return False

    def parse_message(self, raw_message: Dict[str, Any]) -> Optional[StandardMessage]:
        """解析原始消息为标准格式"""
        try:
            # 🔍 调试日志：检查消息类型
            logger.debug(f"🔍 [PARSE] 开始解析消息，消息键: {list(raw_message.keys())}")

            # 检查是否为同步包消息
            if not self._is_sync_package(raw_message):
                logger.debug("🔍 [PARSE] 不是同步包消息，跳过")
                return None

            logger.debug("🔍 [PARSE] 确认为同步包消息，开始处理")

            # 获取并解密数据
            sync_data = raw_message["body"]["syncPushPackage"]["data"][0]
            logger.debug(f"🔍 [PARSE] 同步数据键: {list(sync_data.keys())}")

            # 检查是否有必要的字段
            if "data" not in sync_data:
                logger.debug("🔍 [PARSE] 同步包中无data字段")
                return None

            # 解密数据
            try:
                data = sync_data["data"]
                logger.debug(f"🔍 [PARSE] 原始数据长度: {len(data)}")

                try:
                    # 尝试直接解码
                    import base64
                    decoded_data = base64.b64decode(data).decode("utf-8")
                    message = json.loads(decoded_data)
                    logger.debug(f"🔍 [PARSE] 数据无需解密，直接解码成功，消息内容: {json.dumps(message, ensure_ascii=False)[:300]}...")
                    # 继续处理这些消息，不要跳过
                    # return None
                except Exception as e:
                    # 需要解密
                    logger.debug(f"🔍 [PARSE] 需要解密数据，解码失败: {e}")
                    from utils.xianyu_utils import decrypt
                    decrypted_data = decrypt(data)
                    message = json.loads(decrypted_data)
                    logger.debug(f"🔍 [PARSE] 解密成功，消息键: {list(message.keys())}")
            except Exception as e:
                logger.error(f"🔍 [PARSE] 消息解密失败: {e}")
                return None

            # 判断消息类型
            logger.debug(f"🔍 [PARSE] 开始判断消息类型，消息结构: {json.dumps(message, ensure_ascii=False)[:500]}...")

            if self._is_typing_status(message):
                logger.debug("🔍 [PARSE] 识别为输入状态消息，忽略")
                return None  # 忽略输入状态消息

            if not self._is_chat_message(message):
                logger.debug("🔍 [PARSE] 不是聊天消息，忽略")
                return None  # 忽略非聊天消息

            logger.info("🔍 [PARSE] ✅ 确认为用户聊天消息，开始提取信息")

            # 提取消息信息
            create_time = int(message["1"]["5"])
            send_user_name = message["1"]["10"]["reminderTitle"]
            send_user_id = message["1"]["10"]["senderUserId"]
            send_message = message["1"]["10"]["reminderContent"]

            # 时效性验证
            if (time.time() * 1000 - create_time) > self.message_expire_time:
                return None  # 过期消息

            # 获取商品ID和会话ID
            url_info = message["1"]["10"]["reminderUrl"]
            item_id = url_info.split("itemId=")[1].split("&")[0] if "itemId=" in url_info else ""
            chat_id = message["1"]["2"].split('@')[0]

            if not item_id or not chat_id:
                return None

            return StandardMessage(
                message_id=generate_uuid(),
                chat_id=chat_id,
                user_id=send_user_id,
                item_id=item_id,
                content=send_message,
                message_type=MessageType.USER_MESSAGE,
                timestamp=create_time,
                sender_name=send_user_name,
                platform_data=raw_message
            )

        except Exception as e:
            logger.error(f"解析消息时出错: {e}")
            return None

    def _is_sync_package(self, message_data):
        """判断是否为同步包消息"""
        try:
            return (
                isinstance(message_data, dict)
                and "body" in message_data
                and "syncPushPackage" in message_data["body"]
                and "data" in message_data["body"]["syncPushPackage"]
                and len(message_data["body"]["syncPushPackage"]["data"]) > 0
            )
        except Exception:
            return False

    def _is_chat_message(self, message):
        """判断是否为用户聊天消息"""
        try:
            return (
                isinstance(message, dict)
                and "1" in message
                and isinstance(message["1"], dict)
                and "10" in message["1"]
                and isinstance(message["1"]["10"], dict)
                and "reminderContent" in message["1"]["10"]
            )
        except Exception:
            return False

    def _is_typing_status(self, message):
        """判断是否为用户正在输入状态消息"""
        try:
            return (
                isinstance(message, dict)
                and "1" in message
                and isinstance(message["1"], list)
                and len(message["1"]) > 0
                and isinstance(message["1"][0], dict)
                and "1" in message["1"][0]
                and isinstance(message["1"][0]["1"], str)
                and "@goofish" in message["1"][0]["1"]
            )
        except Exception:
            return False

    def get_item_info(self, item_id: str) -> Optional[ItemInfo]:
        """获取商品信息"""
        raw_data = self._get_raw_item_info(item_id)
        if not raw_data or "error" in raw_data:
            return None

        return self._parse_item_info(raw_data)

    def _get_raw_item_info(self, item_id: str, retry_count=0) -> Optional[Dict[str, Any]]:
        """获取原始商品信息"""
        if retry_count >= 3:
            logger.error("获取商品信息失败，重试次数过多")
            return {"error": "获取商品信息失败，重试次数过多"}

        params = {
            'jsv': '2.7.2',
            'appKey': '********',
            't': str(int(time.time()) * 1000),
            'sign': '',
            'v': '1.0',
            'type': 'originaljson',
            'accountSite': 'xianyu',
            'dataType': 'json',
            'timeout': '20000',
            'api': 'mtop.taobao.idle.pc.detail',
            'sessionOption': 'AutoLoginOnly',
            'spm_cnt': 'a21ybx.im.0.0',
        }

        data_val = '{"itemId":"' + item_id + '"}'
        data = {'data': data_val}

        # 获取token
        token = self.session.cookies.get('_m_h5_tk', '').split('_')[0]
        sign = generate_sign(params['t'], token, data_val)
        params['sign'] = sign

        try:
            response = self.session.post(
                'https://h5api.m.goofish.com/h5/mtop.taobao.idle.pc.detail/1.0/',
                params=params,
                data=data
            )

            res_json = response.json()
            if isinstance(res_json, dict):
                ret_value = res_json.get('ret', [])
                if not any('SUCCESS::调用成功' in ret for ret in ret_value):
                    logger.warning(f"商品信息API调用失败，错误信息: {ret_value}")
                    if 'Set-Cookie' in response.headers:
                        logger.debug("检测到Set-Cookie，更新cookie")
                        self.clear_duplicate_cookies()
                    time.sleep(0.5)
                    return self._get_raw_item_info(item_id, retry_count + 1)
                else:
                    logger.debug(f"商品信息获取成功: {item_id}")
                    return res_json
            else:
                logger.error(f"商品信息API返回格式异常: {res_json}")
                return self._get_raw_item_info(item_id, retry_count + 1)

        except Exception as e:
            logger.error(f"商品信息API请求异常: {str(e)}")
            time.sleep(0.5)
            return self._get_raw_item_info(item_id, retry_count + 1)

    def _parse_item_info(self, raw_data: Dict[str, Any]) -> Optional[ItemInfo]:
        """解析商品信息"""
        try:
            data = raw_data.get('data', {})
            item_do = data.get('itemDO', {})

            # 提取图片信息
            images = []
            image_infos = item_do.get('imageInfos', [])
            for img_info in image_infos:
                if isinstance(img_info, dict) and 'url' in img_info:
                    images.append(img_info['url'])

            return ItemInfo(
                item_id=item_do.get('itemId', ''),
                title=item_do.get('title', ''),
                price=float(item_do.get('soldPrice', 0)),
                description=item_do.get('desc', ''),
                images=images,
                seller_id=data.get('sellerDO', {}).get('sellerId', ''),
                platform_data=raw_data
            )

        except Exception as e:
            logger.error(f"解析商品信息时出错: {e}")
            return None

    async def send_message(self, chat_id: str, content: str) -> bool:
        """发送消息"""
        try:
            if not self.websocket:
                logger.error("WebSocket连接未建立")
                return False

            # 构建消息内容
            import base64
            text_content = {
                "contentType": 1,
                "text": {
                    "text": content
                }
            }
            text_base64 = str(base64.b64encode(json.dumps(text_content).encode('utf-8')), 'utf-8')

            # 获取用户ID
            user_id = self.session.cookies.get('unb', 'default_user')

            # 构建发送消息
            msg = {
                "lwp": "/r/MessageSend/sendByReceiverScope",
                "headers": {
                    "mid": generate_mid()
                },
                "body": [
                    {
                        "uuid": generate_uuid(),
                        "cid": f"{chat_id}@goofish",
                        "conversationType": 1,
                        "content": {
                            "contentType": 101,
                            "custom": {
                                "type": 1,
                                "data": text_base64
                            }
                        },
                        "redPointPolicy": 0,
                        "extension": {
                            "extJson": "{}"
                        },
                        "ctx": {
                            "appVersion": "1.0",
                            "platform": "web"
                        },
                        "mtags": {},
                        "msgReadStatusSetting": 1
                    },
                    {
                        "actualReceivers": [
                            f"{chat_id}@goofish",  # 接收者
                            f"{user_id}@goofish"   # 发送者（自己）
                        ]
                    }
                ]
            }

            # 发送消息
            await self.websocket.send(json.dumps(msg))
            logger.info(f"消息已发送到会话 {chat_id}: {content}")
            return True

        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return False

    def is_chat_message(self, raw_message: Dict[str, Any]) -> bool:
        """判断是否为聊天消息"""
        try:
            # 检查消息结构
            if "1" not in raw_message:
                return False

            message_data = raw_message["1"]
            if "10" not in message_data:
                return False

            reminder_data = message_data["10"]

            # 检查是否有必要的字段
            required_fields = ["reminderTitle", "senderUserId", "reminderContent", "reminderUrl"]
            for field in required_fields:
                if field not in reminder_data:
                    return False

            # 检查URL是否包含itemId
            url_info = reminder_data.get("reminderUrl", "")
            if "itemId=" not in url_info:
                return False

            return True

        except Exception as e:
            logger.debug(f"判断聊天消息时出错: {e}")
            return False

    def is_system_message(self, raw_message: Dict[str, Any]) -> bool:
        """判断是否为系统消息"""
        try:
            # 根据闲鱼平台的消息格式判断系统消息
            message_data = raw_message.get("1", {})
            reminder_data = message_data.get("10", {})

            # 系统消息通常没有senderUserId或者有特殊标识
            sender_id = reminder_data.get("senderUserId", "")
            if not sender_id or sender_id == "system":
                return True

            # 检查消息内容是否为系统通知
            content = reminder_data.get("reminderContent", "")
            system_keywords = ["系统通知", "订单状态", "支付成功", "发货通知"]
            for keyword in system_keywords:
                if keyword in content:
                    return True

            return False

        except Exception as e:
            logger.debug(f"判断系统消息时出错: {e}")
            return False

    def is_typing_status(self, raw_message: Dict[str, Any]) -> bool:
        """判断是否为输入状态消息"""
        try:
            # 根据闲鱼平台的消息格式判断输入状态
            message_data = raw_message.get("1", {})

            # 输入状态消息通常有特殊的类型标识
            message_type = message_data.get("type", "")
            if message_type == "typing":
                return True

            # 检查是否包含输入状态相关的字段
            if "typingStatus" in message_data:
                return True

            return False

        except Exception as e:
            logger.debug(f"判断输入状态时出错: {e}")
            return False

    def _validate_config(self) -> bool:
        """验证配置是否有效"""
        required_keys = ['cookies_str']
        for key in required_keys:
            if not self.config.get(key):
                logger.error(f"缺少必需的配置项: {key}")
                return False
        return True
