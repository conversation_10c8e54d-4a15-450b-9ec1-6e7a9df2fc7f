"""
人工介入模块

专门处理需要人工干预的场景和后续操作
"""

import asyncio
import json
import time
from typing import Dict, Any, List, Optional
from enum import Enum
from loguru import logger
import requests

from modules.platform_connector.base_connector import StandardMessage
from config.settings import settings


class HandoverReason(Enum):
    """人工介入原因"""
    USER_REQUEST = "user_request"  # 用户主动要求
    COMPLAINT = "complaint"  # 投诉纠纷
    COMPLEX_ISSUE = "complex_issue"  # 复杂问题
    SYSTEM_ERROR = "system_error"  # 系统错误
    REPEATED_CONFUSION = "repeated_confusion"  # 重复困惑


class HandoverStatus(Enum):
    """介入状态"""
    PENDING = "pending"  # 等待中
    NOTIFIED = "notified"  # 已通知
    ASSIGNED = "assigned"  # 已分配
    RESOLVED = "resolved"  # 已解决
    CANCELLED = "cancelled"  # 已取消


class HumanHandoverManager:
    """人工介入管理器"""
    
    def __init__(self):
        """初始化人工介入管理器"""
        # 获取通知配置
        self.notification_config = settings.get_notification_config()
        self.enabled = self.notification_config.get('enabled', False)
        
        # 活跃的介入会话
        self.active_handovers: Dict[str, Dict[str, Any]] = {}
        
        # 统计信息
        self.stats = {
            'total_handovers': 0,
            'reason_distribution': {},
            'notification_success': 0,
            'notification_failures': 0,
            'average_response_time': 0
        }
        
        logger.info(f"人工介入管理器初始化完成，通知功能: {'启用' if self.enabled else '禁用'}")
    
    async def request_human_handover(
        self, 
        message: StandardMessage, 
        context: Dict[str, Any], 
        reason: HandoverReason,
        priority: str = "normal"
    ) -> str:
        """
        请求人工介入
        
        Args:
            message: 触发介入的消息
            context: 上下文信息
            reason: 介入原因
            priority: 优先级 (low, normal, high, urgent)
            
        Returns:
            str: 介入请求ID
        """
        handover_id = self._generate_handover_id(message.chat_id)
        
        # 创建介入记录
        handover_record = {
            'id': handover_id,
            'chat_id': message.chat_id,
            'user_id': message.user_id,
            'item_id': message.item_id,
            'message': message,
            'context': context,
            'reason': reason,
            'priority': priority,
            'status': HandoverStatus.PENDING,
            'created_at': time.time(),
            'updated_at': time.time(),
            'assigned_agent': None,
            'notes': []
        }
        
        # 存储介入记录
        self.active_handovers[handover_id] = handover_record
        
        # 更新统计
        self.stats['total_handovers'] += 1
        self.stats['reason_distribution'][reason.value] = (
            self.stats['reason_distribution'].get(reason.value, 0) + 1
        )
        
        # 发送通知
        if self.enabled:
            await self._send_notifications(handover_record)
        
        logger.info(f"人工介入请求已创建: {handover_id}, 原因: {reason.value}")
        return handover_id
    
    async def _send_notifications(self, handover_record: Dict[str, Any]):
        """
        发送通知
        
        Args:
            handover_record: 介入记录
        """
        try:
            # 构建通知内容
            notification_content = self._build_notification_content(handover_record)
            
            # 发送钉钉通知
            if self.notification_config.get('dingtalk_webhook'):
                await self._send_dingtalk_notification(
                    self.notification_config['dingtalk_webhook'],
                    notification_content
                )
            
            # 发送微信通知
            if self.notification_config.get('wechat_webhook'):
                await self._send_wechat_notification(
                    self.notification_config['wechat_webhook'],
                    notification_content
                )
            
            # 更新状态
            handover_record['status'] = HandoverStatus.NOTIFIED
            handover_record['updated_at'] = time.time()
            
            self.stats['notification_success'] += 1
            logger.info(f"通知发送成功: {handover_record['id']}")
            
        except Exception as e:
            logger.error(f"发送通知失败: {e}")
            self.stats['notification_failures'] += 1
    
    def _build_notification_content(self, handover_record: Dict[str, Any]) -> Dict[str, Any]:
        """
        构建通知内容
        
        Args:
            handover_record: 介入记录
            
        Returns:
            Dict: 通知内容
        """
        message = handover_record['message']
        context = handover_record['context']
        item_info = context.get('item_info', {})
        
        return {
            'title': '🚨 闲鱼客服 - 人工介入请求',
            'handover_id': handover_record['id'],
            'chat_id': handover_record['chat_id'],
            'user_name': message.sender_name,
            'user_message': message.content,
            'item_title': item_info.get('title', '未知商品'),
            'item_price': item_info.get('price', '未知价格'),
            'reason': handover_record['reason'].value,
            'priority': handover_record['priority'],
            'created_time': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(handover_record['created_at'])),
            'chat_history_count': len(context.get('chat_history', [])),
            'bargain_count': context.get('bargain_count', 0)
        }
    
    async def _send_dingtalk_notification(self, webhook_url: str, content: Dict[str, Any]):
        """
        发送钉钉通知
        
        Args:
            webhook_url: 钉钉webhook地址
            content: 通知内容
        """
        try:
            # 构建钉钉消息格式
            dingtalk_msg = {
                "msgtype": "markdown",
                "markdown": {
                    "title": content['title'],
                    "text": f"""## {content['title']}
                    
**介入ID**: {content['handover_id']}
**会话ID**: {content['chat_id']}
**用户**: {content['user_name']}
**商品**: {content['item_title']} ({content['item_price']}元)
**原因**: {content['reason']}
**优先级**: {content['priority']}
**时间**: {content['created_time']}

**用户消息**: {content['user_message']}

**上下文信息**:
- 对话历史: {content['chat_history_count']}条
- 议价次数: {content['bargain_count']}次

请及时处理！"""
                }
            }
            
            # 发送请求
            response = requests.post(
                webhook_url,
                json=dingtalk_msg,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.debug("钉钉通知发送成功")
            else:
                logger.warning(f"钉钉通知发送失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"发送钉钉通知时出错: {e}")
    
    async def _send_wechat_notification(self, webhook_url: str, content: Dict[str, Any]):
        """
        发送微信通知
        
        Args:
            webhook_url: 微信webhook地址
            content: 通知内容
        """
        try:
            # 构建微信消息格式
            wechat_msg = {
                "msgtype": "text",
                "text": {
                    "content": f"""{content['title']}

介入ID: {content['handover_id']}
用户: {content['user_name']}
商品: {content['item_title']}
原因: {content['reason']}
优先级: {content['priority']}
时间: {content['created_time']}

用户消息: {content['user_message']}

请及时处理！"""
                }
            }
            
            # 发送请求
            response = requests.post(
                webhook_url,
                json=wechat_msg,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.debug("微信通知发送成功")
            else:
                logger.warning(f"微信通知发送失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"发送微信通知时出错: {e}")
    
    def assign_agent(self, handover_id: str, agent_name: str) -> bool:
        """
        分配客服人员
        
        Args:
            handover_id: 介入ID
            agent_name: 客服人员名称
            
        Returns:
            bool: 分配是否成功
        """
        if handover_id not in self.active_handovers:
            logger.warning(f"介入记录不存在: {handover_id}")
            return False
        
        handover_record = self.active_handovers[handover_id]
        handover_record['assigned_agent'] = agent_name
        handover_record['status'] = HandoverStatus.ASSIGNED
        handover_record['updated_at'] = time.time()
        
        logger.info(f"介入已分配给 {agent_name}: {handover_id}")
        return True
    
    def resolve_handover(self, handover_id: str, resolution_note: str = "") -> bool:
        """
        解决介入问题
        
        Args:
            handover_id: 介入ID
            resolution_note: 解决说明
            
        Returns:
            bool: 解决是否成功
        """
        if handover_id not in self.active_handovers:
            logger.warning(f"介入记录不存在: {handover_id}")
            return False
        
        handover_record = self.active_handovers[handover_id]
        handover_record['status'] = HandoverStatus.RESOLVED
        handover_record['updated_at'] = time.time()
        handover_record['resolution_note'] = resolution_note
        
        # 计算响应时间
        response_time = handover_record['updated_at'] - handover_record['created_at']
        self._update_average_response_time(response_time)
        
        # 移除活跃记录
        del self.active_handovers[handover_id]
        
        logger.info(f"介入已解决: {handover_id}, 响应时间: {response_time:.2f}秒")
        return True
    
    def get_active_handovers(self) -> List[Dict[str, Any]]:
        """
        获取活跃的介入列表
        
        Returns:
            List: 活跃介入列表
        """
        return list(self.active_handovers.values())
    
    def get_handover_by_chat(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """
        根据会话ID获取介入记录
        
        Args:
            chat_id: 会话ID
            
        Returns:
            Dict: 介入记录，如果不存在返回None
        """
        for handover in self.active_handovers.values():
            if handover['chat_id'] == chat_id:
                return handover
        return None
    
    def _generate_handover_id(self, chat_id: str) -> str:
        """
        生成介入ID
        
        Args:
            chat_id: 会话ID
            
        Returns:
            str: 介入ID
        """
        timestamp = int(time.time())
        return f"HO_{chat_id}_{timestamp}"
    
    def _update_average_response_time(self, response_time: float):
        """
        更新平均响应时间
        
        Args:
            response_time: 响应时间（秒）
        """
        current_avg = self.stats['average_response_time']
        total_resolved = sum(1 for status in self.stats['reason_distribution'].values())
        
        if total_resolved == 0:
            self.stats['average_response_time'] = response_time
        else:
            self.stats['average_response_time'] = (
                (current_avg * (total_resolved - 1) + response_time) / total_resolved
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict: 统计信息
        """
        return {
            **self.stats,
            'active_handovers_count': len(self.active_handovers),
            'notification_enabled': self.enabled,
            'success_rate': (
                self.stats['notification_success'] / 
                max(self.stats['notification_success'] + self.stats['notification_failures'], 1)
            ) * 100
        }
