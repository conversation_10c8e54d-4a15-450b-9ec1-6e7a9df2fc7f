"""
上下文管理模块 - 数据库处理器

负责所有数据的持久化存储和访问，围绕会话ID(cid)进行优化
"""

import sqlite3
import os
import json
from datetime import datetime
from typing import Dict, Any, List, Optional
from loguru import logger

from config.settings import settings


class ContextManager:
    """
    聊天上下文管理器
    
    负责存储和检索用户与商品之间的对话历史，使用SQLite数据库进行持久化存储。
    支持按会话ID检索对话历史，以及议价次数统计。
    """
    
    def __init__(self, db_path: Optional[str] = None, max_history: int = 100):
        """
        初始化聊天上下文管理器
        
        Args:
            db_path: SQLite数据库文件路径
            max_history: 每个对话保留的最大消息数
        """
        self.db_path = db_path or settings.get('db_path', 'data/chat_history.db')
        self.max_history = max_history or settings.get('max_history', 100)
        self._init_db()
        logger.info(f"上下文管理器初始化完成，数据库路径: {self.db_path}")
        
    def _init_db(self):
        """初始化数据库表结构"""
        # 确保数据库目录存在
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
            
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 创建消息表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                item_id TEXT NOT NULL,
                role TEXT NOT NULL,
                content TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                chat_id TEXT
            )
            ''')
            
            # 检查是否需要添加chat_id字段（兼容旧数据库）
            cursor.execute("PRAGMA table_info(messages)")
            columns = [column[1] for column in cursor.fetchall()]
            if 'chat_id' not in columns:
                cursor.execute('ALTER TABLE messages ADD COLUMN chat_id TEXT')
                logger.info("已为messages表添加chat_id字段")
            
            # 创建商品信息表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS items (
                item_id TEXT PRIMARY KEY,
                data TEXT NOT NULL,
                price REAL,
                description TEXT,
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # 创建议价次数表（基于chat_id）
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS chat_bargain_counts (
                chat_id TEXT PRIMARY KEY,
                count INTEGER DEFAULT 0,
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # 创建解析后的商品信息表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS parsed_items (
                item_id TEXT PRIMARY KEY,
                title TEXT,
                description TEXT,
                price REAL,
                original_price REAL,
                transport_fee REAL,
                create_time TEXT,
                item_status TEXT,
                browse_count INTEGER DEFAULT 0,
                collect_count INTEGER DEFAULT 0,
                want_count INTEGER DEFAULT 0,
                favor_count INTEGER DEFAULT 0,
                images TEXT,
                main_image TEXT,
                properties TEXT,
                seller_info TEXT,
                support_trade BOOLEAN DEFAULT FALSE,
                bargained BOOLEAN DEFAULT FALSE,
                parsed_at TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # 创建卖家信息表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS sellers (
                seller_id TEXT PRIMARY KEY,
                nickname TEXT,
                city TEXT,
                avatar TEXT,
                last_visit TEXT,
                reply_ratio_24h TEXT,
                reply_interval TEXT,
                sold_items_count INTEGER DEFAULT 0,
                total_items_count INTEGER DEFAULT 0,
                register_days INTEGER DEFAULT 0,
                zhima_auth BOOLEAN DEFAULT FALSE,
                credit_level TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # 创建索引以提高查询性能
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_messages_chat_id ON messages(chat_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_items_item_id ON items(item_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_parsed_items_item_id ON parsed_items(item_id)')
            
            conn.commit()
            logger.debug("数据库表结构初始化完成")
            
        except Exception as e:
            logger.error(f"初始化数据库时出错: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def add_message_by_chat(self, chat_id: str, user_id: str, item_id: str, role: str, content: str):
        """
        基于会话ID添加新消息到对话历史
        
        Args:
            chat_id: 会话ID
            user_id: 用户ID (用户消息存真实user_id，助手消息存卖家ID)
            item_id: 商品ID
            role: 消息角色 (user/assistant)
            content: 消息内容
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 插入新消息，使用chat_id作为额外标识
            cursor.execute(
                "INSERT INTO messages (user_id, item_id, role, content, timestamp, chat_id) VALUES (?, ?, ?, ?, ?, ?)",
                (user_id, item_id, role, content, datetime.now().isoformat(), chat_id)
            )
            
            # 检查是否需要清理旧消息（基于chat_id）
            cursor.execute(
                """
                SELECT id FROM messages 
                WHERE chat_id = ? 
                ORDER BY timestamp DESC 
                LIMIT ?, 1
                """, 
                (chat_id, self.max_history)
            )
            
            oldest_to_keep = cursor.fetchone()
            if oldest_to_keep:
                cursor.execute(
                    "DELETE FROM messages WHERE chat_id = ? AND id < ?",
                    (chat_id, oldest_to_keep[0])
                )
            
            conn.commit()
            logger.debug(f"消息已添加到会话 {chat_id}")
            
        except Exception as e:
            logger.error(f"添加消息到数据库时出错: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def get_context_by_chat(self, chat_id: str) -> List[Dict[str, str]]:
        """
        基于会话ID获取对话历史
        
        Args:
            chat_id: 会话ID
            
        Returns:
            list: 包含对话历史的列表
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                """
                SELECT role, content FROM messages 
                WHERE chat_id = ? 
                ORDER BY timestamp ASC
                LIMIT ?
                """, 
                (chat_id, self.max_history)
            )
            
            messages = [{"role": role, "content": content} for role, content in cursor.fetchall()]
            
            # 获取议价次数并添加到上下文中
            bargain_count = self.get_bargain_count_by_chat(chat_id)
            if bargain_count > 0:
                messages.append({
                    "role": "system", 
                    "content": f"议价次数: {bargain_count}"
                })
            
            return messages
            
        except Exception as e:
            logger.error(f"获取对话历史时出错: {e}")
            return []
        finally:
            conn.close()
    
    def get_bargain_count_by_chat(self, chat_id: str) -> int:
        """
        基于会话ID获取议价次数
        
        Args:
            chat_id: 会话ID
            
        Returns:
            int: 议价次数
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "SELECT count FROM chat_bargain_counts WHERE chat_id = ?",
                (chat_id,)
            )
            
            result = cursor.fetchone()
            return result[0] if result else 0
            
        except Exception as e:
            logger.error(f"获取议价次数时出错: {e}")
            return 0
        finally:
            conn.close()
    
    def increment_bargain_count_by_chat(self, chat_id: str):
        """
        基于会话ID增加议价次数
        
        Args:
            chat_id: 会话ID
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 使用UPSERT语法直接基于chat_id增加议价次数
            cursor.execute(
                """
                INSERT INTO chat_bargain_counts (chat_id, count, last_updated)
                VALUES (?, 1, ?)
                ON CONFLICT(chat_id) 
                DO UPDATE SET count = count + 1, last_updated = ?
                """,
                (chat_id, datetime.now().isoformat(), datetime.now().isoformat())
            )
            
            conn.commit()
            logger.debug(f"会话 {chat_id} 议价次数已增加")
            
        except Exception as e:
            logger.error(f"增加议价次数时出错: {e}")
            conn.rollback()
        finally:
            conn.close()

    def save_item_info(self, item_id: str, item_data: Dict[str, Any]):
        """
        保存商品信息到数据库

        Args:
            item_id: 商品ID
            item_data: 商品数据
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 从商品数据中提取有用信息
            price = float(item_data.get('soldPrice', 0))
            description = item_data.get('desc', '')

            # 将整个商品数据转换为JSON字符串
            data_json = json.dumps(item_data, ensure_ascii=False)

            cursor.execute(
                """
                INSERT INTO items (item_id, data, price, description, last_updated)
                VALUES (?, ?, ?, ?, ?)
                ON CONFLICT(item_id)
                DO UPDATE SET data = ?, price = ?, description = ?, last_updated = ?
                """,
                (
                    item_id, data_json, price, description, datetime.now().isoformat(),
                    data_json, price, description, datetime.now().isoformat()
                )
            )

            conn.commit()
            logger.debug(f"商品信息已保存: {item_id}")

        except Exception as e:
            logger.error(f"保存商品信息时出错: {e}")
            conn.rollback()
        finally:
            conn.close()

    def get_item_info(self, item_id: str) -> Optional[Dict[str, Any]]:
        """
        从数据库获取商品信息

        Args:
            item_id: 商品ID

        Returns:
            dict: 商品信息字典，如果不存在返回None
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute(
                "SELECT data FROM items WHERE item_id = ?",
                (item_id,)
            )

            result = cursor.fetchone()
            if result:
                return json.loads(result[0])
            return None

        except Exception as e:
            logger.error(f"获取商品信息时出错: {e}")
            return None
        finally:
            conn.close()

    def save_parsed_item_info(self, parsed_item: Dict[str, Any]) -> bool:
        """
        保存解析后的商品信息

        Args:
            parsed_item: 解析后的商品信息

        Returns:
            bool: 保存是否成功
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 将复杂字段转换为JSON字符串
            images_json = json.dumps(parsed_item.get('images', []), ensure_ascii=False)
            properties_json = json.dumps(parsed_item.get('properties', {}), ensure_ascii=False)
            seller_info_json = json.dumps(parsed_item.get('seller', {}), ensure_ascii=False)

            cursor.execute(
                """
                INSERT OR REPLACE INTO parsed_items (
                    item_id, title, description, price, original_price, transport_fee,
                    create_time, item_status, browse_count, collect_count, want_count, favor_count,
                    images, main_image, properties, seller_info, support_trade, bargained,
                    parsed_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """,
                (
                    parsed_item.get('item_id'),
                    parsed_item.get('title', ''),
                    parsed_item.get('description', ''),
                    parsed_item.get('price', 0),
                    parsed_item.get('original_price', 0),
                    parsed_item.get('transport_fee', 0),
                    parsed_item.get('create_time', ''),
                    parsed_item.get('item_status', ''),
                    parsed_item.get('browse_count', 0),
                    parsed_item.get('collect_count', 0),
                    parsed_item.get('want_count', 0),
                    parsed_item.get('favor_count', 0),
                    images_json,
                    parsed_item.get('main_image', ''),
                    properties_json,
                    seller_info_json,
                    parsed_item.get('support_trade', False),
                    parsed_item.get('bargained', False),
                    parsed_item.get('parsed_at', ''),
                    datetime.now().isoformat()
                )
            )

            conn.commit()
            logger.debug(f"解析后的商品信息已保存: {parsed_item.get('item_id')}")
            return True

        except Exception as e:
            logger.error(f"保存解析后的商品信息时出错: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_parsed_item_info(self, item_id: str) -> Optional[Dict[str, Any]]:
        """
        获取解析后的商品信息

        Args:
            item_id: 商品ID

        Returns:
            dict: 解析后的商品信息，如果不存在返回None
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute(
                """
                SELECT * FROM parsed_items WHERE item_id = ?
                """,
                (item_id,)
            )

            result = cursor.fetchone()
            if result:
                # 将查询结果转换为字典
                columns = [description[0] for description in cursor.description]
                item_dict = dict(zip(columns, result))

                # 解析JSON字段
                if item_dict.get('images'):
                    item_dict['images'] = json.loads(item_dict['images'])
                if item_dict.get('properties'):
                    item_dict['properties'] = json.loads(item_dict['properties'])
                if item_dict.get('seller_info'):
                    item_dict['seller'] = json.loads(item_dict['seller_info'])
                    del item_dict['seller_info']  # 移除原字段

                return item_dict
            return None

        except Exception as e:
            logger.error(f"获取解析后的商品信息时出错: {e}")
            return None
        finally:
            conn.close()
