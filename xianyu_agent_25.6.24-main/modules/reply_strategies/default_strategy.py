"""
默认通用策略

处理通用的客服场景和其他未分类的对话
"""

import os
from typing import Dict, Any, Optional
from loguru import logger

from .base_strategy import BaseStrategy


class DefaultStrategy(BaseStrategy):
    """默认通用策略"""
    
    def __init__(self):
        super().__init__("default_strategy")
    
    def _load_system_prompt(self) -> str:
        """加载默认策略的系统提示词"""
        try:
            # 尝试从文件加载
            prompt_file = "prompts/default_prompt.txt"
            if os.path.exists(prompt_file):
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    return f.read().strip()
        except Exception as e:
            logger.warning(f"加载默认提示词文件失败: {e}")
        
        # 默认提示词
        return """你是一个友好、专业的闲鱼卖家，致力于为买家提供优质的购物体验。你的目标是解答买家的各种问题，促成交易的同时建立良好的买卖关系。

## 核心原则
1. 保持友好、耐心的服务态度
2. 提供准确、有用的信息
3. 积极解决买家的疑虑和问题
4. 适时引导买家完成购买

## 服务范围
- 商品基本信息介绍
- 购买流程指导
- 物流配送说明
- 售后服务保障
- 支付方式说明
- 其他通用客服问题

## 回复风格
- 使用亲切的称呼（亲、朋友等）
- 语言简洁明了，易于理解
- 适当使用表情符号增加亲和力
- 体现专业性和可信度

## 常见场景处理
- 商品咨询：详细介绍商品特点和优势
- 物流询问：说明发货时间和配送方式
- 售后问题：提供明确的售后保障
- 支付疑问：解释支付流程和安全性
- 其他问题：耐心解答并提供帮助

## 注意事项
- 如果遇到不确定的问题，诚实说明并承诺后续跟进
- 避免过度承诺无法兑现的服务
- 保持积极正面的沟通态度
- 适时提醒买家关注商品的性价比

请根据买家的问题，提供专业、友好的回复。"""
    
    def can_handle(self, intent: str, context: Dict[str, Any]) -> bool:
        """默认策略可以处理所有意图"""
        return True  # 默认策略作为兜底，可以处理所有意图
    
    def generate_reply(
        self, 
        user_msg: str, 
        item_desc: str, 
        context: Dict[str, Any],
        parameters: Optional[Dict[str, Any]] = None
    ) -> str:
        """生成通用客服回复"""
        try:
            # 分析服务参数
            service_analysis = self._analyze_service_parameters(parameters or {})
            
            # 构建消息
            messages = self._build_messages(user_msg, item_desc, context, parameters)
            
            # 添加服务分析到系统消息
            if service_analysis:
                messages[0]['content'] += f"\n▲服务重点：{service_analysis}"
            
            # 调用LLM生成回复
            response = self._call_llm(messages, temperature=0.7)
            
            return self._safe_filter(response)
            
        except Exception as e:
            logger.error(f"默认策略生成回复时出错: {e}")
            return "感谢您的咨询！我会尽快为您处理，请稍等片刻。如有紧急问题，请随时联系我。"
    
    def _analyze_service_parameters(self, parameters: Dict[str, Any]) -> str:
        """
        分析服务相关参数
        
        Args:
            parameters: 意图参数
            
        Returns:
            str: 分析结果
        """
        analysis_parts = []
        
        # 分析服务类型
        if 'service_type' in parameters:
            service_type = parameters['service_type']
            analysis_parts.append(f"服务类型: {service_type}")
        
        # 分析问题类别
        if 'question_category' in parameters:
            category = parameters['question_category']
            analysis_parts.append(f"问题类别: {category}")
        
        # 分析紧急程度
        if 'urgency' in parameters:
            urgency = parameters['urgency']
            analysis_parts.append(f"紧急程度: {urgency}")
        
        # 分析情感倾向
        if 'sentiment' in parameters:
            sentiment = parameters['sentiment']
            analysis_parts.append(f"情感倾向: {sentiment}")
        
        return "; ".join(analysis_parts) if analysis_parts else ""
    
    def _detect_question_type(self, user_msg: str) -> str:
        """
        检测问题类型
        
        Args:
            user_msg: 用户消息
            
        Returns:
            str: 问题类型
        """
        question_patterns = {
            'shipping': ['发货', '物流', '快递', '配送', '邮费', '运费'],
            'payment': ['支付', '付款', '转账', '支付宝', '微信', '银行卡'],
            'return': ['退货', '退款', '换货', '售后', '质量问题'],
            'product_info': ['介绍', '详情', '参数', '规格', '功能', '特点'],
            'availability': ['有货', '现货', '库存', '还有吗'],
            'general': ['你好', '在吗', '咨询', '问一下']
        }
        
        user_msg_lower = user_msg.lower()
        
        for question_type, keywords in question_patterns.items():
            if any(keyword in user_msg_lower for keyword in keywords):
                return question_type
        
        return 'other'
    
    def _get_response_template(self, question_type: str) -> str:
        """
        根据问题类型获取回复模板
        
        Args:
            question_type: 问题类型
            
        Returns:
            str: 回复模板
        """
        templates = {
            'shipping': "关于物流配送，我来为您详细说明...",
            'payment': "关于支付方式，我们支持多种安全的支付方式...",
            'return': "关于售后服务，我们提供完善的保障...",
            'product_info': "关于这款商品，我来为您详细介绍...",
            'availability': "关于库存情况，让我为您确认一下...",
            'general': "您好！很高兴为您服务...",
            'other': "感谢您的咨询，我来为您解答..."
        }
        
        return templates.get(question_type, templates['other'])
    
    def get_default_strategy_info(self) -> Dict[str, Any]:
        """
        获取默认策略特有信息
        
        Returns:
            Dict: 策略信息
        """
        base_info = self.get_strategy_info()
        base_info.update({
            'supported_intents': ['default', 'general_service', 'other'],
            'features': [
                'universal_handler',
                'question_type_detection',
                'service_parameter_analysis',
                'template_based_response'
            ],
            'question_types': [
                'shipping',
                'payment', 
                'return',
                'product_info',
                'availability',
                'general'
            ]
        })
        return base_info
