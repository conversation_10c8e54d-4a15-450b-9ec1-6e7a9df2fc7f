"""
技术咨询策略

专门处理技术相关的问题和咨询
"""

import os
from typing import Dict, Any, Optional
from loguru import logger

from .base_strategy import BaseStrategy


class TechStrategy(BaseStrategy):
    """技术咨询策略"""
    
    def __init__(self):
        super().__init__("tech_strategy")
    
    def _load_system_prompt(self) -> str:
        """加载技术策略的系统提示词"""
        try:
            # 尝试从文件加载
            prompt_file = "prompts/tech_prompt.txt"
            if os.path.exists(prompt_file):
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    return f.read().strip()
        except Exception as e:
            logger.warning(f"加载技术提示词文件失败: {e}")
        
        # 默认提示词
        return """你是一个专业的数码产品专家，对各种电子设备、手机、电脑、配件等都有深入的了解。你的任务是为买家提供准确、专业的技术咨询服务。

## 核心原则
1. 提供准确、专业的技术信息
2. 用通俗易懂的语言解释复杂概念
3. 根据用户需求推荐合适的产品特性
4. 诚实说明产品的优缺点

## 专业领域
- 手机：处理器、内存、摄像头、电池、系统等
- 电脑：CPU、GPU、内存、硬盘、显示器等
- 配件：耳机、充电器、数据线、保护壳等
- 其他数码产品：平板、手表、音响等

## 回复风格
- 专业但不失亲和力
- 提供具体的技术参数和对比
- 结合实际使用场景说明
- 适当提及产品的性价比

## 注意事项
- 如果不确定技术细节，诚实说明
- 避免夸大产品性能
- 提供客观的产品评价
- 根据用户的使用需求给出建议

请根据用户的技术问题，提供专业、准确的回复。"""
    
    def can_handle(self, intent: str, context: Dict[str, Any]) -> bool:
        """判断是否能处理技术相关意图"""
        tech_intents = ['tech', 'technical_inquiry', 'product_details', 'specifications']
        return intent in tech_intents
    
    def generate_reply(
        self, 
        user_msg: str, 
        item_desc: str, 
        context: Dict[str, Any],
        parameters: Optional[Dict[str, Any]] = None
    ) -> str:
        """生成技术咨询回复"""
        try:
            # 分析技术参数
            tech_analysis = self._analyze_tech_parameters(parameters or {})
            
            # 构建消息
            messages = self._build_messages(user_msg, item_desc, context, parameters)
            
            # 添加技术分析到系统消息
            if tech_analysis:
                messages[0]['content'] += f"\n▲技术重点：{tech_analysis}"
            
            # 调用LLM生成回复，启用搜索功能
            response = self._call_llm_with_search(messages)
            
            return self._safe_filter(response)
            
        except Exception as e:
            logger.error(f"技术策略生成回复时出错: {e}")
            return "抱歉，关于这个技术问题我需要查阅一下资料，您可以稍后再问我。"
    
    def _call_llm_with_search(self, messages: list) -> str:
        """
        调用LLM并启用搜索功能
        
        Args:
            messages: 消息列表
            
        Returns:
            str: LLM生成的回复
        """
        try:
            model_name = settings.get('model_name', 'qwen-max')
            response = self.client.chat.completions.create(
                model=model_name,
                messages=messages,
                temperature=0.4,  # 技术回复需要更准确，使用较低温度
                max_tokens=500,
                top_p=0.8,
                extra_body={
                    "enable_search": True,  # 启用搜索功能获取最新技术信息
                }
            )
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"调用LLM搜索功能时出错: {e}")
            # 回退到普通调用
            return self._call_llm(messages, temperature=0.4)
    
    def _analyze_tech_parameters(self, parameters: Dict[str, Any]) -> str:
        """
        分析技术相关参数
        
        Args:
            parameters: 意图参数
            
        Returns:
            str: 分析结果
        """
        analysis_parts = []
        
        # 分析询问的技术规格
        if 'specification' in parameters:
            spec = parameters['specification']
            analysis_parts.append(f"询问规格: {spec}")
        
        # 分析产品类型
        if 'product_type' in parameters:
            product_type = parameters['product_type']
            analysis_parts.append(f"产品类型: {product_type}")
        
        # 分析技术特性
        if 'feature' in parameters:
            feature = parameters['feature']
            analysis_parts.append(f"关注特性: {feature}")
        
        # 分析使用场景
        if 'use_case' in parameters:
            use_case = parameters['use_case']
            analysis_parts.append(f"使用场景: {use_case}")
        
        return "; ".join(analysis_parts) if analysis_parts else ""
    
    def _extract_tech_keywords(self, user_msg: str) -> list:
        """
        提取技术关键词
        
        Args:
            user_msg: 用户消息
            
        Returns:
            list: 技术关键词列表
        """
        tech_keywords = {
            # 手机相关
            '处理器': ['处理器', 'CPU', '芯片', '性能'],
            '内存': ['内存', 'RAM', '运行内存', '存储'],
            '摄像头': ['摄像头', '拍照', '像素', '镜头'],
            '电池': ['电池', '续航', '充电', '电量'],
            '屏幕': ['屏幕', '显示', '分辨率', '尺寸'],
            
            # 电脑相关
            '显卡': ['显卡', 'GPU', '图形', '游戏'],
            '硬盘': ['硬盘', 'SSD', 'HDD', '存储'],
            '主板': ['主板', '接口', '扩展'],
            
            # 通用
            '系统': ['系统', 'OS', '版本', '更新'],
            '接口': ['接口', '端口', '连接', 'USB', 'Type-C'],
            '网络': ['网络', 'WiFi', '5G', '4G', '蓝牙']
        }
        
        found_keywords = []
        user_msg_lower = user_msg.lower()
        
        for category, keywords in tech_keywords.items():
            for keyword in keywords:
                if keyword.lower() in user_msg_lower:
                    found_keywords.append(category)
                    break
        
        return found_keywords
    
    def get_tech_strategy_info(self) -> Dict[str, Any]:
        """
        获取技术策略特有信息
        
        Returns:
            Dict: 策略信息
        """
        base_info = self.get_strategy_info()
        base_info.update({
            'supported_intents': ['tech', 'technical_inquiry', 'product_details'],
            'features': [
                'search_enabled',
                'tech_parameter_analysis',
                'keyword_extraction',
                'low_temperature_generation'
            ],
            'specialties': [
                'mobile_devices',
                'computers',
                'accessories',
                'specifications'
            ]
        })
        return base_info
