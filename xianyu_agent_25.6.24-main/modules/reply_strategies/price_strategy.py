"""
价格议价策略

专门处理价格相关的对话和议价场景
"""

import os
from typing import Dict, Any, Optional
from loguru import logger

from .base_strategy import BaseStrategy


class PriceStrategy(BaseStrategy):
    """价格议价策略"""
    
    def __init__(self):
        super().__init__("price_strategy")
    
    def _load_system_prompt(self) -> str:
        """加载价格策略的系统提示词"""
        try:
            # 尝试从文件加载
            prompt_file = "prompts/price_prompt.txt"
            if os.path.exists(prompt_file):
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    return f.read().strip()
        except Exception as e:
            logger.warning(f"加载价格提示词文件失败: {e}")
        
        # 默认提示词
        return """你是一个专业的闲鱼卖家，擅长价格谈判和议价。你的目标是在保持友好的同时，尽可能维护商品的合理价格。

## 核心原则
1. 保持友好和专业的态度
2. 根据商品价值和市场行情进行合理议价
3. 适当展示商品的价值和优势
4. 在合理范围内给予优惠，促成交易

## 议价策略
- 首次议价：可以给予5-10%的优惠
- 多次议价：逐步减少优惠幅度，但保持谈判空间
- 底线价格：不低于原价的70%

## 回复风格
- 使用亲切的称呼（亲、朋友等）
- 适当使用表情符号增加亲和力
- 突出商品的性价比和优势
- 营造紧迫感（如：最近很多人询问）

请根据用户的议价请求，生成合适的回复。"""
    
    def can_handle(self, intent: str, context: Dict[str, Any]) -> bool:
        """判断是否能处理价格相关意图"""
        price_intents = ['price', 'price_negotiation', 'bargain']
        return intent in price_intents
    
    def generate_reply(
        self, 
        user_msg: str, 
        item_desc: str, 
        context: Dict[str, Any],
        parameters: Optional[Dict[str, Any]] = None
    ) -> str:
        """生成价格议价回复"""
        try:
            # 获取议价次数
            bargain_count = context.get('bargain_count', 0)
            
            # 根据议价次数调整温度参数
            temperature = self._calc_temperature(bargain_count)
            
            # 分析价格参数
            price_analysis = self._analyze_price_parameters(parameters or {})
            
            # 构建消息
            messages = self._build_messages(user_msg, item_desc, context, parameters)
            
            # 添加议价信息到系统消息
            if bargain_count > 0:
                messages[0]['content'] += f"\n▲当前议价轮次：{bargain_count}"
            
            if price_analysis:
                messages[0]['content'] += f"\n▲议价分析：{price_analysis}"
            
            # 调用LLM生成回复
            response = self._call_llm(messages, temperature=temperature)
            
            # 更新议价次数
            if self._is_bargain_message(user_msg):
                self._increment_bargain_count(context)
            
            return self._safe_filter(response)
            
        except Exception as e:
            logger.error(f"价格策略生成回复时出错: {e}")
            return "抱歉，关于价格的问题我需要再想想，您可以稍后再问我。"
    
    def _calc_temperature(self, bargain_count: int) -> float:
        """
        根据议价次数计算温度参数
        
        Args:
            bargain_count: 议价次数
            
        Returns:
            float: 温度参数
        """
        # 议价次数越多，回复越严格（温度越低）
        base_temp = 0.8
        reduction = min(bargain_count * 0.1, 0.4)
        return max(base_temp - reduction, 0.3)
    
    def _analyze_price_parameters(self, parameters: Dict[str, Any]) -> str:
        """
        分析价格相关参数
        
        Args:
            parameters: 意图参数
            
        Returns:
            str: 分析结果
        """
        analysis_parts = []
        
        # 分析提及的价格
        if 'price' in parameters:
            price = parameters['price']
            analysis_parts.append(f"用户提及价格: {price}")
        
        # 分析议价幅度
        if 'discount' in parameters:
            discount = parameters['discount']
            analysis_parts.append(f"期望优惠: {discount}")
        
        # 分析紧急程度
        if 'urgency' in parameters:
            urgency = parameters['urgency']
            analysis_parts.append(f"紧急程度: {urgency}")
        
        return "; ".join(analysis_parts) if analysis_parts else ""
    
    def _is_bargain_message(self, user_msg: str) -> bool:
        """
        判断是否为议价消息
        
        Args:
            user_msg: 用户消息
            
        Returns:
            bool: 是否为议价消息
        """
        bargain_keywords = [
            "便宜", "优惠", "折扣", "降价", "减价",
            "最低", "底价", "能不能", "可以吗",
            "元", "块", "钱", "价格"
        ]
        
        user_msg_lower = user_msg.lower()
        return any(keyword in user_msg_lower for keyword in bargain_keywords)
    
    def _increment_bargain_count(self, context: Dict[str, Any]):
        """
        增加议价次数
        
        Args:
            context: 上下文信息
        """
        try:
            message = context.get('message')
            if message and hasattr(message, 'chat_id'):
                # 这里需要访问context_manager，但为了保持策略的独立性
                # 我们通过context传递这个操作
                if 'context_manager' in context:
                    context['context_manager'].increment_bargain_count_by_chat(message.chat_id)
                    logger.debug(f"议价次数已增加: {message.chat_id}")
        except Exception as e:
            logger.error(f"增加议价次数时出错: {e}")
    
    def get_price_strategy_info(self) -> Dict[str, Any]:
        """
        获取价格策略特有信息
        
        Returns:
            Dict: 策略信息
        """
        base_info = self.get_strategy_info()
        base_info.update({
            'supported_intents': ['price', 'price_negotiation', 'bargain'],
            'features': [
                'dynamic_temperature',
                'bargain_count_tracking',
                'price_parameter_analysis'
            ]
        })
        return base_info
