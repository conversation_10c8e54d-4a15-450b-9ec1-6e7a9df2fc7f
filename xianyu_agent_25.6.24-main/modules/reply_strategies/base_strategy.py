"""
回复策略基类

定义所有回复策略的标准接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from loguru import logger
from openai import OpenAI

from config.settings import settings


class BaseStrategy(ABC):
    """回复策略基类"""
    
    def __init__(self, name: str):
        """
        初始化策略
        
        Args:
            name: 策略名称
        """
        self.name = name
        
        # 初始化OpenAI客户端
        api_config = settings.get_api_config()
        self.client = OpenAI(
            api_key=api_config['api_key'],
            base_url=api_config['base_url'],
        )
        
        # 加载系统提示词
        self.system_prompt = self._load_system_prompt()
        
        logger.debug(f"策略 {self.name} 初始化完成")
    
    @abstractmethod
    def _load_system_prompt(self) -> str:
        """
        加载系统提示词
        
        Returns:
            str: 系统提示词
        """
        pass
    
    @abstractmethod
    def can_handle(self, intent: str, context: Dict[str, Any]) -> bool:
        """
        判断策略是否能处理指定意图
        
        Args:
            intent: 意图类型
            context: 上下文信息
            
        Returns:
            bool: 是否能处理
        """
        pass
    
    @abstractmethod
    def generate_reply(
        self, 
        user_msg: str, 
        item_desc: str, 
        context: Dict[str, Any],
        parameters: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        生成回复
        
        Args:
            user_msg: 用户消息
            item_desc: 商品描述
            context: 上下文信息
            parameters: 意图参数
            
        Returns:
            str: 生成的回复
        """
        pass
    
    def _build_messages(
        self, 
        user_msg: str, 
        item_desc: str, 
        context: Dict[str, Any],
        parameters: Optional[Dict[str, Any]] = None
    ) -> list:
        """
        构建LLM消息列表
        
        Args:
            user_msg: 用户消息
            item_desc: 商品描述
            context: 上下文信息
            parameters: 意图参数
            
        Returns:
            list: 消息列表
        """
        messages = [
            {"role": "system", "content": self.system_prompt}
        ]
        
        # 添加商品信息
        if item_desc:
            messages.append({
                "role": "system", 
                "content": f"商品信息：{item_desc}"
            })
        
        # 添加对话历史
        chat_history = context.get('chat_history', [])
        for msg in chat_history[-10:]:  # 只取最近10条消息
            if msg.get('role') in ['user', 'assistant']:
                messages.append(msg)
        
        # 添加当前用户消息
        messages.append({"role": "user", "content": user_msg})
        
        return messages
    
    def _call_llm(
        self, 
        messages: list, 
        temperature: float = 0.7,
        max_tokens: int = 500
    ) -> str:
        """
        调用LLM生成回复
        
        Args:
            messages: 消息列表
            temperature: 温度参数
            max_tokens: 最大token数
            
        Returns:
            str: LLM生成的回复
        """
        try:
            model_name = settings.get('model_name', 'qwen-max')
            response = self.client.chat.completions.create(
                model=model_name,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=0.8
            )
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"调用LLM时出错: {e}")
            raise
    
    def _safe_filter(self, response: str) -> str:
        """
        安全过滤回复内容
        
        Args:
            response: 原始回复
            
        Returns:
            str: 过滤后的回复
        """
        if not response:
            return "抱歉，我暂时无法回复您的问题。"
        
        # 基础安全过滤
        filtered_response = response.strip()
        
        # 移除可能的敏感信息
        sensitive_patterns = [
            "API_KEY", "密码", "token", "私钥"
        ]
        
        for pattern in sensitive_patterns:
            if pattern in filtered_response:
                logger.warning(f"检测到敏感信息，已过滤: {pattern}")
                filtered_response = filtered_response.replace(pattern, "***")
        
        # 长度限制
        if len(filtered_response) > 1000:
            filtered_response = filtered_response[:1000] + "..."
            logger.debug("回复内容过长，已截断")
        
        return filtered_response
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns:
            Dict: 策略信息
        """
        return {
            'name': self.name,
            'type': self.__class__.__name__,
            'system_prompt_length': len(self.system_prompt) if self.system_prompt else 0
        }
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}(name={self.name})"
