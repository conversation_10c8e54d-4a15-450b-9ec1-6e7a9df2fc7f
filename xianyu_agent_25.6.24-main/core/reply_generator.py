"""
回复生成模块

系统的口才，根据意图或事件决策并生成回复
"""

from typing import Dict, Any, List, Optional
from loguru import logger

from modules.reply_strategies.base_strategy import BaseStrategy
from modules.reply_strategies.price_strategy import PriceStrategy
from modules.reply_strategies.tech_strategy import TechStrategy
from modules.reply_strategies.default_strategy import DefaultStrategy
from modules.intent_engine.engine import IntentEngine
from modules.context_manager.db_handler import ContextManager
from modules.platform_connector.base_connector import StandardMessage


class ReplyGenerator:
    """回复生成器"""
    
    def __init__(self, intent_engine: IntentEngine, context_manager: ContextManager):
        """
        初始化回复生成器
        
        Args:
            intent_engine: 意图识别引擎
            context_manager: 上下文管理器
        """
        self.intent_engine = intent_engine
        self.context_manager = context_manager

        # 统计信息
        self.stats = {
            'total_replies': 0,
            'strategy_usage': {},
            'intent_distribution': {},
            'generation_errors': 0
        }

        # 初始化策略
        self.strategies: List[BaseStrategy] = []
        self._init_strategies()

        # 人工介入处理器
        self.human_handover_handler: Optional[callable] = None
        
        logger.info(f"回复生成器初始化完成，加载了 {len(self.strategies)} 个策略")
    
    def _init_strategies(self):
        """初始化所有策略"""
        try:
            # 添加各种策略
            self.strategies = [
                PriceStrategy(),
                TechStrategy(),
                DefaultStrategy()  # 默认策略放在最后作为兜底
            ]
            
            # 初始化统计信息
            for strategy in self.strategies:
                self.stats['strategy_usage'][strategy.name] = 0
            
            logger.debug(f"策略初始化完成: {[s.name for s in self.strategies]}")
            
        except Exception as e:
            logger.error(f"初始化策略时出错: {e}")
            # 至少保证有默认策略
            self.strategies = [DefaultStrategy()]
    
    def set_human_handover_handler(self, handler: callable):
        """
        设置人工介入处理器
        
        Args:
            handler: 人工介入处理函数
        """
        self.human_handover_handler = handler
        logger.debug("人工介入处理器已设置")
    
    async def generate_reply(
        self, 
        message: StandardMessage, 
        context: Dict[str, Any]
    ) -> Optional[str]:
        """
        生成回复
        
        Args:
            message: 标准化消息
            context: 消息上下文
            
        Returns:
            str: 生成的回复，如果需要人工介入返回None
        """
        self.stats['total_replies'] += 1
        
        try:
            # 1. 获取商品描述
            item_desc = self._extract_item_description(context)
            
            # 2. 进行意图识别
            intent = self.intent_engine.recognize_intent(
                user_input=message.content,
                item_desc=item_desc,
                context=self._format_chat_history(context.get('chat_history', []))
            )
            
            # 更新意图统计
            self.stats['intent_distribution'][intent] = (
                self.stats['intent_distribution'].get(intent, 0) + 1
            )
            
            # 3. 提取意图参数
            parameters = self.intent_engine.extract_intent_parameters(
                message.content, intent
            )
            
            # 4. 检查是否需要人工介入
            if self._should_handover_to_human(intent, message, context):
                return await self._handle_human_handover(message, context, intent)
            
            # 5. 选择合适的策略
            strategy = self._select_strategy(intent, context)
            if not strategy:
                logger.warning(f"未找到合适的策略处理意图: {intent}")
                strategy = self.strategies[-1]  # 使用默认策略
            
            # 6. 生成回复
            reply = strategy.generate_reply(
                user_msg=message.content,
                item_desc=item_desc,
                context=self._enhance_context(context),
                parameters=parameters
            )
            
            # 7. 更新策略使用统计
            self.stats['strategy_usage'][strategy.name] += 1
            
            # 8. 存储助手回复到上下文
            self._store_assistant_reply(message, reply)
            
            logger.info(f"回复生成完成: {strategy.name} -> {reply[:50]}...")
            return reply
            
        except Exception as e:
            logger.error(f"生成回复时出错: {e}")
            self.stats['generation_errors'] += 1
            return "抱歉，我暂时无法回复您的问题，请稍后再试或联系人工客服。"
    
    def _extract_item_description(self, context: Dict[str, Any]) -> str:
        """
        提取商品描述
        
        Args:
            context: 上下文信息
            
        Returns:
            str: 商品描述
        """
        item_info = context.get('item_info')
        if not item_info:
            return ""
        
        # 构建商品描述
        desc_parts = []
        
        if item_info.get('title'):
            desc_parts.append(f"商品名称: {item_info['title']}")
        
        if item_info.get('price'):
            desc_parts.append(f"价格: {item_info['price']}元")
        
        if item_info.get('description'):
            desc_parts.append(f"描述: {item_info['description'][:100]}...")
        
        return "; ".join(desc_parts)
    
    def _format_chat_history(self, chat_history: List[Dict[str, str]]) -> str:
        """
        格式化对话历史
        
        Args:
            chat_history: 对话历史
            
        Returns:
            str: 格式化后的对话历史
        """
        if not chat_history:
            return ""
        
        formatted_history = []
        for msg in chat_history[-5:]:  # 只取最近5条消息
            role = "用户" if msg.get('role') == 'user' else "助手"
            content = msg.get('content', '')
            formatted_history.append(f"{role}: {content}")
        
        return "\n".join(formatted_history)
    
    def _should_handover_to_human(
        self, 
        intent: str, 
        message: StandardMessage, 
        context: Dict[str, Any]
    ) -> bool:
        """
        判断是否需要人工介入
        
        Args:
            intent: 识别的意图
            message: 消息对象
            context: 上下文信息
            
        Returns:
            bool: 是否需要人工介入
        """
        # 检查是否明确要求人工服务
        human_keywords = ['人工', '客服', '转人工', '真人', '工作人员']
        if any(keyword in message.content for keyword in human_keywords):
            return True
        
        # 检查是否为投诉或纠纷
        complaint_keywords = ['投诉', '举报', '欺诈', '骗子', '退款', '纠纷']
        if any(keyword in message.content for keyword in complaint_keywords):
            return True
        
        # 检查意图是否为人工介入
        if intent == 'human_handover':
            return True
        
        # 检查是否连续多次无法理解
        chat_history = context.get('chat_history', [])
        recent_assistant_msgs = [
            msg for msg in chat_history[-6:] 
            if msg.get('role') == 'assistant'
        ]
        
        confusion_indicators = ['抱歉', '不理解', '无法回复', '稍后再试']
        confused_count = sum(
            1 for msg in recent_assistant_msgs 
            if any(indicator in msg.get('content', '') for indicator in confusion_indicators)
        )
        
        if confused_count >= 2:
            logger.info("检测到连续困惑回复，建议人工介入")
            return True
        
        return False
    
    async def _handle_human_handover(
        self, 
        message: StandardMessage, 
        context: Dict[str, Any], 
        intent: str
    ) -> Optional[str]:
        """
        处理人工介入
        
        Args:
            message: 消息对象
            context: 上下文信息
            intent: 意图
            
        Returns:
            str: 人工介入提示消息
        """
        try:
            if self.human_handover_handler:
                await self.human_handover_handler(message, context, intent)
            
            return "您的问题已转接给人工客服，请稍等片刻，我们会尽快为您处理。"
            
        except Exception as e:
            logger.error(f"处理人工介入时出错: {e}")
            return "抱歉，暂时无法转接人工客服，请稍后再试。"
    
    def _select_strategy(self, intent: str, context: Dict[str, Any]) -> Optional[BaseStrategy]:
        """
        选择合适的策略
        
        Args:
            intent: 意图
            context: 上下文
            
        Returns:
            BaseStrategy: 选中的策略
        """
        for strategy in self.strategies:
            if strategy.can_handle(intent, context):
                logger.debug(f"选择策略: {strategy.name} 处理意图: {intent}")
                return strategy
        
        return None
    
    def _enhance_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        增强上下文信息
        
        Args:
            context: 原始上下文
            
        Returns:
            Dict: 增强后的上下文
        """
        enhanced_context = context.copy()
        
        # 添加context_manager引用，供策略使用
        enhanced_context['context_manager'] = self.context_manager
        
        return enhanced_context
    
    def _store_assistant_reply(self, message: StandardMessage, reply: str):
        """
        存储助手回复到上下文
        
        Args:
            message: 原始消息
            reply: 助手回复
        """
        try:
            self.context_manager.add_message_by_chat(
                chat_id=message.chat_id,
                user_id="assistant",  # 助手消息使用固定ID
                item_id=message.item_id,
                role="assistant",
                content=reply
            )
            logger.debug(f"助手回复已存储: {message.chat_id}")
            
        except Exception as e:
            logger.error(f"存储助手回复时出错: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取生成器统计信息
        
        Returns:
            Dict: 统计信息
        """
        return {
            **self.stats,
            'available_strategies': [s.name for s in self.strategies],
            'success_rate': (
                (self.stats['total_replies'] - self.stats['generation_errors']) / 
                max(self.stats['total_replies'], 1)
            ) * 100
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_replies': 0,
            'strategy_usage': {s.name: 0 for s in self.strategies},
            'intent_distribution': {},
            'generation_errors': 0
        }
        logger.info("回复生成器统计信息已重置")
