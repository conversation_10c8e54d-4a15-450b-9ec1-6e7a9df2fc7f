"""
消息处理与路由模块

系统的交通枢纽，负责消息分类和分发
"""

import asyncio
import time
from typing import Dict, Any, Optional, Callable
from loguru import logger
from enum import Enum

from modules.platform_connector.base_connector import StandardMessage, MessageType
from modules.context_manager.db_handler import ContextManager
from modules.intent_engine.engine import IntentEngine
from config.settings import settings


class MessageStatus(Enum):
    """消息处理状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    IGNORED = "ignored"


class MessageRouter:
    """消息处理与路由器"""
    
    def __init__(self, context_manager: ContextManager, intent_engine: IntentEngine):
        """
        初始化消息路由器
        
        Args:
            context_manager: 上下文管理器
            intent_engine: 意图识别引擎
        """
        self.context_manager = context_manager
        self.intent_engine = intent_engine
        
        # 消息处理回调
        self.user_message_handler: Optional[Callable] = None
        self.system_event_handler: Optional[Callable] = None
        
        # 配置
        self.message_expire_time = settings.get('message_expire_time', 300000)  # 5分钟
        self.debug_mode = settings.is_debug_mode()
        
        # 统计信息
        self.stats = {
            'total_messages': 0,
            'user_messages': 0,
            'system_events': 0,
            'ignored_messages': 0,
            'processing_errors': 0
        }
        
        logger.info("消息路由器初始化完成")
    
    def set_user_message_handler(self, handler: Callable):
        """
        设置用户消息处理器
        
        Args:
            handler: 处理用户消息的回调函数
        """
        self.user_message_handler = handler
        logger.debug("用户消息处理器已设置")
    
    def set_system_event_handler(self, handler: Callable):
        """
        设置系统事件处理器
        
        Args:
            handler: 处理系统事件的回调函数
        """
        self.system_event_handler = handler
        logger.debug("系统事件处理器已设置")
    
    async def process_message(self, message: StandardMessage) -> MessageStatus:
        """
        处理标准化消息

        Args:
            message: 标准化消息对象

        Returns:
            MessageStatus: 消息处理状态
        """
        self.stats['total_messages'] += 1

        # 🔍 调试日志：记录收到的消息
        logger.info(f"🔍 [ROUTER] 收到消息处理请求: ID={message.message_id}, 类型={message.message_type}, 用户={message.user_id}, 内容='{message.content}'")

        try:
            # 1. 消息有效性检查
            logger.debug("🔍 [ROUTER] 开始消息有效性检查")
            if not self._is_message_valid(message):
                logger.warning(f"🔍 [ROUTER] 消息无效，忽略处理: {message.message_id}")
                self.stats['ignored_messages'] += 1
                return MessageStatus.IGNORED

            # 2. 时效性检查
            logger.debug("🔍 [ROUTER] 开始时效性检查")
            if not self._is_message_fresh(message):
                logger.warning(f"🔍 [ROUTER] 消息已过期，忽略处理: {message.message_id}")
                self.stats['ignored_messages'] += 1
                return MessageStatus.IGNORED

            # 3. 存储消息到上下文
            logger.debug("🔍 [ROUTER] 存储消息到上下文")
            self._store_message_context(message)

            # 4. 根据消息类型进行路由
            logger.debug(f"🔍 [ROUTER] 开始消息路由，消息类型: {message.message_type}")
            if message.message_type == MessageType.USER_MESSAGE:
                logger.info("🔍 [ROUTER] 路由到用户消息处理器")
                return await self._route_user_message(message)
            elif message.message_type == MessageType.SYSTEM_EVENT:
                logger.info("🔍 [ROUTER] 路由到系统事件处理器")
                return await self._route_system_event(message)
            else:
                logger.warning(f"🔍 [ROUTER] 未知消息类型，忽略处理: {message.message_type}")
                self.stats['ignored_messages'] += 1
                return MessageStatus.IGNORED
                
        except Exception as e:
            logger.error(f"处理消息时出错: {e}")
            self.stats['processing_errors'] += 1
            return MessageStatus.FAILED
    
    async def _route_user_message(self, message: StandardMessage) -> MessageStatus:
        """
        路由用户消息
        
        Args:
            message: 用户消息
            
        Returns:
            MessageStatus: 处理状态
        """
        self.stats['user_messages'] += 1
        
        try:
            if not self.user_message_handler:
                logger.warning("用户消息处理器未设置")
                return MessageStatus.FAILED
            
            logger.info(f"处理用户消息: {message.sender_name} -> {message.content[:50]}...")
            
            # 获取上下文信息
            context = self._build_message_context(message)
            
            # 调用用户消息处理器
            await self.user_message_handler(message, context)
            
            return MessageStatus.COMPLETED
            
        except Exception as e:
            logger.error(f"路由用户消息时出错: {e}")
            return MessageStatus.FAILED
    
    async def _route_system_event(self, message: StandardMessage) -> MessageStatus:
        """
        路由系统事件
        
        Args:
            message: 系统事件消息
            
        Returns:
            MessageStatus: 处理状态
        """
        self.stats['system_events'] += 1
        
        try:
            if not self.system_event_handler:
                logger.warning("系统事件处理器未设置")
                return MessageStatus.FAILED
            
            logger.info(f"处理系统事件: {message.content[:50]}...")
            
            # 获取上下文信息
            context = self._build_message_context(message)
            
            # 调用系统事件处理器
            await self.system_event_handler(message, context)
            
            return MessageStatus.COMPLETED
            
        except Exception as e:
            logger.error(f"路由系统事件时出错: {e}")
            return MessageStatus.FAILED
    
    def _is_message_valid(self, message: StandardMessage) -> bool:
        """
        检查消息是否有效
        
        Args:
            message: 消息对象
            
        Returns:
            bool: 消息是否有效
        """
        # 检查必要字段
        required_fields = ['message_id', 'chat_id', 'user_id', 'item_id', 'content']
        for field in required_fields:
            if not getattr(message, field, None):
                logger.debug(f"消息缺少必要字段: {field}")
                return False
        
        # 检查内容长度
        if len(message.content.strip()) == 0:
            logger.debug("消息内容为空")
            return False
        
        return True
    
    def _is_message_fresh(self, message: StandardMessage) -> bool:
        """
        检查消息是否在有效期内
        
        Args:
            message: 消息对象
            
        Returns:
            bool: 消息是否新鲜
        """
        current_time = time.time() * 1000  # 转换为毫秒
        message_age = current_time - message.timestamp
        
        if message_age > self.message_expire_time:
            logger.debug(f"消息已过期: {message_age}ms > {self.message_expire_time}ms")
            return False
        
        return True
    
    def _store_message_context(self, message: StandardMessage):
        """
        存储消息到上下文管理器
        
        Args:
            message: 消息对象
        """
        try:
            self.context_manager.add_message_by_chat(
                chat_id=message.chat_id,
                user_id=message.user_id,
                item_id=message.item_id,
                role="user" if message.message_type == MessageType.USER_MESSAGE else "system",
                content=message.content
            )
            logger.debug(f"消息已存储到上下文: {message.message_id}")
            
        except Exception as e:
            logger.error(f"存储消息上下文时出错: {e}")
    
    def _build_message_context(self, message: StandardMessage) -> Dict[str, Any]:
        """
        构建消息处理上下文
        
        Args:
            message: 消息对象
            
        Returns:
            Dict: 上下文信息
        """
        try:
            # 获取对话历史
            chat_history = self.context_manager.get_context_by_chat(message.chat_id)
            
            # 获取议价次数
            bargain_count = self.context_manager.get_bargain_count_by_chat(message.chat_id)
            
            # 获取商品信息
            item_info = self.context_manager.get_parsed_item_info(message.item_id)
            if not item_info:
                item_info = self.context_manager.get_item_info(message.item_id)
            
            context = {
                'message': message,
                'chat_history': chat_history,
                'bargain_count': bargain_count,
                'item_info': item_info,
                'debug_mode': self.debug_mode
            }
            
            return context
            
        except Exception as e:
            logger.error(f"构建消息上下文时出错: {e}")
            return {
                'message': message,
                'chat_history': [],
                'bargain_count': 0,
                'item_info': None,
                'debug_mode': self.debug_mode
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取路由器统计信息
        
        Returns:
            Dict: 统计信息
        """
        return {
            **self.stats,
            'success_rate': (
                (self.stats['total_messages'] - self.stats['processing_errors']) / 
                max(self.stats['total_messages'], 1)
            ) * 100
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_messages': 0,
            'user_messages': 0,
            'system_events': 0,
            'ignored_messages': 0,
            'processing_errors': 0
        }
        logger.info("路由器统计信息已重置")
    
    def get_debug_info(self) -> Dict[str, Any]:
        """
        获取调试信息
        
        Returns:
            Dict: 调试信息
        """
        return {
            'stats': self.get_stats(),
            'handlers_configured': {
                'user_message_handler': self.user_message_handler is not None,
                'system_event_handler': self.system_event_handler is not None
            },
            'config': {
                'message_expire_time': self.message_expire_time,
                'debug_mode': self.debug_mode
            }
        }
