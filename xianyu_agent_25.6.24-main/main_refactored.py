"""
闲鱼智能客服系统 - 重构版主程序

基于模块化单体架构的新版本主程序
"""

import asyncio
import sys
import signal
from typing import Dict, Any
from loguru import logger

# 配置和日志
from config.settings import settings
from config.logger_config import setup_logger, logger_manager

# 核心模块
from core.message_router import MessageRouter
from core.reply_generator import ReplyGenerator

# 功能模块
from modules.platform_connector.xianyu_connector import XianyuConnector
from modules.context_manager.db_handler import ContextManager
from modules.intent_engine.engine import IntentEngine
from modules.human_handover.notifier import HumanHandoverManager, HandoverReason

# 平台连接器基类
from modules.platform_connector.base_connector import StandardMessage


class XianyuAgentSystem:
    """闲鱼智能客服系统"""
    
    def __init__(self):
        """初始化系统"""
        self.running = False
        self.platform_connector = None
        self.message_router = None
        self.reply_generator = None
        self.context_manager = None
        self.intent_engine = None
        self.handover_manager = None
        
        logger.info("🚀 闲鱼智能客服系统启动中...")
    
    async def initialize(self):
        """初始化所有模块"""
        try:
            # 1. 验证配置
            if not settings.validate_required_config():
                logger.error("配置验证失败，系统无法启动")
                return False
            
            # 2. 初始化上下文管理器
            logger.info("📊 初始化上下文管理器...")
            db_config = settings.get_db_config()
            self.context_manager = ContextManager(
                db_path=db_config['db_path'],
                max_history=db_config['max_history']
            )
            
            # 3. 初始化意图识别引擎
            logger.info("🧠 初始化意图识别引擎...")
            self.intent_engine = IntentEngine()
            
            # 4. 初始化人工介入管理器
            logger.info("👥 初始化人工介入管理器...")
            self.handover_manager = HumanHandoverManager()
            
            # 5. 初始化回复生成器
            logger.info("💬 初始化回复生成器...")
            self.reply_generator = ReplyGenerator(
                intent_engine=self.intent_engine,
                context_manager=self.context_manager
            )
            
            # 设置人工介入处理器
            self.reply_generator.set_human_handover_handler(
                self._handle_human_handover
            )
            
            # 6. 初始化消息路由器
            logger.info("🚦 初始化消息路由器...")
            self.message_router = MessageRouter(
                context_manager=self.context_manager,
                intent_engine=self.intent_engine
            )
            
            # 设置消息处理器
            self.message_router.set_user_message_handler(self._handle_user_message)
            self.message_router.set_system_event_handler(self._handle_system_event)
            
            # 7. 初始化平台连接器
            logger.info("🔌 初始化平台连接器...")
            platform_config = {
                'cookies_str': settings.get('cookies_str'),
                'token_refresh_interval': settings.get('token_refresh_interval'),
                'message_expire_time': settings.get('message_expire_time'),
                'ws_url': settings.get('ws_url')
            }
            
            self.platform_connector = XianyuConnector(platform_config)
            
            # 8. 连接到平台
            logger.info("🌐 连接到闲鱼平台...")
            if not await self.platform_connector.connect():
                logger.error("连接闲鱼平台失败")
                return False
            
            logger.info("✅ 系统初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            return False
    
    async def start(self):
        """启动系统"""
        try:
            # 初始化系统
            if not await self.initialize():
                logger.error("系统初始化失败，无法启动")
                return
            
            # 设置信号处理
            self._setup_signal_handlers()

            # 🔍 关键修复：在开始监听之前设置运行状态
            logger.info("🎯 准备启动消息监听...")
            self.running = True
            logger.info(f"🔍 [MAIN] ✅ 系统状态已设置为运行中: running={self.running}")

            # 添加短暂延迟确保状态设置完成
            await asyncio.sleep(0.1)

            logger.info("🎯 开始监听消息...")
            await self.platform_connector.listen_messages(self._on_message_received)
            
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在关闭系统...")
        except Exception as e:
            logger.error(f"系统运行时出错: {e}")
        finally:
            await self.shutdown()
    
    async def _on_message_received(self, message: StandardMessage):
        """
        处理接收到的消息

        Args:
            message: 标准化消息
        """
        try:
            # 🔍 详细的状态检查和日志
            logger.info(f"🔍 [MAIN] 📨 收到消息回调，内容: '{message.content}'")
            logger.info(f"🔍 [MAIN] 📊 当前系统状态: running={self.running}")

            # 检查系统是否正在运行
            if not self.running:
                logger.warning(f"🔍 [MAIN] ⚠️ 系统未运行，忽略消息: {message.content}")
                logger.warning(f"🔍 [MAIN] ⚠️ 系统状态详情: running={self.running}")
                logger.warning(f"🔍 [MAIN] ⚠️ 消息详情: ID={message.message_id}, 用户={message.sender_name}")
                return

            # 🔍 系统正在运行，处理消息
            logger.info(f"🔍 [MAIN] ✅ 系统正在运行，开始处理消息")
            logger.info(f"🔍 [MAIN] ✅ 消息详情: ID={message.message_id}, 用户={message.sender_name}({message.user_id}), 内容='{message.content}'")

            # 通过消息路由器处理消息
            logger.info("🔍 [MAIN] 🚦 开始通过消息路由器处理消息")
            status = await self.message_router.process_message(message)

            logger.info(f"🔍 [MAIN] ✅ 消息处理完成: {message.message_id} -> {status.value}")

        except Exception as e:
            logger.error(f"🔍 [MAIN] ❌ 处理消息时出错: {e}")
            import traceback
            logger.error(f"🔍 [MAIN] ❌ 错误堆栈: {traceback.format_exc()}")
    
    async def _handle_user_message(self, message: StandardMessage, context: Dict[str, Any]):
        """
        处理用户消息
        
        Args:
            message: 用户消息
            context: 消息上下文
        """
        try:
            # 生成回复
            reply = await self.reply_generator.generate_reply(message, context)
            
            if reply:
                # 发送回复
                success = await self.platform_connector.send_message(message.chat_id, reply)
                if success:
                    logger.info(f"回复已发送: {message.chat_id} -> {reply[:50]}...")
                else:
                    logger.warning(f"回复发送失败: {message.chat_id}")
            else:
                logger.info(f"消息已转人工处理: {message.chat_id}")
                
        except Exception as e:
            logger.error(f"处理用户消息时出错: {e}")
    
    async def _handle_system_event(self, message: StandardMessage, context: Dict[str, Any]):
        """
        处理系统事件
        
        Args:
            message: 系统事件消息
            context: 消息上下文
        """
        try:
            logger.info(f"处理系统事件: {message.content}")
            
            # 根据事件类型进行处理
            # 这里可以添加具体的系统事件处理逻辑
            # 例如：订单状态变更、支付成功等
            
        except Exception as e:
            logger.error(f"处理系统事件时出错: {e}")
    
    async def _handle_human_handover(
        self, 
        message: StandardMessage, 
        context: Dict[str, Any], 
        intent: str
    ):
        """
        处理人工介入
        
        Args:
            message: 触发介入的消息
            context: 上下文信息
            intent: 识别的意图
        """
        try:
            # 确定介入原因
            reason = HandoverReason.USER_REQUEST
            if '投诉' in message.content or '举报' in message.content:
                reason = HandoverReason.COMPLAINT
            elif intent == 'complex_issue':
                reason = HandoverReason.COMPLEX_ISSUE
            
            # 请求人工介入
            handover_id = await self.handover_manager.request_human_handover(
                message=message,
                context=context,
                reason=reason,
                priority="normal"
            )
            
            logger.info(f"人工介入请求已创建: {handover_id}")
            
        except Exception as e:
            logger.error(f"处理人工介入时出错: {e}")
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"🔍 [SIGNAL] 收到信号 {signum}，正在关闭系统...")
            logger.info(f"🔍 [SIGNAL] 设置系统状态为停止运行")
            self.running = False

        # 只在主线程中设置信号处理器
        try:
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            logger.debug("🔍 [SIGNAL] 信号处理器设置完成")
        except ValueError as e:
            # 在非主线程中可能会出现这个错误，这是正常的
            logger.debug(f"🔍 [SIGNAL] 信号处理器设置跳过: {e}")
    
    async def shutdown(self):
        """关闭系统"""
        try:
            logger.info("🔄 正在关闭系统...")
            
            self.running = False
            
            # 断开平台连接
            if self.platform_connector:
                await self.platform_connector.disconnect()
            
            # 打印统计信息
            self._print_stats()
            
            logger.info("✅ 系统已安全关闭")
            
        except Exception as e:
            logger.error(f"关闭系统时出错: {e}")
    
    def _print_stats(self):
        """打印系统统计信息"""
        try:
            logger.info("📊 系统运行统计:")
            
            if self.message_router:
                router_stats = self.message_router.get_stats()
                logger.info(f"  消息路由: 总计 {router_stats['total_messages']} 条，成功率 {router_stats['success_rate']:.1f}%")
            
            if self.reply_generator:
                generator_stats = self.reply_generator.get_stats()
                logger.info(f"  回复生成: 总计 {generator_stats['total_replies']} 条，成功率 {generator_stats['success_rate']:.1f}%")
            
            if self.handover_manager:
                handover_stats = self.handover_manager.get_stats()
                logger.info(f"  人工介入: 总计 {handover_stats['total_handovers']} 次，活跃 {handover_stats['active_handovers_count']} 个")
            
        except Exception as e:
            logger.error(f"打印统计信息时出错: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """
        获取系统状态
        
        Returns:
            Dict: 系统状态信息
        """
        return {
            'running': self.running,
            'platform_connected': self.platform_connector.is_connected if self.platform_connector else False,
            'modules_initialized': {
                'context_manager': self.context_manager is not None,
                'intent_engine': self.intent_engine is not None,
                'reply_generator': self.reply_generator is not None,
                'message_router': self.message_router is not None,
                'handover_manager': self.handover_manager is not None
            }
        }


async def main():
    """主函数"""
    # 设置日志
    if settings.is_debug_mode():
        setup_logger(log_level="DEBUG", log_file="logs/debug.log")
    else:
        setup_logger(log_level="INFO", log_file="logs/app.log")
    
    # 记录系统信息
    logger_manager.log_system_info()
    
    # 创建并启动系统
    system = XianyuAgentSystem()
    await system.start()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        sys.exit(1)
