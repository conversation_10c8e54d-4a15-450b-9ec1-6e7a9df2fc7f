#!/usr/bin/env python3
"""
系统状态测试脚本

用于测试系统状态管理是否正常工作
"""

import asyncio
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main_refactored import XianyuAgentSystem
from modules.platform_connector.base_connector import StandardMessage, MessageType
from utils.xianyu_utils import generate_uuid
from loguru import logger

async def test_message_processing():
    """测试消息处理功能"""
    print("🧪 开始测试消息处理功能...")

    # 创建系统实例
    system = XianyuAgentSystem()

    # 检查初始状态
    print(f"📊 初始状态: running={system.running}")

    # 初始化系统
    print("🔧 初始化系统...")
    try:
        init_success = await system.initialize()
        if not init_success:
            print("❌ 系统初始化失败")
            return False
        print("✅ 系统初始化成功")
    except Exception as e:
        print(f"❌ 系统初始化异常: {e}")
        return False

    # 手动设置运行状态（模拟start方法中的设置）
    system.running = True
    print(f"📊 设置运行状态: running={system.running}")

    # 创建测试消息
    test_message = StandardMessage(
        message_id=generate_uuid(),
        chat_id="test_chat_123",
        user_id="test_user_456",
        item_id="test_item_789",
        content="你好，这是一条测试消息",
        message_type=MessageType.USER_MESSAGE,
        timestamp=int(time.time() * 1000),
        sender_name="测试用户",
        platform_data={}
    )

    # 测试消息处理（running=True）
    print("📨 测试消息处理（系统运行中）...")
    try:
        await system._on_message_received(test_message)
        print("✅ 消息处理测试通过")
    except Exception as e:
        print(f"❌ 消息处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    # 测试状态重置
    print("🔄 测试状态重置...")
    system.running = False
    print(f"📊 重置后状态: running={system.running}")

    # 再次测试消息处理（应该被忽略）
    print("📨 测试消息处理（系统停止）...")
    try:
        await system._on_message_received(test_message)
        print("✅ 状态检查测试通过（消息被正确忽略）")
    except Exception as e:
        print(f"❌ 状态检查测试失败: {e}")
        return False

    print("🎉 所有测试通过！")
    return True

async def main():
    """主函数"""
    try:
        success = await test_message_processing()
        if success:
            print("✅ 系统状态管理测试成功")
        else:
            print("❌ 系统状态管理测试失败")
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
