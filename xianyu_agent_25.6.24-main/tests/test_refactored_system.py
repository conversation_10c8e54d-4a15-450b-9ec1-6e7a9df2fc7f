"""
重构系统测试

测试模块化单体架构重构后的各个模块功能
"""

import os
import sys
import asyncio
import tempfile
import unittest
import time
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入测试模块
from config.settings import Settings
from config.logger_config import setup_logger
from modules.context_manager.db_handler import ContextManager
from modules.intent_engine.engine import IntentEngine
from modules.reply_strategies.price_strategy import PriceStrategy
from modules.reply_strategies.tech_strategy import TechStrategy
from modules.reply_strategies.default_strategy import DefaultStrategy
from core.message_router import MessageRouter
from core.reply_generator import ReplyGenerator
from modules.human_handover.notifier import HumanHandoverManager, HandoverReason
from modules.platform_connector.base_connector import StandardMessage, MessageType


class TestRefactoredSystem(unittest.TestCase):
    """重构系统测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 设置测试日志
        setup_logger(log_level="DEBUG")
        
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        print(f"\n🧪 开始测试: {self._testMethodName}")
    
    def tearDown(self):
        """测试后清理"""
        # 清理临时数据库
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
        
        print(f"✅ 测试完成: {self._testMethodName}")
    
    def test_config_settings(self):
        """测试配置管理模块"""
        print("📋 测试配置管理模块...")

        # 测试默认配置
        settings = Settings(".env.test")  # 使用不存在的文件测试默认行为

        # 测试配置分组
        api_config = settings.get_api_config()
        self.assertIsInstance(api_config, dict)

        db_config = settings.get_db_config()
        self.assertIsInstance(db_config, dict)

        ws_config = settings.get_ws_config()
        self.assertIsInstance(ws_config, dict)

        # 测试配置验证
        is_valid = settings.validate_required_config()
        self.assertIsInstance(is_valid, bool)

        print("  ✓ 配置分组功能正常")
        print("  ✓ 配置验证功能正常")
    
    def test_context_manager(self):
        """测试上下文管理模块"""
        print("📊 测试上下文管理模块...")
        
        # 创建上下文管理器
        context_manager = ContextManager(db_path=self.temp_db.name, max_history=10)
        
        # 测试消息存储
        chat_id = "test_chat_001"
        user_id = "test_user_001"
        item_id = "test_item_001"
        
        context_manager.add_message_by_chat(
            chat_id=chat_id,
            user_id=user_id,
            item_id=item_id,
            role="user",
            content="测试消息"
        )
        
        # 测试消息检索
        messages = context_manager.get_context_by_chat(chat_id)
        self.assertEqual(len(messages), 1)
        self.assertEqual(messages[0]['content'], "测试消息")
        
        # 测试议价次数
        context_manager.increment_bargain_count_by_chat(chat_id)
        bargain_count = context_manager.get_bargain_count_by_chat(chat_id)
        self.assertEqual(bargain_count, 1)
        
        print("  ✓ 消息存储和检索正常")
        print("  ✓ 议价次数统计正常")
    
    @patch('modules.intent_engine.engine.OpenAI')
    def test_intent_engine(self, mock_openai):
        """测试意图识别模块"""
        print("🧠 测试意图识别模块...")
        
        # Mock LLM响应
        mock_client = Mock()
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "1"
        mock_client.chat.completions.create.return_value = mock_response
        mock_openai.return_value = mock_client
        
        # 创建意图引擎
        intent_engine = IntentEngine()
        
        # 测试意图识别
        intent = intent_engine.recognize_intent(
            user_input="这个价格能便宜点吗？",
            item_desc="iPhone 13",
            context=""
        )
        
        self.assertIsInstance(intent, str)
        
        # 测试意图映射
        legacy_intent = intent_engine.map_to_legacy_intent(intent)
        self.assertIsInstance(legacy_intent, str)
        
        print("  ✓ 意图识别功能正常")
        print("  ✓ 意图映射功能正常")
    
    @patch('modules.reply_strategies.base_strategy.OpenAI')
    def test_reply_strategies(self, mock_openai):
        """测试回复策略模块"""
        print("💬 测试回复策略模块...")
        
        # Mock LLM响应
        mock_client = Mock()
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "这是一个测试回复"
        mock_client.chat.completions.create.return_value = mock_response
        mock_openai.return_value = mock_client
        
        # 测试价格策略
        price_strategy = PriceStrategy()
        self.assertTrue(price_strategy.can_handle("price", {}))
        
        reply = price_strategy.generate_reply(
            user_msg="能便宜点吗？",
            item_desc="iPhone 13",
            context={'chat_history': [], 'bargain_count': 0},
            parameters={}
        )
        self.assertIsInstance(reply, str)
        
        # 测试技术策略
        tech_strategy = TechStrategy()
        self.assertTrue(tech_strategy.can_handle("tech", {}))
        
        # 测试默认策略
        default_strategy = DefaultStrategy()
        self.assertTrue(default_strategy.can_handle("any_intent", {}))
        
        print("  ✓ 价格策略功能正常")
        print("  ✓ 技术策略功能正常")
        print("  ✓ 默认策略功能正常")
    
    def test_message_router(self):
        """测试消息路由模块"""
        print("🚦 测试消息路由模块...")
        
        # 创建依赖模块
        context_manager = ContextManager(db_path=self.temp_db.name)
        
        with patch('modules.intent_engine.engine.OpenAI'):
            intent_engine = IntentEngine()
        
        # 创建消息路由器
        message_router = MessageRouter(context_manager, intent_engine)
        
        # 设置处理器
        user_handler = AsyncMock()
        system_handler = AsyncMock()
        
        message_router.set_user_message_handler(user_handler)
        message_router.set_system_event_handler(system_handler)
        
        # 创建测试消息
        test_message = StandardMessage(
            message_id="test_001",
            chat_id="chat_001",
            user_id="user_001",
            item_id="item_001",
            content="测试消息",
            message_type=MessageType.USER_MESSAGE,
            timestamp=int(time.time() * 1000),  # 当前时间戳
            sender_name="测试用户",
            platform_data={}
        )
        
        # 测试消息路由
        async def run_test():
            status = await message_router.process_message(test_message)
            return status
        
        status = asyncio.run(run_test())
        self.assertIsNotNone(status)
        
        # 验证处理器被调用
        user_handler.assert_called_once()
        
        print("  ✓ 消息路由功能正常")
        print("  ✓ 处理器调用正常")
    
    def test_human_handover_manager(self):
        """测试人工介入模块"""
        print("👥 测试人工介入模块...")
        
        # 创建人工介入管理器
        handover_manager = HumanHandoverManager()
        
        # 创建测试消息
        test_message = StandardMessage(
            message_id="test_001",
            chat_id="chat_001",
            user_id="user_001",
            item_id="item_001",
            content="我要投诉",
            message_type=MessageType.USER_MESSAGE,
            timestamp=int(time.time() * 1000),
            sender_name="测试用户",
            platform_data={}
        )
        
        # 测试人工介入请求
        async def run_test():
            handover_id = await handover_manager.request_human_handover(
                message=test_message,
                context={'chat_history': []},
                reason=HandoverReason.COMPLAINT,
                priority="high"
            )
            return handover_id
        
        handover_id = asyncio.run(run_test())
        self.assertIsInstance(handover_id, str)
        
        # 测试介入记录查询
        active_handovers = handover_manager.get_active_handovers()
        self.assertEqual(len(active_handovers), 1)
        
        # 测试介入解决
        success = handover_manager.resolve_handover(handover_id, "问题已解决")
        self.assertTrue(success)
        
        print("  ✓ 人工介入请求正常")
        print("  ✓ 介入记录管理正常")
    
    def test_system_integration(self):
        """测试系统集成"""
        print("🔧 测试系统集成...")
        
        # 创建所有模块
        context_manager = ContextManager(db_path=self.temp_db.name)
        
        with patch('modules.intent_engine.engine.OpenAI'):
            intent_engine = IntentEngine()
        
        handover_manager = HumanHandoverManager()
        
        with patch('modules.reply_strategies.base_strategy.OpenAI'):
            reply_generator = ReplyGenerator(intent_engine, context_manager)
            reply_generator.set_human_handover_handler(
                lambda msg, ctx, _: handover_manager.request_human_handover(
                    msg, ctx, HandoverReason.USER_REQUEST
                )
            )
        
        message_router = MessageRouter(context_manager, intent_engine)
        message_router.set_user_message_handler(
            lambda msg, ctx: reply_generator.generate_reply(msg, ctx)
        )
        
        # 验证模块间连接
        self.assertIsNotNone(reply_generator.intent_engine)
        self.assertIsNotNone(reply_generator.context_manager)
        self.assertIsNotNone(message_router.context_manager)
        self.assertIsNotNone(message_router.intent_engine)
        
        print("  ✓ 模块依赖注入正常")
        print("  ✓ 系统集成测试通过")


def run_tests():
    """运行所有测试"""
    print("🚀 开始运行重构系统测试...")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestRefactoredSystem)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("=" * 60)
    
    # 输出测试结果
    if result.wasSuccessful():
        print("🎉 所有测试通过！重构系统功能正常。")
        return True
    else:
        print(f"❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        
        # 输出失败详情
        for test, traceback in result.failures + result.errors:
            print(f"\n失败测试: {test}")
            print(f"错误信息: {traceback}")
        
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
