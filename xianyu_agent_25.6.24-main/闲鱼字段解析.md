# 闲鱼字段解析文档

## 概述

本文档详细解析闲鱼平台返回的各种数据字段结构，包括商品信息、用户信息、消息数据、交易状态等，为功能开发提供完整的字段参考。

## 1. WebSocket 消息字段结构

### 1.1 聊天消息格式

闲鱼WebSocket聊天消息的标准格式：

```json
{
  "1": {
    "2": "会话ID@goofish",
    "5": "时间戳(毫秒)",
    "10": {
      "reminderTitle": "发送者昵称",
      "senderUserId": "发送者用户ID",
      "reminderContent": "消息内容",
      "reminderUrl": "消息链接(包含itemId参数)"
    }
  }
}
```

**字段说明：**
- `1`: 主消息容器
- `1.2`: 会话标识符，格式为 `{cid}@goofish`
- `1.5`: 消息创建时间戳（毫秒级别）
- `1.10`: 消息详细信息容器
- `1.10.reminderTitle`: 发送者的显示昵称
- `1.10.senderUserId`: 发送者的用户ID
- `1.10.reminderContent`: 实际消息内容
- `1.10.reminderUrl`: 消息相关的URL，通常包含 `itemId=商品ID` 参数

### 1.2 系统状态消息格式

系统状态消息有两种主要格式：

#### 格式1：redReminder 格式
```json
{
  "3": {
    "redReminder": "系统状态提示文本"
  },
  "1": "用户ID@goofish"
}
```

#### 格式2：直接状态格式
```json
{
  "3": "系统状态文本",
  "1": "用户ID@goofish"
}
```

**常见系统状态值：**
- `等待买家付款`
- `等待卖家发货`
- `交易关闭`
- `买家已确认收货`
- `等待买家确认收货`
- `我已付款，等待你发货`

### 1.3 输入状态消息格式

用户正在输入时的消息格式：

```json
{
  "1": "消息ID.PNM",
  "2": 1,
  "3": 0,
  "4": "会话ID@goofish",
  "5": 1,
  "6": "时间戳"
}
```

**字段说明：**
- `1`: 消息ID，通常以 `.PNM` 结尾表示输入状态
- `2`: 消息类型标识
- `3`: 状态标识（0表示输入状态）
- `4`: 会话ID
- `5`: 消息标识
- `6`: 时间戳

### 1.4 同步消息格式

用于消息同步的格式：

```json
{
  "1": ["消息ID.PNM"],
  "2": 2,
  "3": "会话ID@goofish",
  "4": 1,
  "5": "时间戳"
}
```

## 2. 商品信息字段结构

### 2.1 原始商品信息 (API响应)

#### 基本商品信息
```json
{
  "api": "mtop.taobao.idle.pc.detail",
  "data": {
    "itemDO": {
      "itemId": 903708110158,
      "title": "商品标题",
      "desc": "商品描述",
      "soldPrice": "售价",
      "originalPrice": "原价",
      "transportFee": "运费",
      "GMT_CREATE_DATE_KEY": "创建时间",
      "browseCnt": 1816,
      "itemType": "商品类型",
      "pcSupportTrade": true
    }
  }
}
```

#### 商品图片信息
```json
{
  "imageInfos": [
    {
      "major": true,
      "url": "图片URL",
      "widthSize": 1920,
      "heightSize": 1440,
      "type": 0,
      "extraInfo": {
        "raw": "true",
        "isT": "false",
        "isH": "false"
      }
    }
  ]
}
```

#### 商品标签和属性
```json
{
  "itemLabelExtList": [
    {
      "channelCateId": 127388003,
      "labelId": "15465040",
      "labelType": "common",
      "from": "newPublishChoice",
      "text": "组装台机",
      "properties": "分类属性信息"
    }
  ],
  "priceRelativeTags": [
    {
      "text": "包邮",
      "bgColor": "#FFFFFF",
      "textColor": "#1F1F1F",
      "trackParams": {
        "tagType": "baoyou"
      }
    }
  ],
  "recommendTagList": [
    {
      "text": "近十五天降价53%",
      "labelId": "650"
    }
  ]
}
```

### 2.2 解析后的商品信息结构

```json
{
  "item_id": 903708110158,
  "title": "商品标题",
  "description": "商品描述",
  "price": "1500",
  "original_price": "0",
  "transport_fee": "0.00",
  "create_time": "2025-03-30 00:38:29",
  "item_status": "在线",
  "browse_count": 1817,
  "collect_count": 12,
  "want_count": 22,
  "favor_count": 0,
  "images": [
    {
      "url": "图片URL",
      "width": 1920,
      "height": 1440,
      "is_major": true,
      "type": 0
    }
  ],
  "main_image": "主图URL",
  "price_tags": ["包邮"],
  "common_tags": ["包邮"],
  "item_labels": ["组装台机", "Intel/英特尔"],
  "recommend_tags": ["近十五天降价53%"],
  "properties": {
    "品牌": "Intel/英特尔",
    "成色": "轻微使用痕迹",
    "功能状态": "功能完好无维修"
  },
  "seller": {
    "seller_id": 2279520684,
    "nickname": "卖家昵称",
    "city": "城市",
    "avatar": "头像URL"
  }
}
```

## 3. 用户信息字段结构

### 3.1 基础用户信息

```json
{
  "user_id": 2279520684,
  "nickname": "用户昵称",
  "avatar": "头像URL",
  "city": "所在城市",
  "register_time": "注册时间",
  "user_reg_day": 3386,
  "last_visit_time": "最后访问时间",
  "reply_ratio_24h": "24小时回复率",
  "reply_interval": "平均回复时间",
  "sold_items_count": 89,
  "total_items_count": 102,
  "zhima_auth": true,
  "credit_level": "信用等级",
  "seller_tags": ["标签列表"],
  "identity_tags": ["身份认证标签"]
}
```

### 3.2 用户卡片信息

```json
{
  "display_name": "显示昵称",
  "avatar_url": "头像URL",
  "location": "位置",
  "credit_badge": "信用徽章",
  "stats": {
    "sold_count": 89,
    "total_items": 102,
    "reply_rate": "88%",
    "reply_speed": "47分钟"
  },
  "verified": true,
  "last_online": "最后在线时间"
}
```

### 3.3 登录用户信息

```json
{
  "api": "mtop.taobao.idlemessage.pc.loginuser.get",
  "data": {
    "needDecryptKeys": [],
    "needDecryptKeysV2": [],
    "userId": 2279520684
  },
  "ret": ["SUCCESS::调用成功"],
  "v": "1.0"
}
```

## 4. 交易状态字段结构

### 4.1 WebSocket状态结果

```json
{
  "data_source": "websocket_messages",
  "official_status": "paid",
  "red_reminder": "我已付款，等待你发货",
  "confidence": 0.9,
  "user_id": "2279520684",
  "item_id": "933538443288",
  "conversation_stats": {
    "total_messages": 16,
    "user_messages": 9,
    "assistant_messages": 7,
    "latest_system_time": "2025-06-04T15:05:40.041000",
    "has_recent_activity": true
  },
  "recent_message_sample": [
    {
      "id": 105,
      "user_id": "用户ID",
      "item_id": "商品ID",
      "role": "assistant",
      "content": "消息内容",
      "timestamp": "时间戳"
    }
  ],
  "timestamp": 1749035982
}
```

### 4.2 官方交易状态映射

| 官方状态文本 | 标准状态码 | 状态说明 |
|-------------|-----------|----------|
| 等待买家付款 | unpaid | 待付款 |
| 我已付款，等待你发货 | paid | 已付款 |
| 等待卖家发货 | paid | 已付款 |
| 卖家已发货 | shipped | 已发货 |
| 等待买家确认收货 | shipped | 已发货 |
| 买家已确认收货 | completed | 交易完成 |
| 交易关闭 | closed | 交易关闭 |
| 交易取消 | cancelled | 交易取消 |

## 5. 消息数据库字段结构

### 5.1 聊天历史记录

```json
{
  "id": "记录ID",
  "user_id": "用户ID",
  "item_id": "商品ID",
  "role": "角色(user/assistant/system)",
  "content": "消息内容",
  "timestamp": "时间戳",
  "message_type": "消息类型",
  "metadata": {
    "cid": "会话ID",
    "original_data": "原始数据"
  }
}
```

### 5.2 商品数据库字段

```json
{
  "item_id": "商品ID",
  "title": "商品标题",
  "description": "商品描述",
  "price": "价格",
  "seller_id": "卖家ID",
  "category": "商品分类",
  "status": "商品状态",
  "images": "图片信息JSON",
  "properties": "商品属性JSON",
  "created_at": "创建时间",
  "updated_at": "更新时间"
}
```

## 6. API 响应字段结构

### 6.1 标准API响应格式

```json
{
  "api": "API接口名称",
  "data": {
    "具体数据字段": "数据值"
  },
  "ret": ["SUCCESS::调用成功"],
  "v": "版本号"
}
```

### 6.2 错误响应格式

```json
{
  "error": "错误信息",
  "error_code": "错误码",
  "retry_count": 3,
  "timestamp": "时间戳"
}
```

## 7. 字段使用注意事项

### 7.1 时间戳处理
- WebSocket消息时间戳为毫秒级别
- API响应中的时间通常为字符串格式
- 需要根据具体场景进行转换

### 7.2 ID字段类型
- 用户ID和商品ID通常为数字类型
- 在某些情况下可能以字符串形式传输
- 建议统一转换为字符串处理

### 7.3 消息内容过滤
- 系统消息需要特殊处理，不应触发AI回复
- 用户输入状态消息应该被过滤
- 重复消息需要去重处理

### 7.4 状态同步
- 官方状态消息具有权威性
- 需要建立状态映射表进行标准化
- 状态变更需要记录到数据库

## 8. 开发建议

### 8.1 字段解析策略
1. 优先使用官方API返回的结构化数据
2. WebSocket消息需要进行字段验证
3. 建立统一的数据模型进行字段映射

### 8.2 错误处理
1. 对缺失字段进行默认值处理
2. 建立字段验证机制
3. 记录异常字段结构用于调试

### 8.3 性能优化
1. 对频繁访问的字段进行缓存
2. 批量处理相似的字段解析操作
3. 建立字段索引提高查询效率

## 9. 版本更新记录

- **v1.0** (2025-06-17): 初始版本，包含基础字段解析
- 后续版本将根据闲鱼平台更新进行字段补充和修正

---

*本文档基于实际项目中的闲鱼数据结构分析得出，为闲鱼智能客服系统的功能开发提供字段参考。* 