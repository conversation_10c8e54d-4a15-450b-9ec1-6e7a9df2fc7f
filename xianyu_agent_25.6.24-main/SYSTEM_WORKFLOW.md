# 闲鱼智能客服系统 - 工作流程文档

## 📋 目录
- [整体业务流程图](#整体业务流程图)
- [模块间协作流程](#模块间协作流程)
- [各模块内部工作流程](#各模块内部工作流程)
- [详细流程解读](#详细流程解读)

---

## 🔄 整体业务流程图

### 主要业务流程

```mermaid
flowchart TD
    A[买家发送消息] --> B[闲鱼平台]
    B --> C[XianyuConnector<br/>WebSocket接收]
    
    C --> D{消息类型判断}
    D -->|用户消息| E[MessageRouter<br/>消息路由器]
    D -->|系统事件| F[系统事件处理]
    D -->|心跳包| G[心跳响应]
    
    E --> H{消息有效性检查}
    H -->|无效/过期| I[忽略消息]
    H -->|有效| J[ContextManager<br/>存储上下文]
    
    J --> K[ReplyGenerator<br/>回复生成器]
    K --> L[IntentEngine<br/>意图识别]
    
    L --> M{意图识别结果}
    M -->|价格议价| N[PriceStrategy<br/>价格策略]
    M -->|技术咨询| O[TechStrategy<br/>技术策略]
    M -->|通用服务| P[DefaultStrategy<br/>默认策略]
    M -->|需要人工| Q[HumanHandover<br/>人工介入]
    
    N --> R[生成价格回复]
    O --> S[生成技术回复]
    P --> T[生成通用回复]
    
    R --> U[ContextManager<br/>保存回复记录]
    S --> U
    T --> U
    
    U --> V[XianyuConnector<br/>发送回复]
    V --> W[闲鱼平台]
    W --> X[买家收到回复]
    
    Q --> Y[发送钉钉/微信通知]
    Q --> Z[返回转接提示]
    Z --> U
    
    style A fill:#e1f5fe
    style X fill:#e8f5e8
    style C fill:#fff3e0
    style E fill:#f3e5f5
    style K fill:#e0f2f1
    style L fill:#fce4ec
    style Q fill:#fff8e1
```

### 异常处理流程

```mermaid
flowchart TD
    A[异常发生] --> B{异常类型}
    
    B -->|WebSocket连接断开| C[自动重连机制]
    B -->|消息解析失败| D[记录错误日志]
    B -->|意图识别失败| E[使用默认策略]
    B -->|API调用失败| F[重试机制]
    B -->|数据库错误| G[降级处理]
    
    C --> H[等待5秒]
    H --> I[重新建立连接]
    I --> J{连接成功?}
    J -->|是| K[恢复正常服务]
    J -->|否| H
    
    D --> L[继续处理下一条消息]
    E --> M[生成通用回复]
    F --> N{重试次数<3?}
    N -->|是| O[延迟重试]
    N -->|否| P[使用缓存回复]
    G --> Q[使用内存缓存]
    
    style A fill:#ffebee
    style K fill:#e8f5e8
```

---

## 🤝 模块间协作流程

### 消息处理时序图

```mermaid
sequenceDiagram
    participant User as 买家
    participant Platform as 闲鱼平台
    participant Connector as XianyuConnector
    participant Router as MessageRouter
    participant Context as ContextManager
    participant Generator as ReplyGenerator
    participant Intent as IntentEngine
    participant Strategy as ReplyStrategy
    participant Handover as HumanHandover
    
    User->>Platform: 发送消息
    Platform->>Connector: WebSocket推送消息
    
    Note over Connector: 消息解析和验证
    Connector->>Connector: parse_message()
    Connector->>Connector: _is_sync_package()
    Connector->>Connector: decrypt()
    
    Connector->>Router: StandardMessage对象
    
    Note over Router: 消息路由处理
    Router->>Router: process_message()
    Router->>Router: _is_message_valid()
    Router->>Router: _is_message_fresh()
    
    Router->>Context: store_message()
    Context->>Context: save_message_to_db()
    Context-->>Router: 存储成功
    
    Router->>Generator: generate_reply()
    
    Note over Generator: 回复生成流程
    Generator->>Intent: identify_intent()
    Intent->>Intent: analyze_message()
    Intent->>Intent: extract_parameters()
    Intent-->>Generator: IntentResult对象
    
    Generator->>Generator: select_strategy()
    Generator->>Strategy: generate_response()
    
    alt 普通回复
        Strategy->>Strategy: _call_ai_api()
        Strategy-->>Generator: 回复内容
    else 需要人工介入
        Generator->>Handover: request_human_intervention()
        Handover->>Handover: send_notification()
        Handover-->>Generator: 转接提示
    end
    
    Generator-->>Router: 回复结果
    Router->>Context: save_reply()
    Router->>Connector: send_message()
    
    Note over Connector: 消息发送
    Connector->>Connector: _build_send_message()
    Connector->>Platform: WebSocket发送
    Platform->>User: 收到回复
```

### 数据流转图

```mermaid
flowchart LR
    A[原始WebSocket消息] --> B[StandardMessage对象]
    B --> C[MessageStatus枚举]
    C --> D[IntentResult对象]
    D --> E[ReplyContent字符串]
    E --> F[数据库记录]
    
    subgraph "数据结构"
        G[StandardMessage<br/>- message_id<br/>- chat_id<br/>- user_id<br/>- content<br/>- timestamp]
        H[IntentResult<br/>- intent_type<br/>- confidence<br/>- parameters<br/>- scene_config]
        I[MessageStatus<br/>- PROCESSED<br/>- IGNORED<br/>- ERROR<br/>- HUMAN_REQUIRED]
    end
    
    B -.-> G
    D -.-> H
    C -.-> I
```

---

## ⚙️ 各模块内部工作流程

### 1. XianyuConnector 平台连接器

```mermaid
flowchart TD
    A[WebSocket连接建立] --> B[发送注册消息]
    B --> C[发送同步状态消息]
    C --> D[启动心跳循环]
    
    D --> E[监听消息循环]
    E --> F{收到消息}
    F -->|心跳响应| G[更新心跳状态]
    F -->|同步包消息| H[消息解析流程]
    F -->|其他消息| I[发送ACK响应]
    
    H --> J[检查是否为同步包]
    J -->|是| K[解密消息数据]
    J -->|否| L[跳过处理]
    
    K --> M[判断消息类型]
    M -->|聊天消息| N[创建StandardMessage]
    M -->|系统消息| O[忽略处理]
    
    N --> P[回调主程序]
    
    G --> E
    I --> E
    L --> E
    O --> E
    
    style A fill:#e3f2fd
    style P fill:#e8f5e8
```

### 2. MessageRouter 消息路由器

```mermaid
flowchart TD
    A[接收StandardMessage] --> B[消息有效性检查]
    B -->|无效| C[返回IGNORED状态]
    B -->|有效| D[时效性检查]
    
    D -->|过期| E[返回IGNORED状态]
    D -->|有效| F[存储消息上下文]
    
    F --> G[根据消息类型路由]
    G -->|USER_MESSAGE| H[用户消息处理器]
    G -->|SYSTEM_EVENT| I[系统事件处理器]
    
    H --> J[调用回复生成器]
    J --> K{生成结果}
    K -->|成功| L[返回PROCESSED状态]
    K -->|失败| M[返回ERROR状态]
    K -->|需要人工| N[返回HUMAN_REQUIRED状态]
    
    I --> O[记录系统事件]
    O --> P[返回PROCESSED状态]
    
    style A fill:#f3e5f5
    style L fill:#e8f5e8
    style M fill:#ffebee
    style N fill:#fff8e1
```

### 3. IntentEngine 意图识别引擎

```mermaid
flowchart TD
    A[接收用户消息] --> B[加载场景配置]
    B --> C[消息预处理]
    C --> D[关键词匹配]
    
    D --> E[计算相关性得分]
    E --> F{得分 >= 阈值?}
    F -->|是| G[确定意图类型]
    F -->|否| H[使用默认意图]
    
    G --> I[提取参数信息]
    I --> J[构建IntentResult]
    
    H --> K[设置通用意图]
    K --> J
    
    J --> L[返回识别结果]
    
    subgraph "场景配置"
        M[价格议价场景]
        N[技术咨询场景]
        O[通用服务场景]
        P[人工介入场景]
    end
    
    B -.-> M
    B -.-> N
    B -.-> O
    B -.-> P
    
    style A fill:#fce4ec
    style L fill:#e8f5e8

### 4. ReplyGenerator 回复生成器

```mermaid
flowchart TD
    A[接收消息和意图结果] --> B[选择回复策略]
    B --> C{意图类型判断}

    C -->|price_negotiation| D[PriceStrategy]
    C -->|technical_inquiry| E[TechStrategy]
    C -->|general_service| F[DefaultStrategy]
    C -->|human_handover| G[HumanHandover]

    D --> H[价格策略处理]
    E --> I[技术策略处理]
    F --> J[默认策略处理]
    G --> K[人工介入处理]

    H --> L[调用AI API生成回复]
    I --> L
    J --> L

    L --> M{API调用成功?}
    M -->|是| N[返回生成的回复]
    M -->|否| O[使用备用回复]

    K --> P[发送通知]
    P --> Q[返回转接提示]

    O --> R[记录错误日志]
    R --> S[返回默认回复]

    style A fill:#e0f2f1
    style N fill:#e8f5e8
    style Q fill:#fff8e1
```

### 5. ContextManager 上下文管理器

```mermaid
flowchart TD
    A[数据库操作请求] --> B{操作类型}

    B -->|保存消息| C[save_message()]
    B -->|保存回复| D[save_reply()]
    B -->|获取历史| E[get_chat_history()]
    B -->|获取商品信息| F[get_item_info()]

    C --> G[构建消息记录]
    G --> H[插入messages表]
    H --> I[更新会话状态]

    D --> J[构建回复记录]
    J --> K[插入replies表]
    K --> L[更新统计信息]

    E --> M[查询历史记录]
    M --> N[按时间排序]
    N --> O[限制返回数量]

    F --> P[查询商品表]
    P --> Q[返回商品详情]

    I --> R[返回操作结果]
    L --> R
    O --> S[返回历史列表]
    Q --> T[返回商品信息]

    style A fill:#e3f2fd
    style R fill:#e8f5e8
    style S fill:#e8f5e8
    style T fill:#e8f5e8

### 6. HumanHandover 人工介入管理器

```mermaid
flowchart TD
    A[人工介入请求] --> B{通知功能启用?}

    B -->|是| C[构建通知消息]
    B -->|否| D[记录介入日志]

    C --> E{通知类型}
    E -->|钉钉| F[发送钉钉Webhook]
    E -->|微信| G[发送微信通知]
    E -->|邮件| H[发送邮件通知]

    F --> I[验证发送结果]
    G --> I
    H --> I

    I --> J{发送成功?}
    J -->|是| K[更新通知状态]
    J -->|否| L[记录发送失败]

    D --> M[生成转接提示]
    K --> M
    L --> M

    M --> N[返回处理结果]

    style A fill:#fff8e1
    style N fill:#e8f5e8

### 7. ReplyStrategy 回复策略

```mermaid
flowchart TD
    A[策略执行请求] --> B[加载策略配置]
    B --> C[构建AI提示词]

    C --> D[获取上下文信息]
    D --> E[获取商品信息]
    E --> F[获取用户历史]

    F --> G[组装完整提示]
    G --> H[调用AI API]

    H --> I{API响应状态}
    I -->|成功| J[解析AI回复]
    I -->|失败| K[使用备用策略]

    J --> L[后处理回复内容]
    L --> M[验证回复质量]

    M --> N{质量检查通过?}
    N -->|是| O[返回最终回复]
    N -->|否| P[重新生成]

    K --> Q[使用模板回复]
    Q --> R[个性化处理]
    R --> O

    P --> S{重试次数<3?}
    S -->|是| G
    S -->|否| Q

    style A fill:#f1f8e9
    style O fill:#e8f5e8

---

## 📖 详细流程解读

### 1. 整体业务流程解读

#### 消息接收阶段
**技术实现**：
- `XianyuConnector.listen_messages()` 建立WebSocket连接
- 使用 `wss://wss-goofish.dingtalk.com/` 作为连接URL
- 发送注册消息包含 `app-key`、`token`、`device-id` 等认证信息
- 启动15秒间隔的心跳机制维持连接

**关键决策点**：
- 消息类型判断：通过 `_is_sync_package()` 识别同步包消息
- 消息解密：使用 `decrypt()` 函数解密用户消息内容
- 有效性验证：检查消息时效性（默认5分钟过期）

#### 消息处理阶段
**技术实现**：
- `MessageRouter.process_message()` 进行消息路由
- `ContextManager.save_message()` 存储消息到SQLite数据库
- `IntentEngine.identify_intent()` 基于关键词和场景配置识别意图

**性能考虑**：
- 消息有效性检查避免处理过期消息
- 数据库连接池复用减少连接开销
- 意图识别使用缓存提升响应速度

#### 回复生成阶段
**技术实现**：
- `ReplyGenerator.generate_reply()` 选择合适的策略
- 各策略类继承 `BaseStrategy` 实现统一接口
- AI API调用使用 `openai` 客户端，支持重试机制

**优化点**：
- 策略选择基于意图置信度，避免误判
- API调用失败时使用模板回复作为降级方案
- 回复内容进行质量检查和后处理

### 2. 模块协作流程解读

#### 数据传递格式
**StandardMessage对象**：
```python
@dataclass
class StandardMessage:
    message_id: str          # 消息唯一标识
    chat_id: str            # 会话ID
    user_id: str            # 用户ID
    item_id: str            # 商品ID
    content: str            # 消息内容
    message_type: MessageType # 消息类型枚举
    timestamp: int          # 时间戳
    sender_name: str        # 发送者姓名
    platform_data: Dict    # 平台原始数据
```

**IntentResult对象**：
```python
@dataclass
class IntentResult:
    intent_type: str        # 意图类型
    confidence: float       # 置信度
    parameters: Dict        # 提取的参数
    scene_config: Dict      # 场景配置
    related_keywords: List  # 相关关键词
```

#### 接口调用规范
- 所有模块间调用使用异步方法（async/await）
- 错误处理统一使用异常机制，记录详细日志
- 数据验证在模块边界进行，确保数据完整性

### 3. 异常处理机制

#### WebSocket重连策略
- 连接断开后等待5秒重连
- 最大重连次数无限制，持续尝试
- 重连成功后重新发送注册和同步消息

#### API调用容错
- 单次调用超时时间30秒
- 失败重试最多3次，指数退避延迟
- 最终失败时使用预设模板回复

#### 数据库异常处理
- 连接失败时使用内存缓存
- 写入失败时记录到本地文件
- 定期同步缓存数据到数据库

### 4. 性能优化策略

#### 消息处理优化
- 消息队列缓冲高并发请求
- 批量数据库操作减少I/O次数
- 异步处理避免阻塞主线程

#### 内存管理
- 定期清理过期的上下文数据
- 限制历史消息缓存数量（默认100条）
- 使用弱引用避免循环引用

#### 网络优化
- WebSocket连接复用
- HTTP请求使用连接池
- 启用gzip压缩减少传输量

---

## 💻 关键代码实现示例

### 1. WebSocket消息接收和解析

```python
# XianyuConnector.listen_messages()
async def listen_messages(self, callback):
    """监听平台消息"""
    ws_url = 'wss://wss-goofish.dingtalk.com/'

    while True:
        try:
            headers = {
                "Cookie": self.config.get('cookies_str', ''),
                "Host": "wss-goofish.dingtalk.com",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }

            async with websockets.connect(ws_url, extra_headers=headers) as websocket:
                # 发送注册消息
                await self._send_init_message()

                # 监听消息循环
                async for message in websocket:
                    raw_data = json.loads(message)

                    # 处理心跳响应
                    if await self._handle_heartbeat_response(raw_data):
                        continue

                    # 发送ACK响应
                    await self._send_ack_response(raw_data)

                    # 解析并处理消息
                    standard_message = self.parse_message(raw_data)
                    if standard_message:
                        await callback(standard_message)

        except Exception as e:
            logger.error(f"WebSocket连接失败: {e}")
            await asyncio.sleep(5)  # 5秒后重连
```

### 2. 消息解析和验证

```python
# XianyuConnector.parse_message()
def parse_message(self, raw_message: Dict[str, Any]) -> Optional[StandardMessage]:
    """解析原始消息为标准格式"""
    try:
        # 检查是否为同步包消息
        if not self._is_sync_package(raw_message):
            return None

        # 获取并解密数据
        sync_data = raw_message["body"]["syncPushPackage"]["data"][0]
        data = sync_data["data"]

        # 解密消息内容
        from utils.xianyu_utils import decrypt
        decrypted_data = decrypt(data)
        message = json.loads(decrypted_data)

        # 判断消息类型
        if not self._is_chat_message(message):
            return None

        # 提取消息信息
        create_time = int(message["1"]["5"])
        send_user_name = message["1"]["10"]["reminderTitle"]
        send_user_id = message["1"]["10"]["senderUserId"]
        send_message = message["1"]["10"]["reminderContent"]

        # 时效性验证
        if (time.time() * 1000 - create_time) > self.message_expire_time:
            return None

        # 获取商品ID和会话ID
        url_info = message["1"]["10"]["reminderUrl"]
        item_id = url_info.split("itemId=")[1].split("&")[0]
        chat_id = message["1"]["2"].split('@')[0]

        return StandardMessage(
            message_id=generate_uuid(),
            chat_id=chat_id,
            user_id=send_user_id,
            item_id=item_id,
            content=send_message,
            message_type=MessageType.USER_MESSAGE,
            timestamp=create_time,
            sender_name=send_user_name,
            platform_data=raw_message
        )

    except Exception as e:
        logger.error(f"解析消息时出错: {e}")
        return None
```

### 3. 意图识别实现

```python
# IntentEngine.identify_intent()
async def identify_intent(self, message: StandardMessage) -> IntentResult:
    """识别用户意图"""
    try:
        content = message.content.lower()
        best_match = None
        best_score = 0.0

        # 遍历所有场景配置
        for scene_name, scene_config in self.scene_configs.items():
            score = 0.0
            matched_keywords = []

            # 关键词匹配
            for keyword in scene_config.get('keywords', []):
                if keyword.lower() in content:
                    score += 1.0
                    matched_keywords.append(keyword)

            # 计算相关性得分
            if len(scene_config.get('keywords', [])) > 0:
                score = score / len(scene_config['keywords'])

            # 更新最佳匹配
            if score > best_score and score >= self.threshold:
                best_score = score
                best_match = {
                    'scene_name': scene_name,
                    'scene_config': scene_config,
                    'matched_keywords': matched_keywords
                }

        # 构建结果
        if best_match:
            return IntentResult(
                intent_type=best_match['scene_name'],
                confidence=best_score,
                parameters=self._extract_parameters(content, best_match['scene_config']),
                scene_config=best_match['scene_config'],
                related_keywords=best_match['matched_keywords']
            )
        else:
            # 使用默认意图
            return IntentResult(
                intent_type='general_service',
                confidence=0.5,
                parameters={},
                scene_config=self.scene_configs.get('general_service', {}),
                related_keywords=[]
            )

    except Exception as e:
        logger.error(f"意图识别失败: {e}")
        return self._get_default_intent()
```

### 4. 回复策略执行

```python
# BaseStrategy.generate_response()
async def generate_response(self, message: StandardMessage, intent_result: IntentResult) -> str:
    """生成回复内容"""
    try:
        # 获取上下文信息
        context = await self.context_manager.get_chat_history(
            message.chat_id, limit=5
        )

        # 获取商品信息
        item_info = await self.context_manager.get_item_info(message.item_id)

        # 构建AI提示词
        prompt = self._build_prompt(message, intent_result, context, item_info)

        # 调用AI API
        response = await self._call_ai_api(prompt)

        # 后处理回复内容
        final_reply = self._post_process_reply(response)

        return final_reply

    except Exception as e:
        logger.error(f"生成回复失败: {e}")
        return self._get_fallback_reply(intent_result)

def _build_prompt(self, message, intent_result, context, item_info):
    """构建AI提示词"""
    prompt_template = self.strategy_config.get('prompt_template', '')

    # 替换模板变量
    prompt = prompt_template.format(
        user_message=message.content,
        user_name=message.sender_name,
        item_title=item_info.get('title', ''),
        item_price=item_info.get('price', ''),
        chat_history=self._format_chat_history(context),
        intent_type=intent_result.intent_type,
        parameters=json.dumps(intent_result.parameters, ensure_ascii=False)
    )

    return prompt
```

### 5. 数据库操作实现

```python
# ContextManager.save_message()
async def save_message(self, message: StandardMessage) -> bool:
    """保存消息到数据库"""
    try:
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT INTO messages (
                    message_id, chat_id, user_id, item_id, content,
                    message_type, timestamp, sender_name, platform_data
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                message.message_id,
                message.chat_id,
                message.user_id,
                message.item_id,
                message.content,
                message.message_type.value,
                message.timestamp,
                message.sender_name,
                json.dumps(message.platform_data)
            ))
            await db.commit()

        logger.debug(f"消息已保存: {message.message_id}")
        return True

    except Exception as e:
        logger.error(f"保存消息失败: {e}")
        return False

async def get_chat_history(self, chat_id: str, limit: int = 10) -> List[Dict]:
    """获取聊天历史"""
    try:
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT content, sender_name, timestamp, message_type
                FROM messages
                WHERE chat_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            """, (chat_id, limit))

            rows = await cursor.fetchall()

            history = []
            for row in rows:
                history.append({
                    'content': row[0],
                    'sender': row[1],
                    'timestamp': row[2],
                    'type': row[3]
                })

            return list(reversed(history))  # 按时间正序返回

    except Exception as e:
        logger.error(f"获取聊天历史失败: {e}")
        return []
```

---

## 🔧 配置和部署要点

### 1. 关键配置参数

```env
# WebSocket连接配置
WS_URL=wss://wss-goofish.dingtalk.com/
MESSAGE_EXPIRE_TIME=300000  # 消息过期时间(毫秒)
TOKEN_REFRESH_INTERVAL=3600  # Token刷新间隔(秒)

# 意图识别配置
RELATED_INTENT_THRESHOLD=0.6  # 意图相关性阈值
MAX_HISTORY=100  # 最大历史消息数

# AI API配置
MODEL_NAME=qwen-max
API_TIMEOUT=30  # API超时时间(秒)
MAX_RETRIES=3   # 最大重试次数
```

### 2. 性能监控指标

- **WebSocket连接状态**: 连接建立时间、重连次数
- **消息处理延迟**: 从接收到回复的端到端延迟
- **意图识别准确率**: 正确识别意图的比例
- **API调用成功率**: AI API调用的成功率
- **数据库操作性能**: 查询和写入的响应时间

### 3. 扩展性考虑

- **水平扩展**: 支持多实例部署，通过负载均衡分发请求
- **垂直扩展**: 增加CPU和内存资源提升单实例性能
- **存储扩展**: 支持从SQLite迁移到PostgreSQL/MySQL
- **缓存层**: 引入Redis缓存热点数据
- **消息队列**: 使用RabbitMQ/Kafka处理高并发消息

---

## 📊 系统监控和运维

### 监控大盘关键指标

```mermaid
graph LR
    A[系统监控] --> B[连接状态]
    A --> C[消息处理]
    A --> D[性能指标]
    A --> E[错误统计]

    B --> B1[WebSocket连接数]
    B --> B2[心跳成功率]
    B --> B3[重连次数]

    C --> C1[消息接收量]
    C --> C2[消息处理量]
    C --> C3[回复成功率]

    D --> D1[响应时间]
    D --> D2[CPU使用率]
    D --> D3[内存使用率]

    E --> E1[API调用失败]
    E --> E2[数据库错误]
    E --> E3[解析异常]
```

### 运维操作手册

1. **日常检查**：
   - 检查WebSocket连接状态
   - 监控消息处理量和响应时间
   - 查看错误日志和异常统计

2. **故障处理**：
   - WebSocket连接断开：检查网络和认证配置
   - API调用失败：检查API密钥和网络连接
   - 数据库异常：检查磁盘空间和数据库状态

3. **性能优化**：
   - 定期清理过期数据
   - 优化数据库索引
   - 调整缓存策略

**🎯 这份工作流程文档提供了系统运行的完整技术视图，是开发、运维和故障排查的重要参考资料！**
```
