闲鱼平台智能客服系统 - 模块化单体架构设计文档
本文档旨在为闲鱼智能客服系统的重构提供清晰的架构设计方案。新架构采用模块化单体模式，旨在实现系统的高内聚、低耦合，提升代码的可维护性、可测试性和未来可扩展性。

1. 架构概述
1.1. 核心目标 (Core Objectives)
本次架构重构的核心业务目标是 “最大限度地促成交易”。围绕此目标，架构设计需满足以下技术要求：

快速定位问题: 当系统出现故障时，能迅速将问题范围缩小到单一模块。

安全隔离修复: 对某一模块的修复不应影响其他模块，避免产生连锁反应。

灵活新增功能: 以“搭积木”的方式轻松添加新功能或新策略，无需大规模改动原有代码。

平台可扩展性: 能够快速更换或接入新的电商平台（如淘宝、拼多多），而无需重写核心业务逻辑。

1.2. 设计原则 (Design Principles)
单一职责原则 (SRP): 每个模块只做好一件事，且只有一个需要修改它的理由。

面向接口编程: 模块间通过预先定义的公共接口通信，隐藏内部实现细节。

依赖倒置: 高层业务模块不依赖于底层技术模块的具体实现，而是依赖于抽象。

明确的依赖关系: 建立单向、清晰的依赖流，避免循环依赖。

2. 系统流程图
下图展示了系统处理消息的核心流程。关键点在于 消息处理与路由模块 会对输入进行分类，将“用户消息”和“系统事件”分发到不同的处理路径。

graph TD
    subgraph A[外部平台]
        direction LR
        A1(买家)
        A2(闲鱼平台)
    end

    subgraph B[系统内部]
        B1[1. 平台连接器模块]
        B2[2. 消息处理与路由模块]
        B3[3. 上下文管理模块]
        B4[4. 意图识别模块]
        B5[5. 回复生成模块]
        B6[6. 人工介入模块]
    end

    C[7. 系统配置与监控模块]

    A1 -- 发送消息 --> A2
    A2 -- 推送原始数据 --> B1
    B1 -- 解密/标准化 --> B2
    B2 -- 存储消息 --> B3
    B2 -- "是用户消息？" --o B4
    B2 -- "是系统事件？" --x B5
    
    B4 -- 获取上下文 --> B3
    B4 -- 识别意图 --> B5
    
    B5 -- 获取上下文 --> B3
    B5 -- 生成回复/决策 --> B1
    B5 -- 需要人工介入 --> B6
    
    B1 -- 发送回复 --> A2
    A2 -- 显示给买家 --> A1

    B1 -- 获取配置 --> C
    B3 -- 获取配置 --> C
    B4 -- 获取配置 --> C
    B6 -- 获取配置 --> C

3. 模块化详述
系统被划分为七个核心职责模块：

3.1. 平台连接器模块 (PlatformConnector)
核心职责: 作为系统与外部平台的唯一接口，封装所有与特定平台通信的细节。

业务能力:

处理平台相关的认证和授权。

根据平台协议进行数据的加密与解密。

建立和维护与平台的长连接（如WebSocket）。

调用平台API（如获取商品信息、发送消息）。

依赖关系:

被调用: 消息处理与路由模块

依赖于: 系统配置与监控模块 (获取API密钥等配置)

对应源文件:

重构 XianyuApis.py 的功能并整合进来。

建议目录: modules/platform_connector/，内部可包含 base_connector.py (定义标准接口) 和 xianyu_connector.py (闲鱼的具体实现)。

3.2. 消息处理与路由模块 (MessageRouter)
核心职责: 系统的交通枢纽，对传入消息进行分类和分发。

业务能力:

从平台连接器获取干净、解密后的标准消息数据。

分类消息: 判断消息是“用户消息”还是“系统事件”。

智能路由:

将“用户消息”路由到意图识别模块。

将“系统事件”（如买家已付款）直接路由到回复生成模块。

依赖关系:

被调用: 由主程序循环 main.py 驱动。

依赖于: 平台连接器模块, 上下文管理模块, 意图识别模块, 回复生成模块。

3.3. 上下文管理模块 (ContextManager)
核心职责: 系统的记忆中心，负责所有数据的持久化存储和访问。

业务能力:

以会话ID (cid) 为核心，存储和读取完整的对话历史（包括用户消息和系统事件）。

管理商品信息、用户信息等上下文数据。

提供统一、干净的数据访问接口，如 get_chat_history(cid)。

依赖关系:

被调用: 消息处理与路由模块, 意图识别模块, 回复生成模块。

依赖于: 无。它是数据的最终提供者。

对应源文件:

基本对应现有的 context_manager.py，需围绕cid进行优化。

3.4. 意图识别模块 (IntentEngine)
核心职责: 系统的大脑，负责理解用户的自然语言消息。

业务能力:

结合消息内容和完整的对话历史上下文，分析用户意图。

从消息中提取关键参数（如价格、型号等）。

输出结构化的意图对象给下游模块。

依赖关系:

被调用: 消息处理与路由模块。

依赖于: 上下文管理模块 (获取上下文), 系统配置与监控模块 (获取LLM配置)。

对应源文件:

对应 enhanced_intent_engine.py 及 scene_config/ 目录。

3.5. 回复生成模块 (ReplyGenerator)
核心职责: 系统的口才，根据意图或事件决策并生成回复。

业务能力:

内置可插拔的策略单元 (Strategy)，根据输入类型选择合适的策略。

处理“用户消息”的意图（如价格谈判策略、产品问答策略）。

处理“系统事件”的触发（如付款成功后的感谢策略）。

调用人工介入模块执行转接。

依赖关系:

被调用: 消息处理与路由模块。

依赖于: 上下文管理模块 (确保回复连贯), 人工介入模块。

对应源文件:

将取代 XianyuAgent.py 中的核心逻辑。

建议目录: modules/reply_strategies/，包含各种策略的实现文件。

3.6. 人工介入模块 (HumanHandover)
核心职责: 专门处理所有需要人工干预的场景和后续操作。

业务能力:

向真人发送通知（通过钉钉、微信等）。

在前端UI上高亮或锁定特定会话。

暂停对该会话的自动回复。

依赖关系:

被调用: 回复生成模块。

依赖于: 系统配置与监控模块 (获取通知渠道的配置)。

3.7. 系统配置与监控模块 (Config & Monitoring)
核心职责: 系统的后勤保障部，提供配置管理和日志记录。

业务能力:

加载环境变量和配置文件。

为所有其他模块提供统一的配置信息访问接口。

初始化全局日志系统，方便调试和监控。

依赖关系:

被调用: 被几乎所有其他模块依赖。

依赖于: 无。

4. 推荐项目目录结构
为了配合上述架构，推荐采用以下项目文件结构：

/xianyu-agent-refactored
|
|-- main.py                     # 应用主入口，负责启动和组装模块
|
|-- core/                       # 核心编排逻辑
|   |-- __init__.py
|   |-- message_router.py       # 消息处理与路由模块
|   `-- reply_generator.py      # 回复生成模块（策略调度器）
|
|-- modules/                    # 可插拔的功能模块
|   |-- __init__.py
|   |-- platform_connector/     # 平台连接器模块
|   |   |-- __init__.py
|   |   |-- base_connector.py
|   |   `-- xianyu_connector.py
|   |-- context_manager/        # 上下文管理模块
|   |   |-- __init__.py
|   |   `-- db_handler.py
|   |-- intent_engine/          # 意图识别模块
|   |   |-- __init__.py
|   |   `-- engine.py
|   |-- reply_strategies/       # 各种回复策略单元
|   |   |-- __init__.py
|   |   |-- base_strategy.py
|   |   |-- price_strategy.py
|   |   |-- product_qa_strategy.py
|   |   `-- system_event_strategy.py
|   `-- human_handover/         # 人工介入模块
|       |-- __init__.py
|       `-- notifier.py
|
|-- config/                     # 系统配置与监控模块
|   |-- __init__.py
|   |-- settings.py
|   `-- logger_config.py
|
|-- scene_config/               # 意图识别的场景模板
|   `-- xianyu_scene_templates.json
|
|-- tests/                      # 模块独立测试目录
|   |-- test_context_manager.py
|   |-- test_reply_generator.py
|   `-- ...
|
|-- .env                        # 环境变量
|-- requirements.txt
`-- README.md

5. 总结
本架构方案通过明确的职责划分和清晰的依赖管理，将复杂的业务逻辑解耦到独立的模块中。这不仅能有效解决当前痛点，更能为系统未来的发展和迭代打下坚实的基础，确保系统长期保持健康、有序的演进。