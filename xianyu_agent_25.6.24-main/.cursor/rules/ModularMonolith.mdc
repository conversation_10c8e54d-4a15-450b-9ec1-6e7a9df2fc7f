---
description: 
globs: 
alwaysApply: false
---
# 模块化单体架构核心规则 (Consolidated)

## 1. 核心设计哲学

本仓库遵循**模块化单体架构（Modular Monolith）**。我们的最高目标是在**单一可部署单元**的内部，实现业务模块间的**“高内聚、低耦合”**。

AI在进行任何代码修改或添加时，必须将维护模块的清晰边界和独立性作为首要任务。所有设计决策都应服务于此目标。

---

## 2. 项目目录结构设计

所有代码都必须严格遵守以下目录结构。这是我们架构的物理体现。

MyModularMonolith/
└── src/
├── Modules/                  <-- [核心] 所有业务模块的根目录
│   ├── Ordering/             <-- “订单”模块：代表一个独立的业务领域
│   │   ├── Api/              <-- 模块的公共入口 (REST控制器, 服务接口)
│   │   ├── Application/      <-- 应用服务层 (用例流程编排)
│   │   ├── Domain/           <-- 领域层 (核心业务规则, 实体)
│   │   └── Infrastructure/   <-- 基础设施层 (数据库、外部服务实现)
│   │
│   ├── Catalog/              <-- “商品目录”模块 (结构同上)
│   └── ...                   <-- 其他业务模块
│
├── SharedKernel/             <-- [共享] 跨模块共享的、非业务性的核心代码
│
└── Host/                     <-- [宿主] 应用的启动和组装项目


- **`Modules/`**: 包含所有独立的业务功能模块。每个子目录都是一个**限界上下文（Bounded Context）**。
- **`SharedKernel/`**: 包含被多个模块共享的基础代码。**必须保持精简**，严禁包含任何业务逻辑。
- **`Host/`**: 轻量级的启动项目，负责引用所有模块，配置依赖注入（DI）和服务，是整个应用的“组装线”。

---

## 3. 核心架构规则

以下是必须强制执行的架构规则。

### **规则 3.1: 模块边界与依赖**

- **核心原则**: 每个模块都是一个独立的“微型应用”。严禁任何形式的跨模块内部耦合。
- **规则详情**:
    1. 一个模块的任何代码（包括`Application`, `Domain`, `Infrastructure`层）**【严禁】**直接引用另一个模块的相应内部层。
    2. 一个模块如果需要与另一个模块交互，**【必须】**通过依赖注入另一个模块在`Api/`层中定义的**公共接口**。

- **✅ 正确示例 (通过接口依赖):**
  ```csharp
  // Ordering.Application 层
  public class PlaceOrderHandler {
      private readonly ICatalogApi _catalogApi; // 通过DI注入Catalog模块的公共接口
  
      public PlaceOrderHandler(ICatalogApi catalogApi) { _catalogApi = catalogApi; }
  
      public async Task Handle(PlaceOrderCommand command) {
          // 调用公共接口获取信息
          var product = await _catalogApi.GetProductById(command.ProductId);
          // ...
      }
  }

❌ 错误示例 (直接依赖内部实现):
// Ordering.Application 层
public class PlaceOrderHandler {
    // 错误！直接依赖了Catalog模块的仓储，严重违反边界规则
    private readonly IProductRepository _productRepository; 
    // ...
}

规则 3.2: 模块间通信
核心原则: 为了维持模块的低耦合，模块间的通信必须受控。优先选择异步消息机制。

允许的通信方式:

异步事件驱动 (首选): 一个模块完成操作后，发布一个领域事件。其他模块订阅这些事件并作出响应。这通过进程内事件总线或中介者模式（Mediator）实现。
同步接口调用 (次选): 当需要立即获得结果时，可以同步调用另一个模块的公共API接口。
✅ 正确示例 (发布事件):
// Ordering.Domain 层
var order = Order.Create(...);
order.AddDomainEvent(new OrderPlacedEvent(order.Id));
await _orderRepository.Save(order); // 仓储实现负责发布事件
后续，Payment模块或Notification模块的事件处理器将异步处理此事件。

❌ 错误示例 (直接实例化并调用服务):
// Ordering.Application 层
public async Task Handle(PlaceOrderCommand command) {
    // ... 创建订单 ...

    // 错误！不应直接实例化并调用其他模块的内部服务
    var paymentService = new PaymentService(); 
    await paymentService.ProcessPaymentForOrder(order.Id);
}

规则 3.3: 数据隔离
核心原则: 每个模块在逻辑上拥有自己的数据。这是未来能够将模块平滑演进为微服务的基础。

规则详情:

【严禁】 在数据库层面，一个模块的表不能与另一个模块的表建立外键（Foreign Key）约束。
【严禁】 一个模块的代码不能直接查询或操作属于另一个模块的数据库表。数据关联必须通过模块API完成。
✅ 正确示例 (通过API获取关联数据):
场景：订单详情页面需要显示商品名称。
1. Ordering模块的数据库中只存储ProductId。
2. Ordering模块的服务先从自己数据库查出订单（包含ProductId）。
3. 然后，调用Catalog模块的公共API (ICatalogApi.GetProductById(productId)) 来获取商品名称。
4. 最后，在Ordering模块中将两者数据组合成一个DTO返回。

❌ 错误示例 (跨模块数据库JOIN):
// Ordering.Infrastructure 层
// 错误！在订单查询中直接JOIN了商品目录的表
var query = from order in _context.Orders
            join product in _context.Products on order.ProductId equals product.Id // 绝对禁止！
            select new OrderDetailsDto { ... };

规则 3.4: 模块内部的依赖流
核心原则: 模块内部必须遵循“整洁架构”或“六边形架构”的依赖规则，确保业务核心的纯粹性。

依赖方向: Api -> Application -> Domain <- Infrastructure
Domain (领域层): 是核心，不依赖任何其他层。
Application (应用层): 依赖于Domain层。
Infrastructure (基础设施层): 依赖于Domain和Application层，负责实现它们定义的接口。
Api (API层): 依赖于Application层。
AI 指导: 在生成任何新类时，请严格检查其using语句，确保没有违反上述依赖规则。
例如，Domain层的任何文件中都不应出现using MyProject.Modules.Ordering.Application;。

4. 自动化架构测试
核心原则: 架构规则不仅是文档，更应该是可以被自动验证的代码。
AI 职责:
遵守测试: 本项目使用NetArchTest（.NET）或ArchUnit（Java）等工具来强制执行上述规则。当你的修改导致架构测试失败时，你必须修复代码以符合规则。
维护测试: 如果你引入了新的、合理的依赖关系，你需要相应地更新架构测试文件（通常在tests/ArchitectureTests目录下），并说明原因。

5. AI 交互指南
优先解耦: 当被要求实现一个新功能时，请始终优先考虑如何保持模块间的解耦，即使这意味着需要编写更多的代码（例如，通过事件通信而不是直接调用）。
确认边界: 如果一个功能需求模糊，可能跨越多个模块，请主动提问以澄清职责边界。例如：“这个‘生成报告’的功能，数据源来自Sales模块和Inventory模块，您认为这个功能应该属于哪个模块，还是应该创建一个新的Reporting模块？”
遵循先例: 在添加新功能时，请参考项目内现有模块的实现方式，保持一致性。