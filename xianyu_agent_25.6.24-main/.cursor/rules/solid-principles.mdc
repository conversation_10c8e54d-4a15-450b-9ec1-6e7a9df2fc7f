---
description: 
globs: 
alwaysApply: false
---
# SOLID 设计原则核心规则

## 1. 核心设计哲学

本项目的代码质量基石是 **SOLID** 五大设计原则。所有新生成的代码和重构都必须严格遵循这些原则，以创建高内聚、低耦合、可维护、可扩展的软件系统。

AI在进行任何代码修改或添加时，应主动思考并应用这些原则。当发现现有代码违反了SOLID原则时，应在重构建议中指出。

---

## 2. SOLID 五大原则详解

### **[S] - 单一职责原则 (Single Responsibility Principle - SRP)**

> **核心思想**: 一个类（或模块、函数）应该只有一个引起它变化的原因。

- **AI 指导**: 在创建或修改一个类时，问自己：“这个类承担了多少种职责？”如果答案多于一种（例如，它既处理业务逻辑又负责数据持久化，或者既负责数据转换又负责日志记录），就应该将其拆分。

- **✅ 正确示例 (职责分离):**
  ```csharp
  // 职责一：处理订单核心业务逻辑
  public class OrderService 
  {
      private readonly IOrderRepository _repository;
      private readonly ILogger _logger;

      public OrderService(IOrderRepository repository, ILogger logger)
      {
          _repository = repository;
          _logger = logger;
      }

      public void CreateOrder(OrderData data)
      {
          // 核心业务逻辑...
          var order = new Order(data);
          _repository.Save(order);
          _logger.LogInfo("Order created: " + order.Id);
      }
  }

  // 职责二：将订单导出为特定格式
  public class OrderExporter
  {
      public string ToJson(Order order)
      {
          // 仅负责序列化...
          return JsonConvert.SerializeObject(order);
      }
  }
  ```

- **❌ 错误示例 (职责耦合):**
  ```csharp
  public class Order
  {
      public void CreateOrder(OrderData data) { /* 业务逻辑... */ }
      public void SaveToDatabase() { /* 持久化逻辑... */ }
      public string ToJson() { /* 格式转换逻辑... */ }
      public void LogCreation() { /* 日志记录逻辑... */ }
  }
  ```
  *这个`Order`类有四个变化的原因：业务规则、数据库、JSON格式、日志策略。这是严重违规。*

---

### **[O] - 开放/封闭原则 (Open/Closed Principle - OCP)**

> **核心思想**: 软件实体（类、模块、函数等）应该对扩展开放，对修改封闭。

- **AI 指导**: 当需要添加新功能时，应尽可能通过添加新代码（如实现新子类或新策略）来完成，而不是修改现有且稳定的核心代码。多使用抽象（接口、抽象类）和依赖注入。

- **✅ 正确示例 (使用策略模式扩展):**
  ```csharp
  // 定义一个稳定的支付策略接口
  public interface IPaymentStrategy
  {
      void ProcessPayment(decimal amount);
  }

  // 不同的支付实现
  public class CreditCardPayment : IPaymentStrategy { /* ... */ }
  public class PayPalPayment : IPaymentStrategy { /* ... */ }
  
  // 新增支付方式时，只需添加新类
  public class CryptoPayment : IPaymentStrategy { /* ... */ }

  // 核心处理类对修改封闭，对扩展开放
  public class PaymentProcessor
  {
      public void Process(IPaymentStrategy strategy, decimal amount)
      {
          strategy.ProcessPayment(amount);
      }
  }
  ```

- **❌ 错误示例 (通过修改核心代码扩展):**
  ```csharp
  public class PaymentProcessor
  {
      public void Process(string paymentType, decimal amount)
      {
          // 每增加一种支付方式，都必须修改这个switch语句
          switch (paymentType)
          {
              case "CreditCard":
                  // ... 信用卡逻辑
                  break;
              case "PayPal":
                  // ... PayPal逻辑
                  break;
              // 如果要新增Crypto支付，必须在这里添加 "case 'Crypto':"
          }
      }
  }
  ```

---

### **[L] - 里氏替换原则 (Liskov Substitution Principle - LSP)**

> **核心思想**: 所有引用基类的地方必须能透明地使用其子类的对象，而程序行为不发生改变。子类必须能够替换掉它们的基类。

- **AI 指导**: 在创建继承关系时，确保子类没有“缩小”基类的行为范围，没有改变基类方法原有的意图，也没有抛出基类方法预期之外的异常。子类应该是对基类的“is-a”关系的真正实现。

- **✅ 正确示例 (子类可替换基类):**
  ```csharp
  public abstract class Bird
  {
      public abstract void Move();
  }

  public class Sparrow : Bird // 麻雀是一种鸟
  {
      public override void Move() { /* Fly logic */ }
  }

  public class Penguin : Bird // 企鹅是一种鸟
  {
      public override void Move() { /* Swim logic */ }
  }
  ```
  *任何需要`Bird`对象的地方，传入`Sparrow`或`Penguin`都不会出错，因为它们都实现了`Move`。*

- **❌ 错误示例 (经典的“正方形是长方形”问题):**
  ```csharp
  public class Rectangle
  {
      public virtual int Width { get; set; }
      public virtual int Height { get; set; }
      public int Area => Width * Height;
  }

  public class Square : Rectangle
  {
      public override int Width
      {
          set { base.Width = base.Height = value; }
      }
      public override int Height
      {
          set { base.Width = base.Height = value; }
      }
  }

  // 使用者期望设置Width不影响Height
  public void Test(Rectangle r)
  {
      r.Width = 5;
      r.Height = 10;
      Assert.Equal(50, r.Area); // 如果传入Square对象，Area会是100，测试失败！
  }
  ```

---

### **[I] - 接口隔离原则 (Interface Segregation Principle - ISP)**

> **核心思想**: 客户端不应该被强迫依赖于它们不使用的方法。应使用多个小的、专用的接口，而不是一个大的、通用的接口。

- **AI 指导**: 在定义接口时，使其尽可能小而专注。如果一个类实现了某个接口，却不得不为空着一些方法，或者抛出`NotImplementedException`，这通常意味着接口需要被拆分。

- **✅ 正确示例 (拆分小接口):**
  ```csharp
  public interface IPrinter { void Print(Document d); }
  public interface IScanner { void Scan(Document d); }
  public interface IFax { void Fax(Document d); }

  // 一个简单的打印机，只需实现一个接口
  public class SimplePrinter : IPrinter { /* ... */ }

  // 一个多功能一体机，实现多个接口
  public class MultiFunctionPrinter : IPrinter, IScanner, IFax { /* ... */ }
  ```

- **❌ 错误示例 (臃肿的“胖”接口):**
  ```csharp
  public interface IMultiFunctionDevice
  {
      void Print(Document d);
      void Scan(Document d);
      void Fax(Document d);
  }

  // 这个简单的打印机被迫要实现它根本没有的功能
  public class SimplePrinter : IMultiFunctionDevice
  {
      public void Print(Document d) { /* ... */ }
      public void Scan(Document d) { throw new NotImplementedException(); } // 客户端不应依赖此方法
      public void Fax(Document d) { throw new NotImplementedException(); } // 客户端不应依赖此方法
  }
  ```

---

### **[D] - 依赖倒置原则 (Dependency Inversion Principle - DIP)**

> **核心思想**: 高层模块不应该依赖于低层模块，两者都应该依赖于抽象。抽象不应该依赖于细节，细节应该依赖于抽象。

- **AI 指导**: 这是实现“插件式”架构和解耦的关键。不要直接`new`一个具体的底层服务，而是通过构造函数注入一个接口。高层逻辑（如应用服务）定义它需要什么（接口），而底层实现（如基础设施）则提供具体的实现。

- **✅ 正确示例 (依赖于抽象接口):**
  ```csharp
  // 定义一个抽象（接口）
  public interface INotificationService
  {
      void Send(string message);
  }

  // 高层模块依赖于此接口
  public class HighLevelPolicy
  {
      private readonly INotificationService _notifier;

      public HighLevelPolicy(INotificationService notifier) // 通过DI注入
      {
          _notifier = notifier;
      }

      public void Execute()
      {
          // ...
          _notifier.Send("Policy executed.");
      }
  }

  // 低层模块实现此接口
  public class EmailNotifier : INotificationService { /* ... */ }
  public class SmsNotifier : INotificationService { /* ... */ }
  ```
  *`HighLevelPolicy`完全不知道通知是通过Email还是SMS发送的，可以轻松替换实现。*

- **❌ 错误示例 (高层模块依赖于具体实现):**
  ```csharp
  // 低层模块的具体实现
  public class EmailNotifier
  {
      public void SendEmail(string message) { /* ... */ }
  }

  // 高层模块直接依赖（new）了低层模块
  public class HighLevelPolicy
  {
      private readonly EmailNotifier _notifier;

      public HighLevelPolicy()
      {
          _notifier = new EmailNotifier(); // 强耦合！
      }

      public void Execute()
      {
          // ...
          _notifier.SendEmail("Policy executed.");
      }
  }
  ```
  *如果要换成短信通知，必须修改`HighLevelPolicy`这个高层模块。*
```