{"data_mtime": 1754228257, "dep_lines": [3, 1, 2, 4, 5, 6, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 30], "dependencies": ["collections.abc", "sys", "_typeshed", "types", "typing", "typing_extensions", "builtins", "abc"], "hash": "b1c1f190ecf7207b4d1806753bf63d5707ba758f36ecda1909ab086ac5a78b8e", "id": "traceback", "ignore_all": true, "interface_hash": "bb593c374ba53be01cfec1cc2a90221eafa6b74da903cf262b3d0060721c67b0", "mtime": 1754207148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/mypy/typeshed/stdlib/traceback.pyi", "plugin_data": null, "size": 8993, "suppressed": [], "version_id": "1.7.1"}