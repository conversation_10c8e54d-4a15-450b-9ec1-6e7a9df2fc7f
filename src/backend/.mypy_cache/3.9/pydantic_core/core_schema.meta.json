{"data_mtime": 1754228257, "dep_lines": [10, 6, 8, 9, 11, 12, 13, 15, 33, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "__future__", "sys", "warnings", "datetime", "decimal", "typing", "typing_extensions", "pydantic_core", "builtins", "_decimal", "_operator", "_typeshed", "abc", "pydantic_core._pydantic_core"], "hash": "eeb1ca7a07afcd5139748846cefe93d5d58245ba696eba83667084d2d1d26fb2", "id": "pydantic_core.core_schema", "ignore_all": true, "interface_hash": "666bd53f61376aedf58117625db2d42c935223c221d72dd3b5399589867a63f0", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic_core/core_schema.py", "plugin_data": null, "size": 132810, "suppressed": [], "version_id": "1.7.1"}