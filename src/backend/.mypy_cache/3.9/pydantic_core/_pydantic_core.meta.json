{"data_mtime": 1754228257, "dep_lines": [8, 1, 3, 4, 5, 7, 16, 20, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 5, 30], "dependencies": ["pydantic_core.core_schema", "__future__", "datetime", "sys", "typing", "pydantic_core", "typing_extensions", "_typeshed", "builtins", "abc"], "hash": "3f09a1a4f299ed0457764dae2800f501e924748638802f587fc313e9e18a8974", "id": "pydantic_core._pydantic_core", "ignore_all": true, "interface_hash": "fa01fce9fbad06537895a83f26a48daa8c3f5c08350b5d45ad26dc3b1fc70714", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic_core/_pydantic_core.pyi", "plugin_data": null, "size": 32260, "suppressed": [], "version_id": "1.7.1"}