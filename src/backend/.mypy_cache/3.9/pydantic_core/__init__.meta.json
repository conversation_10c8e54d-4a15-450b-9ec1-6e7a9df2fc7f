{"data_mtime": 1754228257, "dep_lines": [6, 30, 1, 3, 4, 33, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 30, 30], "dependencies": ["pydantic_core._pydantic_core", "pydantic_core.core_schema", "__future__", "sys", "typing", "typing_extensions", "builtins", "_typeshed", "abc"], "hash": "1fe3af8dc2d56673f8bf80d0e82017206a9737fd9b2b73c8108bf595c1f91da4", "id": "pydantic_core", "ignore_all": true, "interface_hash": "ca4ce64cc0aa32446b72c0a4a6eece74c599435b28d2fe18249b31464fd5c3d4", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic_core/__init__.py", "plugin_data": null, "size": 4197, "suppressed": [], "version_id": "1.7.1"}