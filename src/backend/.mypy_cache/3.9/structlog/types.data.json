{".class": "MypyFile", "_fullname": "structlog.types", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BindableLogger": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.BindableLogger", "kind": "Gdef"}, "Context": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.Context", "kind": "Gdef"}, "EventDict": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.EventDict", "kind": "Gdef"}, "ExcInfo": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.ExcInfo", "kind": "Gdef"}, "ExceptionRenderer": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.ExceptionRenderer", "kind": "Gdef"}, "ExceptionTransformer": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.ExceptionTransformer", "kind": "Gdef"}, "FilteringBoundLogger": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.FilteringBoundLogger", "kind": "Gdef"}, "Processor": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.Processor", "kind": "Gdef"}, "WrappedLogger": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.WrappedLogger", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.types.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.types.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.types.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.types.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.types.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.types.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/types.py"}