{".class": "MypyFile", "_fullname": "structlog.contextvars", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BindableLogger": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.BindableLogger", "kind": "Gdef"}, "EventDict": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.EventDict", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "STRUCTLOG_KEY_PREFIX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.contextvars.STRUCTLOG_KEY_PREFIX", "name": "STRUCTLOG_KEY_PREFIX", "type": "builtins.str"}}, "STRUCTLOG_KEY_PREFIX_LEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.contextvars.STRUCTLOG_KEY_PREFIX_LEN", "name": "STRUCTLOG_KEY_PREFIX_LEN", "type": "builtins.int"}}, "WrappedLogger": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.WrappedLogger", "kind": "Gdef"}, "_CONTEXT_VARS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "structlog.contextvars._CONTEXT_VARS", "name": "_CONTEXT_VARS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "contextvars.ContextVar"}], "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.contextvars.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.contextvars.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.contextvars.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.contextvars.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.contextvars.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "bind_contextvars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.contextvars.bind_contextvars", "name": "bind_contextvars", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kw"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind_contextvars", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "contextvars.Token"}], "type_ref": "typing.Mapping"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "bound_contextvars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "structlog.contextvars.bound_contextvars", "name": "bound_contextvars", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kw"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bound_contextvars", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "structlog.contextvars.bound_contextvars", "name": "bound_contextvars", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kw"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bound_contextvars", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "clear_contextvars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.contextvars.clear_contextvars", "name": "clear_contextvars", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_contextvars", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "contextvars": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "get_contextvars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.contextvars.get_contextvars", "name": "get_contextvars", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_contextvars", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_merged_contextvars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["bound_logger"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.contextvars.get_merged_contextvars", "name": "get_merged_contextvars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["bound_logger"], "arg_types": ["structlog.typing.BindableLogger"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_merged_contextvars", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "merge_contextvars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["logger", "method_name", "event_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.contextvars.merge_contextvars", "name": "merge_contextvars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["logger", "method_name", "event_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge_contextvars", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "reset_contextvars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.contextvars.reset_contextvars", "name": "reset_contextvars", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kw"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "contextvars.Token"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset_contextvars", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "structlog": {".class": "SymbolTableNode", "cross_ref": "structlog", "kind": "Gdef"}, "unbind_contextvars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.contextvars.unbind_contextvars", "name": "unbind_contextvars", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["keys"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unbind_contextvars", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/contextvars.py"}