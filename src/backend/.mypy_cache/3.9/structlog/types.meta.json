{"data_mtime": 1754228257, "dep_lines": [15, 13, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30], "dependencies": ["structlog.typing", "__future__", "builtins", "abc", "typing"], "hash": "4b35bfa5daa1b58940f5074c4dad35f476cc5d9c657fc6f02f60546a227ea6fc", "id": "structlog.types", "ignore_all": true, "interface_hash": "00ac194b05f38220d75805478fd41028428bbc57ee132ca209025f8ed974b20d", "mtime": 1754207014, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/types.py", "plugin_data": null, "size": 764, "suppressed": [], "version_id": "1.7.1"}