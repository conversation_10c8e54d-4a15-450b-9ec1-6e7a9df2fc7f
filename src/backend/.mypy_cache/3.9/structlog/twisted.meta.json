{"data_mtime": 1754228260, "dep_lines": [25, 26, 27, 28, 29, 13, 15, 16, 18, 1, 1, 21, 22, 20, 23], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 30, 5, 5, 5, 5], "dependencies": ["structlog._base", "structlog._config", "structlog._utils", "structlog.processors", "structlog.typing", "__future__", "json", "sys", "typing", "builtins", "abc"], "hash": "86c33d0cf5eb0086e074be5afef1097e6dfd3d940fd093b73588b86c7b6acd0a", "id": "structlog.twisted", "ignore_all": true, "interface_hash": "4742a2942adf660ef58ac10c54dac0207449cc64e17adf73bbf51cc9b3978c0a", "mtime": 1754207014, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/twisted.py", "plugin_data": null, "size": 10191, "suppressed": ["twisted.python.failure", "twisted.python.log", "twisted.python", "zope.interface"], "version_id": "1.7.1"}