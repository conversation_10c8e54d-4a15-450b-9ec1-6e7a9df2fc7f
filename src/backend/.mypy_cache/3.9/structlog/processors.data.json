{".class": "MypyFile", "_fullname": "structlog.processors", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "CallsiteParameter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.processors.CallsiteParameter", "name": "CallsiteParameter", "type_vars": []}, "deletable_attributes": [], "flags": ["is_enum"], "fullname": "structlog.processors.CallsiteParameter", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "structlog.processors", "mro": ["structlog.processors.CallsiteParameter", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "FILENAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.processors.CallsiteParameter.FILENAME", "name": "FILENAME", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "filename"}, "type_ref": "builtins.str"}}}, "FUNC_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.processors.CallsiteParameter.FUNC_NAME", "name": "FUNC_NAME", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "func_name"}, "type_ref": "builtins.str"}}}, "LINENO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.processors.CallsiteParameter.LINENO", "name": "LINENO", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "lineno"}, "type_ref": "builtins.str"}}}, "MODULE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.processors.CallsiteParameter.MODULE", "name": "MODULE", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "module"}, "type_ref": "builtins.str"}}}, "PATHNAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.processors.CallsiteParameter.PATHNAME", "name": "PATHNAME", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "pathname"}, "type_ref": "builtins.str"}}}, "PROCESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.processors.CallsiteParameter.PROCESS", "name": "PROCESS", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "process"}, "type_ref": "builtins.str"}}}, "PROCESS_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.processors.CallsiteParameter.PROCESS_NAME", "name": "PROCESS_NAME", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "process_name"}, "type_ref": "builtins.str"}}}, "THREAD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.processors.CallsiteParameter.THREAD", "name": "THREAD", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "thread"}, "type_ref": "builtins.str"}}}, "THREAD_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.processors.CallsiteParameter.THREAD_NAME", "name": "THREAD_NAME", "type": {".class": "Instance", "args": [], "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "thread_name"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.processors.CallsiteParameter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CallsiteParameterAdder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.processors.CallsiteParameterAdder", "name": "CallsiteParameterAdder", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog.processors.CallsiteParameterAdder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.processors", "mro": ["structlog.processors.CallsiteParameterAdder", "builtins.object"], "names": {".class": "SymbolTable", "_RecordMapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping", "name": "_RecordMapping", "type_vars": []}, "deletable_attributes": [], "flags": ["is_named_tuple"], "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["event_dict_key", "record_attribute"]}}, "module_name": "structlog.processors", "mro": ["structlog.processors.CallsiteParameterAdder._RecordMapping", "builtins.tuple", "typing.Sequence", "typing.Collection", "typing.Reversible", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "event_dict_key", "record_attribute"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "event_dict_key", "record_attribute"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _RecordMapping", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of _RecordMapping", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _RecordMapping", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _RecordMapping", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "event_dict_key", "record_attribute"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "event_dict_key", "record_attribute"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of _RecordMapping", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping._source", "name": "_source", "type": "builtins.str"}}, "event_dict_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping.event_dict_key", "name": "event_dict_key", "type": "builtins.str"}}, "event_dict_key-redefinition": {".class": "SymbolTableNode", "cross_ref": "structlog.processors.CallsiteParameterAdder._RecordMapping.event_dict_key", "kind": "<PERSON><PERSON><PERSON>"}, "record_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping.record_attribute", "name": "record_attribute", "type": "builtins.str"}}, "record_attribute-redefinition": {".class": "SymbolTableNode", "cross_ref": "structlog.processors.CallsiteParameterAdder._RecordMapping.record_attribute", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder._RecordMapping.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": "structlog.processors.CallsiteParameterAdder._RecordMapping"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.CallsiteParameterAdder.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "arg_types": ["structlog.processors.CallsiteParameterAdder", "logging.Logger", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of CallsiteParameterAdder", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "parameters", "additional_ignores"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.CallsiteParameterAdder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "parameters", "additional_ignores"], "arg_types": ["structlog.processors.CallsiteParameterAdder", {".class": "Instance", "args": ["structlog.processors.CallsiteParameter"], "type_ref": "typing.Collection"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CallsiteParameterAdder", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "structlog.processors.CallsiteParameterAdder.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_active_handlers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "structlog.processors.CallsiteParameterAdder._active_handlers", "name": "_active_handlers", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["structlog.processors.CallsiteParameter", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "<PERSON>.<PERSON><PERSON>"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "_additional_ignores": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.CallsiteParameterAdder._additional_ignores", "name": "_additional_ignores", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "_all_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "structlog.processors.CallsiteParameterAdder._all_parameters", "name": "_all_parameters", "type": {".class": "Instance", "args": ["structlog.processors.CallsiteParameter"], "type_ref": "builtins.set"}}}, "_handlers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "structlog.processors.CallsiteParameterAdder._handlers", "name": "_handlers", "type": {".class": "Instance", "args": ["structlog.processors.CallsiteParameter", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "<PERSON>.<PERSON><PERSON>"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "type_ref": "builtins.dict"}}}, "_record_attribute_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "structlog.processors.CallsiteParameterAdder._record_attribute_map", "name": "_record_attribute_map", "type": {".class": "Instance", "args": ["structlog.processors.CallsiteParameter", "builtins.str"], "type_ref": "builtins.dict"}}}, "_record_mappings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "structlog.processors.CallsiteParameterAdder._record_mappings", "name": "_record_mappings", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.processors.CallsiteParameterAdder._RecordMapping"}], "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.CallsiteParameterAdder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.processors.CallsiteParameterAdder", "values": [], "variance": 0}, "slots": ["_active_handlers", "_additional_ignores", "_record_mappings"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_public": false}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef", "module_public": false}, "EventDict": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.EventDict", "kind": "Gdef", "module_public": false}, "EventRenamer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.processors.EventRenamer", "name": "EventRenamer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog.processors.EventRenamer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.processors", "mro": ["structlog.processors.EventRenamer", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.EventRenamer.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "arg_types": ["structlog.processors.EventRenamer", "logging.Logger", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of EventRenamer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "to", "replace_by"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.EventRenamer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "to", "replace_by"], "arg_types": ["structlog.processors.EventRenamer", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EventRenamer", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "replace_by": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.EventRenamer.replace_by", "name": "replace_by", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "to": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.EventRenamer.to", "name": "to", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.EventRenamer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.processors.EventRenamer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExcInfo": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.ExcInfo", "kind": "Gdef", "module_public": false}, "ExceptionDictTransformer": {".class": "SymbolTableNode", "cross_ref": "structlog.tracebacks.ExceptionDictTransformer", "kind": "Gdef", "module_public": false}, "ExceptionPrettyPrinter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.processors.ExceptionPrettyPrinter", "name": "ExceptionPrettyPrinter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog.processors.ExceptionPrettyPrinter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.processors", "mro": ["structlog.processors.ExceptionPrettyPrinter", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.ExceptionPrettyPrinter.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "arg_types": ["structlog.processors.ExceptionPrettyPrinter", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ExceptionPrettyPrinter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "file", "exception_formatter"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.ExceptionPrettyPrinter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "file", "exception_formatter"], "arg_types": ["structlog.processors.ExceptionPrettyPrinter", {".class": "UnionType", "items": ["typing.TextIO", {".class": "NoneType"}]}, "structlog.typing.ExceptionTransformer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExceptionPrettyPrinter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.ExceptionPrettyPrinter._file", "name": "_file", "type": "typing.TextIO"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.ExceptionPrettyPrinter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.processors.ExceptionPrettyPrinter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExceptionRenderer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.processors.ExceptionRenderer", "name": "Exception<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog.processors.ExceptionRenderer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.processors", "mro": ["structlog.processors.ExceptionRenderer", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.ExceptionRenderer.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "arg_types": ["structlog.processors.ExceptionRenderer", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ExceptionRenderer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "exception_formatter"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.ExceptionRenderer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "exception_formatter"], "arg_types": ["structlog.processors.ExceptionRenderer", "structlog.typing.ExceptionTransformer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExceptionRenderer", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "format_exception": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.ExceptionRenderer.format_exception", "name": "format_exception", "type": "structlog.typing.ExceptionTransformer"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.ExceptionRenderer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.processors.ExceptionRenderer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExceptionTransformer": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.ExceptionTransformer", "kind": "Gdef", "module_public": false}, "JSONRenderer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.processors.JSONRenderer", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog.processors.JSONRenderer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.processors", "mro": ["structlog.processors.JSONRenderer", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.JSONRenderer.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "arg_types": ["structlog.processors.JSONRenderer", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of JSONR<PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "serializer", "dumps_kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.JSONRenderer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "serializer", "dumps_kw"], "arg_types": ["structlog.processors.JSONRenderer", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of JSONR<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_dumps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.JSONRenderer._dumps", "name": "_dumps", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_dumps_kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.JSONRenderer._dumps_kw", "name": "_dumps_kw", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.JSONRenderer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.processors.JSONRenderer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "KeyValueRenderer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.processors.KeyValueRenderer", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog.processors.KeyValueRenderer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.processors", "mro": ["structlog.processors.KeyValueRenderer", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_", "__", "event_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.KeyValueRenderer.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_", "__", "event_dict"], "arg_types": ["structlog.processors.KeyValueRenderer", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of KeyV<PERSON>ue<PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "sort_keys", "key_order", "drop_missing", "repr_native_str"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.KeyValueRenderer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "sort_keys", "key_order", "drop_missing", "repr_native_str"], "arg_types": ["structlog.processors.KeyValueRenderer", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of KeyValueR<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_ordered_items": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.KeyValueRenderer._ordered_items", "name": "_ordered_items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.object"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_repr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.KeyValueRenderer._repr", "name": "_repr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.KeyValueRenderer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.processors.KeyValueRenderer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LogfmtRenderer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.processors.LogfmtRenderer", "name": "LogfmtRender<PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog.processors.LogfmtRenderer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.processors", "mro": ["structlog.processors.LogfmtRenderer", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_", "__", "event_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.LogfmtRenderer.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_", "__", "event_dict"], "arg_types": ["structlog.processors.LogfmtRenderer", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of LogfmtRender<PERSON>", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "sort_keys", "key_order", "drop_missing", "bool_as_flag"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.LogfmtRenderer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "sort_keys", "key_order", "drop_missing", "bool_as_flag"], "arg_types": ["structlog.processors.LogfmtRenderer", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LogfmtRenderer", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_ordered_items": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.LogfmtRenderer._ordered_items", "name": "_ordered_items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.object"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "bool_as_flag": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.LogfmtRenderer.bool_as_flag", "name": "bool_as_flag", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.LogfmtRenderer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.processors.LogfmtRenderer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MaybeTimeStamper": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.processors.MaybeTimeStamper", "name": "MaybeTimeStamper", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog.processors.MaybeTimeStamper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.processors", "mro": ["structlog.processors.MaybeTimeStamper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.MaybeTimeStamper.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "arg_types": ["structlog.processors.MaybeTimeStamper", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of MaybeTimeStamper", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "fmt", "utc", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.MaybeTimeStamper.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "fmt", "utc", "key"], "arg_types": ["structlog.processors.MaybeTimeStamper", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MaybeTimeStamper", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "structlog.processors.MaybeTimeStamper.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "stamper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.MaybeTimeStamper.stamper", "name": "stamper", "type": "structlog.processors.TimeStamper"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.MaybeTimeStamper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.processors.MaybeTimeStamper", "values": [], "variance": 0}, "slots": ["stamper"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "StackInfoRenderer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.processors.StackInfoRenderer", "name": "StackInfoRenderer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog.processors.StackInfoRenderer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.processors", "mro": ["structlog.processors.StackInfoRenderer", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.StackInfoRenderer.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "arg_types": ["structlog.processors.StackInfoRenderer", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of StackInfoRenderer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "additional_ignores"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.StackInfoRenderer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "additional_ignores"], "arg_types": ["structlog.processors.StackInfoRenderer", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StackInfoRenderer", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "structlog.processors.StackInfoRenderer.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_additional_ignores": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.StackInfoRenderer._additional_ignores", "name": "_additional_ignores", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.StackInfoRenderer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.processors.StackInfoRenderer", "values": [], "variance": 0}, "slots": ["_additional_ignores"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextIO": {".class": "SymbolTableNode", "cross_ref": "typing.TextIO", "kind": "Gdef", "module_public": false}, "TimeStamper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.processors.TimeStamper", "name": "TimeStamper", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog.processors.TimeStamper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.processors", "mro": ["structlog.processors.TimeStamper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.TimeStamper.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "arg_types": ["structlog.processors.TimeStamper", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of TimeStamper", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.TimeStamper.__getstate__", "name": "__getstate__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["structlog.processors.TimeStamper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getstate__ of TimeStamper", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "fmt", "utc", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.TimeStamper.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "fmt", "utc", "key"], "arg_types": ["structlog.processors.TimeStamper", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimeStamper", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.TimeStamper.__setstate__", "name": "__setstate__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["structlog.processors.TimeStamper", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setstate__ of TimeStamper", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "structlog.processors.TimeStamper.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_stamper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.TimeStamper._stamper", "name": "_stamper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fmt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.TimeStamper.fmt", "name": "fmt", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.TimeStamper.key", "name": "key", "type": "builtins.str"}}, "utc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.processors.TimeStamper.utc", "name": "utc", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.TimeStamper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.processors.TimeStamper", "values": [], "variance": 0}, "slots": ["_stamper", "fmt", "key", "utc"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnicodeDecoder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.processors.UnicodeDecoder", "name": "UnicodeDecoder", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog.processors.UnicodeDecoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.processors", "mro": ["structlog.processors.UnicodeDecoder", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.UnicodeDecoder.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "arg_types": ["structlog.processors.UnicodeDecoder", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of UnicodeDecoder", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "encoding", "errors"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.UnicodeDecoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "encoding", "errors"], "arg_types": ["structlog.processors.UnicodeDecoder", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnicodeDecoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "structlog.processors.UnicodeDecoder._encoding", "name": "_encoding", "type": "builtins.str"}}, "_errors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "structlog.processors.UnicodeDecoder._errors", "name": "_errors", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.UnicodeDecoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.processors.UnicodeDecoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnicodeEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.processors.UnicodeEncoder", "name": "UnicodeEncoder", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog.processors.UnicodeEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.processors", "mro": ["structlog.processors.UnicodeEncoder", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.UnicodeEncoder.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "arg_types": ["structlog.processors.UnicodeEncoder", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of UnicodeEncoder", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "encoding", "errors"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors.UnicodeEncoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "encoding", "errors"], "arg_types": ["structlog.processors.UnicodeEncoder", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnicodeEncoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "structlog.processors.UnicodeEncoder._encoding", "name": "_encoding", "type": "builtins.str"}}, "_errors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "structlog.processors.UnicodeEncoder._errors", "name": "_errors", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.processors.UnicodeEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.processors.UnicodeEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WrappedLogger": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.WrappedLogger", "kind": "Gdef", "module_public": false}, "_NAME_TO_LEVEL": {".class": "SymbolTableNode", "cross_ref": "structlog._log_levels._NAME_TO_LEVEL", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.processors.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.processors.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.processors.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.processors.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.processors.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.processors.__package__", "name": "__package__", "type": "builtins.str"}}, "_figure_out_exc_info": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["v"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors._figure_out_exc_info", "name": "_figure_out_exc_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["v"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_figure_out_exc_info", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.ExcInfo"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_find_first_app_frame_and_name": {".class": "SymbolTableNode", "cross_ref": "structlog._frames._find_first_app_frame_and_name", "kind": "Gdef", "module_public": false}, "_format_exception": {".class": "SymbolTableNode", "cross_ref": "structlog._frames._format_exception", "kind": "Gdef", "module_public": false}, "_format_stack": {".class": "SymbolTableNode", "cross_ref": "structlog._frames._format_stack", "kind": "Gdef", "module_public": false}, "_items_sorter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["sort_keys", "key_order", "drop_missing"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors._items_sorter", "name": "_items_sorter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["sort_keys", "key_order", "drop_missing"], "arg_types": ["builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_items_sorter", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.object"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_json_fallback_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors._json_fallback_handler", "name": "_json_fallback_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_json_fallback_handler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_make_stamper": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["fmt", "utc", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.processors._make_stamper", "name": "_make_stamper", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["fmt", "utc", "key"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_stamper", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "add_log_level": {".class": "SymbolTableNode", "cross_ref": "structlog._log_levels.add_log_level", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef", "module_public": false}, "dict_tracebacks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.processors.dict_tracebacks", "name": "dict_tracebacks", "type": "structlog.processors.ExceptionRenderer"}}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef", "module_public": false}, "format_exc_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.processors.format_exc_info", "name": "format_exc_info", "type": "structlog.processors.ExceptionRenderer"}}, "get_processname": {".class": "SymbolTableNode", "cross_ref": "structlog._utils.get_processname", "kind": "Gdef", "module_public": false}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef", "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef", "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/processors.py"}