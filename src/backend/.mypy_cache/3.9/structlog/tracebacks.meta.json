{"data_mtime": 1754228257, "dep_lines": [24, 15, 17, 19, 20, 21, 22, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 30], "dependencies": ["structlog.typing", "__future__", "os", "dataclasses", "traceback", "types", "typing", "builtins", "abc"], "hash": "97a8f831c40f67b02edcf45e675866d4ccdbd3c9bfea8dad96bfc4659a386f93", "id": "structlog.tracebacks", "ignore_all": true, "interface_hash": "43f86d4ed21c08c0c3d97889343f5ca7bc2b41cf4b33c5976cdf9fcf26cb3622", "mtime": 1754207014, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/tracebacks.py", "plugin_data": null, "size": 7751, "suppressed": [], "version_id": "1.7.1"}