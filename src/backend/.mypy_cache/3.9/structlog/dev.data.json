{".class": "MypyFile", "_fullname": "structlog.dev", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BLUE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev.BLUE", "name": "BLUE", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "BRIGHT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev.BRIGHT", "name": "BRIGHT", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "CYAN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev.CYAN", "name": "CYAN", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "Console": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "structlog.dev.<PERSON>e", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.<PERSON>e", "source_any": null, "type_of_any": 3}}}, "ConsoleRenderer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.dev.<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog.dev.<PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.dev", "mro": ["structlog.dev.<PERSON><PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.dev.<PERSON><PERSON><PERSON>.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "arg_types": ["structlog.dev.<PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pad_event", "colors", "force_colors", "repr_native_str", "level_styles", "exception_formatter", "sort_keys", "event_key", "timestamp_key"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.dev.<PERSON><PERSON><PERSON>.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pad_event", "colors", "force_colors", "repr_native_str", "level_styles", "exception_formatter", "sort_keys", "event_key", "timestamp_key"], "arg_types": ["structlog.dev.<PERSON><PERSON><PERSON>", "builtins.int", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.dev.Styles"}, {".class": "NoneType"}]}, {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.ExceptionRenderer"}, "builtins.bool", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_event_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.dev.<PERSON><PERSON><PERSON><PERSON><PERSON>._event_key", "name": "_event_key", "type": "builtins.str"}}, "_exception_formatter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.dev.Console<PERSON><PERSON>er._exception_formatter", "name": "_exception_formatter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["typing.TextIO", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.ExcInfo"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_level_to_color": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.dev.<PERSON><PERSON><PERSON><PERSON><PERSON>._level_to_color", "name": "_level_to_color", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_longest_level": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.dev.<PERSON><PERSON><PERSON><PERSON><PERSON>._longest_level", "name": "_longest_level", "type": "builtins.int"}}, "_pad_event": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.dev.<PERSON><PERSON><PERSON><PERSON><PERSON>._pad_event", "name": "_pad_event", "type": "builtins.int"}}, "_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.dev.<PERSON><PERSON><PERSON><PERSON><PERSON>._repr", "name": "_repr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["structlog.dev.<PERSON><PERSON><PERSON>", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_repr of ConsoleR<PERSON>er", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_repr_native_str": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.dev.<PERSON><PERSON><PERSON><PERSON><PERSON>._repr_native_str", "name": "_repr_native_str", "type": "builtins.bool"}}, "_sort_keys": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.dev.<PERSON><PERSON><PERSON><PERSON><PERSON>._sort_keys", "name": "_sort_keys", "type": "builtins.bool"}}, "_styles": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.dev.Con<PERSON>e<PERSON><PERSON>er._styles", "name": "_styles", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["structlog.dev._PlainStyles"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_PlainStyles", "ret_type": "structlog.dev._PlainStyles", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeType", "item": "structlog.dev._ColorfulStyles"}]}}}, "_timestamp_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.dev.<PERSON><PERSON><PERSON><PERSON><PERSON>._timestamp_key", "name": "_timestamp_key", "type": "builtins.str"}}, "get_default_level_styles": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["colors"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "structlog.dev.ConsoleRenderer.get_default_level_styles", "name": "get_default_level_styles", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["colors"], "arg_types": ["builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_default_level_styles of ConsoleRenderer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "structlog.dev.ConsoleRenderer.get_default_level_styles", "name": "get_default_level_styles", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["colors"], "arg_types": ["builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_default_level_styles of ConsoleRenderer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.dev.<PERSON><PERSON><PERSON><PERSON><PERSON>.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.dev.<PERSON><PERSON><PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DIM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev.DIM", "name": "DIM", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "EventDict": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.EventDict", "kind": "Gdef", "module_public": false}, "ExcInfo": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.ExcInfo", "kind": "Gdef", "module_public": false}, "ExceptionRenderer": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.ExceptionRenderer", "kind": "Gdef", "module_public": false}, "GREEN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev.GREEN", "name": "GREEN", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "MAGENTA": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev.MAGENTA", "name": "MAGENTA", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef", "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_public": false}, "RED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev.RED", "name": "RED", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "RED_BACK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev.RED_BACK", "name": "RED_BACK", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "RESET_ALL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev.RESET_ALL", "name": "RESET_ALL", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "RichTracebackFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.dev.RichTracebackFormatter", "name": "RichTracebackFormatter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog.dev.RichTracebackFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 195, "name": "color_system", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "truecolor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "windows"}]}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 198, "name": "show_locals", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 199, "name": "max_frames", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 200, "name": "theme", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 201, "name": "word_wrap", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 202, "name": "extra_lines", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 203, "name": "width", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 204, "name": "indent_guides", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 205, "name": "locals_max_length", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 206, "name": "locals_max_string", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 207, "name": "locals_hide_dunder", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 208, "name": "locals_hide_sunder", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 209, "name": "suppress", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "types.ModuleType"]}], "type_ref": "typing.Sequence"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "structlog.dev", "mro": ["structlog.dev.RichTracebackFormatter", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "sio", "exc_info"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.dev.RichTracebackFormatter.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "sio", "exc_info"], "arg_types": ["structlog.dev.RichTracebackFormatter", "typing.TextIO", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.ExcInfo"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of RichTracebackFormatter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "structlog.dev.RichTracebackFormatter.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "dataclasses.Field"}], "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "color_system", "show_locals", "max_frames", "theme", "word_wrap", "extra_lines", "width", "indent_guides", "locals_max_length", "locals_max_string", "locals_hide_dunder", "locals_hide_sunder", "suppress"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.dev.RichTracebackFormatter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "color_system", "show_locals", "max_frames", "theme", "word_wrap", "extra_lines", "width", "indent_guides", "locals_max_length", "locals_max_string", "locals_hide_dunder", "locals_hide_sunder", "suppress"], "arg_types": ["structlog.dev.RichTracebackFormatter", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "truecolor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "windows"}]}, "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", "builtins.int", "builtins.int", "builtins.bool", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "types.ModuleType"]}], "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RichTracebackFormatter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["color_system", "show_locals", "max_frames", "theme", "word_wrap", "extra_lines", "width", "indent_guides", "locals_max_length", "locals_max_string", "locals_hide_dunder", "locals_hide_sunder", "suppress"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "structlog.dev.RichTracebackFormatter.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["color_system", "show_locals", "max_frames", "theme", "word_wrap", "extra_lines", "width", "indent_guides", "locals_max_length", "locals_max_string", "locals_hide_dunder", "locals_hide_sunder", "suppress"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "truecolor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "windows"}]}, "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", "builtins.int", "builtins.int", "builtins.bool", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "types.ModuleType"]}], "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Rich<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "structlog.dev.RichTracebackFormatter.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["color_system", "show_locals", "max_frames", "theme", "word_wrap", "extra_lines", "width", "indent_guides", "locals_max_length", "locals_max_string", "locals_hide_dunder", "locals_hide_sunder", "suppress"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "truecolor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "windows"}]}, "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", "builtins.int", "builtins.int", "builtins.bool", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "types.ModuleType"]}], "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Rich<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "color_system": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog.dev.RichTracebackFormatter.color_system", "name": "color_system", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "truecolor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "windows"}]}}}, "extra_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog.dev.RichTracebackFormatter.extra_lines", "name": "extra_lines", "type": "builtins.int"}}, "indent_guides": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog.dev.RichTracebackFormatter.indent_guides", "name": "indent_guides", "type": "builtins.bool"}}, "locals_hide_dunder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog.dev.RichTracebackFormatter.locals_hide_dunder", "name": "locals_hide_dunder", "type": "builtins.bool"}}, "locals_hide_sunder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog.dev.RichTracebackFormatter.locals_hide_sunder", "name": "locals_hide_sunder", "type": "builtins.bool"}}, "locals_max_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog.dev.RichTracebackFormatter.locals_max_length", "name": "locals_max_length", "type": "builtins.int"}}, "locals_max_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog.dev.RichTracebackFormatter.locals_max_string", "name": "locals_max_string", "type": "builtins.int"}}, "max_frames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog.dev.RichTracebackFormatter.max_frames", "name": "max_frames", "type": "builtins.int"}}, "show_locals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog.dev.RichTracebackFormatter.show_locals", "name": "show_locals", "type": "builtins.bool"}}, "suppress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog.dev.RichTracebackFormatter.suppress", "name": "suppress", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "types.ModuleType"]}], "type_ref": "typing.Sequence"}}}, "theme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog.dev.RichTracebackFormatter.theme", "name": "theme", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog.dev.RichTracebackFormatter.width", "name": "width", "type": "builtins.int"}}, "word_wrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog.dev.RichTracebackFormatter.word_wrap", "name": "word_wrap", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.dev.RichTracebackFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.dev.RichTracebackFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "StringIO": {".class": "SymbolTableNode", "cross_ref": "io.StringIO", "kind": "Gdef", "module_public": false}, "Styles": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "structlog.dev.Styles", "line": 128, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": ["structlog.dev._Styles", {".class": "TypeType", "item": "structlog.dev._Styles"}]}}}, "TextIO": {".class": "SymbolTableNode", "cross_ref": "typing.TextIO", "kind": "Gdef", "module_public": false}, "Traceback": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "structlog.dev.<PERSON>", "name": "<PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.<PERSON>", "source_any": null, "type_of_any": 3}}}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "WrappedLogger": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.WrappedLogger", "kind": "Gdef", "module_public": false}, "YELLOW": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev.YELLOW", "name": "YELLOW", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "_ColorfulStyles": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.dev._ColorfulStyles", "name": "_ColorfulStyles", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog.dev._ColorfulStyles", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.dev", "mro": ["structlog.dev._ColorfulStyles", "builtins.object"], "names": {".class": "SymbolTable", "bright": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._ColorfulStyles.bright", "name": "bright", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "kv_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._ColorfulStyles.kv_key", "name": "kv_key", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "kv_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._ColorfulStyles.kv_value", "name": "kv_value", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "level_critical": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._ColorfulStyles.level_critical", "name": "level_critical", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "level_debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._ColorfulStyles.level_debug", "name": "level_debug", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "level_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._ColorfulStyles.level_error", "name": "level_error", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "level_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._ColorfulStyles.level_exception", "name": "level_exception", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "level_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._ColorfulStyles.level_info", "name": "level_info", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "level_notset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._ColorfulStyles.level_notset", "name": "level_notset", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "level_warn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._ColorfulStyles.level_warn", "name": "level_warn", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "logger_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._ColorfulStyles.logger_name", "name": "logger_name", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._ColorfulStyles.reset", "name": "reset", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "timestamp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._ColorfulStyles.timestamp", "name": "timestamp", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.dev._ColorfulStyles.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.dev._ColorfulStyles", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_EVENT_WIDTH": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._EVENT_WIDTH", "name": "_EVENT_WIDTH", "type": "builtins.int"}}, "_IS_WINDOWS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev._IS_WINDOWS", "name": "_IS_WINDOWS", "type": "builtins.bool"}}, "_MISSING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._MISSING", "name": "_MISSING", "type": "builtins.str"}}, "_PlainStyles": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.dev._PlainStyles", "name": "_PlainStyles", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog.dev._PlainStyles", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.dev", "mro": ["structlog.dev._PlainStyles", "builtins.object"], "names": {".class": "SymbolTable", "bright": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._PlainStyles.bright", "name": "bright", "type": "builtins.str"}}, "kv_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._PlainStyles.kv_key", "name": "kv_key", "type": "builtins.str"}}, "kv_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._PlainStyles.kv_value", "name": "kv_value", "type": "builtins.str"}}, "level_critical": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._PlainStyles.level_critical", "name": "level_critical", "type": "builtins.str"}}, "level_debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._PlainStyles.level_debug", "name": "level_debug", "type": "builtins.str"}}, "level_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._PlainStyles.level_error", "name": "level_error", "type": "builtins.str"}}, "level_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._PlainStyles.level_exception", "name": "level_exception", "type": "builtins.str"}}, "level_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._PlainStyles.level_info", "name": "level_info", "type": "builtins.str"}}, "level_notset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._PlainStyles.level_notset", "name": "level_notset", "type": "builtins.str"}}, "level_warn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._PlainStyles.level_warn", "name": "level_warn", "type": "builtins.str"}}, "logger_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._PlainStyles.logger_name", "name": "logger_name", "type": "builtins.str"}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._PlainStyles.reset", "name": "reset", "type": "builtins.str"}}, "timestamp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog.dev._PlainStyles.timestamp", "name": "timestamp", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.dev._PlainStyles.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.dev._PlainStyles", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SENTINEL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev._SENTINEL", "name": "_SENTINEL", "type": "builtins.object"}}, "_Styles": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["bright", 1], ["kv_key", 1], ["kv_value", 1], ["level_critical", 1], ["level_debug", 1], ["level_error", 1], ["level_exception", 1], ["level_info", 1], ["level_notset", 1], ["level_warn", 1], ["logger_name", 1], ["reset", 1], ["timestamp", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.dev._Styles", "name": "_Styles", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "structlog.dev._Styles", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "structlog.dev", "mro": ["structlog.dev._Styles", "builtins.object"], "names": {".class": "SymbolTable", "bright": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "structlog.dev._Styles.bright", "name": "bright", "type": "builtins.str"}}, "kv_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "structlog.dev._Styles.kv_key", "name": "kv_key", "type": "builtins.str"}}, "kv_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "structlog.dev._Styles.kv_value", "name": "kv_value", "type": "builtins.str"}}, "level_critical": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "structlog.dev._Styles.level_critical", "name": "level_critical", "type": "builtins.str"}}, "level_debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "structlog.dev._Styles.level_debug", "name": "level_debug", "type": "builtins.str"}}, "level_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "structlog.dev._Styles.level_error", "name": "level_error", "type": "builtins.str"}}, "level_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "structlog.dev._Styles.level_exception", "name": "level_exception", "type": "builtins.str"}}, "level_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "structlog.dev._Styles.level_info", "name": "level_info", "type": "builtins.str"}}, "level_notset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "structlog.dev._Styles.level_notset", "name": "level_notset", "type": "builtins.str"}}, "level_warn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "structlog.dev._Styles.level_warn", "name": "level_warn", "type": "builtins.str"}}, "logger_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "structlog.dev._Styles.logger_name", "name": "logger_name", "type": "builtins.str"}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "structlog.dev._Styles.reset", "name": "reset", "type": "builtins.str"}}, "timestamp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "structlog.dev._Styles.timestamp", "name": "timestamp", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.dev._Styles.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.dev._Styles", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.dev.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.dev.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.dev.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.dev.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.dev.__package__", "name": "__package__", "type": "builtins.str"}}, "_figure_out_exc_info": {".class": "SymbolTableNode", "cross_ref": "structlog.processors._figure_out_exc_info", "kind": "Gdef", "module_public": false}, "_format_exception": {".class": "SymbolTableNode", "cross_ref": "structlog._frames._format_exception", "kind": "Gdef", "module_public": false}, "_has_colors": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev._has_colors", "name": "_has_colors", "type": "builtins.bool"}}, "_pad": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["s", "length"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.dev._pad", "name": "_pad", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["s", "length"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pad", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_use_colors": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev._use_colors", "name": "_use_colors", "type": "builtins.bool"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "better_exceptions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "structlog.dev.better_exceptions", "name": "better_exceptions", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.better_exceptions", "source_any": null, "type_of_any": 3}}}, "better_traceback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["sio", "exc_info"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.dev.better_traceback", "name": "better_traceback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["sio", "exc_info"], "arg_types": ["typing.TextIO", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.ExcInfo"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "better_traceback", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "colorama": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "structlog.dev.colorama", "name": "colorama", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.colorama", "source_any": null, "type_of_any": 3}}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef", "module_public": false}, "default_exception_formatter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev.default_exception_formatter", "name": "default_exception_formatter", "type": "structlog.dev.RichTracebackFormatter"}}, "plain_traceback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["sio", "exc_info"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.dev.plain_traceback", "name": "plain_traceback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["sio", "exc_info"], "arg_types": ["typing.TextIO", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.ExcInfo"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "plain_traceback", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "rich": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "structlog.dev.rich", "name": "rich", "type": {".class": "AnyType", "missing_import_name": "structlog.dev.rich", "source_any": null, "type_of_any": 3}}}, "rich_traceback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.dev.rich_traceback", "name": "rich_traceback", "type": "structlog.dev.RichTracebackFormatter"}}, "set_exc_info": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["logger", "method_name", "event_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog.dev.set_exc_info", "name": "set_exc_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["logger", "method_name", "event_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_exc_info", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/dev.py"}