{"data_mtime": 1754228257, "dep_lines": [15, 17, 18, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30], "dependencies": ["__future__", "types", "typing", "builtins", "abc", "typing_extensions"], "hash": "72d0c14461dd6aa527c9ff186c2f39d38c0d47dc62164ff592c1d3c327c965f6", "id": "structlog.typing", "ignore_all": true, "interface_hash": "e12545b6695f4b9bd723570560e4fe2140fd0b224a06d83e590ce75109402879", "mtime": 1754207014, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/typing.py", "plugin_data": null, "size": 7945, "suppressed": [], "version_id": "1.7.1"}