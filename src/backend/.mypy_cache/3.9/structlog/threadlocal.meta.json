{"data_mtime": 1754228260, "dep_lines": [27, 28, 15, 17, 18, 19, 20, 21, 23, 25, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 10, 5, 10, 5, 30], "dependencies": ["structlog._config", "structlog.typing", "__future__", "contextlib", "sys", "threading", "uuid", "warnings", "typing", "structlog", "builtins", "abc"], "hash": "2f875d4d6ad6c788c2653a789c35738fb7663f3d355286daac11eb2b4586f2da", "id": "structlog.threadlocal", "ignore_all": true, "interface_hash": "8b7f7491d0b80d048d02f71c672f7732d7112c90da2123d183353409522c1b4e", "mtime": 1754207014, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/threadlocal.py", "plugin_data": null, "size": 9145, "suppressed": [], "version_id": "1.7.1"}