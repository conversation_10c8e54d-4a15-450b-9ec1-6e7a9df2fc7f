{"data_mtime": 1754228260, "dep_lines": [18, 19, 20, 21, 22, 23, 10, 12, 13, 14, 16, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 30, 30, 30, 30], "dependencies": ["structlog._log_levels", "structlog._output", "structlog.contextvars", "structlog.dev", "structlog.processors", "structlog.typing", "__future__", "os", "sys", "warnings", "typing", "builtins", "_typeshed", "abc", "logging", "types"], "hash": "c4ae1225bb5fac0784acfa959e3dc3949b7242339678c352db4d5ad6a8e8f87e", "id": "structlog._config", "ignore_all": true, "interface_hash": "b44fd1423c74fc90f826ad1b12733ee063d1cd6617da0267f5394abb40350cf4", "mtime": 1754207014, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/_config.py", "plugin_data": null, "size": 13726, "suppressed": [], "version_id": "1.7.1"}