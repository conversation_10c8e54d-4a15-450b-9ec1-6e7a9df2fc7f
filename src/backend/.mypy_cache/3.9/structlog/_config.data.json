{".class": "MypyFile", "_fullname": "structlog._config", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BindableLogger": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.BindableLogger", "kind": "Gdef"}, "BoundLoggerLazyProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog._config.BoundLoggerLazyProxy", "name": "BoundLoggerLazyProxy", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog._config.BoundLoggerLazyProxy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog._config", "mro": ["structlog._config.BoundLoggerLazyProxy", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog._config.BoundLoggerLazyProxy.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["structlog._config.BoundLoggerLazyProxy", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of BoundLoggerLazyProxy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog._config.BoundLoggerLazyProxy.__getstate__", "name": "__getstate__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["structlog._config.BoundLoggerLazyProxy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getstate__ of BoundLoggerLazyProxy", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "logger", "wrapper_class", "processors", "context_class", "cache_logger_on_first_use", "initial_values", "logger_factory_args"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog._config.BoundLoggerLazyProxy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "logger", "wrapper_class", "processors", "context_class", "cache_logger_on_first_use", "initial_values", "logger_factory_args"], "arg_types": ["structlog._config.BoundLoggerLazyProxy", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "structlog.typing.BindableLogger"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Processor"}], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BoundLoggerLazyProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog._config.BoundLoggerLazyProxy.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["structlog._config.BoundLoggerLazyProxy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of BoundLoggerLazyProxy", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog._config.BoundLoggerLazyProxy.__setstate__", "name": "__setstate__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["structlog._config.BoundLoggerLazyProxy", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setstate__ of BoundLoggerLazyProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_cache_logger_on_first_use": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog._config.BoundLoggerLazyProxy._cache_logger_on_first_use", "name": "_cache_logger_on_first_use", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}, "_context_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog._config.BoundLoggerLazyProxy._context_class", "name": "_context_class", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}, {".class": "NoneType"}]}}}, "_initial_values": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog._config.BoundLoggerLazyProxy._initial_values", "name": "_initial_values", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}}}, "_logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog._config.BoundLoggerLazyProxy._logger", "name": "_logger", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, {".class": "NoneType"}]}}}, "_logger_factory_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog._config.BoundLoggerLazyProxy._logger_factory_args", "name": "_logger_factory_args", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}]}}}, "_processors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog._config.BoundLoggerLazyProxy._processors", "name": "_processors", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Processor"}], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}}}, "_wrapper_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog._config.BoundLoggerLazyProxy._wrapper_class", "name": "_wrapper_class", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "structlog.typing.BindableLogger"}, {".class": "NoneType"}]}}}, "bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "new_values"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog._config.BoundLoggerLazyProxy.bind", "name": "bind", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "new_values"], "arg_types": ["structlog._config.BoundLoggerLazyProxy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind of BoundLoggerLazyProxy", "ret_type": "structlog.typing.BindableLogger", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "new": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "new_values"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog._config.BoundLoggerLazyProxy.new", "name": "new", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "new_values"], "arg_types": ["structlog._config.BoundLoggerLazyProxy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new of BoundLoggerLazyProxy", "ret_type": "structlog.typing.BindableLogger", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "try_unbind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog._config.BoundLoggerLazyProxy.try_unbind", "name": "try_unbind", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "arg_types": ["structlog._config.BoundLoggerLazyProxy", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "try_unbind of BoundLoggerLazyProxy", "ret_type": "structlog.typing.BindableLogger", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "unbind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog._config.BoundLoggerLazyProxy.unbind", "name": "unbind", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "arg_types": ["structlog._config.BoundLoggerLazyProxy", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unbind of BoundLoggerLazyProxy", "ret_type": "structlog.typing.BindableLogger", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog._config.BoundLoggerLazyProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog._config.BoundLoggerLazyProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ConsoleRenderer": {".class": "SymbolTableNode", "cross_ref": "structlog.dev.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "Context": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.Context", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "PrintLoggerFactory": {".class": "SymbolTableNode", "cross_ref": "structlog._output.PrintLoggerFactory", "kind": "Gdef"}, "Processor": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.Processor", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "StackInfoRenderer": {".class": "SymbolTableNode", "cross_ref": "structlog.processors.StackInfoRenderer", "kind": "Gdef"}, "TimeStamper": {".class": "SymbolTableNode", "cross_ref": "structlog.processors.TimeStamper", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "WrappedLogger": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.WrappedLogger", "kind": "Gdef"}, "_BUILTIN_CACHE_LOGGER_ON_FIRST_USE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "structlog._config._BUILTIN_CACHE_LOGGER_ON_FIRST_USE", "name": "_BUILTIN_CACHE_LOGGER_ON_FIRST_USE", "type": "builtins.bool"}}, "_BUILTIN_DEFAULT_CONTEXT_CLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog._config._BUILTIN_DEFAULT_CONTEXT_CLASS", "name": "_BUILTIN_DEFAULT_CONTEXT_CLASS", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}]}}}, "_BUILTIN_DEFAULT_LOGGER_FACTORY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog._config._BUILTIN_DEFAULT_LOGGER_FACTORY", "name": "_BUILTIN_DEFAULT_LOGGER_FACTORY", "type": "structlog._output.PrintLoggerFactory"}}, "_BUILTIN_DEFAULT_PROCESSORS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "structlog._config._BUILTIN_DEFAULT_PROCESSORS", "name": "_BUILTIN_DEFAULT_PROCESSORS", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Processor"}], "type_ref": "typing.Sequence"}}}, "_BUILTIN_DEFAULT_WRAPPER_CLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog._config._BUILTIN_DEFAULT_WRAPPER_CLASS", "name": "_BUILTIN_DEFAULT_WRAPPER_CLASS", "type": {".class": "TypeType", "item": "structlog.typing.FilteringBoundLogger"}}}, "_CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog._config._CONFIG", "name": "_CONFIG", "type": "structlog._config._Configuration"}}, "_Configuration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog._config._Configuration", "name": "_Configuration", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "structlog._config._Configuration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog._config", "mro": ["structlog._config._Configuration", "builtins.object"], "names": {".class": "SymbolTable", "cache_logger_on_first_use": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog._config._Configuration.cache_logger_on_first_use", "name": "cache_logger_on_first_use", "type": "builtins.bool"}}, "default_context_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog._config._Configuration.default_context_class", "name": "default_context_class", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}]}}}, "default_processors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog._config._Configuration.default_processors", "name": "default_processors", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Processor"}], "type_ref": "typing.Iterable"}}}, "default_wrapper_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog._config._Configuration.default_wrapper_class", "name": "default_wrapper_class", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "is_configured": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog._config._Configuration.is_configured", "name": "is_configured", "type": "builtins.bool"}}, "logger_factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "structlog._config._Configuration.logger_factory", "name": "logger_factory", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog._config._Configuration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog._config._Configuration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog._config.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog._config.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog._config.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog._config.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog._config.__package__", "name": "__package__", "type": "builtins.str"}}, "_has_colors": {".class": "SymbolTableNode", "cross_ref": "structlog.dev._has_colors", "kind": "Gdef"}, "add_log_level": {".class": "SymbolTableNode", "cross_ref": "structlog._log_levels.add_log_level", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "configure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1], "arg_names": ["processors", "wrapper_class", "context_class", "logger_factory", "cache_logger_on_first_use"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog._config.configure", "name": "configure", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1], "arg_names": ["processors", "wrapper_class", "context_class", "logger_factory", "cache_logger_on_first_use"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Processor"}], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "structlog.typing.BindableLogger"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configure", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "configure_once": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1], "arg_names": ["processors", "wrapper_class", "context_class", "logger_factory", "cache_logger_on_first_use"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog._config.configure_once", "name": "configure_once", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1], "arg_names": ["processors", "wrapper_class", "context_class", "logger_factory", "cache_logger_on_first_use"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Processor"}], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "structlog.typing.BindableLogger"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configure_once", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "getLogger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog._config.getLogger", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "initial_values"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog._config.get_config", "name": "get_config", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_config", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "initial_values"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog._config.get_logger", "name": "get_logger", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "initial_values"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_logger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_configured": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog._config.is_configured", "name": "is_configured", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_configured", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "make_filtering_bound_logger": {".class": "SymbolTableNode", "cross_ref": "structlog._log_levels.make_filtering_bound_logger", "kind": "Gdef"}, "merge_contextvars": {".class": "SymbolTableNode", "cross_ref": "structlog.contextvars.merge_contextvars", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "reset_defaults": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog._config.reset_defaults", "name": "reset_defaults", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset_defaults", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set_exc_info": {".class": "SymbolTableNode", "cross_ref": "structlog.dev.set_exc_info", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "wrap_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["logger", "processors", "wrapper_class", "context_class", "cache_logger_on_first_use", "logger_factory_args", "initial_values"], "dataclass_transform_spec": null, "flags": [], "fullname": "structlog._config.wrap_logger", "name": "wrap_logger", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["logger", "processors", "wrapper_class", "context_class", "cache_logger_on_first_use", "logger_factory_args", "initial_values"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Processor"}], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "structlog.typing.BindableLogger"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap_logger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/_config.py"}