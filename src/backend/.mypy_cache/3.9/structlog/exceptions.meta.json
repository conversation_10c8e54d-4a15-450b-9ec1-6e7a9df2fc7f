{"data_mtime": 1754228257, "dep_lines": [10, 1, 1, 1], "dep_prios": [5, 5, 30, 30], "dependencies": ["__future__", "builtins", "abc", "typing"], "hash": "3c2e8728c394a91343a06692e82f2cd4b3bace414e59ee685ba0364489fb49a1", "id": "structlog.exceptions", "ignore_all": true, "interface_hash": "e81dc72fc9b52c8d51818e678b2b5bf6fcad76abb2f370fa3f1c79238faec4e3", "mtime": 1754207014, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/exceptions.py", "plugin_data": null, "size": 503, "suppressed": [], "version_id": "1.7.1"}