{"data_mtime": 1754228257, "dep_lines": [14, 6, 8, 9, 11, 12, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 30, 30], "dependencies": ["structlog.typing", "__future__", "sys", "traceback", "io", "types", "builtins", "abc", "typing"], "hash": "1d5b6e56d93de6ce22f81fef5c1a4a01431187f01981ac3911bb7bf67063a159", "id": "structlog._frames", "ignore_all": true, "interface_hash": "cf23c6028c0fb87e2f314441cec6cd2d64dd4ac1ec7c97fa78fdd687abb941a5", "mtime": 1754207014, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/_frames.py", "plugin_data": null, "size": 1890, "suppressed": [], "version_id": "1.7.1"}