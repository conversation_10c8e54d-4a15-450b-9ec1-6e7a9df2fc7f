{"data_mtime": 1754228257, "dep_lines": [10, 12, 13, 15, 16, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 30], "dependencies": ["__future__", "errno", "sys", "contextlib", "typing", "builtins", "abc"], "hash": "d71c3cf01f69ef1feb7cd610ec76de835b4de6bbdd8017176da926753fd8a2ed", "id": "structlog._utils", "ignore_all": true, "interface_hash": "f39715bb7dc94887a1b9c6ab89ea9a523434cf84feeb2089456bd989711bcbff", "mtime": 1754207014, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/_utils.py", "plugin_data": null, "size": 1471, "suppressed": [], "version_id": "1.7.1"}