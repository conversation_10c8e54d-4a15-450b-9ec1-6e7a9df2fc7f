{"data_mtime": 1754228257, "dep_lines": [20, 10, 12, 13, 14, 16, 18, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 10, 5, 5, 5, 30, 30], "dependencies": ["structlog._utils", "__future__", "copy", "sys", "threading", "pickle", "typing", "builtins", "abc", "typing_extensions"], "hash": "d579756c9cc7546317941be395ef20d61e5b9511d58e7a7ea702c7becab25638", "id": "structlog._output", "ignore_all": true, "interface_hash": "4453c94b7ef0c642c6d309547ff08a36a1c4f0802da4a195e46634eb6f48b8bb", "mtime": 1754207014, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/_output.py", "plugin_data": null, "size": 9311, "suppressed": [], "version_id": "1.7.1"}