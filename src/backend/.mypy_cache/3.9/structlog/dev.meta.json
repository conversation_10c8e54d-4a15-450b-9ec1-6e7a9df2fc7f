{"data_mtime": 1754228260, "dep_lines": [32, 33, 34, 12, 14, 15, 16, 18, 19, 20, 21, 1, 1, 1, 1, 50, 51, 38, 43, 48], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 5, 5, 10, 10, 10], "dependencies": ["structlog._frames", "structlog.processors", "structlog.typing", "__future__", "shutil", "sys", "warnings", "dataclasses", "io", "types", "typing", "builtins", "_typeshed", "abc", "os"], "hash": "517af7ab5eaded5ab14d8b02b9601b809d2f1ffe100415b2963ea23f669ce484", "id": "structlog.dev", "ignore_all": true, "interface_hash": "6038cf9459d2f15b1add9550a922b43fd17cc2d25a08efbcba933b9b6bf37c6a", "mtime": 1754207014, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/dev.py", "plugin_data": null, "size": 17368, "suppressed": ["rich.console", "rich.traceback", "colorama", "better_exceptions", "rich"], "version_id": "1.7.1"}