{"data_mtime": 1754228260, "dep_lines": [27, 18, 20, 21, 23, 25, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 10, 5, 30], "dependencies": ["structlog.typing", "__future__", "contextlib", "<PERSON><PERSON><PERSON>", "typing", "structlog", "builtins", "abc"], "hash": "5fedd96cbc9562b9ce26934da99c6f7710c19550f38548a246ab04ed6ac783e7", "id": "structlog.contextvars", "ignore_all": true, "interface_hash": "0b212fa1b2e543729367f35513454b5de44028384f2b201a4aa4e9e48f3099cd", "mtime": 1754207014, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/structlog/contextvars.py", "plugin_data": null, "size": 5141, "suppressed": [], "version_id": "1.7.1"}