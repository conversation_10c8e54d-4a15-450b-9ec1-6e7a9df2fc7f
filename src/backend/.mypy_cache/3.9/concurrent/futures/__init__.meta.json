{"data_mtime": 1754228257, "dep_lines": [3, 15, 16, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 30, 30, 30], "dependencies": ["concurrent.futures._base", "concurrent.futures.process", "concurrent.futures.thread", "sys", "builtins", "_typeshed", "abc", "typing"], "hash": "f0eb3066e0fa6f4f5c5abb5d218457c20f869ebfb6771ff8a5da2619f36604b4", "id": "concurrent.futures", "ignore_all": true, "interface_hash": "ec2e0ce90017ea6cc405993a71d9226ba576c991598a3c2f20b1ae3499931606", "mtime": 1754207148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/mypy/typeshed/stdlib/concurrent/futures/__init__.pyi", "plugin_data": null, "size": 880, "suppressed": [], "version_id": "1.7.1"}