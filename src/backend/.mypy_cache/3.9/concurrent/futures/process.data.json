{".class": "MypyFile", "_fullname": "concurrent.futures.process", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseContext": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.context.BaseContext", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BrokenExecutor": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.BrokenExecutor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BrokenProcessPool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["concurrent.futures._base.BrokenExecutor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures.process.BrokenProcessPool", "name": "BrokenProcessPool", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "concurrent.futures.process.BrokenProcessPool", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures.process", "mro": ["concurrent.futures.process.BrokenProcessPool", "concurrent.futures._base.BrokenExecutor", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process.BrokenProcessPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures.process.BrokenProcessPool", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Connection": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.connection.Connection", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EXTRA_QUEUED_CALLS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.process.EXTRA_QUEUED_CALLS", "name": "EXTRA_QUEUED_CALLS", "type": "builtins.int"}}, "Executor": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.Executor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Future": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.Future", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Lock": {".class": "SymbolTableNode", "cross_ref": "threading.Lock", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MutableSequence": {".class": "SymbolTableNode", "cross_ref": "typing.MutableSequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Process": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.context.Process", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ProcessPoolExecutor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["concurrent.futures._base.Executor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures.process.ProcessPoolExecutor", "name": "ProcessPoolExecutor", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "concurrent.futures.process.ProcessPoolExecutor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures.process", "mro": ["concurrent.futures.process.ProcessPoolExecutor", "concurrent.futures._base.Executor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "max_workers", "mp_context", "initializer", "initargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process.ProcessPoolExecutor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "max_workers", "mp_context", "initializer", "initargs"], "arg_types": ["concurrent.futures.process.ProcessPoolExecutor", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ProcessPoolExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_adjust_process_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process.ProcessPoolExecutor._adjust_process_count", "name": "_adjust_process_count", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["concurrent.futures.process.ProcessPoolExecutor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_adjust_process_count of ProcessPoolExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_broken": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process.ProcessPoolExecutor._broken", "name": "_broken", "type": "builtins.bool"}}, "_cancel_pending_futures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process.ProcessPoolExecutor._cancel_pending_futures", "name": "_cancel_pending_futures", "type": "builtins.bool"}}, "_executor_manager_thread": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process.ProcessPoolExecutor._executor_manager_thread", "name": "_executor_manager_thread", "type": "concurrent.futures.process._ThreadWakeup"}}, "_executor_manager_thread_wakeup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process.ProcessPoolExecutor._executor_manager_thread_wakeup", "name": "_executor_manager_thread_wakeup", "type": "concurrent.futures.process._ThreadWakeup"}}, "_idle_worker_semaphore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process.ProcessPoolExecutor._idle_worker_semaphore", "name": "_idle_worker_semaphore", "type": "threading.Semaphore"}}, "_initargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process.ProcessPoolExecutor._initargs", "name": "_initargs", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}}, "_initializer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process.ProcessPoolExecutor._initializer", "name": "_initializer", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}}, "_mp_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process.ProcessPoolExecutor._mp_context", "name": "_mp_context", "type": {".class": "UnionType", "items": ["multiprocessing.context.BaseContext", {".class": "NoneType"}]}}}, "_pending_work_items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process.ProcessPoolExecutor._pending_work_items", "name": "_pending_work_items", "type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "concurrent.futures.process._WorkItem"}], "type_ref": "builtins.dict"}}}, "_processes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process.ProcessPoolExecutor._processes", "name": "_processes", "type": {".class": "Instance", "args": ["builtins.int", "multiprocessing.context.Process"], "type_ref": "typing.MutableMapping"}}}, "_queue_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process.ProcessPoolExecutor._queue_count", "name": "_queue_count", "type": "builtins.int"}}, "_result_queue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process.ProcessPoolExecutor._result_queue", "name": "_result_queue", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "multiprocessing.queues.SimpleQueue"}}}, "_shutdown_lock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process.ProcessPoolExecutor._shutdown_lock", "name": "_shutdown_lock", "type": "threading.Lock"}}, "_shutdown_thread": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process.ProcessPoolExecutor._shutdown_thread", "name": "_shutdown_thread", "type": "builtins.bool"}}, "_start_executor_manager_thread": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process.ProcessPoolExecutor._start_executor_manager_thread", "name": "_start_executor_manager_thread", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["concurrent.futures.process.ProcessPoolExecutor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_start_executor_manager_thread of ProcessPoolExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_work_ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process.ProcessPoolExecutor._work_ids", "name": "_work_ids", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "multiprocessing.queues.Queue"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process.ProcessPoolExecutor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures.process.ProcessPoolExecutor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Queue": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.queues.Queue", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Semaphore": {".class": "SymbolTableNode", "cross_ref": "threading.Semaphore", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SimpleQueue": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.queues.SimpleQueue", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Thread": {".class": "SymbolTableNode", "cross_ref": "threading.Thread", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CallItem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures.process._CallItem", "name": "_CallItem", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "concurrent.futures.process._CallItem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures.process", "mro": ["concurrent.futures.process._CallItem", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "work_id", "fn", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._CallItem.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "work_id", "fn", "args", "kwargs"], "arg_types": ["concurrent.futures.process._CallItem", "builtins.int", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _CallItem", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._CallItem.args", "name": "args", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterable"}}}, "fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._CallItem.fn", "name": "fn", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._CallItem.kwargs", "name": "kwargs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}}}, "work_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._CallItem.work_id", "name": "work_id", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._CallItem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures.process._CallItem", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ExceptionWithTraceback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures.process._ExceptionWithTraceback", "name": "_ExceptionWithTraceback", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "concurrent.futures.process._ExceptionWithTraceback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures.process", "mro": ["concurrent.futures.process._ExceptionWithTraceback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "exc", "tb"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ExceptionWithTraceback.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "exc", "tb"], "arg_types": ["concurrent.futures.process._ExceptionWithTraceback", "builtins.BaseException", "types.TracebackType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ExceptionWithTraceback", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ExceptionWithTraceback.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["concurrent.futures.process._ExceptionWithTraceback"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of _ExceptionWithTraceback", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "exc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._ExceptionWithTraceback.exc", "name": "exc", "type": "builtins.BaseException"}}, "tb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._ExceptionWithTraceback.tb", "name": "tb", "type": "types.TracebackType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._ExceptionWithTraceback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures.process._ExceptionWithTraceback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ExecutorManagerThread": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["threading.Thread"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures.process._ExecutorManagerThread", "name": "_ExecutorManagerThread", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "concurrent.futures.process._ExecutorManagerThread", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures.process", "mro": ["concurrent.futures.process._ExecutorManagerThread", "threading.Thread", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "executor"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ExecutorManagerThread.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "executor"], "arg_types": ["concurrent.futures.process._ExecutorManagerThread", "concurrent.futures.process.ProcessPoolExecutor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ExecutorManagerThread", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "add_call_item_to_queue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ExecutorManagerThread.add_call_item_to_queue", "name": "add_call_item_to_queue", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["concurrent.futures.process._ExecutorManagerThread"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_call_item_to_queue of _ExecutorManagerThread", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "call_queue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._ExecutorManagerThread.call_queue", "name": "call_queue", "type": {".class": "Instance", "args": ["concurrent.futures.process._CallItem"], "type_ref": "multiprocessing.queues.Queue"}}}, "executor_reference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._ExecutorManagerThread.executor_reference", "name": "executor_reference", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "_weakref.ReferenceType"}}}, "flag_executor_shutting_down": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ExecutorManagerThread.flag_executor_shutting_down", "name": "flag_executor_shutting_down", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["concurrent.futures.process._ExecutorManagerThread"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flag_executor_shutting_down of _ExecutorManagerThread", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_n_children_alive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ExecutorManagerThread.get_n_children_alive", "name": "get_n_children_alive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["concurrent.futures.process._ExecutorManagerThread"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_n_children_alive of _ExecutorManagerThread", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_shutting_down": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ExecutorManagerThread.is_shutting_down", "name": "is_shutting_down", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["concurrent.futures.process._ExecutorManagerThread"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_shutting_down of _ExecutorManagerThread", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "join_executor_internals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ExecutorManagerThread.join_executor_internals", "name": "join_executor_internals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["concurrent.futures.process._ExecutorManagerThread"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join_executor_internals of _ExecutorManagerThread", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pending_work_items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._ExecutorManagerThread.pending_work_items", "name": "pending_work_items", "type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "concurrent.futures.process._WorkItem"}], "type_ref": "builtins.dict"}}}, "process_result_item": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result_item"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ExecutorManagerThread.process_result_item", "name": "process_result_item", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result_item"], "arg_types": ["concurrent.futures.process._ExecutorManagerThread", {".class": "UnionType", "items": ["builtins.int", "concurrent.futures.process._ResultItem"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_result_item of _ExecutorManagerThread", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "processes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._ExecutorManagerThread.processes", "name": "processes", "type": {".class": "Instance", "args": ["builtins.int", "multiprocessing.context.Process"], "type_ref": "typing.MutableMapping"}}}, "result_queue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._ExecutorManagerThread.result_queue", "name": "result_queue", "type": {".class": "Instance", "args": ["concurrent.futures.process._ResultItem"], "type_ref": "multiprocessing.queues.SimpleQueue"}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ExecutorManagerThread.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["concurrent.futures.process._ExecutorManagerThread"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of _ExecutorManagerThread", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "shutdown_lock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._ExecutorManagerThread.shutdown_lock", "name": "shutdown_lock", "type": "threading.Lock"}}, "shutdown_workers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ExecutorManagerThread.shutdown_workers", "name": "shutdown_workers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["concurrent.futures.process._ExecutorManagerThread"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shutdown_workers of _ExecutorManagerThread", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "terminate_broken": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cause"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ExecutorManagerThread.terminate_broken", "name": "terminate_broken", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cause"], "arg_types": ["concurrent.futures.process._ExecutorManagerThread", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "terminate_broken of _ExecutorManagerThread", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "thread_wakeup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._ExecutorManagerThread.thread_wakeup", "name": "thread_wakeup", "type": "concurrent.futures.process._ThreadWakeup"}}, "wait_result_broken_or_wakeup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ExecutorManagerThread.wait_result_broken_or_wakeup", "name": "wait_result_broken_or_wakeup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["concurrent.futures.process._ExecutorManagerThread"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait_result_broken_or_wakeup of _ExecutorManagerThread", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "work_ids_queue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._ExecutorManagerThread.work_ids_queue", "name": "work_ids_queue", "type": {".class": "Instance", "args": ["builtins.int"], "type_ref": "multiprocessing.queues.Queue"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._ExecutorManagerThread.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures.process._ExecutorManagerThread", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MAX_WINDOWS_WORKERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.process._MAX_WINDOWS_WORKERS", "name": "_MAX_WINDOWS_WORKERS", "type": "builtins.int"}}, "_RemoteTraceback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures.process._RemoteTraceback", "name": "_RemoteTraceback", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "concurrent.futures.process._RemoteTraceback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures.process", "mro": ["concurrent.futures.process._RemoteTraceback", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tb"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._RemoteTraceback.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tb"], "arg_types": ["concurrent.futures.process._RemoteTraceback", "types.TracebackType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _RemoteTraceback", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "tb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._RemoteTraceback.tb", "name": "tb", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._RemoteTraceback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures.process._RemoteTraceback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ResultItem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures.process._ResultItem", "name": "_ResultItem", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "concurrent.futures.process._ResultItem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures.process", "mro": ["concurrent.futures.process._ResultItem", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "work_id", "exception", "result"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ResultItem.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "work_id", "exception", "result"], "arg_types": ["concurrent.futures.process._ResultItem", "builtins.int", {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ResultItem", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._ResultItem.exception", "name": "exception", "type": "builtins.Exception"}}, "result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._ResultItem.result", "name": "result", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "work_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._ResultItem.work_id", "name": "work_id", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._ResultItem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures.process._ResultItem", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SafeQueue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "concurrent.futures._base.Future"}], "type_ref": "multiprocessing.queues.Queue"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures.process._SafeQueue", "name": "_SafeQueue", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "concurrent.futures.process._SafeQueue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures.process", "mro": ["concurrent.futures.process._SafeQueue", "multiprocessing.queues.Queue", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 3, 3, 3, 3], "arg_names": ["self", "max_size", "ctx", "pending_work_items", "shutdown_lock", "thread_wakeup"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._SafeQueue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 3, 3, 3, 3], "arg_names": ["self", "max_size", "ctx", "pending_work_items", "shutdown_lock", "thread_wakeup"], "arg_types": ["concurrent.futures.process._SafeQueue", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "multiprocessing.context.BaseContext", {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "concurrent.futures.process._WorkItem"}], "type_ref": "builtins.dict"}, "threading.Lock", "concurrent.futures.process._ThreadWakeup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _SafeQueue", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_on_queue_feeder_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "e", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._SafeQueue._on_queue_feeder_error", "name": "_on_queue_feeder_error", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "e", "obj"], "arg_types": ["concurrent.futures.process._SafeQueue", "builtins.Exception", "concurrent.futures.process._CallItem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_on_queue_feeder_error of _SafeQueue", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pending_work_items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._SafeQueue.pending_work_items", "name": "pending_work_items", "type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "concurrent.futures.process._WorkItem"}], "type_ref": "builtins.dict"}}}, "shutdown_lock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._SafeQueue.shutdown_lock", "name": "shutdown_lock", "type": "threading.Lock"}}, "thread_wakeup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._SafeQueue.thread_wakeup", "name": "thread_wakeup", "type": "concurrent.futures.process._ThreadWakeup"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._SafeQueue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures.process._SafeQueue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_ThreadWakeup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures.process._ThreadWakeup", "name": "_ThreadWakeup", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "concurrent.futures.process._ThreadWakeup", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures.process", "mro": ["concurrent.futures.process._ThreadWakeup", "builtins.object"], "names": {".class": "SymbolTable", "_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._ThreadWakeup._closed", "name": "_closed", "type": "builtins.bool"}}, "_reader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._ThreadWakeup._reader", "name": "_reader", "type": "multiprocessing.connection.Connection"}}, "_writer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._ThreadWakeup._writer", "name": "_writer", "type": "multiprocessing.connection.Connection"}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ThreadWakeup.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["concurrent.futures.process._ThreadWakeup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of _ThreadWakeup", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ThreadWakeup.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["concurrent.futures.process._ThreadWakeup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of _ThreadWakeup", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "wakeup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._ThreadWakeup.wakeup", "name": "wakeup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["concurrent.futures.process._ThreadWakeup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wakeup of _ThreadWakeup", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._ThreadWakeup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "concurrent.futures.process._ThreadWakeup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WorkItem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "concurrent.futures.process._WorkItem", "name": "_WorkItem", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._T", "id": 1, "name": "_T", "namespace": "concurrent.futures.process._WorkItem", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "concurrent.futures.process._WorkItem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "concurrent.futures.process", "mro": ["concurrent.futures.process._WorkItem", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "future", "fn", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._WorkItem.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "future", "fn", "args", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._T", "id": 1, "name": "_T", "namespace": "concurrent.futures.process._WorkItem", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "concurrent.futures.process._WorkItem"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._T", "id": 1, "name": "_T", "namespace": "concurrent.futures.process._WorkItem", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "concurrent.futures._base.Future"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._T", "id": 1, "name": "_T", "namespace": "concurrent.futures.process._WorkItem", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _WorkItem", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._WorkItem.args", "name": "args", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterable"}}}, "fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._WorkItem.fn", "name": "fn", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._T", "id": 1, "name": "_T", "namespace": "concurrent.futures.process._WorkItem", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "future": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._WorkItem.future", "name": "future", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._T", "id": 1, "name": "_T", "namespace": "concurrent.futures.process._WorkItem", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "concurrent.futures._base.Future"}}}, "kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "concurrent.futures.process._WorkItem.kwargs", "name": "kwargs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._WorkItem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._T", "id": 1, "name": "_T", "namespace": "concurrent.futures.process._WorkItem", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "concurrent.futures.process._WorkItem"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.process.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.process.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.process.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.process.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.process.__package__", "name": "__package__", "type": "builtins.str"}}, "_chain_from_iterable_of_lists": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["iterable"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._chain_from_iterable_of_lists", "name": "_chain_from_iterable_of_lists", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["iterable"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.MutableSequence"}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_chain_from_iterable_of_lists", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_check_system_limits": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._check_system_limits", "name": "_check_system_limits", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_system_limits", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_get_chunks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 3], "arg_names": ["iterables", "chunksize"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._get_chunks", "name": "_get_chunks", "type": {".class": "CallableType", "arg_kinds": [2, 3], "arg_names": ["iterables", "chunksize"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_chunks", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_global_shutdown": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.process._global_shutdown", "name": "_global_shutdown", "type": "builtins.bool"}}, "_process_chunk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fn", "chunk"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._process_chunk", "name": "_process_chunk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fn", "chunk"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_chunk", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "concurrent.futures.process._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_process_worker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["call_queue", "result_queue", "initializer", "initargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._process_worker", "name": "_process_worker", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["call_queue", "result_queue", "initializer", "initargs"], "arg_types": [{".class": "Instance", "args": ["concurrent.futures.process._CallItem"], "type_ref": "multiprocessing.queues.Queue"}, {".class": "Instance", "args": ["concurrent.futures.process._ResultItem"], "type_ref": "multiprocessing.queues.SimpleQueue"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_worker", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_python_exit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._python_exit", "name": "_python_exit", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_python_exit", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_rebuild_exc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["exc", "tb"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._rebuild_exc", "name": "_rebuild_exc", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["exc", "tb"], "arg_types": ["builtins.Exception", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_rebuild_exc", "ret_type": "builtins.Exception", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_sendback_result": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["result_queue", "work_id", "result", "exception"], "dataclass_transform_spec": null, "flags": [], "fullname": "concurrent.futures.process._sendback_result", "name": "_sendback_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["result_queue", "work_id", "result", "exception"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "concurrent.futures.process._WorkItem"}], "type_ref": "multiprocessing.queues.SimpleQueue"}, "builtins.int", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sendback_result", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_system_limited": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.process._system_limited", "name": "_system_limited", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}, "_system_limits_checked": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.process._system_limits_checked", "name": "_system_limits_checked", "type": "builtins.bool"}}, "_threads_wakeups": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.process._threads_wakeups", "name": "_threads_wakeups", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.MutableMapping"}}}, "ref": {".class": "SymbolTableNode", "cross_ref": "_weakref.ref", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/mypy/typeshed/stdlib/concurrent/futures/process.pyi"}