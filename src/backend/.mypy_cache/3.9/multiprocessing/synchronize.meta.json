{"data_mtime": 1754228257, "dep_lines": [2, 4, 1, 3, 5, 6, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "multiprocessing.context", "threading", "contextlib", "types", "typing_extensions", "builtins", "abc", "typing"], "hash": "83baee5fac4f689b390c0c516211b0a63bdcbb5c0d00ef8fe3014d061c1b71ce", "id": "multiprocessing.synchronize", "ignore_all": true, "interface_hash": "8af2f5395bf2e91e812a9c5490383b2b790a15c066db909e23bc9534e795ac48", "mtime": 1754207148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/mypy/typeshed/stdlib/multiprocessing/synchronize.pyi", "plugin_data": null, "size": 2128, "suppressed": [], "version_id": "1.7.1"}