{"data_mtime": 1754228257, "dep_lines": [6, 8, 1, 2, 3, 4, 5, 7, 8, 10, 11, 12, 1], "dep_prios": [5, 10, 5, 10, 5, 5, 5, 5, 20, 5, 5, 5, 30], "dependencies": ["collections.abc", "multiprocessing.connection", "pickle", "sys", "_typeshed", "abc", "builtins", "copyreg", "multiprocessing", "socket", "typing", "typing_extensions", "_socket"], "hash": "3ff0e1669f3d0457d08b49ca9beed71d000d28230488494d019df662c3080336", "id": "multiprocessing.reduction", "ignore_all": true, "interface_hash": "05f9cfec0f4dc4fe4665671e13c3e6c2c0a0102a591ea92435de00564879bea7", "mtime": 1754207148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/mypy/typeshed/stdlib/multiprocessing/reduction.pyi", "plugin_data": null, "size": 3330, "suppressed": [], "version_id": "1.7.1"}