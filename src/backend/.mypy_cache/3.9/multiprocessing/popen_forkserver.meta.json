{"data_mtime": 1754228257, "dep_lines": [4, 5, 1, 2, 4, 1, 1], "dep_prios": [10, 5, 10, 5, 20, 5, 30], "dependencies": ["multiprocessing.popen_fork", "multiprocessing.util", "sys", "typing", "multiprocessing", "builtins", "abc"], "hash": "f9ff39d5c1d011b33f2fda176b0e8fad41c8e9b28055ab11475ece8ab39277ad", "id": "multiprocessing.popen_forkserver", "ignore_all": true, "interface_hash": "6c1f74b8ebde200ab9974a4879369bd1407649ca028eeb394bcc33e87114b623", "mtime": 1754207148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/mypy/typeshed/stdlib/multiprocessing/popen_forkserver.pyi", "plugin_data": null, "size": 353, "suppressed": [], "version_id": "1.7.1"}