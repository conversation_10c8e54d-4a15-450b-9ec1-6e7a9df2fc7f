{"data_mtime": 1754228257, "dep_lines": [13, 1, 2, 3, 12, 14, 15, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 30], "dependencies": ["collections.abc", "sys", "_typeshed", "_weakref", "_weakrefset", "typing", "typing_extensions", "builtins", "abc"], "hash": "c6c5363c092743e64a7b04ea06abcfadb105c7f5ec193b98a2712667ddf9b850", "id": "weakref", "ignore_all": true, "interface_hash": "0683323a8a677a148c19af3ca33cf79848137a7d5d7623d3dd73ab5f9872b8b3", "mtime": 1754207148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/mypy/typeshed/stdlib/weakref.pyi", "plugin_data": null, "size": 6471, "suppressed": [], "version_id": "1.7.1"}