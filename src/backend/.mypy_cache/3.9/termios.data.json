{".class": "MypyFile", "_fullname": "termios", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "B0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B0", "name": "B0", "type": "builtins.int"}}, "B1000000": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B1000000", "name": "B1000000", "type": "builtins.int"}}, "B110": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B110", "name": "B110", "type": "builtins.int"}}, "B115200": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B115200", "name": "B115200", "type": "builtins.int"}}, "B1152000": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B1152000", "name": "B1152000", "type": "builtins.int"}}, "B1200": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B1200", "name": "B1200", "type": "builtins.int"}}, "B134": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B134", "name": "B134", "type": "builtins.int"}}, "B150": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B150", "name": "B150", "type": "builtins.int"}}, "B1500000": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B1500000", "name": "B1500000", "type": "builtins.int"}}, "B1800": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B1800", "name": "B1800", "type": "builtins.int"}}, "B19200": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B19200", "name": "B19200", "type": "builtins.int"}}, "B200": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B200", "name": "B200", "type": "builtins.int"}}, "B2000000": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B2000000", "name": "B2000000", "type": "builtins.int"}}, "B230400": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B230400", "name": "B230400", "type": "builtins.int"}}, "B2400": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B2400", "name": "B2400", "type": "builtins.int"}}, "B2500000": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B2500000", "name": "B2500000", "type": "builtins.int"}}, "B300": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B300", "name": "B300", "type": "builtins.int"}}, "B3000000": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B3000000", "name": "B3000000", "type": "builtins.int"}}, "B3500000": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B3500000", "name": "B3500000", "type": "builtins.int"}}, "B38400": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B38400", "name": "B38400", "type": "builtins.int"}}, "B4000000": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B4000000", "name": "B4000000", "type": "builtins.int"}}, "B460800": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B460800", "name": "B460800", "type": "builtins.int"}}, "B4800": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B4800", "name": "B4800", "type": "builtins.int"}}, "B50": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B50", "name": "B50", "type": "builtins.int"}}, "B500000": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B500000", "name": "B500000", "type": "builtins.int"}}, "B57600": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B57600", "name": "B57600", "type": "builtins.int"}}, "B576000": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B576000", "name": "B576000", "type": "builtins.int"}}, "B600": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B600", "name": "B600", "type": "builtins.int"}}, "B75": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B75", "name": "B75", "type": "builtins.int"}}, "B921600": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B921600", "name": "B921600", "type": "builtins.int"}}, "B9600": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.B9600", "name": "B9600", "type": "builtins.int"}}, "BRKINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.BRKINT", "name": "BRKINT", "type": "builtins.int"}}, "BS0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.BS0", "name": "BS0", "type": "builtins.int"}}, "BS1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.BS1", "name": "BS1", "type": "builtins.int"}}, "BSDLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.BSDLY", "name": "BSDLY", "type": "builtins.int"}}, "CBAUD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CBAUD", "name": "CBAUD", "type": "builtins.int"}}, "CBAUDEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CBAUDEX", "name": "CBAUDEX", "type": "builtins.int"}}, "CDEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CDEL", "name": "CDEL", "type": "builtins.int"}}, "CDSUSP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CDSUSP", "name": "CDSUSP", "type": "builtins.int"}}, "CEOF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CEOF", "name": "CEOF", "type": "builtins.int"}}, "CEOL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CEOL", "name": "CEOL", "type": "builtins.int"}}, "CEOL2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CEOL2", "name": "CEOL2", "type": "builtins.int"}}, "CEOT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CEOT", "name": "CEOT", "type": "builtins.int"}}, "CERASE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CERASE", "name": "CERASE", "type": "builtins.int"}}, "CESC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CESC", "name": "CESC", "type": "builtins.int"}}, "CFLUSH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CFLUSH", "name": "CFLUSH", "type": "builtins.int"}}, "CIBAUD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CIBAUD", "name": "CIBAUD", "type": "builtins.int"}}, "CINTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CINTR", "name": "CINTR", "type": "builtins.int"}}, "CKILL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CKILL", "name": "CKILL", "type": "builtins.int"}}, "CLNEXT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CLNEXT", "name": "CLNEXT", "type": "builtins.int"}}, "CLOCAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CLOCAL", "name": "CLOCAL", "type": "builtins.int"}}, "CNUL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CNUL", "name": "CNUL", "type": "builtins.int"}}, "COMMON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.COMMON", "name": "COMMON", "type": "builtins.int"}}, "CQUIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CQUIT", "name": "CQUIT", "type": "builtins.int"}}, "CR0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CR0", "name": "CR0", "type": "builtins.int"}}, "CR1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CR1", "name": "CR1", "type": "builtins.int"}}, "CR2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CR2", "name": "CR2", "type": "builtins.int"}}, "CR3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CR3", "name": "CR3", "type": "builtins.int"}}, "CRDLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CRDLY", "name": "CRDLY", "type": "builtins.int"}}, "CREAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CREAD", "name": "CREAD", "type": "builtins.int"}}, "CRPRNT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CRPRNT", "name": "CRPRNT", "type": "builtins.int"}}, "CRTSCTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CRTSCTS", "name": "CRTSCTS", "type": "builtins.int"}}, "CS5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CS5", "name": "CS5", "type": "builtins.int"}}, "CS6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CS6", "name": "CS6", "type": "builtins.int"}}, "CS7": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CS7", "name": "CS7", "type": "builtins.int"}}, "CS8": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CS8", "name": "CS8", "type": "builtins.int"}}, "CSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CSIZE", "name": "CSIZE", "type": "builtins.int"}}, "CSTART": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CSTART", "name": "CSTART", "type": "builtins.int"}}, "CSTOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CSTOP", "name": "CSTOP", "type": "builtins.int"}}, "CSTOPB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CSTOPB", "name": "CSTOPB", "type": "builtins.int"}}, "CSUSP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CSUSP", "name": "CSUSP", "type": "builtins.int"}}, "CSWTCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CSWTCH", "name": "CSWTCH", "type": "builtins.int"}}, "CWERASE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.CWERASE", "name": "CWERASE", "type": "builtins.int"}}, "ECHO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.ECHO", "name": "ECHO", "type": "builtins.int"}}, "ECHOCTL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.ECHOCTL", "name": "ECHOCTL", "type": "builtins.int"}}, "ECHOE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.ECHOE", "name": "ECHOE", "type": "builtins.int"}}, "ECHOK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.ECHOK", "name": "ECHOK", "type": "builtins.int"}}, "ECHOKE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.ECHOKE", "name": "ECHOKE", "type": "builtins.int"}}, "ECHONL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.ECHONL", "name": "ECHONL", "type": "builtins.int"}}, "ECHOPRT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.ECHOPRT", "name": "ECHOPRT", "type": "builtins.int"}}, "EXTA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.EXTA", "name": "EXTA", "type": "builtins.int"}}, "EXTB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.EXTB", "name": "EXTB", "type": "builtins.int"}}, "FF0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.FF0", "name": "FF0", "type": "builtins.int"}}, "FF1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.FF1", "name": "FF1", "type": "builtins.int"}}, "FFDLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.FFDLY", "name": "FFDLY", "type": "builtins.int"}}, "FIOASYNC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.FIOASYNC", "name": "FIOASYNC", "type": "builtins.int"}}, "FIOCLEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.FIOCLEX", "name": "FIOCLEX", "type": "builtins.int"}}, "FIONBIO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.FIONBIO", "name": "FIONBIO", "type": "builtins.int"}}, "FIONCLEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.FIONCLEX", "name": "FIONCLEX", "type": "builtins.int"}}, "FIONREAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.FIONREAD", "name": "FIONREAD", "type": "builtins.int"}}, "FLUSHO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.FLUSHO", "name": "FLUSHO", "type": "builtins.int"}}, "FileDescriptorLike": {".class": "SymbolTableNode", "cross_ref": "_typeshed.FileDescriptorLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HUPCL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.HUPCL", "name": "HUPCL", "type": "builtins.int"}}, "IBSHIFT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.IBSHIFT", "name": "IBSHIFT", "type": "builtins.int"}}, "ICANON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.ICANON", "name": "ICANON", "type": "builtins.int"}}, "ICRNL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.ICRNL", "name": "ICRNL", "type": "builtins.int"}}, "IEXTEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.IEXTEN", "name": "IEXTEN", "type": "builtins.int"}}, "IGNBRK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.IGNBRK", "name": "IGNBRK", "type": "builtins.int"}}, "IGNCR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.IGNCR", "name": "IGNCR", "type": "builtins.int"}}, "IGNPAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.IGNPAR", "name": "IGNPAR", "type": "builtins.int"}}, "IMAXBEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.IMAXBEL", "name": "IMAXBEL", "type": "builtins.int"}}, "INIT_C_CC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.INIT_C_CC", "name": "INIT_C_CC", "type": "builtins.int"}}, "INLCR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.INLCR", "name": "INLCR", "type": "builtins.int"}}, "INPCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.INPCK", "name": "INPCK", "type": "builtins.int"}}, "IOCSIZE_MASK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.IOCSIZE_MASK", "name": "IOCSIZE_MASK", "type": "builtins.int"}}, "IOCSIZE_SHIFT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.IOCSIZE_SHIFT", "name": "IOCSIZE_SHIFT", "type": "builtins.int"}}, "ISIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.ISIG", "name": "ISIG", "type": "builtins.int"}}, "ISTRIP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.ISTRIP", "name": "ISTRIP", "type": "builtins.int"}}, "IUCLC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.IUCLC", "name": "IUCLC", "type": "builtins.int"}}, "IXANY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.IXANY", "name": "IXANY", "type": "builtins.int"}}, "IXOFF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.IXOFF", "name": "IXOFF", "type": "builtins.int"}}, "IXON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.IXON", "name": "IXON", "type": "builtins.int"}}, "NCC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.NCC", "name": "NCC", "type": "builtins.int"}}, "NCCS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.NCCS", "name": "NCCS", "type": "builtins.int"}}, "NL0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.NL0", "name": "NL0", "type": "builtins.int"}}, "NL1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.NL1", "name": "NL1", "type": "builtins.int"}}, "NLDLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.NLDLY", "name": "NLDLY", "type": "builtins.int"}}, "NOFLSH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.NOFLSH", "name": "NOFLSH", "type": "builtins.int"}}, "NSWTCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.NSWTCH", "name": "NSWTCH", "type": "builtins.int"}}, "N_MOUSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.N_MOUSE", "name": "N_MOUSE", "type": "builtins.int"}}, "N_PPP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.N_PPP", "name": "N_PPP", "type": "builtins.int"}}, "N_SLIP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.N_SLIP", "name": "N_SLIP", "type": "builtins.int"}}, "N_STRIP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.N_STRIP", "name": "N_STRIP", "type": "builtins.int"}}, "N_TTY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.N_TTY", "name": "N_TTY", "type": "builtins.int"}}, "OCRNL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.OCRNL", "name": "OCRNL", "type": "builtins.int"}}, "OFDEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.OFDEL", "name": "OFDEL", "type": "builtins.int"}}, "OFILL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.OFILL", "name": "OFILL", "type": "builtins.int"}}, "OLCUC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.OLCUC", "name": "OLCUC", "type": "builtins.int"}}, "ONLCR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.ONLCR", "name": "ONLCR", "type": "builtins.int"}}, "ONLRET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.ONLRET", "name": "ONLRET", "type": "builtins.int"}}, "ONOCR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.ONOCR", "name": "ONOCR", "type": "builtins.int"}}, "OPOST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.OPOST", "name": "OPOST", "type": "builtins.int"}}, "PARENB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.PARENB", "name": "PARENB", "type": "builtins.int"}}, "PARMRK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.PARMRK", "name": "PARMRK", "type": "builtins.int"}}, "PARODD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.PARODD", "name": "PARODD", "type": "builtins.int"}}, "PENDIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.PENDIN", "name": "PENDIN", "type": "builtins.int"}}, "TAB0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TAB0", "name": "TAB0", "type": "builtins.int"}}, "TAB1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TAB1", "name": "TAB1", "type": "builtins.int"}}, "TAB2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TAB2", "name": "TAB2", "type": "builtins.int"}}, "TAB3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TAB3", "name": "TAB3", "type": "builtins.int"}}, "TABDLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TABDLY", "name": "TABDLY", "type": "builtins.int"}}, "TCFLSH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCFLSH", "name": "TCFLSH", "type": "builtins.int"}}, "TCGETA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCGETA", "name": "TCGETA", "type": "builtins.int"}}, "TCGETS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCGETS", "name": "TCGETS", "type": "builtins.int"}}, "TCIFLUSH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCIFLUSH", "name": "TCIFLUSH", "type": "builtins.int"}}, "TCIOFF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCIOFF", "name": "TCIOFF", "type": "builtins.int"}}, "TCIOFLUSH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCIOFLUSH", "name": "TCIOFLUSH", "type": "builtins.int"}}, "TCION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCION", "name": "TCION", "type": "builtins.int"}}, "TCOFLUSH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCOFLUSH", "name": "TCOFLUSH", "type": "builtins.int"}}, "TCOOFF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCOOFF", "name": "TCOOFF", "type": "builtins.int"}}, "TCOON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCOON", "name": "TCOON", "type": "builtins.int"}}, "TCSADRAIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCSADRAIN", "name": "TCSADRAIN", "type": "builtins.int"}}, "TCSAFLUSH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCSAFLUSH", "name": "TCSAFLUSH", "type": "builtins.int"}}, "TCSANOW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCSANOW", "name": "TCSANOW", "type": "builtins.int"}}, "TCSASOFT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCSASOFT", "name": "TCSASOFT", "type": "builtins.int"}}, "TCSBRK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCSBRK", "name": "TCSBRK", "type": "builtins.int"}}, "TCSBRKP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCSBRKP", "name": "TCSBRKP", "type": "builtins.int"}}, "TCSETA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCSETA", "name": "TCSETA", "type": "builtins.int"}}, "TCSETAF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCSETAF", "name": "TCSETAF", "type": "builtins.int"}}, "TCSETAW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCSETAW", "name": "TCSETAW", "type": "builtins.int"}}, "TCSETS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCSETS", "name": "TCSETS", "type": "builtins.int"}}, "TCSETSF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCSETSF", "name": "TCSETSF", "type": "builtins.int"}}, "TCSETSW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCSETSW", "name": "TCSETSW", "type": "builtins.int"}}, "TCXONC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TCXONC", "name": "TCXONC", "type": "builtins.int"}}, "TIOCCONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCCONS", "name": "TIOCCONS", "type": "builtins.int"}}, "TIOCEXCL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCEXCL", "name": "TIOCEXCL", "type": "builtins.int"}}, "TIOCGETD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCGETD", "name": "TIOCGETD", "type": "builtins.int"}}, "TIOCGICOUNT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCGICOUNT", "name": "TIOCGICOUNT", "type": "builtins.int"}}, "TIOCGLCKTRMIOS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCGLCKTRMIOS", "name": "TIOCGLCKTRMIOS", "type": "builtins.int"}}, "TIOCGPGRP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCGPGRP", "name": "TIOCGPGRP", "type": "builtins.int"}}, "TIOCGSERIAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCGSERIAL", "name": "TIOCGSERIAL", "type": "builtins.int"}}, "TIOCGSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCGSIZE", "name": "TIOCGSIZE", "type": "builtins.int"}}, "TIOCGSOFTCAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCGSOFTCAR", "name": "TIOCGSOFTCAR", "type": "builtins.int"}}, "TIOCGWINSZ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCGWINSZ", "name": "TIOCGWINSZ", "type": "builtins.int"}}, "TIOCINQ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCINQ", "name": "TIOCINQ", "type": "builtins.int"}}, "TIOCLINUX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCLINUX", "name": "TIOCLINUX", "type": "builtins.int"}}, "TIOCMBIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCMBIC", "name": "TIOCMBIC", "type": "builtins.int"}}, "TIOCMBIS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCMBIS", "name": "TIOCMBIS", "type": "builtins.int"}}, "TIOCMGET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCMGET", "name": "TIOCMGET", "type": "builtins.int"}}, "TIOCMIWAIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCMIWAIT", "name": "TIOCMIWAIT", "type": "builtins.int"}}, "TIOCMSET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCMSET", "name": "TIOCMSET", "type": "builtins.int"}}, "TIOCM_CAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCM_CAR", "name": "TIOCM_CAR", "type": "builtins.int"}}, "TIOCM_CD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCM_CD", "name": "TIOCM_CD", "type": "builtins.int"}}, "TIOCM_CTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCM_CTS", "name": "TIOCM_CTS", "type": "builtins.int"}}, "TIOCM_DSR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCM_DSR", "name": "TIOCM_DSR", "type": "builtins.int"}}, "TIOCM_DTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCM_DTR", "name": "TIOCM_DTR", "type": "builtins.int"}}, "TIOCM_LE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCM_LE", "name": "TIOCM_LE", "type": "builtins.int"}}, "TIOCM_RI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCM_RI", "name": "TIOCM_RI", "type": "builtins.int"}}, "TIOCM_RNG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCM_RNG", "name": "TIOCM_RNG", "type": "builtins.int"}}, "TIOCM_RTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCM_RTS", "name": "TIOCM_RTS", "type": "builtins.int"}}, "TIOCM_SR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCM_SR", "name": "TIOCM_SR", "type": "builtins.int"}}, "TIOCM_ST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCM_ST", "name": "TIOCM_ST", "type": "builtins.int"}}, "TIOCNOTTY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCNOTTY", "name": "TIOCNOTTY", "type": "builtins.int"}}, "TIOCNXCL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCNXCL", "name": "TIOCNXCL", "type": "builtins.int"}}, "TIOCOUTQ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCOUTQ", "name": "TIOCOUTQ", "type": "builtins.int"}}, "TIOCPKT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCPKT", "name": "TIOCPKT", "type": "builtins.int"}}, "TIOCPKT_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCPKT_DATA", "name": "TIOCPKT_DATA", "type": "builtins.int"}}, "TIOCPKT_DOSTOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCPKT_DOSTOP", "name": "TIOCPKT_DOSTOP", "type": "builtins.int"}}, "TIOCPKT_FLUSHREAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCPKT_FLUSHREAD", "name": "TIOCPKT_FLUSHREAD", "type": "builtins.int"}}, "TIOCPKT_FLUSHWRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCPKT_FLUSHWRITE", "name": "TIOCPKT_FLUSHWRITE", "type": "builtins.int"}}, "TIOCPKT_NOSTOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCPKT_NOSTOP", "name": "TIOCPKT_NOSTOP", "type": "builtins.int"}}, "TIOCPKT_START": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCPKT_START", "name": "TIOCPKT_START", "type": "builtins.int"}}, "TIOCPKT_STOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCPKT_STOP", "name": "TIOCPKT_STOP", "type": "builtins.int"}}, "TIOCSCTTY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSCTTY", "name": "TIOCSCTTY", "type": "builtins.int"}}, "TIOCSERCONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSERCONFIG", "name": "TIOCSERCONFIG", "type": "builtins.int"}}, "TIOCSERGETLSR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSERGETLSR", "name": "TIOCSERGETLSR", "type": "builtins.int"}}, "TIOCSERGETMULTI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSERGETMULTI", "name": "TIOCSERGETMULTI", "type": "builtins.int"}}, "TIOCSERGSTRUCT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSERGSTRUCT", "name": "TIOCSERGSTRUCT", "type": "builtins.int"}}, "TIOCSERGWILD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSERGWILD", "name": "TIOCSERGWILD", "type": "builtins.int"}}, "TIOCSERSETMULTI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSERSETMULTI", "name": "TIOCSERSETMULTI", "type": "builtins.int"}}, "TIOCSERSWILD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSERSWILD", "name": "TIOCSERSWILD", "type": "builtins.int"}}, "TIOCSER_TEMT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSER_TEMT", "name": "TIOCSER_TEMT", "type": "builtins.int"}}, "TIOCSETD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSETD", "name": "TIOCSETD", "type": "builtins.int"}}, "TIOCSLCKTRMIOS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSLCKTRMIOS", "name": "TIOCSLCKTRMIOS", "type": "builtins.int"}}, "TIOCSPGRP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSPGRP", "name": "TIOCSPGRP", "type": "builtins.int"}}, "TIOCSSERIAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSSERIAL", "name": "TIOCSSERIAL", "type": "builtins.int"}}, "TIOCSSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSSIZE", "name": "TIOCSSIZE", "type": "builtins.int"}}, "TIOCSSOFTCAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSSOFTCAR", "name": "TIOCSSOFTCAR", "type": "builtins.int"}}, "TIOCSTI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSTI", "name": "TIOCSTI", "type": "builtins.int"}}, "TIOCSWINSZ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCSWINSZ", "name": "TIOCSWINSZ", "type": "builtins.int"}}, "TIOCTTYGSTRUCT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TIOCTTYGSTRUCT", "name": "TIOCTTYGSTRUCT", "type": "builtins.int"}}, "TOSTOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.TOSTOP", "name": "TOSTOP", "type": "builtins.int"}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "VDISCARD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VDISCARD", "name": "VDISCARD", "type": "builtins.int"}}, "VEOF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VEOF", "name": "VEOF", "type": "builtins.int"}}, "VEOL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VEOL", "name": "VEOL", "type": "builtins.int"}}, "VEOL2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VEOL2", "name": "VEOL2", "type": "builtins.int"}}, "VERASE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VERASE", "name": "VERASE", "type": "builtins.int"}}, "VINTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VINTR", "name": "VINTR", "type": "builtins.int"}}, "VKILL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VKILL", "name": "VKILL", "type": "builtins.int"}}, "VLNEXT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VLNEXT", "name": "VLNEXT", "type": "builtins.int"}}, "VMIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VMIN", "name": "VMIN", "type": "builtins.int"}}, "VQUIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VQUIT", "name": "VQUIT", "type": "builtins.int"}}, "VREPRINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VREPRINT", "name": "VREPRINT", "type": "builtins.int"}}, "VSTART": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VSTART", "name": "VSTART", "type": "builtins.int"}}, "VSTOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VSTOP", "name": "VSTOP", "type": "builtins.int"}}, "VSUSP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VSUSP", "name": "VSUSP", "type": "builtins.int"}}, "VSWTC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VSWTC", "name": "VSWTC", "type": "builtins.int"}}, "VSWTCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VSWTCH", "name": "VSWTCH", "type": "builtins.int"}}, "VT0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VT0", "name": "VT0", "type": "builtins.int"}}, "VT1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VT1", "name": "VT1", "type": "builtins.int"}}, "VTDLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VTDLY", "name": "VTDLY", "type": "builtins.int"}}, "VTIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VTIME", "name": "VTIME", "type": "builtins.int"}}, "VWERASE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.VWERASE", "name": "VWERASE", "type": "builtins.int"}}, "XCASE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.XCASE", "name": "XCASE", "type": "builtins.int"}}, "XTABS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.XTABS", "name": "XTABS", "type": "builtins.int"}}, "_Attr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "termios._Attr", "line": 7, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.int"]}], "type_ref": "builtins.list"}]}], "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.bytes"], "type_ref": "builtins.list"}]}], "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "type_ref": "builtins.list"}]}], "type_ref": "builtins.list"}]}}}, "_AttrReturn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "termios._AttrReturn", "line": 9, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "termios.__package__", "name": "__package__", "type": "builtins.str"}}, "error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "termios.error", "name": "error", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "termios.error", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "termios", "mro": ["termios.error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "termios.error.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "termios.error", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "tcdrain": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "termios.tcdrain", "name": "tcdrain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.FileDescriptorLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tcdrain", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "tcflow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "termios.tcflow", "name": "tcflow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.FileDescriptorLike"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tcflow", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "tcflush": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "termios.tcflush", "name": "tcflush", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.FileDescriptorLike"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tcflush", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "tcgetattr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "termios.tcgetattr", "name": "tcgetattr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.FileDescriptorLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tcgetattr", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "termios._AttrReturn"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "tcsendbreak": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "termios.tcsendbreak", "name": "tcsendbreak", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.FileDescriptorLike"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tcsendbreak", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "tcsetattr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "termios.tcsetattr", "name": "tcsetattr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.FileDescriptorLike"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "termios._Attr"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tcsetattr", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/mypy/typeshed/stdlib/termios.pyi"}