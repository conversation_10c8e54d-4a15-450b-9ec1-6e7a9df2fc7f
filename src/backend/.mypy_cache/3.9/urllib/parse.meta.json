{"data_mtime": 1754228257, "dep_lines": [2, 1, 3, 4, 7, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "sys", "typing", "typing_extensions", "types", "builtins", "_typeshed", "abc"], "hash": "9287a7f222fba8bc2687913f39901a13c2ea5f00638158f8f20397227702b7d1", "id": "urllib.parse", "ignore_all": true, "interface_hash": "9dbc303d3050f7d3b58de3286b55d7d5ecdef19806158127f84509e15b569704", "mtime": 1754207148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/mypy/typeshed/stdlib/urllib/parse.pyi", "plugin_data": null, "size": 6532, "suppressed": [], "version_id": "1.7.1"}