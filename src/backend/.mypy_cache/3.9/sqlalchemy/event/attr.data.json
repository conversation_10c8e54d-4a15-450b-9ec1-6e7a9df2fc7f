{".class": "MypyFile", "_fullname": "sqlalchemy.event.attr", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncAdaptedLock": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.AsyncAdaptedLock", "kind": "Gdef"}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef"}, "Deque": {".class": "SymbolTableNode", "cross_ref": "typing.Deque", "kind": "Gdef"}, "FrozenSet": {".class": "SymbolTableNode", "cross_ref": "typing.FrozenSet", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "MutableSequence": {".class": "SymbolTableNode", "cross_ref": "typing.MutableSequence", "kind": "Gdef"}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef"}, "RefCollection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.util.langhelpers.MemoizedSlots"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.attr.RefCollection", "name": "RefCollection", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr.RefCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.event.attr.RefCollection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.event.attr", "mro": ["sqlalchemy.event.attr.RefCollection", "sqlalchemy.util.langhelpers.MemoizedSlots", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.event.attr.RefCollection.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_memoized_attr_ref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr.RefCollection._memoized_attr_ref", "name": "_memoized_attr_ref", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr.RefCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr.RefCollection"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_memoized_attr_ref of RefCollection", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr.RefCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr.RefCollection"}], "type_ref": "_weakref.ReferenceType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "ref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr.RefCollection.ref", "name": "ref", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr.RefCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr.RefCollection"}], "type_ref": "_weakref.ReferenceType"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr.RefCollection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr.RefCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr.RefCollection"}, "values": [], "variance": 0}, "slots": ["ref"], "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_ClsLevelDispatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr.RefCollection"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.attr._ClsLevelDispatch", "name": "_ClsLevelDispatch", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.event.attr", "mro": ["sqlalchemy.event.attr._ClsLevelDispatch", "sqlalchemy.event.attr.RefCollection", "sqlalchemy.util.langhelpers.MemoizedSlots", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent_dispatch_cls", "fn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent_dispatch_cls", "fn"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ClsLevelDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_adjust_fn_spec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fn", "named"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch._adjust_fn_spec", "name": "_adjust_fn_spec", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fn", "named"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_adjust_fn_spec of _ClsLevelDispatch", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_clslevel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch._clslevel", "name": "_clslevel", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "type_ref": "sqlalchemy.event.attr._ListenerFnSequenceType"}], "type_ref": "typing.MutableMapping"}}}, "_do_insert_or_append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "is_append"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch._do_insert_or_append", "name": "_do_insert_or_append", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "is_append"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_do_insert_or_append of _ClsLevelDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_wrap_fn_for_kw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch._wrap_fn_for_kw", "name": "_wrap_fn_for_kw", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_wrap_fn_for_kw of _ClsLevelDispatch", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "propagate"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "propagate"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append of _ClsLevelDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "arg_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch.arg_names", "name": "arg_names", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of _ClsLevelDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "clsname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch.clsname", "name": "clsname", "type": "builtins.str"}}, "for_modify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch.for_modify", "name": "for_modify", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.base._Dispatch"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "for_modify of _ClsLevelDispatch", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "has_kw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch.has_kw", "name": "has_kw", "type": "builtins.bool"}}, "insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "propagate"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch.insert", "name": "insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "propagate"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert of _ClsLevelDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "legacy_signatures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch.legacy_signatures", "name": "legacy_signatures", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.legacy._LegacySignatureType"}], "type_ref": "typing.MutableSequence"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch.name", "name": "name", "type": "builtins.str"}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event_key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event_key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove of _ClsLevelDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "update_subclass": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "target"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ClsLevelDispatch.update_subclass", "name": "update_subclass", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "target"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_subclass of _ClsLevelDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._ClsLevelDispatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ClsLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, "values": [], "variance": 0}, "slots": ["__weakref__", "_clslevel", "arg_names", "clsname", "has_kw", "legacy_signatures", "name", "ref"], "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "_CompoundListener": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._CompoundListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.attr._CompoundListener", "name": "_CompoundListener", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._CompoundListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.event.attr._CompoundListener", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.event.attr", "mro": ["sqlalchemy.event.attr._CompoundListener", "sqlalchemy.event.attr._InstanceLevelDispatch", "sqlalchemy.event.attr.RefCollection", "sqlalchemy.util.langhelpers.MemoizedSlots", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._CompoundListener.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._CompoundListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._CompoundListener"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bool__ of _CompoundListener", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._CompoundListener.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._CompoundListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._CompoundListener"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _CompoundListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._CompoundListener.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._CompoundListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._CompoundListener"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of _CompoundListener", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._CompoundListener.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._CompoundListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._CompoundListener"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of _CompoundListener", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._CompoundListener.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._CompoundListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._CompoundListener"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of _CompoundListener", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.event.attr._CompoundListener.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_exec_once": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._CompoundListener._exec_once", "name": "_exec_once", "type": "builtins.bool"}}, "_exec_once_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "retry_on_exception", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._CompoundListener._exec_once_impl", "name": "_exec_once_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "retry_on_exception", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._CompoundListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._CompoundListener"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_exec_once_impl of _CompoundListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_exec_once_mutex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._CompoundListener._exec_once_mutex", "name": "_exec_once_mutex", "type": "sqlalchemy.event.attr._MutexProtocol"}}, "_exec_w_sync_on_first_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._CompoundListener._exec_w_sync_on_first_run", "name": "_exec_w_sync_on_first_run", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._CompoundListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._CompoundListener"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_exec_w_sync_on_first_run of _CompoundListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_exec_w_sync_once": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._CompoundListener._exec_w_sync_once", "name": "_exec_w_sync_once", "type": "builtins.bool"}}, "_memoized_attr__exec_once_mutex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._CompoundListener._memoized_attr__exec_once_mutex", "name": "_memoized_attr__exec_once_mutex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._CompoundListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._CompoundListener"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_memoized_attr__exec_once_mutex of _CompoundListener", "ret_type": "sqlalchemy.event.attr._MutexProtocol", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_set_asyncio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._CompoundListener._set_asyncio", "name": "_set_asyncio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._CompoundListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._CompoundListener"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_asyncio of _CompoundListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "exec_once": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._CompoundListener.exec_once", "name": "exec_once", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._CompoundListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._CompoundListener"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exec_once of _CompoundListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "exec_once_unless_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._CompoundListener.exec_once_unless_exception", "name": "exec_once_unless_exception", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._CompoundListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._CompoundListener"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exec_once_unless_exception of _CompoundListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "listeners": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._CompoundListener.listeners", "name": "listeners", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "type_ref": "typing.Collection"}}}, "parent_listeners": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._CompoundListener.parent_listeners", "name": "parent_listeners", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "type_ref": "typing.Collection"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._CompoundListener.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._CompoundListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._CompoundListener"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "_Dispatch": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.base._Dispatch", "kind": "Gdef"}, "_DispatchCommon": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.base._DispatchCommon", "kind": "Gdef"}, "_ET": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry._ET", "kind": "Gdef"}, "_EmptyListener": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.attr._EmptyListener", "name": "_EmptyListener", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.event.attr._EmptyListener", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.event.attr", "mro": ["sqlalchemy.event.attr._EmptyListener", "sqlalchemy.event.attr._InstanceLevelDispatch", "sqlalchemy.event.attr.RefCollection", "sqlalchemy.util.langhelpers.MemoizedSlots", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._EmptyListener.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._EmptyListener"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bool__ of _EmptyListener", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._EmptyListener.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._EmptyListener"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _EmptyListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._EmptyListener.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._EmptyListener"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of _EmptyListener", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "target_cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._EmptyListener.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "target_cls"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._EmptyListener"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _EmptyListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._EmptyListener.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._EmptyListener"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of _EmptyListener", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._EmptyListener.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._EmptyListener"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of _EmptyListener", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.event.attr._EmptyListener.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_needs_modify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._EmptyListener._needs_modify", "name": "_needs_modify", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._EmptyListener"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_needs_modify of _EmptyListener", "ret_type": {".class": "UninhabitedType", "is_noreturn": true}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._EmptyListener.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._EmptyListener"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append of _EmptyListener", "ret_type": {".class": "UninhabitedType", "is_noreturn": true}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._EmptyListener.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._EmptyListener"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of _EmptyListener", "ret_type": {".class": "UninhabitedType", "is_noreturn": true}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "exec_once": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._EmptyListener.exec_once", "name": "exec_once", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._EmptyListener"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exec_once of _EmptyListener", "ret_type": {".class": "UninhabitedType", "is_noreturn": true}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "exec_once_unless_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._EmptyListener.exec_once_unless_exception", "name": "exec_once_unless_exception", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._EmptyListener"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exec_once_unless_exception of _EmptyListener", "ret_type": {".class": "UninhabitedType", "is_noreturn": true}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "for_modify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._EmptyListener.for_modify", "name": "for_modify", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._EmptyListener"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.base._DispatchCommon"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "for_modify of _EmptyListener", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ListenerCollection"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._EmptyListener.insert", "name": "insert", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._EmptyListener"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert of _EmptyListener", "ret_type": {".class": "UninhabitedType", "is_noreturn": true}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "listeners": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.event.attr._EmptyListener.listeners", "name": "listeners", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._EmptyListener.name", "name": "name", "type": "builtins.str"}}, "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._EmptyListener.parent", "name": "parent", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}}}, "parent_listeners": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._EmptyListener.parent_listeners", "name": "parent_listeners", "type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "type_ref": "sqlalchemy.event.attr._ListenerFnSequenceType"}}}, "propagate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.event.attr._EmptyListener.propagate", "name": "propagate", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "type_ref": "builtins.frozenset"}}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._EmptyListener.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._EmptyListener"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove of _EmptyListener", "ret_type": {".class": "UninhabitedType", "is_noreturn": true}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._EmptyListener.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._EmptyListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._EmptyListener"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "_EventKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry._EventKey", "kind": "Gdef"}, "_HasEventsDispatch": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.base._HasEventsDispatch", "kind": "Gdef"}, "_InstanceLevelDispatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr.RefCollection"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "type_ref": "typing.Collection"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch", "name": "_InstanceLevelDispatch", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.event.attr", "mro": ["sqlalchemy.event.attr._InstanceLevelDispatch", "sqlalchemy.event.attr.RefCollection", "sqlalchemy.util.langhelpers.MemoizedSlots", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bool__ of _InstanceLevelDispatch", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _InstanceLevelDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of _InstanceLevelDispatch", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of _InstanceLevelDispatch", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of _InstanceLevelDispatch", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_adjust_fn_spec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fn", "named"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch._adjust_fn_spec", "name": "_adjust_fn_spec", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fn", "named"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_adjust_fn_spec of _InstanceLevelDispatch", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_exec_w_sync_on_first_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch._exec_w_sync_on_first_run", "name": "_exec_w_sync_on_first_run", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_exec_w_sync_on_first_run of _InstanceLevelDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "propagate"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "propagate"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append of _InstanceLevelDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "exec_once": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch.exec_once", "name": "exec_once", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exec_once of _InstanceLevelDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "exec_once_unless_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch.exec_once_unless_exception", "name": "exec_once_unless_exception", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exec_once_unless_exception of _InstanceLevelDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "for_modify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch.for_modify", "name": "for_modify", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.base._DispatchCommon"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "for_modify of _InstanceLevelDispatch", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "propagate"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch.insert", "name": "insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "propagate"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert of _InstanceLevelDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch.parent", "name": "parent", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event_key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event_key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove of _InstanceLevelDispatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._InstanceLevelDispatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._InstanceLevelDispatch", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "_JoinedListener": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._CompoundListener"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.attr._JoinedListener", "name": "_JoinedListener", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.event.attr._JoinedListener", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.event.attr", "mro": ["sqlalchemy.event.attr._JoinedListener", "sqlalchemy.event.attr._CompoundListener", "sqlalchemy.event.attr._InstanceLevelDispatch", "sqlalchemy.event.attr.RefCollection", "sqlalchemy.util.langhelpers.MemoizedSlots", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "parent_dispatch", "name", "local"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._JoinedListener.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "parent_dispatch", "name", "local"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._JoinedListener"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.base._DispatchCommon"}, "builtins.str", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._EmptyListener"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _JoinedListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.event.attr._JoinedListener.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_adjust_fn_spec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fn", "named"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._JoinedListener._adjust_fn_spec", "name": "_adjust_fn_spec", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fn", "named"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._JoinedListener"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_adjust_fn_spec of _JoinedListener", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "propagate"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._JoinedListener.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "propagate"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._JoinedListener"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append of _JoinedListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._JoinedListener.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._JoinedListener"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of _JoinedListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "for_modify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._JoinedListener.for_modify", "name": "for_modify", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._JoinedListener"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.base._DispatchCommon"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "for_modify of _JoinedListener", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._JoinedListener"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "propagate"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._JoinedListener.insert", "name": "insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "propagate"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._JoinedListener"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert of _JoinedListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "local": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._JoinedListener.local", "name": "local", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._InstanceLevelDispatch"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._JoinedListener.name", "name": "name", "type": "builtins.str"}}, "parent_dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._JoinedListener.parent_dispatch", "name": "parent_dispatch", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.base._DispatchCommon"}}}, "parent_listeners": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._JoinedListener.parent_listeners", "name": "parent_listeners", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "type_ref": "typing.Collection"}}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event_key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._JoinedListener.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event_key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._JoinedListener"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove of _JoinedListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._JoinedListener.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._JoinedListener", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._JoinedListener"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "_ListenerCollection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._CompoundListener"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.attr._ListenerCollection", "name": "_ListenerCollection", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.event.attr._ListenerCollection", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.event.attr", "mro": ["sqlalchemy.event.attr._ListenerCollection", "sqlalchemy.event.attr._CompoundListener", "sqlalchemy.event.attr._InstanceLevelDispatch", "sqlalchemy.event.attr.RefCollection", "sqlalchemy.util.langhelpers.MemoizedSlots", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "target_cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ListenerCollection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "target_cls"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ListenerCollection"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ListenerCollection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.event.attr._ListenerCollection.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "only_propagate"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ListenerCollection._update", "name": "_update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "other", "only_propagate"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ListenerCollection"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ListenerCollection"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update of _ListenerCollection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "propagate"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ListenerCollection.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "propagate"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ListenerCollection"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append of _ListenerCollection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ListenerCollection.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ListenerCollection"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of _ListenerCollection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "for_modify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ListenerCollection.for_modify", "name": "for_modify", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ListenerCollection"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.base._DispatchCommon"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "for_modify of _ListenerCollection", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ListenerCollection"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "propagate"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ListenerCollection.insert", "name": "insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event_key", "propagate"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ListenerCollection"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert of _ListenerCollection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "listeners": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._ListenerCollection.listeners", "name": "listeners", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "type_ref": "collections.deque"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._ListenerCollection.name", "name": "name", "type": "builtins.str"}}, "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._ListenerCollection.parent", "name": "parent", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ClsLevelDispatch"}}}, "parent_listeners": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._ListenerCollection.parent_listeners", "name": "parent_listeners", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "type_ref": "typing.Collection"}}}, "propagate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.attr._ListenerCollection.propagate", "name": "propagate", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "type_ref": "builtins.set"}}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event_key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._ListenerCollection.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event_key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ListenerCollection"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove of _ListenerCollection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._ListenerCollection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.attr._ListenerCollection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._ListenerCollection"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "_ListenerFnSequenceType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._ListenerFnSequenceType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.event.attr._ListenerFnSequenceType", "line": 110, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._ListenerFnSequenceType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "collections.deque"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._ListenerFnSequenceType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._empty_collection"}]}}}, "_ListenerFnType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry._ListenerFnType", "kind": "Gdef"}, "_MutexProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__enter__", 2], ["__exit__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.attr._MutexProtocol", "name": "_MutexProtocol", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.event.attr._MutexProtocol", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.event.attr", "mro": ["sqlalchemy.event.attr._MutexProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.event.attr._MutexProtocol.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.event.attr._MutexProtocol"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of _MutexProtocol", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.event.attr._MutexProtocol.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["sqlalchemy.event.attr._MutexProtocol", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of _MutexProtocol", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._MutexProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.event.attr._MutexProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.attr.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.attr.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.attr.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.attr.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.attr.__package__", "name": "__package__", "type": "builtins.str"}}, "_empty_collection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._empty_collection", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "typing.Collection"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.attr._empty_collection", "name": "_empty_collection", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._empty_collection", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.event.attr._empty_collection", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.event.attr", "mro": ["sqlalchemy.event.attr._empty_collection", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._empty_collection.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._empty_collection", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._empty_collection"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of _empty_collection", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._empty_collection.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._empty_collection", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._empty_collection"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of _empty_collection", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._empty_collection", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._empty_collection.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._empty_collection", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._empty_collection"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of _empty_collection", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "element"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._empty_collection.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "element"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._empty_collection", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._empty_collection"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._empty_collection", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append of _empty_collection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "appendleft": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "element"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._empty_collection.appendleft", "name": "appendleft", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "element"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._empty_collection", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._empty_collection"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._empty_collection", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "appendleft of _empty_collection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._empty_collection.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._empty_collection", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._empty_collection"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of _empty_collection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "extend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._empty_collection.extend", "name": "extend", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._empty_collection", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._empty_collection"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._empty_collection", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extend of _empty_collection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "element"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.event.attr._empty_collection.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "element"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._empty_collection", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._empty_collection"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._empty_collection", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove of _empty_collection", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._empty_collection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.attr._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.event.attr._empty_collection", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr._empty_collection"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "itertools.chain", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "legacy": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.legacy", "kind": "Gdef"}, "registry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/event/attr.py"}