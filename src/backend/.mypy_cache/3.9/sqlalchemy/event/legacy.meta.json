{"data_mtime": 1754228259, "dep_lines": [22, 25, 28, 29, 24, 12, 14, 24, 1, 1, 1], "dep_prios": [5, 5, 25, 25, 10, 5, 5, 20, 5, 30, 30], "dependencies": ["sqlalchemy.event.registry", "sqlalchemy.util.compat", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "abc", "sqlalchemy.util.langhelpers"], "hash": "3a93ea13ae319353988cb5b5b1cbdcea28a38686b919926de5fefe6de5a180e7", "id": "sqlalchemy.event.legacy", "ignore_all": true, "interface_hash": "d5d639e0667b674b0f6377fafb408ad57c9a104e3d8f3fcc43400235e85e766e", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/event/legacy.py", "plugin_data": null, "size": 8211, "suppressed": [], "version_id": "1.7.1"}