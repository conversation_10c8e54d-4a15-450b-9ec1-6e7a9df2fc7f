{"data_mtime": 1754228259, "dep_lines": [10, 16, 18, 22, 23, 8, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["sqlalchemy.event.api", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.legacy", "sqlalchemy.event.registry", "__future__", "builtins", "abc", "typing"], "hash": "09204ca74caee63a130bab56bf1e34078a7cecdee818ac42f992f1d942ca5274", "id": "sqlalchemy.event", "ignore_all": true, "interface_hash": "9f8cd8907b6b5c7e5970e439ae854ac308a068526899450a430cf13f78cc271f", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/event/__init__.py", "plugin_data": null, "size": 997, "suppressed": [], "version_id": "1.7.1"}