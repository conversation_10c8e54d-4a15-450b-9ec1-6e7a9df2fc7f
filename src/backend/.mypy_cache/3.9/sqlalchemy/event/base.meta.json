{"data_mtime": 1754228259, "dep_lines": [36, 40, 43, 42, 18, 20, 34, 42, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.event.attr", "sqlalchemy.event.registry", "sqlalchemy.util.typing", "sqlalchemy.util", "__future__", "typing", "weakref", "sqlalchemy", "builtins", "_typeshed", "abc", "collections", "sqlalchemy.util.langhelpers", "types"], "hash": "0abfcf349942609494debb53f039299728c145bf84d966b738078af70a052649", "id": "sqlalchemy.event.base", "ignore_all": true, "interface_hash": "b8e4799db9a5f5566acdc37863b7368435f59e80f6c4966e9beb3743e38784b5", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/event/base.py", "plugin_data": null, "size": 14980, "suppressed": [], "version_id": "1.7.1"}