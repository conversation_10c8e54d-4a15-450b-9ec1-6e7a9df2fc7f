{"data_mtime": 1754228259, "dep_lines": [39, 40, 35, 36, 17, 19, 20, 21, 33, 35, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 10, 10, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30], "dependencies": ["sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "collections", "types", "typing", "weakref", "sqlalchemy", "builtins", "_typeshed", "_weakref", "abc", "sqlalchemy.util.langhelpers"], "hash": "66283dab619a968f243b66aaafb6b6acd02198891d27e9ed1d211c33931f4a0c", "id": "sqlalchemy.event.registry", "ignore_all": true, "interface_hash": "2a4af3e0c1c1e5208a044f3a7ddec5cc5b9c3f02b89219e6c853bf14bce86576", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/event/registry.py", "plugin_data": null, "size": 10833, "suppressed": [], "version_id": "1.7.1"}