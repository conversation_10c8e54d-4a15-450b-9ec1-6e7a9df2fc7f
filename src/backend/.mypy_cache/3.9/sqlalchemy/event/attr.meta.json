{"data_mtime": 1754228259, "dep_lines": [57, 58, 64, 65, 70, 57, 62, 63, 31, 33, 34, 35, 36, 37, 55, 62, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 25, 20, 10, 10, 5, 10, 5, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.event.legacy", "sqlalchemy.event.registry", "sqlalchemy.util.concurrency", "sqlalchemy.util.typing", "sqlalchemy.event.base", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "collections", "itertools", "threading", "types", "typing", "weakref", "sqlalchemy", "builtins", "_typeshed", "_weakref", "abc", "sqlalchemy.util._concurrency_py3k", "sqlalchemy.util.compat", "sqlalchemy.util.langhelpers"], "hash": "34c7bfb0f4138eed8f13e7faf02f1370a2465be1b1ca2d422d7ba6026137ebc6", "id": "sqlalchemy.event.attr", "ignore_all": true, "interface_hash": "e02746ec2acdd799c9f513636d2f5572069d2260365057bffa1a87469e1a6094", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/event/attr.py", "plugin_data": null, "size": 20438, "suppressed": [], "version_id": "1.7.1"}