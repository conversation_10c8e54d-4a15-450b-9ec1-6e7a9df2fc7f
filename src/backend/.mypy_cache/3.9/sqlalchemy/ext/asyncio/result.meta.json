{"data_mtime": 1754228260, "dep_lines": [19, 19, 22, 28, 30, 31, 32, 20, 21, 7, 9, 10, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 5, 5, 5, 5, 10, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.ext.asyncio.exc", "sqlalchemy.ext.asyncio", "sqlalchemy.engine.result", "sqlalchemy.engine.row", "sqlalchemy.sql.base", "sqlalchemy.util.concurrency", "sqlalchemy.util.typing", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "operator", "typing", "sqlalchemy", "builtins", "_operator", "abc", "sqlalchemy.engine._py_row", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._concurrency_py3k", "sqlalchemy.util.langhelpers"], "hash": "cc44dead5079de0aa5d432fab643bf262a9e53e9b538cfbc917d142f19a82ff2", "id": "sqlalchemy.ext.asyncio.result", "ignore_all": true, "interface_hash": "1bf4f45893ac5c2002e1bac8f111eff3327dc24de2c1e8fa506bbf99166e756a", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/ext/asyncio/result.py", "plugin_data": null, "size": 30554, "suppressed": [], "version_id": "1.7.1"}