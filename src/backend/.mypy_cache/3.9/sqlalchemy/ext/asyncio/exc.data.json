{".class": "MypyFile", "_fullname": "sqlalchemy.ext.asyncio.exc", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncContextAlreadyStarted": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.ext.asyncio.exc.AsyncContextAlreadyStarted", "name": "AsyncContextAlreadyStarted", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.ext.asyncio.exc.AsyncContextAlreadyStarted", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.ext.asyncio.exc", "mro": ["sqlalchemy.ext.asyncio.exc.AsyncContextAlreadyStarted", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.exc.AsyncContextAlreadyStarted.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.ext.asyncio.exc.AsyncContextAlreadyStarted", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncContextNotStarted": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.ext.asyncio.exc.AsyncContextNotStarted", "name": "AsyncContextNotStarted", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.ext.asyncio.exc.AsyncContextNotStarted", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.ext.asyncio.exc", "mro": ["sqlalchemy.ext.asyncio.exc.AsyncContextNotStarted", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.exc.AsyncContextNotStarted.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.ext.asyncio.exc.AsyncContextNotStarted", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncMethodRequired": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.ext.asyncio.exc.AsyncMethodRequired", "name": "AsyncMethodRequired", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.ext.asyncio.exc.AsyncMethodRequired", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.ext.asyncio.exc", "mro": ["sqlalchemy.ext.asyncio.exc.AsyncMethodRequired", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.exc.AsyncMethodRequired.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.ext.asyncio.exc.AsyncMethodRequired", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.exc.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.exc.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.exc.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.exc.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.exc.__package__", "name": "__package__", "type": "builtins.str"}}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/ext/asyncio/exc.py"}