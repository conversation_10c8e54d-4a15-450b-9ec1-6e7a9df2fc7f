{"data_mtime": 1754228260, "dep_lines": [28, 28, 30, 29, 8, 10, 11, 12, 26, 29, 1, 1, 1], "dep_prios": [10, 20, 5, 10, 5, 10, 10, 5, 10, 20, 5, 30, 30], "dependencies": ["sqlalchemy.ext.asyncio.exc", "sqlalchemy.ext.asyncio", "sqlalchemy.util.typing", "sqlalchemy.util", "__future__", "abc", "functools", "typing", "weakref", "sqlalchemy", "builtins", "_weakref", "sqlalchemy.exc"], "hash": "3d717862a7d18b6fa600302d68bdbff94bfb0b3a015688cf6f3c80e69849f53a", "id": "sqlalchemy.ext.asyncio.base", "ignore_all": true, "interface_hash": "05b6cbc907fa5c69945fffda08e5128c41ea4852c6381e43abe561989a906884", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/ext/asyncio/base.py", "plugin_data": null, "size": 8959, "suppressed": [], "version_id": "1.7.1"}