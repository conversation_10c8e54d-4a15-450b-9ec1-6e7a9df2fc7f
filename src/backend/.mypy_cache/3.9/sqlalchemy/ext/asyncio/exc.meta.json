{"data_mtime": 1754228260, "dep_lines": [8, 8, 1, 1, 1], "dep_prios": [10, 20, 5, 30, 30], "dependencies": ["sqlalchemy.exc", "sqlalchemy", "builtins", "abc", "typing"], "hash": "d6109d38acef4abc9cfd81388e08f497d24048e9995eeb5dcd284460f88b6c62", "id": "sqlalchemy.ext.asyncio.exc", "ignore_all": true, "interface_hash": "2eaa26a5dee5535bda9bbdb95dcee7402bcb424ffa10c187a320cae427bef778", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/ext/asyncio/exc.py", "plugin_data": null, "size": 639, "suppressed": [], "version_id": "1.7.1"}