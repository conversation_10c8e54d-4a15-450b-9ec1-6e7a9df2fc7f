{"data_mtime": 1754228260, "dep_lines": [8, 14, 18, 19, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30], "dependencies": ["sqlalchemy.ext.asyncio.engine", "sqlalchemy.ext.asyncio.result", "sqlalchemy.ext.asyncio.scoping", "sqlalchemy.ext.asyncio.session", "builtins", "abc", "typing"], "hash": "886ff44e604ed69081df5e964bea75ec0226c2a46d52868aa3b52985867b6d8c", "id": "sqlalchemy.ext.asyncio", "ignore_all": true, "interface_hash": "b645d7786dc5f36e9e468527d90624ef5ef67ca852bbe48bc277c3d5e45b4931", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/ext/asyncio/__init__.py", "plugin_data": null, "size": 1317, "suppressed": [], "version_id": "1.7.1"}