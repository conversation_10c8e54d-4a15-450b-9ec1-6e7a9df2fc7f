{"data_mtime": 1754228259, "dep_lines": [22, 23, 10, 12, 22, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 5, 30, 30], "dependencies": ["sqlalchemy.util", "sqlalchemy.exc", "__future__", "typing", "sqlalchemy", "builtins", "abc", "collections"], "hash": "8632562f70bf07b469bfdb3b8e3ef071372471951293173ac510e188dd71c9e7", "id": "sqlalchemy.util.topological", "ignore_all": true, "interface_hash": "921a765e608cd000dc2ee513573f38b2fc49de99de06cc90d25cb83e5116500d", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/util/topological.py", "plugin_data": null, "size": 3458, "suppressed": [], "version_id": "1.7.1"}