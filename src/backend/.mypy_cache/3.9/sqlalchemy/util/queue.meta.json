{"data_mtime": 1754228259, "dep_lines": [35, 37, 21, 23, 24, 25, 26, 27, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 10, 5, 5, 5, 30, 30], "dependencies": ["sqlalchemy.util.concurrency", "sqlalchemy.util.langhelpers", "__future__", "asyncio", "collections", "threading", "time", "typing", "builtins", "abc", "asyncio.queues"], "hash": "2137a3b3a292e07cffa23aecb36a057943bd32821e477a9699943c27bcb2acd5", "id": "sqlalchemy.util.queue", "ignore_all": true, "interface_hash": "48ec783627d89f67ac4dbb03f908c6598e9d1d2099f5568901301867af9bf230", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/util/queue.py", "plugin_data": null, "size": 10205, "suppressed": [], "version_id": "1.7.1"}