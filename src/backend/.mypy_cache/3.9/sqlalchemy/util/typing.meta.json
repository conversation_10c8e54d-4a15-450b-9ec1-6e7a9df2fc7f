{"data_mtime": 1754228259, "dep_lines": [34, 34, 9, 11, 12, 13, 14, 37, 68, 1], "dep_prios": [10, 20, 5, 10, 10, 10, 5, 5, 5, 30], "dependencies": ["sqlalchemy.util.compat", "sqlalchemy.util", "__future__", "builtins", "re", "sys", "typing", "typing_extensions", "types", "abc"], "hash": "112626e2842db7e49aacdd386135c2828bd74fcb45ba93223e6b8608308c1087", "id": "sqlalchemy.util.typing", "ignore_all": true, "interface_hash": "0d9511cc0110e624fa14664aca411172ffd9c8db7df10b01f8668ff6cccd2e66", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/util/typing.py", "plugin_data": null, "size": 15831, "suppressed": [], "version_id": "1.7.1"}