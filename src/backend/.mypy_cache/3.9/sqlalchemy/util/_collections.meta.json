{"data_mtime": 1754228259, "dep_lines": [38, 39, 43, 12, 10, 12, 13, 14, 15, 16, 36, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 10, 5, 20, 10, 10, 10, 5, 10, 5, 30, 30, 30], "dependencies": ["sqlalchemy.util._has_cy", "sqlalchemy.util.typing", "sqlalchemy.util._py_collections", "collections.abc", "__future__", "collections", "operator", "threading", "types", "typing", "weakref", "builtins", "_typeshed", "_weakref", "abc"], "hash": "158a95420dc26aa884776d4e14dd693427c56d0f20be57215bf4ccb6285214d1", "id": "sqlalchemy.util._collections", "ignore_all": true, "interface_hash": "b2d95640f46dcf2981e610684ebb2395767185263413eca56543cf78e9808df2", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/util/_collections.py", "plugin_data": null, "size": 20169, "suppressed": [], "version_id": "1.7.1"}