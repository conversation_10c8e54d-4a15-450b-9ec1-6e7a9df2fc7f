{"data_mtime": 1754228259, "dep_lines": [23, 9, 11, 12, 1, 1, 1, 17], "dep_prios": [5, 5, 10, 10, 5, 30, 30, 10], "dependencies": ["sqlalchemy.util._concurrency_py3k", "__future__", "asyncio", "typing", "builtins", "abc", "typing_extensions"], "hash": "67171060e2b2f8606c4243e60ab04ee4cccca6a5b725999ed878b2aa96edf6e7", "id": "sqlalchemy.util.concurrency", "ignore_all": true, "interface_hash": "f3a38508ab33ee0c94d0be69ce988a3956a14d4554dffc3bbf84aee373eca5b5", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/util/concurrency.py", "plugin_data": null, "size": 2284, "suppressed": ["greenlet"], "version_id": "1.7.1"}