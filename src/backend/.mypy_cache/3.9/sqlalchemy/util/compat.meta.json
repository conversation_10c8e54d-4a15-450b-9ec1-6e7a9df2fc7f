{"data_mtime": 1754228257, "dep_lines": [136, 156, 11, 13, 14, 15, 16, 17, 18, 19, 20, 136, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 25, 5, 30, 30, 30, 30], "dependencies": ["importlib.metadata", "collections.abc", "__future__", "base64", "dataclasses", "<PERSON><PERSON><PERSON>", "inspect", "operator", "platform", "sys", "typing", "importlib", "builtins", "_operator", "_typeshed", "abc", "typing_extensions"], "hash": "1647879d6f5ab09609bcd9b1565b5e7bc8d037041275545d289955451227248e", "id": "sqlalchemy.util.compat", "ignore_all": true, "interface_hash": "99ab7f5ec8eadecfd6f7e13faca2f35db123046d0c14c4cdf33c0c678c49d8fc", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/util/compat.py", "plugin_data": null, "size": 9388, "suppressed": [], "version_id": "1.7.1"}