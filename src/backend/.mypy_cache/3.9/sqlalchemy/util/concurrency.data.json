{".class": "MypyFile", "_fullname": "sqlalchemy.util.concurrency", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncAdaptedLock": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.AsyncAdaptedLock", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.concurrency.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.concurrency.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.concurrency.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.concurrency.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.concurrency.__package__", "name": "__package__", "type": "builtins.str"}}, "_util_async_run": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k._util_async_run", "kind": "Gdef"}, "_util_async_run_coroutine_function": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k._util_async_run_coroutine_function", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "await_fallback": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.await_fallback", "kind": "Gdef"}, "await_only": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.await_only", "kind": "Gdef"}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.util.concurrency.e", "name": "e", "type": {".class": "DeletedType", "source": "e"}}}, "greenlet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sqlalchemy.util.concurrency.greenlet", "name": "greenlet", "type": {".class": "AnyType", "missing_import_name": "sqlalchemy.util.concurrency.greenlet", "source_any": null, "type_of_any": 3}}}, "greenlet_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.util.concurrency.greenlet_error", "name": "greenlet_error", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "greenlet_spawn": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.greenlet_spawn", "kind": "Gdef"}, "have_greenlet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.util.concurrency.have_greenlet", "name": "have_greenlet", "type": "builtins.bool"}}, "is_exit_exception": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.is_exit_exception", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/util/concurrency.py"}