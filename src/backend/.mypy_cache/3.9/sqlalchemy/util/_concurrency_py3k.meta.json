{"data_mtime": 1754228259, "dep_lines": [23, 25, 24, 9, 11, 12, 13, 14, 24, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 10, 5, 10, 5, 20, 5, 30, 30, 30], "dependencies": ["sqlalchemy.util.langhelpers", "sqlalchemy.util.typing", "sqlalchemy.exc", "__future__", "asyncio", "<PERSON><PERSON><PERSON>", "sys", "typing", "sqlalchemy", "builtins", "abc", "asyncio.events", "asyncio.locks"], "hash": "df5becd685da2f379346098e5d1ad64e8450b245a6264f8206cdfe27149371c9", "id": "sqlalchemy.util._concurrency_py3k", "ignore_all": true, "interface_hash": "7c82af8b749ddd37ec7c3b4f439b784f231166d8e0451ac52ba29f140789444d", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/util/_concurrency_py3k.py", "plugin_data": null, "size": 8223, "suppressed": [], "version_id": "1.7.1"}