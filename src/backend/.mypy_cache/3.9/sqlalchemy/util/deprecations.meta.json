{"data_mtime": 1754228259, "dep_lines": [27, 28, 27, 33, 12, 14, 15, 33, 1, 1], "dep_prios": [10, 5, 20, 10, 5, 10, 5, 20, 5, 30], "dependencies": ["sqlalchemy.util.compat", "sqlalchemy.util.langhelpers", "sqlalchemy.util", "sqlalchemy.exc", "__future__", "re", "typing", "sqlalchemy", "builtins", "abc"], "hash": "a6bf434807f5102a8393b5fb17a4cd7358eb84e7858a12f7dee11be56b76fd3d", "id": "sqlalchemy.util.deprecations", "ignore_all": true, "interface_hash": "0f56da92f4a2420193a869c59da6d0cb659f479520da221150c76a68889762c4", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/util/deprecations.py", "plugin_data": null, "size": 11971, "suppressed": [], "version_id": "1.7.1"}