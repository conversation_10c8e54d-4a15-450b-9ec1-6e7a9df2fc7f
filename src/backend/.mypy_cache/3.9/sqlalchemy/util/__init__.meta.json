{"data_mtime": 1754228259, "dep_lines": [14, 15, 50, 73, 77, 83, 9, 10, 12, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30], "dependencies": ["sqlalchemy.util.preloaded", "sqlalchemy.util._collections", "sqlalchemy.util.compat", "sqlalchemy.util.concurrency", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "collections", "functools", "typing", "builtins", "abc"], "hash": "061d1291f91e0accfafab6c3982e3594058eb82bca0a25d566d84dd9c5891179", "id": "sqlalchemy.util", "ignore_all": true, "interface_hash": "fc25cad4b07ff6134abd8a0adf0cbd65447c4c071f1b8f2529ee945c062c8958", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/util/__init__.py", "plugin_data": null, "size": 8245, "suppressed": [], "version_id": "1.7.1"}