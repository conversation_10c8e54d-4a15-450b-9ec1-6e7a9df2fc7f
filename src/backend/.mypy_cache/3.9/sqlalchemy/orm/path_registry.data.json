{".class": "MypyFile", "_fullname": "sqlalchemy.orm.path_registry", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractEntityRegistry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.path_registry.CreatesToken"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "name": "AbstractEntityRegistry", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.path_registry", "mro": ["sqlalchemy.orm.path_registry.AbstractEntityRegistry", "sqlalchemy.orm.path_registry.CreatesToken", "sqlalchemy.orm.path_registry.PathRegistry", "sqlalchemy.sql.cache_key.HasCacheKey", "builtins.object"], "names": {".class": "SymbolTable", "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.AbstractEntityRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bool__ of AbstractEntityRegistry", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "entity"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "entity"], "arg_types": ["sqlalchemy.orm.path_registry.AbstractEntityRegistry", {".class": "UnionType", "items": ["sqlalchemy.orm.path_registry.RootRegistry", "sqlalchemy.orm.path_registry.PropRegistry"]}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AbstractEntityRegistry", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_getitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "entity"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry._getitem", "name": "_getitem", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "entity"], "arg_types": ["sqlalchemy.orm.path_registry.AbstractEntityRegistry", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_getitem of AbstractEntityRegistry", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathElementType"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}, "sqlalchemy.orm.path_registry.PathRegistry"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_truncate_recursive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry._truncate_recursive", "name": "_truncate_recursive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.AbstractEntityRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_truncate_recursive of AbstractEntityRegistry", "ret_type": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry.entity", "name": "entity", "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}}}, "entity_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry.entity_path", "name": "entity_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.AbstractEntityRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity_path of AbstractEntityRegistry", "ret_type": "sqlalchemy.orm.path_registry.PathRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry.entity_path", "name": "entity_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.AbstractEntityRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity_path of AbstractEntityRegistry", "ret_type": "sqlalchemy.orm.path_registry.PathRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "has_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry.has_entity", "name": "has_entity", "type": "builtins.bool"}}, "is_aliased_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry.is_aliased_class", "name": "is_aliased_class", "type": "builtins.bool"}}, "is_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry.is_entity", "name": "is_entity", "type": "builtins.bool"}}, "key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry.key", "name": "key", "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}}}, "mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry.mapper", "name": "mapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.AbstractEntityRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapper of AbstractEntityRegistry", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry.mapper", "name": "mapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.AbstractEntityRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapper of AbstractEntityRegistry", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry.parent", "name": "parent", "type": {".class": "UnionType", "items": ["sqlalchemy.orm.path_registry.RootRegistry", "sqlalchemy.orm.path_registry.PropRegistry"]}}}, "root_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry.root_entity", "name": "root_entity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.AbstractEntityRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_entity of AbstractEntityRegistry", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry.root_entity", "name": "root_entity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.AbstractEntityRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "root_entity of AbstractEntityRegistry", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.path_registry.AbstractEntityRegistry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "values": [], "variance": 0}, "slots": ["entity", "is_aliased_class", "key", "natural_path", "parent", "path"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AliasedInsp": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.AliasedInsp", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BindParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BindParameter", "kind": "Gdef"}, "CachingEntityRegistry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.path_registry.AbstractEntityRegistry"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.path_registry.CachingEntityRegistry", "name": "CachingEntityRegistry", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.path_registry.CachingEntityRegistry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.path_registry", "mro": ["sqlalchemy.orm.path_registry.CachingEntityRegistry", "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "sqlalchemy.orm.path_registry.CreatesToken", "sqlalchemy.orm.path_registry.PathRegistry", "sqlalchemy.sql.cache_key.HasCacheKey", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "entity"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.CachingEntityRegistry.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "entity"], "arg_types": ["sqlalchemy.orm.path_registry.CachingEntityRegistry", {".class": "UnionType", "items": ["sqlalchemy.orm.path_registry.RootRegistry", "sqlalchemy.orm.path_registry.PropRegistry"]}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CachingEntityRegistry", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.path_registry.CachingEntityRegistry.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.path_registry.CachingEntityRegistry._cache", "name": "_cache", "type": "sqlalchemy.orm.path_registry._ERDict"}}, "_getitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "entity"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.CachingEntityRegistry._getitem", "name": "_getitem", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "entity"], "arg_types": ["sqlalchemy.orm.path_registry.CachingEntityRegistry", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_getitem of CachingEntityRegistry", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.CachingEntityRegistry.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "pop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "default"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.CachingEntityRegistry.pop", "name": "pop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "default"], "arg_types": ["sqlalchemy.orm.path_registry.CachingEntityRegistry", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop of CachingEntityRegistry", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.path_registry.CachingEntityRegistry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.path_registry.CachingEntityRegistry", "values": [], "variance": 0}, "slots": ["_cache", "entity", "is_aliased_class", "key", "natural_path", "parent", "path"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CreatesToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.path_registry.PathRegistry"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.path_registry.CreatesToken", "name": "CreatesToken", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.path_registry.CreatesToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.path_registry", "mro": ["sqlalchemy.orm.path_registry.CreatesToken", "sqlalchemy.orm.path_registry.PathRegistry", "sqlalchemy.sql.cache_key.HasCacheKey", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.path_registry.CreatesToken.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "is_aliased_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.path_registry.CreatesToken.is_aliased_class", "name": "is_aliased_class", "type": "builtins.bool"}}, "is_root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.path_registry.CreatesToken.is_root", "name": "is_root", "type": "builtins.bool"}}, "token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "token"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.CreatesToken.token", "name": "token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "token"], "arg_types": ["sqlalchemy.orm.path_registry.CreatesToken", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "token of CreatesToken", "ret_type": "sqlalchemy.orm.path_registry.TokenRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.path_registry.CreatesToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.path_registry.CreatesToken", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "HasCacheKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.cache_key.HasCacheKey", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.Mapper", "kind": "Gdef"}, "MapperProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.MapperProperty", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PathRegistry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.cache_key.HasCacheKey"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.path_registry.PathRegistry", "name": "PathRegistry", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.path_registry", "mro": ["sqlalchemy.orm.path_registry.PathRegistry", "sqlalchemy.sql.cache_key.HasCacheKey", "builtins.object"], "names": {".class": "SymbolTable", "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", "sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.PathRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of PathRegistry", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__getitem__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "UnionType", "items": ["builtins.str", "builtins.int", "builtins.slice", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PathRegistry", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.path_registry.TokenRegistry", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathElementType"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}, "sqlalchemy.orm.path_registry.PropRegistry", "sqlalchemy.orm.path_registry.AbstractEntityRegistry"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.TokenRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.TokenRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PathRegistry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathElementType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PathRegistry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathElementType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", "builtins.slice"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PathRegistry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", "builtins.slice"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PathRegistry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.PropRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.PropRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.TokenRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PathRegistry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathElementType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", "builtins.slice"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PathRegistry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.PropRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of PathRegistry", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of PathRegistry", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__ne__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__ne__", "name": "__ne__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ne__ of PathRegistry", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of PathRegistry", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of PathRegistry", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of PathRegistry", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_cache_key_traversal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry._cache_key_traversal", "name": "_cache_key_traversal", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.cache_key._CacheKeyTraversalType"}}}, "_deserialize_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry._deserialize_path", "name": "_deserialize_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathRegistry"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._SerializedPath"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deserialize_path of PathRegistry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry._deserialize_path", "name": "_deserialize_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathRegistry"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._SerializedPath"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deserialize_path of PathRegistry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_path_for_compare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry._path_for_compare", "name": "_path_for_compare", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_path_for_compare of PathRegistry", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry._path_for_compare", "name": "_path_for_compare", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_path_for_compare of PathRegistry", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_serialize_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry._serialize_path", "name": "_serialize_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathRegistry"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_serialize_path of PathRegistry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._SerializedPath"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry._serialize_path", "name": "_serialize_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathRegistry"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_serialize_path of PathRegistry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._SerializedPath"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "coerce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "raw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.coerce", "name": "coerce", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "raw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathRegistry"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "coerce of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.PathRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.coerce", "name": "coerce", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "raw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathRegistry"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "coerce of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.PathRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "contains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "attributes", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.contains", "name": "contains", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "attributes", "key"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contains of PathRegistry", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "contains_mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.contains_mapper", "name": "contains_mapper", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contains_mapper of PathRegistry", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "deserialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.deserialize", "name": "deserialize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathRegistry"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._SerializedPath"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deserialize of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.PathRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.deserialize", "name": "deserialize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathRegistry"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._SerializedPath"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deserialize of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.PathRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "attributes", "key", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "attributes", "key", "value"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of PathRegistry", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "has_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.has_entity", "name": "has_entity", "type": "builtins.bool"}}, "is_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.is_entity", "name": "is_entity", "type": "builtins.bool"}}, "is_property": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.is_property", "name": "is_property", "type": "builtins.bool"}}, "is_root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.is_root", "name": "is_root", "type": "builtins.bool"}}, "is_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.is_token", "name": "is_token", "type": "builtins.bool"}}, "is_unnatural": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.is_unnatural", "name": "is_unnatural", "type": "builtins.bool"}}, "length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.length", "name": "length", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "length of PathRegistry", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.length", "name": "length", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "length of PathRegistry", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "natural_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.natural_path", "name": "natural_path", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}}}, "odd_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.odd_element", "name": "odd_element", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "odd_element of PathRegistry", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pairs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.pairs", "name": "pairs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pairs of PathRegistry", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.parent", "name": "parent", "type": {".class": "UnionType", "items": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "NoneType"}]}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.path", "name": "path", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}}}, "per_mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": ["is_class"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.per_mapper", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "mapper"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.per_mapper", "name": "per_mapper", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "mapper"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathRegistry"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "per_mapper of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.per_mapper", "name": "per_mapper", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "mapper"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathRegistry"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "per_mapper of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "mapper"], "dataclass_transform_spec": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.per_mapper", "name": "per_mapper", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "mapper"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathRegistry"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "per_mapper of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.CachingEntityRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.per_mapper", "name": "per_mapper", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "mapper"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathRegistry"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "per_mapper of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.CachingEntityRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "mapper"], "dataclass_transform_spec": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.per_mapper", "name": "per_mapper", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "mapper"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathRegistry"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "per_mapper of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.SlotsEntityRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.per_mapper", "name": "per_mapper", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "mapper"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathRegistry"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "per_mapper of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.SlotsEntityRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "mapper"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathRegistry"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "per_mapper of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.CachingEntityRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "mapper"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathRegistry"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "per_mapper of PathRegistry", "ret_type": "sqlalchemy.orm.path_registry.SlotsEntityRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.root", "name": "root", "type": "sqlalchemy.orm.path_registry.RootRegistry"}}, "serialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.serialize", "name": "serialize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serialize of PathRegistry", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._SerializedPath"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "attributes", "key", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "attributes", "key", "value"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of PathRegistry", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "setdefault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "attributes", "key", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathRegistry.setdefault", "name": "set<PERSON><PERSON>ult", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "attributes", "key", "value"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setdefault of PathRegistry", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.path_registry.PathRegistry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.path_registry.PathRegistry", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PathToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.base.InspectionAttr", "sqlalchemy.sql.cache_key.HasCacheKey", "builtins.str"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.path_registry.PathToken", "name": "PathToken", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathToken", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.orm.path_registry", "mro": ["sqlalchemy.orm.path_registry.PathToken", "sqlalchemy.orm.base.InspectionAttr", "sqlalchemy.sql.cache_key.HasCacheKey", "builtins.str", "typing.Sequence", "typing.Collection", "typing.Reversible", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_gen_cache_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "anon_map", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PathToken._gen_cache_key", "name": "_gen_cache_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "anon_map", "<PERSON><PERSON><PERSON>"], "arg_types": ["sqlalchemy.orm.path_registry.PathToken", "sqlalchemy.sql._py_util.cache_anon_map", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_gen_cache_key of PathToken", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_intern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.PathToken._intern", "name": "_intern", "type": {".class": "Instance", "args": ["builtins.str", "sqlalchemy.orm.path_registry.PathToken"], "type_ref": "builtins.dict"}}}, "_path_for_compare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PathToken._path_for_compare", "name": "_path_for_compare", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.PathToken"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_path_for_compare of PathToken", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PathToken._path_for_compare", "name": "_path_for_compare", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.PathToken"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_path_for_compare of PathToken", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "intern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "strvalue"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PathToken.intern", "name": "intern", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "strvalue"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathToken"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intern of PathToken", "ret_type": "sqlalchemy.orm.path_registry.PathToken", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PathToken.intern", "name": "intern", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "strvalue"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.path_registry.PathToken"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intern of PathToken", "ret_type": "sqlalchemy.orm.path_registry.PathToken", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.path_registry.PathToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.path_registry.PathToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PropRegistry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.path_registry.PathRegistry"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.path_registry.PropRegistry", "name": "PropRegistry", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.path_registry.PropRegistry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.path_registry", "mro": ["sqlalchemy.orm.path_registry.PropRegistry", "sqlalchemy.orm.path_registry.PathRegistry", "sqlalchemy.sql.cache_key.HasCacheKey", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "prop"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PropRegistry.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "prop"], "arg_types": ["sqlalchemy.orm.path_registry.PropRegistry", "sqlalchemy.orm.path_registry.AbstractEntityRegistry", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PropRegistry", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.path_registry.PropRegistry.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_default_path_loader_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PropRegistry._default_path_loader_key", "name": "_default_path_loader_key", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_getitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "entity"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PropRegistry._getitem", "name": "_getitem", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "entity"], "arg_types": ["sqlalchemy.orm.path_registry.PropRegistry", {".class": "UnionType", "items": ["builtins.int", "builtins.slice", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_getitem of PropRegistry", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.path_registry.AbstractEntityRegistry", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathElementType"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_loader_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PropRegistry._loader_key", "name": "_loader_key", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathElementType"}], "type_ref": "builtins.tuple"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_truncate_recursive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.PropRegistry._truncate_recursive", "name": "_truncate_recursive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.PropRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_truncate_recursive of PropRegistry", "ret_type": "sqlalchemy.orm.path_registry.PropRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_wildcard_path_loader_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PropRegistry._wildcard_path_loader_key", "name": "_wildcard_path_loader_key", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.path_registry.PropRegistry.entity", "name": "entity", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "NoneType"}]}}}, "entity_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.path_registry.PropRegistry.entity_path", "name": "entity_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.PropRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity_path of PropRegistry", "ret_type": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.path_registry.PropRegistry.entity_path", "name": "entity_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.PropRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity_path of PropRegistry", "ret_type": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.PropRegistry.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "is_property": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.PropRegistry.is_property", "name": "is_property", "type": "builtins.bool"}}, "mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.path_registry.PropRegistry.mapper", "name": "mapper", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}}}, "prop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.path_registry.PropRegistry.prop", "name": "prop", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.path_registry.PropRegistry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.path_registry.PropRegistry", "values": [], "variance": 0}, "slots": ["_default_path_loader_key", "_loader_key", "_wildcard_path_loader_key", "entity", "has_entity", "is_unnatural", "mapper", "natural_path", "parent", "path", "prop"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RelationshipProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.relationships.RelationshipProperty", "kind": "Gdef"}, "RootRegistry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.path_registry.CreatesToken"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.path_registry.RootRegistry", "name": "RootRegistry", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.path_registry.RootRegistry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.path_registry", "mro": ["sqlalchemy.orm.path_registry.RootRegistry", "sqlalchemy.orm.path_registry.CreatesToken", "sqlalchemy.orm.path_registry.PathRegistry", "sqlalchemy.sql.cache_key.HasCacheKey", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.path_registry.RootRegistry.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_getitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "entity"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.RootRegistry._getitem", "name": "_getitem", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "entity"], "arg_types": ["sqlalchemy.orm.path_registry.RootRegistry", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_getitem of RootRegistry", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.path_registry.TokenRegistry", "sqlalchemy.orm.path_registry.AbstractEntityRegistry"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_truncate_recursive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.RootRegistry._truncate_recursive", "name": "_truncate_recursive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.RootRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_truncate_recursive of RootRegistry", "ret_type": "sqlalchemy.orm.path_registry.RootRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "has_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.RootRegistry.has_entity", "name": "has_entity", "type": "builtins.bool"}}, "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.RootRegistry.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "is_aliased_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.RootRegistry.is_aliased_class", "name": "is_aliased_class", "type": "builtins.bool"}}, "is_root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.RootRegistry.is_root", "name": "is_root", "type": "builtins.bool"}}, "is_unnatural": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.RootRegistry.is_unnatural", "name": "is_unnatural", "type": "builtins.bool"}}, "natural_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.RootRegistry.natural_path", "name": "natural_path", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.RootRegistry.path", "name": "path", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.path_registry.RootRegistry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.path_registry.RootRegistry", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SlotsEntityRegistry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.path_registry.AbstractEntityRegistry"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.path_registry.SlotsEntityRegistry", "name": "SlotsEntityRegistry", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.path_registry.SlotsEntityRegistry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.path_registry", "mro": ["sqlalchemy.orm.path_registry.SlotsEntityRegistry", "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "sqlalchemy.orm.path_registry.CreatesToken", "sqlalchemy.orm.path_registry.PathRegistry", "sqlalchemy.sql.cache_key.HasCacheKey", "builtins.object"], "names": {".class": "SymbolTable", "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.SlotsEntityRegistry.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.path_registry.SlotsEntityRegistry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.path_registry.SlotsEntityRegistry", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TokenRegistry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.path_registry.PathRegistry"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.path_registry.TokenRegistry", "name": "TokenRegistry", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.path_registry.TokenRegistry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.path_registry", "mro": ["sqlalchemy.orm.path_registry.TokenRegistry", "sqlalchemy.orm.path_registry.PathRegistry", "sqlalchemy.sql.cache_key.HasCacheKey", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "token"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.TokenRegistry.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "token"], "arg_types": ["sqlalchemy.orm.path_registry.TokenRegistry", "sqlalchemy.orm.path_registry.CreatesToken", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TokenRegistry", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.path_registry.TokenRegistry.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_generate_natural_for_superclasses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.TokenRegistry._generate_natural_for_superclasses", "name": "_generate_natural_for_superclasses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.TokenRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_natural_for_superclasses of TokenRegistry", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_getitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "entity"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.TokenRegistry._getitem", "name": "_getitem", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "entity"], "arg_types": ["sqlalchemy.orm.path_registry.TokenRegistry", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_getitem of TokenRegistry", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "generate_for_superclasses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry.TokenRegistry.generate_for_superclasses", "name": "generate_for_superclasses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.path_registry.TokenRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_for_superclasses of TokenRegistry", "ret_type": {".class": "Instance", "args": ["sqlalchemy.orm.path_registry.PathRegistry"], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "has_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.TokenRegistry.has_entity", "name": "has_entity", "type": "builtins.bool"}}, "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.TokenRegistry.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "is_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.TokenRegistry.is_token", "name": "is_token", "type": "builtins.bool"}}, "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.path_registry.TokenRegistry.parent", "name": "parent", "type": "sqlalchemy.orm.path_registry.CreatesToken"}}, "token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.path_registry.TokenRegistry.token", "name": "token", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.path_registry.TokenRegistry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.path_registry.TokenRegistry", "values": [], "variance": 0}, "slots": ["natural_path", "parent", "path", "token"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeGuard": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeGuard", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_CacheKeyTraversalType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.cache_key._CacheKeyTraversalType", "kind": "Gdef"}, "_DEFAULT_TOKEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry._DEFAULT_TOKEN", "name": "_DEFAULT_TOKEN", "type": "builtins.str"}}, "_ERDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.path_registry._ERDict", "name": "_ERDict", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.path_registry._ERDict", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.orm.path_registry", "mro": ["sqlalchemy.orm.path_registry._ERDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "registry"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry._ERDict.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "registry"], "arg_types": ["sqlalchemy.orm.path_registry._ERDict", "sqlalchemy.orm.path_registry.CachingEntityRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ERDict", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__missing__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry._ERDict.__missing__", "name": "__missing__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["sqlalchemy.orm.path_registry._ERDict", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__missing__ of _ERDict", "ret_type": "sqlalchemy.orm.path_registry.PropRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "registry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.path_registry._ERDict.registry", "name": "registry", "type": "sqlalchemy.orm.path_registry.CachingEntityRegistry"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.path_registry._ERDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.path_registry._ERDict", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_EvenPathRepresentation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.path_registry._EvenPathRepresentation", "line": 76, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, "builtins.str"]}], "type_ref": "typing.Sequence"}}}, "_InternalEntityType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._InternalEntityType", "kind": "Gdef"}, "_LiteralStar": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.typing._LiteralStar", "kind": "Gdef"}, "_OddPathRepresentation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.path_registry._OddPathRepresentation", "line": 75, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}], "type_ref": "typing.Sequence"}}}, "_PathElementType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.path_registry._PathElementType", "line": 61, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}]}}}, "_PathRepresentation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.path_registry._PathRepresentation", "line": 71, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathElementType"}], "type_ref": "builtins.tuple"}}}, "_SerializedPath": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.path_registry._SerializedPath", "line": 59, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.list"}}}, "_StrPathToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.path_registry._StrPathToken", "line": 60, "no_args": true, "normalized": false, "target": "builtins.str"}}, "_WILDCARD_TOKEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry._WILDCARD_TOKEN", "name": "_WILDCARD_TOKEN", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._LiteralStar"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.path_registry.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.path_registry.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.path_registry.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.path_registry.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.path_registry.__package__", "name": "__package__", "type": "builtins.str"}}, "_unreduce_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.path_registry._unreduce_path", "name": "_unreduce_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._SerializedPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unreduce_path", "ret_type": "sqlalchemy.orm.path_registry.PathRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "anon_map": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._py_util.cache_anon_map", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "itertools.chain", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "insp_is_mapper_property": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.insp_is_mapper_property", "kind": "Gdef"}, "is_entity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.path_registry.is_entity", "name": "is_entity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_entity", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "unpack_kwargs": false, "variables": []}}}, "is_root": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.path_registry.is_root", "name": "is_root", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_root", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.orm.path_registry.RootRegistry", "unpack_kwargs": false, "variables": []}}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.path_registry.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "orm_base": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "path_is_entity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.path_registry.path_is_entity", "name": "path_is_entity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path_is_entity", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "unpack_kwargs": false, "variables": []}}}, "path_is_property": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.path_registry.path_is_property", "name": "path_is_property", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path_is_property", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.orm.path_registry.PropRegistry", "unpack_kwargs": false, "variables": []}}}, "reduce": {".class": "SymbolTableNode", "cross_ref": "functools.reduce", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "visitors": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/path_registry.py"}