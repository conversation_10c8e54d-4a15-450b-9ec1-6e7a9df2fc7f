{"data_mtime": 1754228260, "dep_lines": [43, 44, 45, 63, 64, 65, 66, 68, 70, 71, 73, 77, 82, 84, 87, 88, 89, 90, 92, 93, 94, 95, 96, 98, 99, 43, 60, 61, 62, 63, 19, 21, 22, 23, 59, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 10, 10, 5, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 20, 10, 10, 5, 20, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.exc", "sqlalchemy.orm.path_registry", "sqlalchemy.orm.base", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.visitors", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.schema", "sqlalchemy.sql.type_api", "sqlalchemy.util.typing", "sqlalchemy.orm._typing", "sqlalchemy.orm.attributes", "sqlalchemy.orm.context", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.decl_base", "sqlalchemy.orm.loading", "sqlalchemy.orm.mapper", "sqlalchemy.orm.query", "sqlalchemy.orm.session", "sqlalchemy.orm.state", "sqlalchemy.orm.strategy_options", "sqlalchemy.orm.util", "sqlalchemy.engine.result", "sqlalchemy.sql._typing", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "collections", "dataclasses", "typing", "sqlalchemy", "builtins", "_typeshed", "abc", "enum", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.sql.annotation", "sqlalchemy.sql.elements", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.util", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers"], "hash": "456edb057196b591d8db05c5392aadbd89ba50397bc87654c915ffe9877719f4", "id": "sqlalchemy.orm.interfaces", "ignore_all": true, "interface_hash": "3a4e8e2c162cd95a0a28ce208abf67701e8685bee610faa64fd31dc6bda7c859", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/interfaces.py", "plugin_data": null, "size": 48395, "suppressed": [], "version_id": "1.7.1"}