{".class": "MypyFile", "_fullname": "sqlalchemy.orm.persistence", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BooleanClauseList": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BooleanClauseList", "kind": "Gdef"}, "LABEL_STYLE_TABLENAME_PLUS_COL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_TABLENAME_PLUS_COL", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.persistence.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.persistence.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.persistence.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.persistence.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.persistence.__package__", "name": "__package__", "type": "builtins.str"}}, "_collect_delete_commands": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["base_mapper", "uowtransaction", "table", "states_to_delete"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._collect_delete_commands", "name": "_collect_delete_commands", "type": null}}, "_collect_insert_commands": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["table", "states_to_insert", "bulk", "return_defaults", "render_nulls", "include_bulk_keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._collect_insert_commands", "name": "_collect_insert_commands", "type": null}}, "_collect_post_update_commands": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["base_mapper", "uowtransaction", "table", "states_to_update", "post_update_cols"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._collect_post_update_commands", "name": "_collect_post_update_commands", "type": null}}, "_collect_update_commands": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["uowtransaction", "table", "states_to_update", "bulk", "use_orm_update_stmt", "include_bulk_keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._collect_update_commands", "name": "_collect_update_commands", "type": null}}, "_connections_for_states": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["base_mapper", "uowtransaction", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._connections_for_states", "name": "_connections_for_states", "type": null}}, "_cursor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor", "kind": "Gdef"}, "_emit_delete_statements": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["base_mapper", "uowtransaction", "mapper", "table", "delete"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._emit_delete_statements", "name": "_emit_delete_statements", "type": null}}, "_emit_insert_statements": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 5, 5, 5], "arg_names": ["base_mapper", "uowtransaction", "mapper", "table", "insert", "bookkeeping", "use_orm_insert_stmt", "execution_options"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._emit_insert_statements", "name": "_emit_insert_statements", "type": null}}, "_emit_post_update_statements": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["base_mapper", "uowtransaction", "mapper", "table", "update"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._emit_post_update_statements", "name": "_emit_post_update_statements", "type": null}}, "_emit_update_statements": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 5, 5, 5], "arg_names": ["base_mapper", "uowtransaction", "mapper", "table", "update", "bookkeeping", "use_orm_update_stmt", "enable_check_rowcount"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._emit_update_statements", "name": "_emit_update_statements", "type": null}}, "_finalize_insert_update_commands": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["base_mapper", "uowtransaction", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._finalize_insert_update_commands", "name": "_finalize_insert_update_commands", "type": null}}, "_organize_states_for_delete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["base_mapper", "states", "uowtransaction"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._organize_states_for_delete", "name": "_organize_states_for_delete", "type": null}}, "_organize_states_for_post_update": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["base_mapper", "states", "uowtransaction"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._organize_states_for_post_update", "name": "_organize_states_for_post_update", "type": null}}, "_organize_states_for_save": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["base_mapper", "states", "uowtransaction"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._organize_states_for_save", "name": "_organize_states_for_save", "type": null}}, "_postfetch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["mapper", "uowtransaction", "table", "state", "dict_", "result", "params", "value_params", "isupdate", "returned_defaults"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._postfetch", "name": "_postfetch", "type": null}}, "_postfetch_bulk_save": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["mapper", "dict_", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._postfetch_bulk_save", "name": "_postfetch_bulk_save", "type": null}}, "_postfetch_post_update": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["mapper", "uowtransaction", "table", "state", "dict_", "result", "params"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._postfetch_post_update", "name": "_postfetch_post_update", "type": null}}, "_sort_states": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mapper", "states"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence._sort_states", "name": "_sort_states", "type": null}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "attributes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "itertools.chain", "kind": "Gdef"}, "delete_obj": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["base_mapper", "states", "uowtransaction"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence.delete_obj", "name": "delete_obj", "type": null}}, "future": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.future", "kind": "Gdef"}, "groupby": {".class": "SymbolTableNode", "cross_ref": "itertools.groupby", "kind": "Gdef"}, "loading": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.loading", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "operators": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators", "kind": "Gdef"}, "orm_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.exc", "kind": "Gdef"}, "post_update": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["base_mapper", "states", "uowtransaction", "post_update_cols"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence.post_update", "name": "post_update", "type": null}}, "sa_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "save_obj": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["base_mapper", "states", "uowtransaction", "single"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.persistence.save_obj", "name": "save_obj", "type": null}}, "sql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql", "kind": "Gdef"}, "state_str": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.state_str", "kind": "Gdef"}, "sync": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.sync", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "zip_longest": {".class": "SymbolTableNode", "cross_ref": "itertools.zip_longest", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/persistence.py"}