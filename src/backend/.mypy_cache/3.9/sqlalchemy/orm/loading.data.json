{".class": "MypyFile", "_fullname": "sqlalchemy.orm.loading", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ChunkedIteratorResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.ChunkedIteratorResult", "kind": "Gdef"}, "CursorResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor.CursorResult", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EMPTY_DICT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.EMPTY_DICT", "kind": "Gdef"}, "ForUpdateArg": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.ForUpdateArg", "kind": "Gdef"}, "FromStatement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.FromStatement", "kind": "Gdef"}, "FrozenResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.FrozenResult", "kind": "Gdef"}, "InstanceState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state.InstanceState", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "LABEL_STYLE_TABLENAME_PLUS_COL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_TABLENAME_PLUS_COL", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "LoaderCallableStatus": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.LoaderCallableStatus", "kind": "Gdef"}, "Mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.Mapper", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "ORMCompileState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.ORMCompileState", "kind": "Gdef"}, "ORMOption": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.ORMOption", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PassiveFlag": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.PassiveFlag", "kind": "Gdef"}, "PostLoad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.loading.PostLoad", "name": "PostLoad", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.loading.PostLoad", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.loading", "mro": ["sqlalchemy.orm.loading.PostLoad", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading.PostLoad.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.loading.PostLoad.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "add_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "overwrite"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading.PostLoad.add_state", "name": "add_state", "type": null}}, "callable_for_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 2, 4], "arg_names": ["cls", "context", "path", "limit_to_mapper", "token", "loader_callable", "arg", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.loading.PostLoad.callable_for_path", "name": "callable_for_path", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.loading.PostLoad.callable_for_path", "name": "callable_for_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 2, 4], "arg_names": ["cls", "context", "path", "limit_to_mapper", "token", "loader_callable", "arg", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.loading.PostLoad"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "callable_for_path of PostLoad", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "for_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "context", "path", "only_load_props"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.loading.PostLoad.for_context", "name": "for_context", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.loading.PostLoad.for_context", "name": "for_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "context", "path", "only_load_props"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.loading.PostLoad"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "for_context of PostLoad", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "invoke": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "path"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading.PostLoad.invoke", "name": "invoke", "type": null}}, "load_keys": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.loading.PostLoad.load_keys", "name": "load_keys", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "loaders": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.loading.PostLoad.loaders", "name": "loaders", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "path_exists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "path", "key"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.loading.PostLoad.path_exists", "name": "path_exists", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.loading.PostLoad.path_exists", "name": "path_exists", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "path", "key"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.loading.PostLoad"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path_exists of PostLoad", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "states": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.loading.PostLoad.states", "name": "states", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.loading.PostLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.loading.PostLoad", "values": [], "variance": 0}, "slots": ["load_keys", "loaders", "states"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Query": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.query.Query", "kind": "Gdef"}, "QueryContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.QueryContext", "kind": "Gdef"}, "Result": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.Result", "kind": "Gdef"}, "Select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Select", "kind": "Gdef"}, "SelectState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.SelectState", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.Session", "kind": "Gdef"}, "SimpleResultMetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.SimpleResultMetaData", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_DEFER_FOR_STATE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._DEFER_FOR_STATE", "kind": "Gdef"}, "_ExecuteOptions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._ExecuteOptions", "kind": "Gdef"}, "_IdentityKeyType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._IdentityKeyType", "kind": "Gdef"}, "_O": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.loading._O", "name": "_O", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_PopulatorDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.loading._PopulatorDict", "line": 75, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}], "type_ref": "builtins.dict"}}}, "_RAISE_FOR_STATE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._RAISE_FOR_STATE", "kind": "Gdef"}, "_SET_DEFERRED_EXPIRED": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._SET_DEFERRED_EXPIRED", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.loading._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.loading.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.loading.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.loading.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.loading.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.loading.__package__", "name": "__package__", "type": "builtins.str"}}, "_decorate_polymorphic_switch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["instance_fn", "context", "query_entity", "mapper", "result", "path", "polymorphic_discriminator", "adapter", "ensure_no_pk"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading._decorate_polymorphic_switch", "name": "_decorate_polymorphic_switch", "type": null}}, "_instance_processor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["query_entity", "mapper", "context", "result", "path", "adapter", "only_load_props", "refresh_state", "polymorphic_discriminator", "_polymorphic_from"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading._instance_processor", "name": "_instance_processor", "type": null}}, "_load_subclass_via_in": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["context", "path", "entity", "polymorphic_from", "option_entities"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading._load_subclass_via_in", "name": "_load_subclass_via_in", "type": null}}, "_new_runid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.loading._new_runid", "name": "_new_runid", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_none_set": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._none_set", "kind": "Gdef"}, "_populate_full": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["context", "row", "state", "dict_", "isnew", "load_path", "loaded_instance", "populate_existing", "populators"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading._populate_full", "name": "_populate_full", "type": null}}, "_populate_partial": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["context", "row", "state", "dict_", "isnew", "load_path", "unloaded", "populators"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading._populate_partial", "name": "_populate_partial", "type": null}}, "_set_get_options": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["compile_opt", "load_opt", "populate_existing", "version_check", "only_load_props", "refresh_state", "identity_token", "is_user_refresh"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading._set_get_options", "name": "_set_get_options", "type": null}}, "_setup_entity_query": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 4], "arg_names": ["compile_state", "mapper", "query_entity", "path", "adapter", "column_collection", "with_polymorphic", "only_load_props", "polymorphic_discriminator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading._setup_entity_query", "name": "_setup_entity_query", "type": null}}, "_validate_version_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["mapper", "state", "dict_", "row", "getter"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading._validate_version_id", "name": "_validate_version_id", "type": null}}, "_warn_for_runid_changed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading._warn_for_runid_changed", "name": "_warn_for_runid_changed", "type": null}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "attributes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes", "kind": "Gdef"}, "get_from_identity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["session", "mapper", "key", "passive"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading.get_from_identity", "name": "get_from_identity", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["session", "mapper", "key", "passive"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.loading._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.loading._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._IdentityKeyType"}, "sqlalchemy.orm.base.PassiveFlag"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_from_identity", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.base.LoaderCallableStatus", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.loading._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.loading._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "instances": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cursor", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading.instances", "name": "instances", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cursor", "context"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, "sqlalchemy.orm.context.QueryContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instances", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.result.Result"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "load_on_ident": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["session", "statement", "key", "load_options", "refresh_state", "with_for_update", "only_load_props", "no_autoflush", "bind_arguments", "execution_options", "require_pk_cols", "is_user_refresh"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading.load_on_ident", "name": "load_on_ident", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["session", "statement", "key", "load_options", "refresh_state", "with_for_update", "only_load_props", "no_autoflush", "bind_arguments", "execution_options", "require_pk_cols", "is_user_refresh"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.orm.context.FromStatement"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.orm._typing._IdentityKeyType"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.orm.interfaces.ORMOption"], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.ForUpdateArg", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_on_ident", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "load_on_pk_identity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["session", "statement", "primary_key_identity", "load_options", "refresh_state", "with_for_update", "only_load_props", "identity_token", "no_autoflush", "bind_arguments", "execution_options", "require_pk_cols", "is_user_refresh"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading.load_on_pk_identity", "name": "load_on_pk_identity", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["session", "statement", "primary_key_identity", "load_options", "refresh_state", "with_for_update", "only_load_props", "identity_token", "no_autoflush", "bind_arguments", "execution_options", "require_pk_cols", "is_user_refresh"], "arg_types": ["sqlalchemy.orm.session.Session", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.orm.context.FromStatement"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.orm.interfaces.ORMOption"], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.ForUpdateArg", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_on_pk_identity", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "load_scalar_attributes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["mapper", "state", "attribute_names", "passive"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.loading.load_scalar_attributes", "name": "load_scalar_attributes", "type": null}}, "merge_frozen_result": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["session", "statement", "frozen_result", "load"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.loading.merge_frozen_result", "name": "merge_frozen_result", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.loading.merge_frozen_result", "name": "merge_frozen_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["session", "statement", "frozen_result", "load"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge_frozen_result", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "merge_result": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["query", "iterator", "load"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.loading.merge_result", "name": "merge_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["query", "iterator", "load"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.query.Query"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.engine.result.FrozenResult"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Sequence"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.object"], "type_ref": "typing.Iterable"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge_result", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.engine.result.FrozenResult"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterable"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.loading.merge_result", "name": "merge_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["query", "iterator", "load"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.query.Query"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.engine.result.FrozenResult"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Sequence"}], "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.object"], "type_ref": "typing.Iterable"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge_result", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.engine.result.FrozenResult"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterable"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "orm_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.exc", "kind": "Gdef"}, "path_registry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.path_registry", "kind": "Gdef"}, "result_tuple": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.result_tuple", "kind": "Gdef"}, "sa_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.select", "kind": "Gdef"}, "sql_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.util", "kind": "Gdef"}, "state_str": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.state_str", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/loading.py"}