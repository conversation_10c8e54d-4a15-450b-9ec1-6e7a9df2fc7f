{"data_mtime": 1754228260, "dep_lines": [43, 44, 45, 46, 47, 48, 49, 70, 71, 72, 74, 75, 76, 78, 79, 84, 86, 94, 103, 104, 105, 107, 108, 109, 113, 23, 43, 60, 62, 63, 64, 65, 66, 68, 21, 23, 24, 25, 60, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 5, 5, 5, 10, 5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 10, 20, 10, 10, 10, 5, 10, 5, 5, 5, 20, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.loading", "sqlalchemy.orm.util", "sqlalchemy.orm._typing", "sqlalchemy.orm.base", "sqlalchemy.orm.context", "sqlalchemy.sql.coercions", "sqlalchemy.sql.expression", "sqlalchemy.sql.roles", "sqlalchemy.sql.util", "sqlalchemy.sql.visitors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.util.typing", "sqlalchemy.orm.mapper", "sqlalchemy.orm.path_registry", "sqlalchemy.orm.session", "sqlalchemy.orm.state", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.result", "collections.abc", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.event", "__future__", "collections", "operator", "typing", "sqlalchemy", "builtins", "_decimal", "_typeshed", "abc", "datetime", "enum", "sqlalchemy.engine._py_row", "sqlalchemy.engine.row", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.sql._dml_constructors", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.dml", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.schema", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "uuid"], "hash": "541483d24d79c54fd7ca4820bcb180c0677036094102804a6ce93ca80a0c29d2", "id": "sqlalchemy.orm.query", "ignore_all": true, "interface_hash": "f85d9f95157f758dc7c7e13851f441930761006b66c696b1a49ec5a37c1e2fbe", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/query.py", "plugin_data": null, "size": 117714, "suppressed": [], "version_id": "1.7.1"}