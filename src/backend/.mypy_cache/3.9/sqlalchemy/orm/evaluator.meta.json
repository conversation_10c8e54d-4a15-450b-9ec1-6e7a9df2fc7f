{"data_mtime": 1754228260, "dep_lines": [24, 25, 30, 31, 24, 27, 29, 33, 20, 22, 27, 1, 1], "dep_prios": [10, 5, 10, 5, 20, 10, 5, 5, 5, 5, 5, 5, 30], "dependencies": ["sqlalchemy.orm.exc", "sqlalchemy.orm.base", "sqlalchemy.sql.operators", "sqlalchemy.sql.sqltypes", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "abc"], "hash": "8cf8d5acfed76d5386e9a5d3081444ab4ac5de83472ea0785c04fe82dfdca5a0", "id": "sqlalchemy.orm.evaluator", "ignore_all": true, "interface_hash": "7f0fe169f9be5d795b291503f907a8053f1861f2d647769ff09c9a567116b6ed", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/evaluator.py", "plugin_data": null, "size": 11925, "suppressed": [], "version_id": "1.7.1"}