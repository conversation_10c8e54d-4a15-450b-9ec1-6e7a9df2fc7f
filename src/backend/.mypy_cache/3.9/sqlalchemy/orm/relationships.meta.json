{"data_mtime": 1754228260, "dep_lines": [44, 45, 46, 48, 56, 64, 74, 75, 76, 77, 78, 79, 81, 82, 84, 92, 105, 107, 108, 109, 110, 111, 112, 113, 124, 125, 19, 44, 67, 69, 70, 71, 72, 73, 16, 18, 20, 21, 22, 23, 42, 67, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 10, 20, 10, 10, 10, 10, 10, 5, 5, 10, 10, 10, 10, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.strategy_options", "sqlalchemy.orm._typing", "sqlalchemy.orm.base", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.util", "sqlalchemy.sql.coercions", "sqlalchemy.sql.expression", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.visitors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.elements", "sqlalchemy.sql.util", "sqlalchemy.util.typing", "sqlalchemy.orm.clsregistry", "sqlalchemy.orm.decl_base", "sqlalchemy.orm.dependency", "sqlalchemy.orm.mapper", "sqlalchemy.orm.query", "sqlalchemy.orm.session", "sqlalchemy.orm.state", "sqlalchemy.orm.strategies", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "collections.abc", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.log", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.inspection", "__future__", "collections", "dataclasses", "inspect", "re", "typing", "weakref", "sqlalchemy", "builtins", "_typeshed", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.orm.decl_api", "sqlalchemy.sql._elements_constructors", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.traversals", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types", "typing_extensions"], "hash": "c2b1f22026fc039a8fa32c5ffa7213415275de408daf631e743a8463c40c4adf", "id": "sqlalchemy.orm.relationships", "ignore_all": true, "interface_hash": "974bc98acdd9107ec36d23d18a2c745b11de8ff598d909d1f3edb1dea324123e", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/relationships.py", "plugin_data": null, "size": 127816, "suppressed": [], "version_id": "1.7.1"}