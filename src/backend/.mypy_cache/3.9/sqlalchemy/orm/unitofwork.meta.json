{"data_mtime": 1754228260, "dep_lines": [26, 27, 28, 31, 35, 36, 37, 38, 40, 26, 29, 30, 18, 20, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 25, 25, 25, 25, 25, 20, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.exc", "sqlalchemy.orm.util", "sqlalchemy.util.topological", "sqlalchemy.orm.dependency", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.orm.session", "sqlalchemy.orm.state", "sqlalchemy.orm", "sqlalchemy.event", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "_typeshed", "abc", "collections", "sqlalchemy.engine", "sqlalchemy.engine.util", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.base", "sqlalchemy.orm.state_changes", "sqlalchemy.sql", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.roles", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded"], "hash": "5a4e586687016f17b89b5c14d9a150ee06350a9e4244e8b5de40c433588e4b3e", "id": "sqlalchemy.orm.unitofwork", "ignore_all": true, "interface_hash": "6110a5380a5f24358fdc9c55873aa5bb39906508b20c8796119a56c17e90b9d9", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/unitofwork.py", "plugin_data": null, "size": 27033, "suppressed": [], "version_id": "1.7.1"}