{"data_mtime": 1754228260, "dep_lines": [21, 22, 23, 27, 28, 29, 33, 37, 38, 39, 43, 44, 45, 46, 49, 22, 8, 10, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 20, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.interfaces", "sqlalchemy.sql.roles", "sqlalchemy.sql._orm_types", "sqlalchemy.sql._typing", "sqlalchemy.sql.elements", "sqlalchemy.util.typing", "sqlalchemy.orm.attributes", "sqlalchemy.orm.base", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.orm.relationships", "sqlalchemy.orm.state", "sqlalchemy.orm.util", "sqlalchemy.sql.base", "sqlalchemy.sql", "__future__", "operator", "typing", "builtins", "_typeshed", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.orm.clsregistry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.operators", "sqlalchemy.sql.schema", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "a115095401a9537fc38529086f56a75e09dec1e5480118c1e751e53e130d7dc3", "id": "sqlalchemy.orm._typing", "ignore_all": true, "interface_hash": "a68dcc9ff3904490765aaf9e693e8c99e2c439a4ba9373602b157b2b5ce0d80b", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/_typing.py", "plugin_data": null, "size": 5015, "suppressed": [], "version_id": "1.7.1"}