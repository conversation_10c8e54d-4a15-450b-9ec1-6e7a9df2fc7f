{".class": "MypyFile", "_fullname": "sqlalchemy.orm.instrumentation", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AttributeImpl": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes.AttributeImpl", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClassManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.util.langhelpers.HasMemoized", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}], "type_ref": "builtins.dict"}, "sqlalchemy.event.registry.EventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.instrumentation.ClassManager", "name": "ClassManager", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.orm.instrumentation", "mro": ["sqlalchemy.orm.instrumentation.ClassManager", "sqlalchemy.util.langhelpers.HasMemoized", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "MANAGER_ATTR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.MANAGER_ATTR", "name": "MANAGER_ATTR", "type": "builtins.str"}}, "STATE_ATTR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.STATE_ATTR", "name": "STATE_ATTR", "type": "builtins.str"}}, "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bool__ of ClassManager", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of ClassManager", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of ClassManager", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of ClassManager", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_all_key_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._all_key_set", "name": "_all_key_set", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._all_key_set", "name": "_all_key_set", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "sqlalchemy.util.langhelpers.HasMemoized.memoized_attribute"}}}}, "_all_sqla_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "exclude"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._all_sqla_attributes", "name": "_all_sqla_attributes", "type": null}}, "_attr_has_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._attr_has_impl", "name": "_attr_has_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_attr_has_impl of ClassManager", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_bases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._bases", "name": "_bases", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "type_ref": "builtins.list"}}}, "_collection_impl_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._collection_impl_keys", "name": "_collection_impl_keys", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._collection_impl_keys", "name": "_collection_impl_keys", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "sqlalchemy.util.langhelpers.HasMemoized.memoized_attribute"}}}}, "_finalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._finalize", "name": "_finalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_finalize of ClassManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_finalized": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._finalized", "name": "_finalized", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_get_class_attr_mro": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "default"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._get_class_attr_mro", "name": "_get_class_attr_mro", "type": null}}, "_instrument_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._instrument_init", "name": "_instrument_init", "type": null}}, "_loader_impls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._loader_impls", "name": "_loader_impls", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._loader_impls", "name": "_loader_impls", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "sqlalchemy.util.langhelpers.HasMemoized.memoized_attribute"}}}}, "_new_state_if_none": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._new_state_if_none", "name": "_new_state_if_none", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_new_state_if_none of ClassManager", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_scalar_loader_impls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._scalar_loader_impls", "name": "_scalar_loader_impls", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._scalar_loader_impls", "name": "_scalar_loader_impls", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "sqlalchemy.util.langhelpers.HasMemoized.memoized_attribute"}}}}, "_serialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "state_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._serialize", "name": "_serialize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "state_dict"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_serialize of ClassManager", "ret_type": "sqlalchemy.orm.instrumentation._SerializeManager", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_state_constructor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._state_constructor", "name": "_state_constructor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_state_constructor of ClassManager", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._state_constructor", "name": "_state_constructor", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}}], "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_state_setter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._state_setter", "name": "_state_setter", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "builtins.staticmethod"}}}, "_subclass_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._subclass_manager", "name": "_subclass_manager", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.instrumentation._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_subclass_manager of ClassManager", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.instrumentation._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.instrumentation._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "_update_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "finalize", "mapper", "registry", "declarative_scan", "expired_attribute_loader", "init_method"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager._update_state", "name": "_update_state", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "finalize", "mapper", "registry", "declarative_scan", "expired_attribute_loader", "init_method"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.orm.decl_api.registry", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.orm.decl_base._MapperConfig", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.orm.instrumentation._ExpiredAttributeLoaderProto", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_state of ClassManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.attributes", "name": "attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attributes of ClassManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.attributes", "name": "attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attributes of ClassManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "class_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.class_", "name": "class_", "type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}}}}, "declarative_scan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.declarative_scan", "name": "declarative_scan", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.orm.decl_base._MapperConfig"], "type_ref": "_weakref.ReferenceType"}, {".class": "NoneType"}]}}}, "deferred_scalar_loader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": ["is_property"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.deferred_scalar_loader", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.deferred_scalar_loader", "name": "deferred_scalar_loader", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.deferred_scalar_loader", "name": "deferred_scalar_loader", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deferred_scalar_loader of ClassManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.deferred_scalar_loader", "name": "deferred_scalar_loader", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "deferred_scalar_loader", "type": null}}], "type": null}}, "dict_getter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.dict_getter", "name": "dict_getter", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.dict_getter", "name": "dict_getter", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "sqlalchemy.util.langhelpers.hybridmethod"}}}}, "dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.dispatch", "name": "dispatch", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "type_ref": "sqlalchemy.event.base.dispatcher"}}}, "expired_attribute_loader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.expired_attribute_loader", "name": "expired_attribute_loader", "type": "sqlalchemy.orm.instrumentation._ExpiredAttributeLoaderProto"}}, "factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.factory", "name": "factory", "type": {".class": "UnionType", "items": ["sqlalchemy.orm.instrumentation._ManagerFactory", {".class": "NoneType"}]}}}, "get_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.get_impl", "name": "get_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_impl of ClassManager", "ret_type": "sqlalchemy.orm.attributes.AttributeImpl", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "has_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "state", "key", "optimistic"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.has_parent", "name": "has_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "state", "key", "optimistic"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_parent of ClassManager", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "has_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.has_state", "name": "has_state", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_state of ClassManager", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.info", "name": "info", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "init_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.init_method", "name": "init_method", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}}, "initialize_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "key", "state", "factory"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.initialize_collection", "name": "initialize_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "key", "state", "factory"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "builtins.str", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.collections._CollectionFactoryType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize_collection of ClassManager", "ret_type": {".class": "TupleType", "implicit": false, "items": ["sqlalchemy.orm.collections.CollectionAdapter", "sqlalchemy.orm.collections._AdaptedCollectionProtocol"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "install_descriptor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "inst"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.install_descriptor", "name": "install_descriptor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "inst"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "install_descriptor of ClassManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "install_member": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "implementation"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.install_member", "name": "install_member", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "implementation"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "install_member of ClassManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "instrument_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "key", "inst", "propagated"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.instrument_attribute", "name": "instrument_attribute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "key", "inst", "propagated"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instrument_attribute of ClassManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "instrument_collection_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "collection_class"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.instrument_collection_class", "name": "instrument_collection_class", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "collection_class"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "builtins.str", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Collection"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instrument_collection_class of ClassManager", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.collections._CollectionFactoryType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_instrumented": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "search"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.is_instrumented", "name": "is_instrumented", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "search"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_instrumented of ClassManager", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_mapped": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.is_mapped", "name": "is_mapped", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_mapped of ClassManager", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.is_mapped", "name": "is_mapped", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_mapped of ClassManager", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "local_attrs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.local_attrs", "name": "local_attrs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "manage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.manage", "name": "manage", "type": null}}, "manager_getter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.manager_getter", "name": "manager_getter", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.manager_getter", "name": "manager_getter", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "sqlalchemy.util.langhelpers.hybridmethod"}}}}, "mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.mapper", "name": "mapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapper of ClassManager", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.mapper", "name": "mapper", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "new_init": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.new_init", "name": "new_init", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "new_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.new_instance", "name": "new_instance", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "state"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_instance of ClassManager", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "original_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.original_init", "name": "original_init", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}}}, "originals": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.originals", "name": "originals", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "post_configure_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.post_configure_attribute", "name": "post_configure_attribute", "type": null}}, "registry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.registry", "name": "registry", "type": "sqlalchemy.orm.decl_api.registry"}}, "setup_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "instance", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.setup_instance", "name": "setup_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "instance", "state"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_instance of ClassManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "state_getter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.state_getter", "name": "state_getter", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.state_getter", "name": "state_getter", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "sqlalchemy.util.langhelpers.hybridmethod"}}}}, "subclass_managers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "recursive"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.subclass_managers", "name": "subclass_managers", "type": null}}, "teardown_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.teardown_instance", "name": "teardown_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "teardown_instance of ClassManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "uninstall_descriptor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.uninstall_descriptor", "name": "uninstall_descriptor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "uninstall_descriptor of ClassManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "uninstall_member": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.uninstall_member", "name": "uninstall_member", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "uninstall_member of ClassManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "uninstrument_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "propagated"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.uninstrument_attribute", "name": "uninstrument_attribute", "type": null}}, "unregister": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.ClassManager.unregister", "name": "unregister", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unregister of ClassManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.instrumentation.ClassManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_O"], "typeddict_type": null}}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef"}, "DEL_ATTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.instrumentation.DEL_ATTR", "name": "DEL_ATTR", "type": "sqlalchemy.util.langhelpers.symbol"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EventTarget": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry.EventTarget", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "HasMemoized": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.HasMemoized", "kind": "Gdef"}, "InstanceEvents": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.events.InstanceEvents", "kind": "Gdef"}, "InstanceState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state.InstanceState", "kind": "Gdef"}, "InstrumentationFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.event.registry.EventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.instrumentation.InstrumentationFactory", "name": "InstrumentationFactory", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.instrumentation.InstrumentationFactory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.instrumentation", "mro": ["sqlalchemy.orm.instrumentation.InstrumentationFactory", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "_check_conflicts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "class_", "factory"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.InstrumentationFactory._check_conflicts", "name": "_check_conflicts", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "class_", "factory"], "arg_types": ["sqlalchemy.orm.instrumentation.InstrumentationFactory", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_conflicts of InstrumentationFactory", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_locate_extended_factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.InstrumentationFactory._locate_extended_factory", "name": "_locate_extended_factory", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "arg_types": ["sqlalchemy.orm.instrumentation.InstrumentationFactory", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_locate_extended_factory of InstrumentationFactory", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.orm.instrumentation._ManagerFactory", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "create_manager_for_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.InstrumentationFactory.create_manager_for_cls", "name": "create_manager_for_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "arg_types": ["sqlalchemy.orm.instrumentation.InstrumentationFactory", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_manager_for_cls of InstrumentationFactory", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.instrumentation.InstrumentationFactory.dispatch", "name": "dispatch", "type": {".class": "Instance", "args": ["sqlalchemy.orm.instrumentation.InstrumentationFactory"], "type_ref": "sqlalchemy.event.base.dispatcher"}}}, "unregister": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.InstrumentationFactory.unregister", "name": "unregister", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "arg_types": ["sqlalchemy.orm.instrumentation.InstrumentationFactory", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unregister of InstrumentationFactory", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.instrumentation.InstrumentationFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.instrumentation.InstrumentationFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "Mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.Mapper", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef"}, "QueryableAttribute": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes.QueryableAttribute", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_AdaptedCollectionProtocol": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.collections._AdaptedCollectionProtocol", "kind": "Gdef"}, "_CollectionFactoryType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.collections._CollectionFactoryType", "kind": "Gdef"}, "_ExpiredAttributeLoaderProto": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.instrumentation._ExpiredAttributeLoaderProto", "name": "_ExpiredAttributeLoaderProto", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.orm.instrumentation._ExpiredAttributeLoaderProto", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.orm.instrumentation", "mro": ["sqlalchemy.orm.instrumentation._ExpiredAttributeLoaderProto", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "state", "toload", "passive"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.orm.instrumentation._ExpiredAttributeLoaderProto.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "state", "toload", "passive"], "arg_types": ["sqlalchemy.orm.instrumentation._ExpiredAttributeLoaderProto", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}, "sqlalchemy.orm.base.PassiveFlag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ExpiredAttributeLoaderProto", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.instrumentation._ExpiredAttributeLoaderProto.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.instrumentation._ExpiredAttributeLoaderProto", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ManagerFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.instrumentation._ManagerFactory", "name": "_ManagerFactory", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.orm.instrumentation._ManagerFactory", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.orm.instrumentation", "mro": ["sqlalchemy.orm.instrumentation._ManagerFactory", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.orm.instrumentation._ManagerFactory.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "arg_types": ["sqlalchemy.orm.instrumentation._ManagerFactory", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ManagerFactory", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.instrumentation._ManagerFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.instrumentation._ManagerFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MapperConfig": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_base._MapperConfig", "kind": "Gdef"}, "_O": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._O", "kind": "Gdef"}, "_RegistryType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._RegistryType", "kind": "Gdef"}, "_SerializeManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.instrumentation._SerializeManager", "name": "_SerializeManager", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.instrumentation._SerializeManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.instrumentation", "mro": ["sqlalchemy.orm.instrumentation._SerializeManager", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "state", "inst", "state_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation._SerializeManager.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "d"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation._SerializeManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "d"], "arg_types": ["sqlalchemy.orm.instrumentation._SerializeManager", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _SerializeManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "class_": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.instrumentation._SerializeManager.class_", "name": "class_", "type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.instrumentation._SerializeManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.instrumentation._SerializeManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.instrumentation._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.instrumentation.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.instrumentation.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.instrumentation.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.instrumentation.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.instrumentation.__package__", "name": "__package__", "type": "builtins.str"}}, "_default_dict_getter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.instrumentation._default_dict_getter", "name": "_default_dict_getter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["instance"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_default_manager_getter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.instrumentation._default_manager_getter", "name": "_default_manager_getter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_default_opt_manager_getter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.instrumentation._default_opt_manager_getter", "name": "_default_opt_manager_getter", "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._ExternalEntityType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "_default_state_getter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.instrumentation._default_state_getter", "name": "_default_state_getter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["instance"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_generate_init": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["class_", "class_manager", "original_init"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation._generate_init", "name": "_generate_init", "type": null}}, "_instrumentation_factory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.instrumentation._instrumentation_factory", "name": "_instrumentation_factory", "type": "sqlalchemy.orm.instrumentation.InstrumentationFactory"}}, "_is_collection_attribute_impl": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes._is_collection_attribute_impl", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "base": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.collections", "kind": "Gdef"}, "dispatcher": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.base.dispatcher", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.exc", "kind": "Gdef"}, "instance_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.instrumentation.instance_dict", "name": "instance_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["instance"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "instance_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.instrumentation.instance_state", "name": "instance_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["instance"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "interfaces": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces", "kind": "Gdef"}, "is_instrumented": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["instance", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.is_instrumented", "name": "is_instrumented", "type": null}}, "manager_of_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.instrumentation.manager_of_class", "name": "manager_of_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "opt_manager_of_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.instrumentation.opt_manager_of_class", "name": "opt_manager_of_class", "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._ExternalEntityType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "register_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["class_", "finalize", "mapper", "registry", "declarative_scan", "expired_attribute_loader", "init_method"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.register_class", "name": "register_class", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["class_", "finalize", "mapper", "registry", "declarative_scan", "expired_attribute_loader", "init_method"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.orm.decl_api.registry", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.orm.decl_base._MapperConfig", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.orm.instrumentation._ExpiredAttributeLoaderProto", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_class", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "state": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state", "kind": "Gdef"}, "unregister_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["class_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.instrumentation.unregister_class", "name": "unregister_class", "type": null}}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/instrumentation.py"}