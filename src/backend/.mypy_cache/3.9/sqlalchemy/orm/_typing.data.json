{".class": "MypyFile", "_fullname": "sqlalchemy.orm._typing", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AliasedClass": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.AliasedClass", "kind": "Gdef"}, "AliasedInsp": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.AliasedInsp", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AttributeImpl": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes.AttributeImpl", "kind": "Gdef"}, "CollectionAttributeImpl": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes.CollectionAttributeImpl", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "DMLStrategyArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._orm_types.DMLStrategyArgument", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ExecutableOption": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.ExecutableOption", "kind": "Gdef"}, "HasCollectionAdapter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes.HasCollectionAdapter", "kind": "Gdef"}, "InspectionAttr": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.InspectionAttr", "kind": "Gdef"}, "InstanceState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state.InstanceState", "kind": "Gdef"}, "Mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.Mapper", "kind": "Gdef"}, "MapperProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.MapperProperty", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "ORMOption": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.ORMOption", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrmExecuteOptionsParameter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter", "line": 99, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._OrmKnownExecutionOptions"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}]}}}, "PassiveFlag": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.PassiveFlag", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef"}, "QueryableAttribute": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes.QueryableAttribute", "kind": "Gdef"}, "RelationshipProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.relationships.RelationshipProperty", "kind": "Gdef"}, "SynchronizeSessionArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._orm_types.SynchronizeSessionArgument", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeGuard": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeGuard", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UserDefinedOption": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.UserDefinedOption", "kind": "Gdef"}, "_CE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._CE", "kind": "Gdef"}, "_ClassDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm._typing._ClassDict", "line": 74, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Mapping"}}}, "_CoreKnownExecutionOptions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._CoreKnownExecutionOptions", "kind": "Gdef"}, "_EntityType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm._typing._EntityType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.orm._typing._EntityType", "line": 69, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm._typing._EntityType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm._typing._EntityType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm._typing._EntityType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm._typing._EntityType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}]}}}, "_ExternalEntityType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm._typing._ExternalEntityType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.orm._typing._ExternalEntityType", "line": 67, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm._typing._ExternalEntityType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm._typing._ExternalEntityType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}]}}}, "_HasClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement", "kind": "Gdef"}, "_IdentityKeyType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm._typing._IdentityKeyType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.orm._typing._IdentityKeyType", "line": 77, "no_args": false, "normalized": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm._typing._IdentityKeyType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_InstanceDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm._typing._InstanceDict", "line": 75, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_InternalEntityType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm._typing._InternalEntityType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.orm._typing._InternalEntityType", "line": 65, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm._typing._InternalEntityType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm._typing._InternalEntityType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}]}}}, "_LoaderCallable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm._typing._LoaderCallable", "name": "_LoaderCallable", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.orm._typing._LoaderCallable", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.orm._typing", "mro": ["sqlalchemy.orm._typing._LoaderCallable", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "passive"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.orm._typing._LoaderCallable.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "passive"], "arg_types": ["sqlalchemy.orm._typing._LoaderCallable", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.state.InstanceState"}, "sqlalchemy.orm.base.PassiveFlag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _LoaderCallable", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._LoaderCallable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm._typing._LoaderCallable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_O": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "name": "_O", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_ORMAdapterProto": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm._typing._ORMAdapterProto", "name": "_ORMAdapterProto", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.orm._typing._ORMAdapterProto", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.orm._typing", "mro": ["sqlalchemy.orm._typing._ORMAdapterProto", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "key"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.orm._typing._ORMAdapterProto.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "key"], "arg_types": ["sqlalchemy.orm._typing._ORMAdapterProto", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._CE", "id": -1, "name": "_CE", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ORMAdapterProto", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._CE", "id": -1, "name": "_CE", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._CE", "id": -1, "name": "_CE", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._ORMAdapterProto.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm._typing._ORMAdapterProto", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ORMCOLEXPR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._ORMCOLEXPR", "name": "_ORMCOLEXPR", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}}, "_ORMColumnExprArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm._typing._ORMColumnExprArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "column": 0, "fullname": "sqlalchemy.orm._typing._ORMColumnExprArgument", "line": 79, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm._typing._ORMColumnExprArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm._typing._ORMColumnExprArgument", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}]}}}, "_OrmKnownExecutionOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm._typing._OrmKnownExecutionOptions", "name": "_OrmKnownExecutionOptions", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm._typing._OrmKnownExecutionOptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm._typing", "mro": ["sqlalchemy.orm._typing._OrmKnownExecutionOptions", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["compiled_cache", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.CompiledCacheType"}, {".class": "NoneType"}]}], ["logging_token", "builtins.str"], ["isolation_level", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.IsolationLevel"}], ["no_parameters", "builtins.bool"], ["stream_results", "builtins.bool"], ["max_row_buffer", "builtins.int"], ["yield_per", "builtins.int"], ["insertmanyvalues_page_size", "builtins.int"], ["schema_translate_map", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.SchemaTranslateMapType"}, {".class": "NoneType"}]}], ["populate_existing", "builtins.bool"], ["autoflush", "builtins.bool"], ["synchronize_session", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._orm_types.SynchronizeSessionArgument"}], ["dml_strategy", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._orm_types.DMLStrategyArgument"}], ["is_delete_using", "builtins.bool"], ["is_update_from", "builtins.bool"], ["render_nulls", "builtins.bool"]], "required_keys": []}}}, "_RegistryType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "sqlalchemy.orm._typing._RegistryType", "line": 63, "no_args": true, "normalized": false, "target": "sqlalchemy.orm.decl_api.registry"}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_T_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T_co", "name": "_T_co", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm._typing.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm._typing.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm._typing.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm._typing.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm._typing.__package__", "name": "__package__", "type": "builtins.str"}}, "_registry_type": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.registry", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "attr_is_internal_proxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm._typing.attr_is_internal_proxy", "name": "attr_is_internal_proxy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["sqlalchemy.orm.base.InspectionAttr"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attr_is_internal_proxy", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}, "unpack_kwargs": false, "variables": []}}}, "insp_is_aliased_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm._typing.insp_is_aliased_class", "name": "insp_is_aliased_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insp_is_aliased_class", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, "unpack_kwargs": false, "variables": []}}}, "insp_is_attribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm._typing.insp_is_attribute", "name": "insp_is_attribute", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["sqlalchemy.orm.base.InspectionAttr"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insp_is_attribute", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}, "unpack_kwargs": false, "variables": []}}}, "insp_is_mapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm._typing.insp_is_mapper", "name": "insp_is_mapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insp_is_mapper", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "unpack_kwargs": false, "variables": []}}}, "insp_is_mapper_property": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm._typing.insp_is_mapper_property", "name": "insp_is_mapper_property", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insp_is_mapper_property", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, "unpack_kwargs": false, "variables": []}}}, "is_collection_impl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["impl"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm._typing.is_collection_impl", "name": "is_collection_impl", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["impl"], "arg_types": ["sqlalchemy.orm.attributes.AttributeImpl"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_collection_impl", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.orm.attributes.CollectionAttributeImpl", "unpack_kwargs": false, "variables": []}}}, "is_composite_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm._typing.is_composite_class", "name": "is_composite_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_composite_class", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_has_collection_adapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["impl"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm._typing.is_has_collection_adapter", "name": "is_has_collection_adapter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["impl"], "arg_types": ["sqlalchemy.orm.attributes.AttributeImpl"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_has_collection_adapter", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.orm.attributes.HasCollectionAdapter", "unpack_kwargs": false, "variables": []}}}, "is_orm_option": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["opt"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm._typing.is_orm_option", "name": "is_orm_option", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["opt"], "arg_types": ["sqlalchemy.sql.base.ExecutableOption"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_orm_option", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.orm.interfaces.ORMOption", "unpack_kwargs": false, "variables": []}}}, "is_user_defined_option": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["opt"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm._typing.is_user_defined_option", "name": "is_user_defined_option", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["opt"], "arg_types": ["sqlalchemy.sql.base.ExecutableOption"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_user_defined_option", "ret_type": "builtins.bool", "type_guard": "sqlalchemy.orm.interfaces.UserDefinedOption", "unpack_kwargs": false, "variables": []}}}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "prop_is_relationship": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["prop"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm._typing.prop_is_relationship", "name": "prop_is_relationship", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["prop"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prop_is_relationship", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.relationships.RelationshipProperty"}, "unpack_kwargs": false, "variables": []}}}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/_typing.py"}