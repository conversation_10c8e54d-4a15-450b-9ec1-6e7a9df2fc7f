{".class": "MypyFile", "_fullname": "sqlalchemy.orm.util", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractEntityRegistry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "kind": "Gdef"}, "AbstractSet": {".class": "SymbolTableNode", "cross_ref": "typing.AbstractSet", "kind": "Gdef"}, "AliasedClass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "type_ref": "sqlalchemy.inspection.Inspectable"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.interfaces.ORMColumnsClauseRole"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.util.AliasedClass", "name": "AliasedClass", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.util.AliasedClass", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.util", "mro": ["sqlalchemy.orm.util.AliasedClass", "sqlalchemy.inspection.Inspectable", "sqlalchemy.orm.interfaces.ORMColumnsClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedClass.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of AliasedClass", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "mapped_class_or_ac", "alias", "name", "flat", "adapt_on_names", "with_polymorphic_mappers", "with_polymorphic_discriminator", "base_alias", "use_mapper_path", "represents_outer_join"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedClass.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "mapped_class_or_ac", "alias", "name", "flat", "adapt_on_names", "with_polymorphic_mappers", "with_polymorphic_discriminator", "base_alias", "use_mapper_path", "represents_outer_join"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._EntityType"}, {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AliasedClass", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.AliasedClass.__name__", "name": "__name__", "type": "builtins.str"}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedClass.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of AliasedClass", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedClass.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of AliasedClass", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_aliased_insp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedClass._aliased_insp", "name": "_aliased_insp", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}}}, "_get_from_serialized": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "key", "mapped_class", "aliased_insp"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedClass._get_from_serialized", "name": "_get_from_serialized", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "key", "mapped_class", "aliased_insp"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_from_serialized of AliasedClass", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_reconstitute_from_aliased_insp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "aliased_insp"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.util.AliasedClass._reconstitute_from_aliased_insp", "name": "_reconstitute_from_aliased_insp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "aliased_insp"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reconstitute_from_aliased_insp of AliasedClass", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedClass._reconstitute_from_aliased_insp", "name": "_reconstitute_from_aliased_insp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "aliased_insp"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reconstitute_from_aliased_insp of AliasedClass", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util.AliasedClass.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedClass", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_O"], "typeddict_type": null}}, "AliasedInsp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.interfaces.ORMEntityColumnsClauseRole"}, "sqlalchemy.orm.interfaces.ORMFromClauseRole", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.orm.base.InspectionAttr", "sqlalchemy.util.langhelpers.MemoizedSlots", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "type_ref": "sqlalchemy.inspection.Inspectable"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.util.AliasedInsp", "name": "AliasedInsp", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.util.AliasedInsp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.util", "mro": ["sqlalchemy.orm.util.AliasedInsp", "sqlalchemy.orm.interfaces.ORMEntityColumnsClauseRole", "sqlalchemy.orm.interfaces.ORMColumnsClauseRole", "sqlalchemy.orm.interfaces.ORMFromClauseRole", "sqlalchemy.sql.roles.StrictFromClauseRole", "sqlalchemy.sql.roles.FromClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.orm.base.InspectionAttr", "sqlalchemy.util.langhelpers.MemoizedSlots", "sqlalchemy.inspection.Inspectable", "builtins.object"], "names": {".class": "SymbolTable", "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedInsp.__getstate__", "name": "__getstate__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getstate__ of AliasedInsp", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "entity", "inspected", "selectable", "name", "with_polymorphic_mappers", "polymorphic_on", "_base_alias", "_use_mapper_path", "adapt_on_names", "represents_outer_join", "nest_adapters"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedInsp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "entity", "inspected", "selectable", "name", "with_polymorphic_mappers", "polymorphic_on", "_base_alias", "_use_mapper_path", "adapt_on_names", "represents_outer_join", "nest_adapters"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, "sqlalchemy.sql.selectable.FromClause", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AliasedInsp", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedInsp.__repr__", "name": "__repr__", "type": null}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedInsp.__setstate__", "name": "__setstate__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setstate__ of AliasedInsp", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.util.AliasedInsp.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedInsp.__str__", "name": "__str__", "type": null}}, "_adapt_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedInsp._adapt_element", "name": "_adapt_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "expr", "key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._ORMCOLEXPR", "id": -1, "name": "_ORMCOLEXPR", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_adapt_element of AliasedInsp", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._ORMCOLEXPR", "id": -1, "name": "_ORMCOLEXPR", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._ORMCOLEXPR", "id": -1, "name": "_ORMCOLEXPR", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}]}}}, "_adapt_on_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedInsp._adapt_on_names", "name": "_adapt_on_names", "type": "builtins.bool"}}, "_adapter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.AliasedInsp._adapter", "name": "_adapter", "type": "sqlalchemy.orm.util.ORMAdapter"}}, "_alias_factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["cls", "element", "alias", "name", "flat", "adapt_on_names"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.util.AliasedInsp._alias_factory", "name": "_alias_factory", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["cls", "element", "alias", "name", "flat", "adapt_on_names"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._EntityType"}, "sqlalchemy.sql.selectable.FromClause"]}, {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_alias_factory of AliasedInsp", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, "sqlalchemy.sql.selectable.FromClause"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedInsp._alias_factory", "name": "_alias_factory", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["cls", "element", "alias", "name", "flat", "adapt_on_names"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._EntityType"}, "sqlalchemy.sql.selectable.FromClause"]}, {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_alias_factory of AliasedInsp", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, "sqlalchemy.sql.selectable.FromClause"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_base_alias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedInsp._base_alias", "name": "_base_alias", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}]}], "type_ref": "_weakref.ReferenceType"}}}, "_cache_key_traversal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.AliasedInsp._cache_key_traversal", "name": "_cache_key_traversal", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.visitors.InternalTraversal"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "_entity_for_mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedInsp._entity_for_mapper", "name": "_entity_for_mapper", "type": null}}, "_is_with_polymorphic": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedInsp._is_with_polymorphic", "name": "_is_with_polymorphic", "type": "builtins.bool"}}, "_memo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "key", "callable_", "args", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedInsp._memo", "name": "_memo", "type": null}}, "_memoized_attr__all_column_expressions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedInsp._memoized_attr__all_column_expressions", "name": "_memoized_attr__all_column_expressions", "type": null}}, "_memoized_attr__get_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedInsp._memoized_attr__get_clause", "name": "_memoized_attr__get_clause", "type": null}}, "_memoized_attr__memoized_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedInsp._memoized_attr__memoized_values", "name": "_memoized_attr__memoized_values", "type": null}}, "_memoized_method___clause_element__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedInsp._memoized_method___clause_element__", "name": "_memoized_method___clause_element__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_memoized_method___clause_element__ of AliasedInsp", "ret_type": "sqlalchemy.sql.selectable.FromClause", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_merge_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.AliasedInsp._merge_with", "name": "_merge_with", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_merge_with of AliasedInsp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_nest_adapters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedInsp._nest_adapters", "name": "_nest_adapters", "type": "builtins.bool"}}, "_orm_adapt_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "key"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.util.AliasedInsp._orm_adapt_element", "name": "_orm_adapt_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._CE", "id": -1, "name": "_CE", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_orm_adapt_element of AliasedInsp", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._CE", "id": -1, "name": "_CE", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._typing._CE", "id": -1, "name": "_CE", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "values": [], "variance": 0}]}}}, "_path_registry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.util.AliasedInsp._path_registry", "name": "_path_registry", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_path_registry of AliasedInsp", "ret_type": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedInsp._path_registry", "name": "_path_registry", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_path_registry of AliasedInsp", "ret_type": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.AliasedInsp._target", "name": "_target", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}]}}}, "_use_mapper_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedInsp._use_mapper_path", "name": "_use_mapper_path", "type": "builtins.bool"}}, "_weak_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.AliasedInsp._weak_entity", "name": "_weak_entity", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}], "type_ref": "_weakref.ReferenceType"}}}, "_with_polymorphic_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.AliasedInsp._with_polymorphic_entities", "name": "_with_polymorphic_entities", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "type_ref": "typing.Sequence"}}}, "_with_polymorphic_factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["cls", "base", "classes", "selectable", "flat", "polymorphic_on", "aliased", "innerjoin", "adapt_on_names", "_use_mapper_path"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.util.AliasedInsp._with_polymorphic_factory", "name": "_with_polymorphic_factory", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["cls", "base", "classes", "selectable", "flat", "polymorphic_on", "aliased", "innerjoin", "adapt_on_names", "_use_mapper_path"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "*"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._EntityType"}], "type_ref": "typing.Iterable"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}, "sqlalchemy.sql.selectable.FromClause"]}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_with_polymorphic_factory of AliasedInsp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedInsp._with_polymorphic_factory", "name": "_with_polymorphic_factory", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["cls", "base", "classes", "selectable", "flat", "polymorphic_on", "aliased", "innerjoin", "adapt_on_names", "_use_mapper_path"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "*"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._EntityType"}], "type_ref": "typing.Iterable"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}, "sqlalchemy.sql.selectable.FromClause"]}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_with_polymorphic_factory of AliasedInsp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "class_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.util.AliasedInsp.class_", "name": "class_", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "class_ of AliasedInsp", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedInsp.class_", "name": "class_", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "class_ of AliasedInsp", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.util.AliasedInsp.entity", "name": "entity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity of AliasedInsp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedInsp.entity", "name": "entity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity of AliasedInsp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "entity_namespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.util.AliasedInsp.entity_namespace", "name": "entity_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity_namespace of AliasedInsp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedInsp.entity_namespace", "name": "entity_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity_namespace of AliasedInsp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_aliased_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.AliasedInsp.is_aliased_class", "name": "is_aliased_class", "type": "builtins.bool"}}, "local_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedInsp.local_table", "name": "local_table", "type": "sqlalchemy.sql.selectable.FromClause"}}, "mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.AliasedInsp.mapper", "name": "mapper", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedInsp.name", "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "persist_selectable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedInsp.persist_selectable", "name": "persist_selectable", "type": "sqlalchemy.sql.selectable.FromClause"}}, "polymorphic_on": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedInsp.polymorphic_on", "name": "polymorphic_on", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "NoneType"}]}}}, "represents_outer_join": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util.AliasedInsp.represents_outer_join", "name": "represents_outer_join", "type": "builtins.bool"}}, "selectable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.AliasedInsp.selectable", "name": "selectable", "type": "sqlalchemy.sql.selectable.FromClause"}}, "with_polymorphic_mappers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.AliasedInsp.with_polymorphic_mappers", "name": "with_polymorphic_mappers", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "type_ref": "typing.Sequence"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util.AliasedInsp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.util.AliasedInsp", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, "values": [], "variance": 0}, "slots": ["__clause_element__", "__weakref__", "_adapt_on_names", "_adapter", "_all_column_expressions", "_base_alias", "_is_with_polymorphic", "_memoized_values", "_nest_adapters", "_target", "_use_mapper_path", "_weak_entity", "_with_polymorphic_entities", "local_table", "mapper", "name", "persist_selectable", "polymorphic_on", "represents_outer_join", "selectable", "with_polymorphic_mappers"], "tuple_type": null, "type_vars": ["_O"], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ArgsTypeProcotol": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.typing.ArgsTypeProcotol", "kind": "Gdef"}, "BindParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BindParameter", "kind": "Gdef"}, "Bundle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.util.B<PERSON>le", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.interfaces.ORMColumnsClauseRole"}, "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.util.B<PERSON>le", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.B<PERSON>le"}], "type_ref": "sqlalchemy.inspection.Inspectable"}, "sqlalchemy.orm.base.InspectionAttr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.util.B<PERSON>le", "name": "Bundle", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.util.B<PERSON>le", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.util.B<PERSON>le", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.util", "mro": ["sqlalchemy.orm.util.B<PERSON>le", "sqlalchemy.orm.interfaces.ORMColumnsClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.visitors.Visitable", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.inspection.Inspectable", "sqlalchemy.orm.base.InspectionAttr", "builtins.object"], "names": {".class": "SymbolTable", "__clause_element__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.Bundle.__clause_element__", "name": "__clause_element__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "name", "exprs", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.Bundle.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "name", "exprs", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.util.B<PERSON>le", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.B<PERSON>le"}, "builtins.str", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Bundle", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_clone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.Bundle._clone", "name": "_clone", "type": null}}, "_gen_cache_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "anon_map", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.Bundle._gen_cache_key", "name": "_gen_cache_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "anon_map", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.util.B<PERSON>le", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.B<PERSON>le"}, "sqlalchemy.sql._py_util.cache_anon_map", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_gen_cache_key of Bundle", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_label": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util.Bundle._label", "name": "_label", "type": "builtins.str"}}, "_propagate_attrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.Bundle._propagate_attrs", "name": "_propagate_attrs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._PropagateAttrsType"}}}, "c": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.Bundle.c", "name": "c", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.KeyedColumnElement"}], "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}}}, "clauses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.util.Bundle.clauses", "name": "clauses", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.util.Bundle.clauses", "name": "clauses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.util.B<PERSON>le", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.B<PERSON>le"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clauses of Bundle", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.Bundle.columns", "name": "columns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.KeyedColumnElement"}], "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}}}, "create_row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "query", "procs", "labels"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.Bundle.create_row_processor", "name": "create_row_processor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "query", "procs", "labels"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.util.B<PERSON>le", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.B<PERSON>le"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.row.Row"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_row_processor of Bundle", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.row.Row"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.util.Bundle.entity", "name": "entity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.util.B<PERSON>le", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.B<PERSON>le"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity of Bundle", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.util.Bundle.entity", "name": "entity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.util.B<PERSON>le", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.B<PERSON>le"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity of Bundle", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "entity_namespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.util.Bundle.entity_namespace", "name": "entity_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.util.B<PERSON>le", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.B<PERSON>le"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity_namespace of Bundle", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.KeyedColumnElement"}], "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.util.Bundle.entity_namespace", "name": "entity_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.util.B<PERSON>le", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.B<PERSON>le"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity_namespace of Bundle", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.KeyedColumnElement"}], "type_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "exprs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.Bundle.exprs", "name": "exprs", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable._ColumnsClauseElement"}], "type_ref": "builtins.list"}}}, "is_aliased_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.Bundle.is_aliased_class", "name": "is_aliased_class", "type": "builtins.bool"}}, "is_bundle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.Bundle.is_bundle", "name": "is_bundle", "type": "builtins.bool"}}, "is_clause_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.Bundle.is_clause_element", "name": "is_clause_element", "type": "builtins.bool"}}, "is_mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.Bundle.is_mapper", "name": "is_mapper", "type": "builtins.bool"}}, "label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.Bundle.label", "name": "label", "type": null}}, "mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.util.Bundle.mapper", "name": "mapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.util.B<PERSON>le", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.B<PERSON>le"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapper of Bundle", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.util.Bundle.mapper", "name": "mapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.util.B<PERSON>le", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.B<PERSON>le"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapper of Bundle", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util.Bundle.name", "name": "name", "type": "builtins.str"}}, "proxy_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.Bundle.proxy_set", "name": "proxy_set", "type": null}}, "single_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.Bundle.single_entity", "name": "single_entity", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util.Bundle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.util.B<PERSON>le", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.util.B<PERSON>le"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CascadeOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.frozenset"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.util.CascadeOptions", "name": "CascadeOptions", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.util.CascadeOptions", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.orm.util", "mro": ["sqlalchemy.orm.util.CascadeOptions", "builtins.frozenset", "typing.AbstractSet", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "value_list"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "sqlalchemy.orm.util.CascadeOptions.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value_list"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.util.CascadeOptions"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, "builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of CascadeOptions", "ret_type": "sqlalchemy.orm.util.CascadeOptions", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.CascadeOptions.__repr__", "name": "__repr__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.util.CascadeOptions.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_add_w_all_cascades": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.CascadeOptions._add_w_all_cascades", "name": "_add_w_all_cascades", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.frozenset"}}}, "_allowed_cascades": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.CascadeOptions._allowed_cascades", "name": "_allowed_cascades", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.frozenset"}}}, "_viewonly_cascades": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.CascadeOptions._viewonly_cascades", "name": "_viewonly_cascades", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.CascadeOptions.delete", "name": "delete", "type": "builtins.bool"}}, "delete_orphan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.CascadeOptions.delete_orphan", "name": "delete_orphan", "type": "builtins.bool"}}, "expunge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.CascadeOptions.expunge", "name": "expunge", "type": "builtins.bool"}}, "from_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "arg"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.util.CascadeOptions.from_string", "name": "from_string", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.util.CascadeOptions.from_string", "name": "from_string", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arg"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.util.CascadeOptions"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_string of CascadeOptions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "merge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.CascadeOptions.merge", "name": "merge", "type": "builtins.bool"}}, "refresh_expire": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.CascadeOptions.refresh_expire", "name": "refresh_expire", "type": "builtins.bool"}}, "save_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.CascadeOptions.save_update", "name": "save_update", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util.CascadeOptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.util.CascadeOptions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColumnCollection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.ColumnCollection", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "CriteriaOption": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.CriteriaOption", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DynamicMapped": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.DynamicMapped", "kind": "Gdef"}, "FromClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.FromClause", "kind": "Gdef"}, "FrozenSet": {".class": "SymbolTableNode", "cross_ref": "typing.FrozenSet", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "GenericAlias": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.GenericAlias", "name": "GenericAlias", "type": {".class": "TypeType", "item": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "builtins._T", "id": 1, "name": "_T", "namespace": "builtins.list", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "abc.ABCMeta", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Iterable"}], "bound_args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "builtins._T", "id": 1, "name": "_T", "namespace": "builtins.list", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "abc.ABCMeta", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}}, "HasCacheKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.cache_key.HasCacheKey", "kind": "Gdef"}, "InspectionAttr": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.InspectionAttr", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "KeyedColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.KeyedColumnElement", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "LoaderCriteriaOption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.interfaces.CriteriaOption"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption", "name": "LoaderCriteriaOption", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.util", "mro": ["sqlalchemy.orm.util.LoaderCriteriaOption", "sqlalchemy.orm.interfaces.CriteriaOption", "sqlalchemy.orm.interfaces.CompileStateOption", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.orm.interfaces.ORMOption", "sqlalchemy.sql.base.ExecutableOption", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.HasTraverseInternals", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "entity_or_base", "where_criteria", "loader_only", "include_aliases", "propagate_to_loaders", "track_closure_variables"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "entity_or_base", "where_criteria", "loader_only", "include_aliases", "propagate_to_loaders", "track_closure_variables"], "arg_types": ["sqlalchemy.orm.util.LoaderCriteriaOption", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._EntityType"}, {".class": "TypeAliasType", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LoaderCriteriaOption", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption.__reduce__", "name": "__reduce__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_all_mappers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption._all_mappers", "name": "_all_mappers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.util.LoaderCriteriaOption"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_all_mappers of LoaderCriteriaOption", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_where_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ext_info"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption._resolve_where_criteria", "name": "_resolve_where_criteria", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ext_info"], "arg_types": ["sqlalchemy.orm.util.LoaderCriteriaOption", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_where_criteria of LoaderCriteriaOption", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_should_include": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "compile_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption._should_include", "name": "_should_include", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "compile_state"], "arg_types": ["sqlalchemy.orm.util.LoaderCriteriaOption", "sqlalchemy.orm.context.ORMCompileState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_should_include of LoaderCriteriaOption", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_traverse_internals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption._traverse_internals", "name": "_traverse_internals", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.visitors.InternalTraversal"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "_unreduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "entity", "where_criteria", "include_aliases", "propagate_to_loaders"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption._unreduce", "name": "_unreduce", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption._unreduce", "name": "_unreduce", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "entity", "where_criteria", "include_aliases", "propagate_to_loaders"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.util.LoaderCriteriaOption"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unreduce of LoaderCriteriaOption", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_where_crit_orig": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption._where_crit_orig", "name": "_where_crit_orig", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "deferred_where_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption.deferred_where_criteria", "name": "deferred_where_criteria", "type": "builtins.bool"}}, "entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption.entity", "name": "entity", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "NoneType"}]}}}, "get_global_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attributes"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption.get_global_criteria", "name": "get_global_criteria", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attributes"], "arg_types": ["sqlalchemy.orm.util.LoaderCriteriaOption", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_global_criteria of LoaderCriteriaOption", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "include_aliases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption.include_aliases", "name": "include_aliases", "type": "builtins.bool"}}, "process_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "compile_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption.process_compile_state", "name": "process_compile_state", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "compile_state"], "arg_types": ["sqlalchemy.orm.util.LoaderCriteriaOption", "sqlalchemy.orm.context.ORMCompileState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_compile_state of LoaderCriteriaOption", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "process_compile_state_replaced_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "mapper_entities"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption.process_compile_state_replaced_entities", "name": "process_compile_state_replaced_entities", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "mapper_entities"], "arg_types": ["sqlalchemy.orm.util.LoaderCriteriaOption", "sqlalchemy.orm.context.ORMCompileState", {".class": "Instance", "args": ["sqlalchemy.orm.context._MapperEntity"], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_compile_state_replaced_entities of LoaderCriteriaOption", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "propagate_to_loaders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption.propagate_to_loaders", "name": "propagate_to_loaders", "type": "builtins.bool"}}, "root_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption.root_entity", "name": "root_entity", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}]}}}, "where_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption.where_criteria", "name": "where_criteria", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.lambdas.DeferredLambdaElement"]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util.LoaderCriteriaOption.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.util.LoaderCriteriaOption", "values": [], "variance": 0}, "slots": ["_where_crit_orig", "deferred_where_criteria", "entity", "include_aliases", "propagate_to_loaders", "root_entity", "where_criteria"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mapped": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.Mapped", "kind": "Gdef"}, "Mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.Mapper", "kind": "Gdef"}, "MapperProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.MapperProperty", "kind": "Gdef"}, "Match": {".class": "SymbolTableNode", "cross_ref": "re.Match", "kind": "Gdef"}, "MemoizedHasCacheKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "kind": "Gdef"}, "MemoizedSlots": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.MemoizedSlots", "kind": "Gdef"}, "ORMAdapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.util.ColumnAdapter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.util.ORMAdapter", "name": "ORMAdapter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.util.ORMAdapter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.util", "mro": ["sqlalchemy.orm.util.ORMAdapter", "sqlalchemy.sql.util.ColumnAdapter", "sqlalchemy.sql.util.ClauseAdapter", "sqlalchemy.sql.visitors.ReplacingExternalTraversal", "sqlalchemy.sql.visitors.CloningExternalTraversal", "sqlalchemy.sql.visitors.ExternalTraversal", "sqlalchemy.util.langhelpers.MemoizedSlots", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "role", "entity", "equivalents", "adapt_required", "allow_label_resolve", "anonymize_labels", "selectable", "limit_on_entity", "adapt_on_names", "adapt_from_selectables"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.ORMAdapter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "role", "entity", "equivalents", "adapt_required", "allow_label_resolve", "anonymize_labels", "selectable", "limit_on_entity", "adapt_on_names", "adapt_from_selectables"], "arg_types": ["sqlalchemy.orm.util.ORMAdapter", "sqlalchemy.orm.util._TraceAdaptRole", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._EquivalentColumnMap"}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.Selectable", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.sql.selectable.FromClause"], "type_ref": "typing.AbstractSet"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ORMAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.util.ORMAdapter.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_include_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "elem"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.ORMAdapter._include_fn", "name": "_include_fn", "type": null}}, "aliased_insp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.ORMAdapter.aliased_insp", "name": "aliased_insp", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "NoneType"}]}}}, "is_aliased_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.util.ORMAdapter.is_aliased_class", "name": "is_aliased_class", "type": "builtins.bool"}}, "mapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util.ORMAdapter.mapper", "name": "mapper", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}}}, "role": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util.ORMAdapter.role", "name": "role", "type": "sqlalchemy.orm.util._TraceAdaptRole"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util.ORMAdapter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.util.ORMAdapter", "values": [], "variance": 0}, "slots": ["__traverse_options__", "__weakref__", "_next", "_visitor_dict", "_wrap", "adapt_from_selectables", "adapt_on_names", "adapt_required", "aliased_insp", "allow_label_resolve", "columns", "equivalents", "exclude_fn", "include_fn", "is_aliased_class", "mapper", "role", "selectable"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ORMColumnsClauseRole": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.ORMColumnsClauseRole", "kind": "Gdef"}, "ORMCompileState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.ORMCompileState", "kind": "Gdef"}, "ORMDescriptor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.ORMDescriptor", "kind": "Gdef"}, "ORMEntityColumnsClauseRole": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.ORMEntityColumnsClauseRole", "kind": "Gdef"}, "ORMFromClauseRole": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.ORMFromClauseRole", "kind": "Gdef"}, "ORMStatementAdapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.util.ColumnAdapter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.util.ORMStatementAdapter", "name": "ORMStatementAdapter", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.util.ORMStatementAdapter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.util", "mro": ["sqlalchemy.orm.util.ORMStatementAdapter", "sqlalchemy.sql.util.ColumnAdapter", "sqlalchemy.sql.util.ClauseAdapter", "sqlalchemy.sql.visitors.ReplacingExternalTraversal", "sqlalchemy.sql.visitors.CloningExternalTraversal", "sqlalchemy.sql.visitors.ExternalTraversal", "sqlalchemy.util.langhelpers.MemoizedSlots", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "role", "selectable", "equivalents", "adapt_required", "allow_label_resolve", "anonymize_labels", "adapt_on_names", "adapt_from_selectables"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.ORMStatementAdapter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "role", "selectable", "equivalents", "adapt_required", "allow_label_resolve", "anonymize_labels", "adapt_on_names", "adapt_from_selectables"], "arg_types": ["sqlalchemy.orm.util.ORMStatementAdapter", "sqlalchemy.orm.util._TraceAdaptRole", "sqlalchemy.sql.selectable.Selectable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._EquivalentColumnMap"}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.sql.selectable.FromClause"], "type_ref": "typing.AbstractSet"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ORMStatementAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.util.ORMStatementAdapter.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "role": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util.ORMStatementAdapter.role", "name": "role", "type": "sqlalchemy.orm.util._TraceAdaptRole"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util.ORMStatementAdapter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.util.ORMStatementAdapter", "values": [], "variance": 0}, "slots": ["__traverse_options__", "__weakref__", "_next", "_visitor_dict", "_wrap", "adapt_from_selectables", "adapt_on_names", "adapt_required", "allow_label_resolve", "columns", "equivalents", "exclude_fn", "include_fn", "role", "selectable"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PathRegistry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.path_registry.PathRegistry", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef"}, "Query": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.query.Query", "kind": "Gdef"}, "ReadOnlyColumnCollection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.ReadOnlyColumnCollection", "kind": "Gdef"}, "RelationshipProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.relationships.RelationshipProperty", "kind": "Gdef"}, "Row": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.row.Row", "kind": "Gdef"}, "RowMapping": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.row.RowMapping", "kind": "Gdef"}, "Select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Select", "kind": "Gdef"}, "Selectable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Selectable", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SupportsCloneAnnotations": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.annotation.SupportsCloneAnnotations", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WriteOnlyMapped": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.WriteOnlyMapped", "kind": "Gdef"}, "_AnnotationScanType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.typing._AnnotationScanType", "kind": "Gdef"}, "_CE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._CE", "kind": "Gdef"}, "_CleanupError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.util._CleanupError", "name": "_CleanupError", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.util._CleanupError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.util", "mro": ["sqlalchemy.orm.util._CleanupError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._CleanupError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.util._CleanupError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ColumnExpressionArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument", "kind": "Gdef"}, "_ColumnsClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable._ColumnsClauseElement", "kind": "Gdef"}, "_DeStringifyAnnotation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.util._DeStringifyAnnotation", "name": "_DeStringifyAnnotation", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.orm.util._DeStringifyAnnotation", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.orm.util", "mro": ["sqlalchemy.orm.util._DeStringifyAnnotation", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0, 5, 5], "arg_names": ["self", "cls", "annotation", "originating_module", "str_cleanup_fn", "include_generic"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.orm.util._DeStringifyAnnotation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5], "arg_names": ["self", "cls", "annotation", "originating_module", "str_cleanup_fn", "include_generic"], "arg_types": ["sqlalchemy.orm.util._DeStringifyAnnotation", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, "builtins.str", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _DeStringifyAnnotation", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._DeStringifyAnnotation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.util._DeStringifyAnnotation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DeStringifyUnionElements": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.util._DeStringifyUnionElements", "name": "_DeStringifyUnionElements", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.orm.util._DeStringifyUnionElements", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.orm.util", "mro": ["sqlalchemy.orm.util._DeStringifyUnionElements", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "cls", "annotation", "originating_module", "str_cleanup_fn"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.orm.util._DeStringifyUnionElements.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "cls", "annotation", "originating_module", "str_cleanup_fn"], "arg_types": ["sqlalchemy.orm.util._DeStringifyUnionElements", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "sqlalchemy.util.typing.ArgsTypeProcotol", "builtins.str", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _DeStringifyUnionElements", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._DeStringifyUnionElements.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.util._DeStringifyUnionElements", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_EntityType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._EntityType", "kind": "Gdef"}, "_EquivalentColumnMap": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._EquivalentColumnMap", "kind": "Gdef"}, "_EvalNameOnly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.util._EvalNameOnly", "name": "_EvalNameOnly", "type_vars": []}, "deletable_attributes": [], "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.orm.util._EvalNameOnly", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.orm.util", "mro": ["sqlalchemy.orm.util._EvalNameOnly", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "module_name"], "dataclass_transform_spec": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.orm.util._EvalNameOnly.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "module_name"], "arg_types": ["sqlalchemy.orm.util._EvalNameOnly", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _EvalNameOnly", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._EvalNameOnly.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.util._EvalNameOnly", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FromClauseArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._FromClauseArgument", "kind": "Gdef"}, "_IdentityKeyType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._IdentityKeyType", "kind": "Gdef"}, "_InternalEntityType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._InternalEntityType", "kind": "Gdef"}, "_MappedAnnotationBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._MappedAnnotationBase", "kind": "Gdef"}, "_MapperEntity": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context._MapperEntity", "kind": "Gdef"}, "_O": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._O", "kind": "Gdef"}, "_ORMCOLEXPR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._ORMCOLEXPR", "kind": "Gdef"}, "_ORMJoin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.selectable.Join"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.util._ORMJoin", "name": "_ORM<PERSON><PERSON>n", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.util._ORMJoin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.util", "mro": ["sqlalchemy.orm.util._ORMJoin", "sqlalchemy.sql.selectable.Join", "sqlalchemy.sql.roles.DMLTableRole", "sqlalchemy.sql.selectable.FromClause", "sqlalchemy.sql.roles.AnonymizedFromClauseRole", "sqlalchemy.sql.roles.StrictFromClauseRole", "sqlalchemy.sql.roles.FromClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.selectable.Selectable", "sqlalchemy.sql.selectable.ReturnsRows", "sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "left", "right", "onclause", "isouter", "full", "_left_memo", "_right_memo", "_extra_criteria"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util._ORMJoin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "left", "right", "onclause", "isouter", "full", "_left_memo", "_right_memo", "_extra_criteria"], "arg_types": ["sqlalchemy.orm.util._ORMJoin", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._FromClauseArgument"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._FromClauseArgument"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}, "sqlalchemy.sql.roles.OnClauseRole", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ORMJoin", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util._ORMJoin.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_left_memo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util._ORMJoin._left_memo", "name": "_left_memo", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}}}, "_right_memo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util._ORMJoin._right_memo", "name": "_right_memo", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}}}, "_splice_into_center": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util._ORMJoin._splice_into_center", "name": "_splice_into_center", "type": null}}, "_target_adapter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util._ORMJoin._target_adapter", "name": "_target_adapter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util._ORMJoin.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "right", "onclause", "isouter", "full"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util._ORMJoin.join", "name": "join", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "right", "onclause", "isouter", "full"], "arg_types": ["sqlalchemy.orm.util._ORMJoin", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._FromClauseArgument"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}, "sqlalchemy.sql.roles.OnClauseRole", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join of _ORMJoin", "ret_type": "sqlalchemy.orm.util._ORMJoin", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "outerjoin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "right", "onclause", "full"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util._ORMJoin.outerjoin", "name": "outerjoin", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "right", "onclause", "full"], "arg_types": ["sqlalchemy.orm.util._ORMJoin", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._FromClauseArgument"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}, "sqlalchemy.sql.roles.OnClauseRole", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outerjoin of _ORMJoin", "ret_type": "sqlalchemy.orm.util._ORMJoin", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._ORMJoin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.util._ORMJoin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_OnClauseArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._OnClauseArgument", "kind": "Gdef"}, "_PropagateAttrsType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._PropagateAttrsType", "kind": "Gdef"}, "_SA": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.annotation._SA", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_TraceAdaptRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.util._TraceAdaptRole", "name": "_TraceAdaptRole", "type_vars": []}, "deletable_attributes": [], "flags": ["is_enum"], "fullname": "sqlalchemy.orm.util._TraceAdaptRole", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.orm.util", "mro": ["sqlalchemy.orm.util._TraceAdaptRole", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "ADAPT_FROM_STATEMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util._TraceAdaptRole.ADAPT_FROM_STATEMENT", "name": "ADAPT_FROM_STATEMENT", "type": "enum.auto"}}, "ALIASED_INSP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util._TraceAdaptRole.ALIASED_INSP", "name": "ALIASED_INSP", "type": "enum.auto"}}, "COMPOUND_EAGER_STATEMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util._TraceAdaptRole.COMPOUND_EAGER_STATEMENT", "name": "COMPOUND_EAGER_STATEMENT", "type": "enum.auto"}}, "DEPRECATED_JOIN_ADAPT_RIGHT_SIDE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util._TraceAdaptRole.DEPRECATED_JOIN_ADAPT_RIGHT_SIDE", "name": "DEPRECATED_JOIN_ADAPT_RIGHT_SIDE", "type": "enum.auto"}}, "JOINEDLOAD_MEMOIZED_ADAPTER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util._TraceAdaptRole.JOINEDLOAD_MEMOIZED_ADAPTER", "name": "JOINEDLOAD_MEMOIZED_ADAPTER", "type": "enum.auto"}}, "********************************": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util._TraceAdaptRole.********************************", "name": "********************************", "type": "enum.auto"}}, "JOINEDLOAD_USER_DEFINED_ALIAS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util._TraceAdaptRole.JOINEDLOAD_USER_DEFINED_ALIAS", "name": "JOINEDLOAD_USER_DEFINED_ALIAS", "type": "enum.auto"}}, "LEGACY_SELECT_FROM_ALIAS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util._TraceAdaptRole.LEGACY_SELECT_FROM_ALIAS", "name": "LEGACY_SELECT_FROM_ALIAS", "type": "enum.auto"}}, "MAPPER_POLYMORPHIC_ADAPTER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util._TraceAdaptRole.MAPPER_POLYMORPHIC_ADAPTER", "name": "MAPPER_POLYMORPHIC_ADAPTER", "type": "enum.auto"}}, "WITH_POLYMORPHIC_ADAPTER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util._TraceAdaptRole.WITH_POLYMORPHIC_ADAPTER", "name": "WITH_POLYMORPHIC_ADAPTER", "type": "enum.auto"}}, "WITH_POLYMORPHIC_ADAPTER_RIGHT_JOIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util._TraceAdaptRole.WITH_POLYMORPHIC_ADAPTER_RIGHT_JOIN", "name": "WITH_POLYMORPHIC_ADAPTER_RIGHT_JOIN", "type": "enum.auto"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._TraceAdaptRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.util._TraceAdaptRole", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WrapUserEntity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.util._WrapUserEntity", "name": "_WrapUserEntity", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.util._WrapUserEntity", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.util", "mro": ["sqlalchemy.orm.util._WrapUserEntity", "builtins.object"], "names": {".class": "SymbolTable", "__getattribute__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.util._WrapUserEntity.__getattribute__", "name": "__getattribute__", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.util._WrapUserEntity.__getattribute__", "name": "__getattribute__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlalchemy.orm.util._WrapUserEntity", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattribute__ of _WrapUserEntity", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "subject"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util._WrapUserEntity.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.util._WrapUserEntity.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "subject": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.util._WrapUserEntity.subject", "name": "subject", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._WrapUserEntity.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.util._WrapUserEntity", "values": [], "variance": 0}, "slots": ["subject"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.util.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.util.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.util.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.util.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.util.__package__", "name": "__package__", "type": "builtins.str"}}, "_class_to_mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._class_to_mapper", "kind": "Gdef"}, "_cleanup_mapped_str_annotation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["annotation", "originating_module"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util._cleanup_mapped_str_annotation", "name": "_cleanup_mapped_str_annotation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["annotation", "originating_module"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cleanup_mapped_str_annotation", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_de_stringify_annotation": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.typing.de_stringify_annotation", "kind": "Gdef"}, "_de_stringify_partial": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util._de_stringify_partial", "name": "_de_stringify_partial", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "type_ref": "functools.partial"}], "type_ref": "functools.partial"}}}, "_de_stringify_union_elements": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.typing.de_stringify_union_elements", "kind": "Gdef"}, "_entity_corresponds_to": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["given", "entity"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util._entity_corresponds_to", "name": "_entity_corresponds_to", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["given", "entity"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_entity_corresponds_to", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_entity_corresponds_to_use_path_impl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["given", "entity"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util._entity_corresponds_to_use_path_impl", "name": "_entity_corresponds_to_use_path_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["given", "entity"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_entity_corresponds_to_use_path_impl", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_entity_isa": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["given", "mapper"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util._entity_isa", "name": "_entity_isa", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["given", "mapper"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_entity_isa", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_eval_name_only": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.typing.eval_name_only", "kind": "Gdef"}, "_extract_mapped_subtype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["raw_annotation", "cls", "originating_module", "key", "attr_cls", "required", "is_dataclass_field", "expect_mapped", "raiseerr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util._extract_mapped_subtype", "name": "_extract_mapped_subtype", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["raw_annotation", "cls", "originating_module", "key", "attr_cls", "required", "is_dataclass_field", "expect_mapped", "raiseerr"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}, "builtins.str", "typing.ForwardRef", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.util.typing.GenericProtocol"}, {".class": "NoneType"}]}, "builtins.type", "builtins.str", "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_mapped_subtype", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.type", "builtins.str"]}, {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_getitem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["iterable_query", "item"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util._getitem", "name": "_getitem", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["iterable_query", "item"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.query.Query"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_getitem", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_inspect_generic_alias": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["class_"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.util._inspect_generic_alias", "name": "_inspect_generic_alias", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["class_"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_inspect_generic_alias", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.util._inspect_generic_alias", "name": "_inspect_generic_alias", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["class_"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_inspect_generic_alias", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "_inspect_mc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["class_"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.util._inspect_mc", "name": "_inspect_mc", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["class_"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_inspect_mc", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.util._inspect_mc", "name": "_inspect_mc", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["class_"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_inspect_mc", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "_is_mapped_annotation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["raw_annotation", "cls", "originating_cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util._is_mapped_annotation", "name": "_is_mapped_annotation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["raw_annotation", "cls", "originating_cls"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_mapped_annotation", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_never_set": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._never_set", "kind": "Gdef"}, "_none_set": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._none_set", "kind": "Gdef"}, "_orm_annotate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["element", "exclude"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util._orm_annotate", "name": "_orm_annotate", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["element", "exclude"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_orm_annotate", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}]}}}, "_orm_deannotate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["element"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util._orm_deannotate", "name": "_orm_deannotate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["element"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_orm_deannotate", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}]}}}, "_orm_full_deannotate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["element"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util._orm_full_deannotate", "name": "_orm_full_deannotate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["element"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_orm_full_deannotate", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.annotation._SA", "id": -1, "name": "_SA", "namespace": "", "upper_bound": "sqlalchemy.sql.annotation.SupportsAnnotations", "values": [], "variance": 0}]}}}, "_validator_events": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["desc", "key", "validator", "include_removes", "include_backrefs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util._validator_events", "name": "_validator_events", "type": null}}, "all_cascades": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.all_cascades", "name": "all_cascades", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.frozenset"}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "anon_map": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._py_util.cache_anon_map", "kind": "Gdef"}, "attribute_str": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.attribute_str", "kind": "Gdef"}, "attributes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "class_mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.class_mapper", "kind": "Gdef"}, "coercions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.coercions", "kind": "Gdef"}, "de_stringify_annotation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.de_stringify_annotation", "name": "de_stringify_annotation", "type": "sqlalchemy.orm.util._DeStringifyAnnotation"}}, "de_stringify_union_elements": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.de_stringify_union_elements", "name": "de_stringify_union_elements", "type": "sqlalchemy.orm.util._DeStringifyUnionElements"}}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef"}, "eval_name_only": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.util.eval_name_only", "name": "eval_name_only", "type": "sqlalchemy.orm.util._EvalNameOnly"}}, "event": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.exc", "kind": "Gdef"}, "expression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.expression", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "has_identity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["object_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.has_identity", "name": "has_identity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["object_"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_identity", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "identity_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 5, 5, 5], "arg_names": ["class_", "ident", "instance", "row", "identity_token"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.identity_key", "name": "identity_key", "type": {".class": "CallableType", "arg_kinds": [1, 1, 5, 5, 5], "arg_names": ["class_", "ident", "instance", "row", "identity_token"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}]}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.engine.row.Row"}, "sqlalchemy.engine.row.RowMapping", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity_key", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._IdentityKeyType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.util._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "insp_is_aliased_class": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.insp_is_aliased_class", "kind": "Gdef"}, "insp_is_mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.insp_is_mapper", "kind": "Gdef"}, "inspection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.inspection", "kind": "Gdef"}, "instance_str": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.instance_str", "kind": "Gdef"}, "is_origin_of_cls": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.typing.is_origin_of_cls", "kind": "Gdef"}, "is_selectable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing.is_selectable", "kind": "Gdef"}, "lambdas": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.lambdas", "kind": "Gdef"}, "object_mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.object_mapper", "kind": "Gdef"}, "object_state": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.object_state", "kind": "Gdef"}, "opt_manager_of_class": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.opt_manager_of_class", "kind": "Gdef"}, "polymorphic_union": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["table_map", "typecolname", "aliasname", "cast_nulls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.polymorphic_union", "name": "polymorphic_union", "type": null}}, "prop_is_relationship": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.prop_is_relationship", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "result_tuple": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.result_tuple", "kind": "Gdef"}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef"}, "sa_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "sql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql", "kind": "Gdef"}, "sql_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.util", "kind": "Gdef"}, "state_attribute_str": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.state_attribute_str", "kind": "Gdef"}, "state_class_str": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.state_class_str", "kind": "Gdef"}, "state_str": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.state_str", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "typing_get_origin": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.typing.typing_get_origin", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "visitors": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors", "kind": "Gdef"}, "was_deleted": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["object_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.was_deleted", "name": "was_deleted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["object_"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "was_deleted", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}, "with_parent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["instance", "prop", "from_entity"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.util.with_parent", "name": "with_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["instance", "prop", "from_entity"], "arg_types": ["builtins.object", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedClass"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_parent", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/util.py"}