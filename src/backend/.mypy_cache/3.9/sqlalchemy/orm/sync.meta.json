{"data_mtime": 1754228260, "dep_lines": [17, 18, 19, 17, 15, 1, 1, 1], "dep_prios": [10, 10, 5, 20, 5, 5, 30, 30], "dependencies": ["sqlalchemy.orm.exc", "sqlalchemy.orm.util", "sqlalchemy.orm.base", "sqlalchemy.orm", "__future__", "builtins", "abc", "typing"], "hash": "e4db7f3aa3f821f840b47c0545aaf8770f988cb10d44bbe9e1dde30c2e30a67c", "id": "sqlalchemy.orm.sync", "ignore_all": true, "interface_hash": "5eb1e830b75d0bd3c53276ba8107d0d2a57e70731ad000f111836412c5dbc657", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/sync.py", "plugin_data": null, "size": 5749, "suppressed": [], "version_id": "1.7.1"}