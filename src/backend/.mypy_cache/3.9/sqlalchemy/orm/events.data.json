{".class": "MypyFile", "_fullname": "sqlalchemy.orm.events", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AttributeEvents": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}], "type_ref": "sqlalchemy.event.base.Events"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.events.AttributeEvents", "name": "AttributeEvents", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.events.AttributeEvents", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.events", "mro": ["sqlalchemy.orm.events.AttributeEvents", "sqlalchemy.event.base.Events", "sqlalchemy.event.base._HasEventsDispatch", "builtins.object"], "names": {".class": "SymbolTable", "_accept_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events.AttributeEvents._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.AttributeEvents"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}}]}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of AttributeEvents", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.AttributeEvents._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.AttributeEvents"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}}]}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of AttributeEvents", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_dispatch_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.events.AttributeEvents._dispatch_target", "name": "_dispatch_target", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["class_", "key", "parententity", "comparator", "impl", "of_type", "extra_criteria"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._ExternalEntityType"}, "builtins.str", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.attributes._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.attributes.QueryableAttribute", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, {".class": "UnionType", "items": ["sqlalchemy.orm.attributes.AttributeImpl", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "NoneType"}]}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "type_ref": "builtins.tuple"}], "bound_args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.attributes._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.attributes.QueryableAttribute", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.attributes._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.attributes.QueryableAttribute", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.attributes._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.attributes.QueryableAttribute", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["cls", "event_key", "active_history", "raw", "retval", "propagate", "include_key"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events.AttributeEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["cls", "event_key", "active_history", "raw", "retval", "propagate", "include_key"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.AttributeEvents"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of AttributeEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.AttributeEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["cls", "event_key", "active_history", "raw", "retval", "propagate", "include_key"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.AttributeEvents"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of AttributeEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_set_dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "dispatch_cls"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "sqlalchemy.orm.events.AttributeEvents._set_dispatch", "name": "_set_dispatch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "dispatch_cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.event.base._Dispatch"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_dispatch of AttributeEvents", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.event.base._Dispatch"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.AttributeEvents._set_dispatch", "name": "_set_dispatch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "dispatch_cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.event.base._HasEventsDispatch"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.event.base._Dispatch"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_dispatch of AttributeEvents", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.event.base._Dispatch"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_target_class_doc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.events.AttributeEvents._target_class_doc", "name": "_target_class_doc", "type": "builtins.str"}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "target", "value", "initiator", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.AttributeEvents.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "target", "value", "initiator", "key"], "arg_types": ["sqlalchemy.orm.events.AttributeEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, "sqlalchemy.orm.attributes.AttributeEventToken", "sqlalchemy.orm.base.EventConstants"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append of AttributeEvents", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "append_wo_mutation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "target", "value", "initiator", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.AttributeEvents.append_wo_mutation", "name": "append_wo_mutation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "target", "value", "initiator", "key"], "arg_types": ["sqlalchemy.orm.events.AttributeEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, "sqlalchemy.orm.attributes.AttributeEventToken", "sqlalchemy.orm.base.EventConstants"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append_wo_mutation of AttributeEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "bulk_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "target", "values", "initiator", "keys"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.AttributeEvents.bulk_replace", "name": "bulk_replace", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "target", "values", "initiator", "keys"], "arg_types": ["sqlalchemy.orm.events.AttributeEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "typing.Iterable"}, "sqlalchemy.orm.attributes.AttributeEventToken", {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.orm.base.EventConstants"], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bulk_replace of AttributeEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "dispose_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "collection", "collection_adapter"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.AttributeEvents.dispose_collection", "name": "dispose_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "collection", "collection_adapter"], "arg_types": ["sqlalchemy.orm.events.AttributeEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Collection"}, "sqlalchemy.orm.collections.CollectionAdapter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dispose_collection of AttributeEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "init_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "collection", "collection_adapter"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.AttributeEvents.init_collection", "name": "init_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "collection", "collection_adapter"], "arg_types": ["sqlalchemy.orm.events.AttributeEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Collection"}}, "sqlalchemy.orm.collections.CollectionAdapter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_collection of AttributeEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "init_scalar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "value", "dict_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.AttributeEvents.init_scalar", "name": "init_scalar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "value", "dict_"], "arg_types": ["sqlalchemy.orm.events.AttributeEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_scalar of AttributeEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "modified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "initiator"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.AttributeEvents.modified", "name": "modified", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "initiator"], "arg_types": ["sqlalchemy.orm.events.AttributeEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "sqlalchemy.orm.attributes.AttributeEventToken"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modified of AttributeEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "target", "value", "initiator", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.AttributeEvents.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "target", "value", "initiator", "key"], "arg_types": ["sqlalchemy.orm.events.AttributeEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, "sqlalchemy.orm.attributes.AttributeEventToken", "sqlalchemy.orm.base.EventConstants"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove of AttributeEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "target", "value", "oldvalue", "initiator"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.AttributeEvents.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "target", "value", "oldvalue", "initiator"], "arg_types": ["sqlalchemy.orm.events.AttributeEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, "sqlalchemy.orm.attributes.AttributeEventToken"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of AttributeEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -2, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events.AttributeEvents.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.events.AttributeEvents", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BulkDelete": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.query.BulkDelete", "kind": "Gdef"}, "BulkUpdate": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.query.BulkUpdate", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClassManager": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.instrumentation.ClassManager", "kind": "Gdef"}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef"}, "CollectionAdapter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.collections.CollectionAdapter", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "DeclarativeAttributeIntercept": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.DeclarativeAttributeIntercept", "kind": "Gdef"}, "DeclarativeMeta": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.DeclarativeMeta", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Event": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes.Event", "kind": "Gdef"}, "EventConstants": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.EventConstants", "kind": "Gdef"}, "EventTarget": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry.EventTarget", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "InstanceEvents": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "type_ref": "sqlalchemy.event.base.Events"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.events.InstanceEvents", "name": "InstanceEvents", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.events.InstanceEvents", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.events", "mro": ["sqlalchemy.orm.events.InstanceEvents", "sqlalchemy.event.base.Events", "sqlalchemy.event.base._HasEventsDispatch", "builtins.object"], "names": {".class": "SymbolTable", "_accept_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events.InstanceEvents._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.InstanceEvents"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}}]}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of InstanceEvents", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.InstanceEvents._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.InstanceEvents"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}}]}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of InstanceEvents", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events.InstanceEvents._clear", "name": "_clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.InstanceEvents"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear of InstanceEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.InstanceEvents._clear", "name": "_clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.InstanceEvents"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear of InstanceEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_dispatch_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.events.InstanceEvents._dispatch_target", "name": "_dispatch_target", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["class_"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "def_extras": {"first_arg": "self"}, "fallback": "abc.ABCMeta", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.instrumentation.ClassManager", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["cls", "event_key", "raw", "propagate", "restore_load_context", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events.InstanceEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["cls", "event_key", "raw", "propagate", "restore_load_context", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.InstanceEvents"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of InstanceEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.InstanceEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["cls", "event_key", "raw", "propagate", "restore_load_context", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.InstanceEvents"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of InstanceEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_new_classmanager_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "class_", "classmanager"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events.InstanceEvents._new_classmanager_instance", "name": "_new_classmanager_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "class_", "classmanager"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.InstanceEvents"}, {".class": "UnionType", "items": ["sqlalchemy.orm.decl_api.DeclarativeAttributeIntercept", "sqlalchemy.orm.decl_api.DeclarativeMeta", "builtins.type"]}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_new_classmanager_instance of InstanceEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.InstanceEvents._new_classmanager_instance", "name": "_new_classmanager_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "class_", "classmanager"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.InstanceEvents"}, {".class": "UnionType", "items": ["sqlalchemy.orm.decl_api.DeclarativeAttributeIntercept", "sqlalchemy.orm.decl_api.DeclarativeMeta", "builtins.type"]}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_new_classmanager_instance of InstanceEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "_sa_event_merge_wo_load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.InstanceEvents._sa_event_merge_wo_load", "name": "_sa_event_merge_wo_load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "context"], "arg_types": ["sqlalchemy.orm.events.InstanceEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "sqlalchemy.orm.context.QueryContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sa_event_merge_wo_load of InstanceEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_target_class_doc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.events.InstanceEvents._target_class_doc", "name": "_target_class_doc", "type": "builtins.str"}}, "expire": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "attrs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.InstanceEvents.expire", "name": "expire", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "attrs"], "arg_types": ["sqlalchemy.orm.events.InstanceEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expire of InstanceEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "first_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.InstanceEvents.first_init", "name": "first_init", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "cls"], "arg_types": ["sqlalchemy.orm.events.InstanceEvents", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "first_init of InstanceEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.InstanceEvents.init", "name": "init", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "arg_types": ["sqlalchemy.orm.events.InstanceEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init of InstanceEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "init_failure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.InstanceEvents.init_failure", "name": "init_failure", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "args", "kwargs"], "arg_types": ["sqlalchemy.orm.events.InstanceEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_failure of InstanceEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.InstanceEvents.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "context"], "arg_types": ["sqlalchemy.orm.events.InstanceEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "sqlalchemy.orm.context.QueryContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of InstanceEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "pickle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "state_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.InstanceEvents.pickle", "name": "pickle", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "state_dict"], "arg_types": ["sqlalchemy.orm.events.InstanceEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._InstanceDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pickle of InstanceEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "refresh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "context", "attrs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.InstanceEvents.refresh", "name": "refresh", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "context", "attrs"], "arg_types": ["sqlalchemy.orm.events.InstanceEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "sqlalchemy.orm.context.QueryContext", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refresh of InstanceEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "refresh_flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "flush_context", "attrs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.InstanceEvents.refresh_flush", "name": "refresh_flush", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target", "flush_context", "attrs"], "arg_types": ["sqlalchemy.orm.events.InstanceEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "sqlalchemy.orm.unitofwork.UOWTransaction", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refresh_flush of InstanceEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "unpickle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "state_dict"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.InstanceEvents.unpickle", "name": "unpickle", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "state_dict"], "arg_types": ["sqlalchemy.orm.events.InstanceEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._InstanceDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unpickle of InstanceEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events.InstanceEvents.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.events.InstanceEvents", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InstanceState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state.InstanceState", "kind": "Gdef"}, "InstrumentationEvents": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["sqlalchemy.orm.instrumentation.InstrumentationFactory"], "type_ref": "sqlalchemy.event.base.Events"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.events.InstrumentationEvents", "name": "InstrumentationEvents", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.events.InstrumentationEvents", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.events", "mro": ["sqlalchemy.orm.events.InstrumentationEvents", "sqlalchemy.event.base.Events", "sqlalchemy.event.base._HasEventsDispatch", "builtins.object"], "names": {".class": "SymbolTable", "_accept_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events.InstrumentationEvents._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.InstrumentationEvents"}, {".class": "UnionType", "items": ["sqlalchemy.orm.instrumentation.InstrumentationFactory", {".class": "TypeType", "item": "sqlalchemy.orm.instrumentation.InstrumentationFactory"}]}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of InstrumentationEvents", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.instrumentation.InstrumentationFactory", {".class": "TypeType", "item": "sqlalchemy.orm.instrumentation.InstrumentationFactory"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.InstrumentationEvents._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.InstrumentationEvents"}, {".class": "UnionType", "items": ["sqlalchemy.orm.instrumentation.InstrumentationFactory", {".class": "TypeType", "item": "sqlalchemy.orm.instrumentation.InstrumentationFactory"}]}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of InstrumentationEvents", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.instrumentation.InstrumentationFactory", {".class": "TypeType", "item": "sqlalchemy.orm.instrumentation.InstrumentationFactory"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events.InstrumentationEvents._clear", "name": "_clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.InstrumentationEvents"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear of InstrumentationEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.InstrumentationEvents._clear", "name": "_clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.InstrumentationEvents"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear of InstrumentationEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_dispatch_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.events.InstrumentationEvents._dispatch_target", "name": "_dispatch_target", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["sqlalchemy.orm.instrumentation.InstrumentationFactory"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.orm.instrumentation.InstrumentationFactory", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["cls", "event_key", "propagate", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events.InstrumentationEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["cls", "event_key", "propagate", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.InstrumentationEvents"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of InstrumentationEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.InstrumentationEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["cls", "event_key", "propagate", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.InstrumentationEvents"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of InstrumentationEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}}, "_target_class_doc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.events.InstrumentationEvents._target_class_doc", "name": "_target_class_doc", "type": "builtins.str"}}, "attribute_instrument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cls", "key", "inst"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.InstrumentationEvents.attribute_instrument", "name": "attribute_instrument", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cls", "key", "inst"], "arg_types": ["sqlalchemy.orm.events.InstrumentationEvents", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._KT", "id": -2, "name": "_KT", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attribute_instrument of InstrumentationEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._KT", "id": -2, "name": "_KT", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "class_instrument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.InstrumentationEvents.class_instrument", "name": "class_instrument", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "arg_types": ["sqlalchemy.orm.events.InstrumentationEvents", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "class_instrument of InstrumentationEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "class_uninstrument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.InstrumentationEvents.class_uninstrument", "name": "class_uninstrument", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "arg_types": ["sqlalchemy.orm.events.InstrumentationEvents", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "class_uninstrument of InstrumentationEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events.InstrumentationEvents.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.events.InstrumentationEvents", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InstrumentationFactory": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.instrumentation.InstrumentationFactory", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.Mapper", "kind": "Gdef"}, "MapperEvents": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "type_ref": "sqlalchemy.event.base.Events"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.events.MapperEvents", "name": "MapperEvents", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.events.MapperEvents", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.events", "mro": ["sqlalchemy.orm.events.MapperEvents", "sqlalchemy.event.base.Events", "sqlalchemy.event.base._HasEventsDispatch", "builtins.object"], "names": {".class": "SymbolTable", "_accept_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events.MapperEvents._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.MapperEvents"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}}]}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of MapperEvents", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.MapperEvents._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.MapperEvents"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}}]}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of MapperEvents", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events.MapperEvents._clear", "name": "_clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.MapperEvents"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.MapperEvents._clear", "name": "_clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.MapperEvents"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_dispatch_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.events.MapperEvents._dispatch_target", "name": "_dispatch_target", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["class_", "local_table", "properties", "primary_key", "non_primary", "inherits", "inherit_condition", "inherit_foreign_keys", "always_refresh", "version_id_col", "version_id_generator", "polymorphic_on", "_polymorphic_map", "polymorphic_identity", "concrete", "with_polymorphic", "polymorphic_abstract", "polymorphic_load", "allow_partial_pks", "batch", "column_prefix", "include_properties", "exclude_properties", "passive_updates", "passive_deletes", "confirm_deleted_rows", "eager_defaults", "legacy_is_orphan", "_compiled_cache_size"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.mapper.Mapper", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._ORMColumnExprArgument"}], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement", {".class": "Instance", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, {".class": "Instance", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "sqlalchemy.sql.lambdas.LambdaElement", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._ORMColumnExprArgument"}], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}, "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "*"}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "*"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}]}], "type_ref": "typing.Sequence"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}]}], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "selectin"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "inline"}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}]}, "builtins.bool", "builtins.int"], "bound_args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.mapper.Mapper", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.mapper.Mapper", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": 1, "name": "_O", "namespace": "sqlalchemy.orm.mapper.Mapper", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["cls", "event_key", "raw", "retval", "propagate", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events.MapperEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["cls", "event_key", "raw", "retval", "propagate", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.MapperEvents"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.MapperEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["cls", "event_key", "raw", "retval", "propagate", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.MapperEvents"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}}}}, "_new_mapper_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "class_", "mapper"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events.MapperEvents._new_mapper_instance", "name": "_new_mapper_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "class_", "mapper"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.MapperEvents"}, {".class": "UnionType", "items": ["sqlalchemy.orm.decl_api.DeclarativeAttributeIntercept", "sqlalchemy.orm.decl_api.DeclarativeMeta", "builtins.type"]}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_new_mapper_instance of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.MapperEvents._new_mapper_instance", "name": "_new_mapper_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "class_", "mapper"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.MapperEvents"}, {".class": "UnionType", "items": ["sqlalchemy.orm.decl_api.DeclarativeAttributeIntercept", "sqlalchemy.orm.decl_api.DeclarativeMeta", "builtins.type"]}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_new_mapper_instance of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "_target_class_doc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.events.MapperEvents._target_class_doc", "name": "_target_class_doc", "type": "builtins.str"}}, "after_configured": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.MapperEvents.after_configured", "name": "after_configured", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.events.MapperEvents"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_configured of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "after_delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mapper", "connection", "target"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.MapperEvents.after_delete", "name": "after_delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mapper", "connection", "target"], "arg_types": ["sqlalchemy.orm.events.MapperEvents", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "sqlalchemy.engine.base.Connection", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_delete of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "after_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mapper", "connection", "target"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.MapperEvents.after_insert", "name": "after_insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mapper", "connection", "target"], "arg_types": ["sqlalchemy.orm.events.MapperEvents", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "sqlalchemy.engine.base.Connection", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_insert of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "after_mapper_constructed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mapper", "class_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.MapperEvents.after_mapper_constructed", "name": "after_mapper_constructed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mapper", "class_"], "arg_types": ["sqlalchemy.orm.events.MapperEvents", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_mapper_constructed of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "after_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mapper", "connection", "target"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.MapperEvents.after_update", "name": "after_update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mapper", "connection", "target"], "arg_types": ["sqlalchemy.orm.events.MapperEvents", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "sqlalchemy.engine.base.Connection", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_update of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "before_configured": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.MapperEvents.before_configured", "name": "before_configured", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.events.MapperEvents"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_configured of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "before_delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mapper", "connection", "target"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.MapperEvents.before_delete", "name": "before_delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mapper", "connection", "target"], "arg_types": ["sqlalchemy.orm.events.MapperEvents", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "sqlalchemy.engine.base.Connection", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_delete of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "before_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mapper", "connection", "target"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.MapperEvents.before_insert", "name": "before_insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mapper", "connection", "target"], "arg_types": ["sqlalchemy.orm.events.MapperEvents", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "sqlalchemy.engine.base.Connection", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_insert of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "before_mapper_configured": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mapper", "class_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.MapperEvents.before_mapper_configured", "name": "before_mapper_configured", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mapper", "class_"], "arg_types": ["sqlalchemy.orm.events.MapperEvents", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_mapper_configured of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "before_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mapper", "connection", "target"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.MapperEvents.before_update", "name": "before_update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "mapper", "connection", "target"], "arg_types": ["sqlalchemy.orm.events.MapperEvents", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "sqlalchemy.engine.base.Connection", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_update of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "instrument_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mapper", "class_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.MapperEvents.instrument_class", "name": "instrument_class", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mapper", "class_"], "arg_types": ["sqlalchemy.orm.events.MapperEvents", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instrument_class of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "mapper_configured": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mapper", "class_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.MapperEvents.mapper_configured", "name": "mapper_configured", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mapper", "class_"], "arg_types": ["sqlalchemy.orm.events.MapperEvents", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mapper_configured of MapperEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events.MapperEvents.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.events.MapperEvents", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NO_KEY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.NO_KEY", "kind": "Gdef"}, "ORMExecuteState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.ORMExecuteState", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Query": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.query.Query", "kind": "Gdef"}, "QueryContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.QueryContext", "kind": "Gdef"}, "QueryEvents": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.query.Query"}], "type_ref": "sqlalchemy.event.base.Events"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.events.QueryEvents", "name": "QueryEvents", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.events.QueryEvents", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.events", "mro": ["sqlalchemy.orm.events.QueryEvents", "sqlalchemy.event.base.Events", "sqlalchemy.event.base._HasEventsDispatch", "builtins.object"], "names": {".class": "SymbolTable", "_dispatch_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.events.QueryEvents._dispatch_target", "name": "_dispatch_target", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["entities", "session"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.roles.TypedColumnsClauseRole"}, "sqlalchemy.sql.roles.ColumnsClauseRole", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "*"}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "Instance", "args": ["sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"], "type_ref": "sqlalchemy.inspection.Inspectable"}, "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnsClauseArgument"}], "type_ref": "typing.Sequence"}]}, {".class": "UnionType", "items": ["sqlalchemy.orm.session.Session", {".class": "NoneType"}]}], "bound_args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.query._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.query.Query", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.query.Query"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.query._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.query.Query", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.query.Query"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.query._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.query.Query", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "_listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["cls", "event_key", "retval", "bake_ok", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events.QueryEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["cls", "event_key", "retval", "bake_ok", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.QueryEvents"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of QueryEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.QueryEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["cls", "event_key", "retval", "bake_ok", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.QueryEvents"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of QueryEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}}}}, "_target_class_doc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.events.QueryEvents._target_class_doc", "name": "_target_class_doc", "type": "builtins.str"}}, "before_compile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.QueryEvents.before_compile", "name": "before_compile", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query"], "arg_types": ["sqlalchemy.orm.events.QueryEvents", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.query.Query"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_compile of QueryEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "before_compile_delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "query", "delete_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.QueryEvents.before_compile_delete", "name": "before_compile_delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "query", "delete_context"], "arg_types": ["sqlalchemy.orm.events.QueryEvents", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.query.Query"}, "sqlalchemy.orm.query.BulkDelete"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_compile_delete of QueryEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "before_compile_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "query", "update_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.QueryEvents.before_compile_update", "name": "before_compile_update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "query", "update_context"], "arg_types": ["sqlalchemy.orm.events.QueryEvents", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.query.Query"}, "sqlalchemy.orm.query.BulkUpdate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_compile_update of QueryEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events.QueryEvents.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.events.QueryEvents", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QueryableAttribute": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes.QueryableAttribute", "kind": "Gdef"}, "ReferenceType": {".class": "SymbolTableNode", "cross_ref": "_weakref.ReferenceType", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.Session", "kind": "Gdef"}, "SessionEvents": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["sqlalchemy.orm.session.Session"], "type_ref": "sqlalchemy.event.base.Events"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.events.SessionEvents", "name": "SessionEvents", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.events.SessionEvents", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.events", "mro": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.event.base.Events", "sqlalchemy.event.base._HasEventsDispatch", "builtins.object"], "names": {".class": "SymbolTable", "_accept_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events.SessionEvents._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.SessionEvents"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of SessionEvents", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.session.Session", "builtins.type"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.SessionEvents._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.SessionEvents"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accept_with of SessionEvents", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.session.Session", "builtins.type"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_dispatch_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.events.SessionEvents._dispatch_target", "name": "_dispatch_target", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["bind", "autoflush", "future", "expire_on_commit", "autobegin", "twophase", "binds", "enable_baked_queries", "info", "query_cls", "autocommit", "join_transaction_mode", "close_resets_only"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection", {".class": "NoneType"}]}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._SessionBind<PERSON>ey"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._SessionBind"}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.query.Query"}}, {".class": "NoneType"}]}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session.JoinTransactionMode"}, {".class": "UnionType", "items": ["builtins.bool", "sqlalchemy.sql.base._NoArg"]}], "bound_args": ["sqlalchemy.orm.session.Session"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.orm.session.Session", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_lifecycle_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.SessionEvents._lifecycle_event", "name": "_lifecycle_event", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["fn"], "arg_types": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "fn"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_lifecycle_event of SessionEvents", "ret_type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "persistent_to_detached of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["cls", "event_key", "raw", "restore_load_context", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events.SessionEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["cls", "event_key", "raw", "restore_load_context", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.SessionEvents"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.SessionEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["cls", "event_key", "raw", "restore_load_context", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.events.SessionEvents"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_target_class_doc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.events.SessionEvents._target_class_doc", "name": "_target_class_doc", "type": "builtins.str"}}, "after_attach": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.events.SessionEvents.after_attach", "name": "after_attach", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_attach of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.SessionEvents.after_attach", "name": "after_attach", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_attach of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "after_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "session", "transaction", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.SessionEvents.after_begin", "name": "after_begin", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "session", "transaction", "connection"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", "sqlalchemy.orm.session.SessionTransaction", "sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_begin of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "after_bulk_delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "delete_context"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.events.SessionEvents.after_bulk_delete", "name": "after_bulk_delete", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "delete_context"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_bulk_delete of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.SessionEvents.after_bulk_delete", "name": "after_bulk_delete", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "after_bulk_delete of SessionEvents", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "after_bulk_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "update_context"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.events.SessionEvents.after_bulk_update", "name": "after_bulk_update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "update_context"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_bulk_update of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.SessionEvents.after_bulk_update", "name": "after_bulk_update", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "after_bulk_update of SessionEvents", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "after_commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.SessionEvents.after_commit", "name": "after_commit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "session"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_commit of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "after_flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "flush_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.SessionEvents.after_flush", "name": "after_flush", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "flush_context"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", "sqlalchemy.orm.unitofwork.UOWTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_flush of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "after_flush_postexec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "flush_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.SessionEvents.after_flush_postexec", "name": "after_flush_postexec", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "flush_context"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", "sqlalchemy.orm.unitofwork.UOWTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_flush_postexec of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "after_rollback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.SessionEvents.after_rollback", "name": "after_rollback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "session"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_rollback of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "after_soft_rollback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "previous_transaction"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.SessionEvents.after_soft_rollback", "name": "after_soft_rollback", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "previous_transaction"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", "sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_soft_rollback of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "after_transaction_create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "transaction"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.SessionEvents.after_transaction_create", "name": "after_transaction_create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "transaction"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", "sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_transaction_create of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "after_transaction_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "transaction"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.SessionEvents.after_transaction_end", "name": "after_transaction_end", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "transaction"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", "sqlalchemy.orm.session.SessionTransaction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_transaction_end of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "before_attach": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.events.SessionEvents.before_attach", "name": "before_attach", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_attach of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.SessionEvents.before_attach", "name": "before_attach", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_attach of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "before_commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.SessionEvents.before_commit", "name": "before_commit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "session"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_commit of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "before_flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "session", "flush_context", "instances"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.SessionEvents.before_flush", "name": "before_flush", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "session", "flush_context", "instances"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", "sqlalchemy.orm.unitofwork.UOWTransaction", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "typing.Sequence"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_flush of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "deleted_to_detached": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.events.SessionEvents.deleted_to_detached", "name": "deleted_to_detached", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deleted_to_detached of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.SessionEvents.deleted_to_detached", "name": "deleted_to_detached", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deleted_to_detached of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "deleted_to_persistent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.events.SessionEvents.deleted_to_persistent", "name": "deleted_to_persistent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deleted_to_persistent of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.SessionEvents.deleted_to_persistent", "name": "deleted_to_persistent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deleted_to_persistent of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "detached_to_persistent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.events.SessionEvents.detached_to_persistent", "name": "detached_to_persistent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detached_to_persistent of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.SessionEvents.detached_to_persistent", "name": "detached_to_persistent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detached_to_persistent of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "do_orm_execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "orm_execute_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events.SessionEvents.do_orm_execute", "name": "do_orm_execute", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "orm_execute_state"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.ORMExecuteState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_orm_execute of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "loaded_as_persistent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.events.SessionEvents.loaded_as_persistent", "name": "loaded_as_persistent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loaded_as_persistent of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.SessionEvents.loaded_as_persistent", "name": "loaded_as_persistent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loaded_as_persistent of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "pending_to_persistent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.events.SessionEvents.pending_to_persistent", "name": "pending_to_persistent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pending_to_persistent of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.SessionEvents.pending_to_persistent", "name": "pending_to_persistent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pending_to_persistent of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "pending_to_transient": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.events.SessionEvents.pending_to_transient", "name": "pending_to_transient", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pending_to_transient of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.SessionEvents.pending_to_transient", "name": "pending_to_transient", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pending_to_transient of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "persistent_to_deleted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.events.SessionEvents.persistent_to_deleted", "name": "persistent_to_deleted", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "persistent_to_deleted of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.SessionEvents.persistent_to_deleted", "name": "persistent_to_deleted", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "persistent_to_deleted of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "persistent_to_detached": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.events.SessionEvents.persistent_to_detached", "name": "persistent_to_detached", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "persistent_to_detached of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.SessionEvents.persistent_to_detached", "name": "persistent_to_detached", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "persistent_to_detached of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "persistent_to_transient": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.events.SessionEvents.persistent_to_transient", "name": "persistent_to_transient", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "persistent_to_transient of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.SessionEvents.persistent_to_transient", "name": "persistent_to_transient", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "persistent_to_transient of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "transient_to_pending": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.events.SessionEvents.transient_to_pending", "name": "transient_to_pending", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "instance"], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transient_to_pending of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events.SessionEvents.transient_to_pending", "name": "transient_to_pending", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["sqlalchemy.orm.events.SessionEvents", "sqlalchemy.orm.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transient_to_pending of SessionEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events.SessionEvents.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.events.SessionEvents", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SessionTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.SessionTransaction", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "UOWTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.unitofwork.UOWTransaction", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_Dispatch": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.base._Dispatch", "kind": "Gdef"}, "_ET": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry._ET", "kind": "Gdef"}, "_ET2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._ET2", "name": "_ET2", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}, "_EventKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry._EventKey", "kind": "Gdef"}, "_EventsHold": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._EventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.attr.RefCollection"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.events._EventsHold", "name": "_EventsHold", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._EventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.events._EventsHold", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.events", "mro": ["sqlalchemy.orm.events._EventsHold", "sqlalchemy.event.attr.RefCollection", "sqlalchemy.util.langhelpers.MemoizedSlots", "builtins.object"], "names": {".class": "SymbolTable", "HoldEvents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.events._EventsHold.HoldEvents", "name": "HoldEvents", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._ET2", "id": 1, "name": "_ET2", "namespace": "sqlalchemy.orm.events._EventsHold.HoldEvents", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.events._EventsHold.HoldEvents", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.events", "mro": ["sqlalchemy.orm.events._EventsHold.HoldEvents", "builtins.object"], "names": {".class": "SymbolTable", "_dispatch_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.events._EventsHold.HoldEvents._dispatch_target", "name": "_dispatch_target", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._ET2", "id": 1, "name": "_ET2", "namespace": "sqlalchemy.orm.events._EventsHold.HoldEvents", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}, {".class": "NoneType"}]}}}, "_listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["cls", "event_key", "raw", "propagate", "retval", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events._EventsHold.HoldEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["cls", "event_key", "raw", "propagate", "retval", "kw"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._ET2", "id": 1, "name": "_ET2", "namespace": "sqlalchemy.orm.events._EventsHold.HoldEvents", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._EventsHold.HoldEvents"}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._ET2", "id": 1, "name": "_ET2", "namespace": "sqlalchemy.orm.events._EventsHold.HoldEvents", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of HoldEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events._EventsHold.HoldEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["cls", "event_key", "raw", "propagate", "retval", "kw"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._ET2", "id": 1, "name": "_ET2", "namespace": "sqlalchemy.orm.events._EventsHold.HoldEvents", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._EventsHold.HoldEvents"}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._ET2", "id": 1, "name": "_ET2", "namespace": "sqlalchemy.orm.events._EventsHold.HoldEvents", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen of HoldEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._EventsHold.HoldEvents.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._ET2", "id": 1, "name": "_ET2", "namespace": "sqlalchemy.orm.events._EventsHold.HoldEvents", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._EventsHold.HoldEvents"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ET2"], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events._EventsHold.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._EventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._EventsHold"}, {".class": "UnionType", "items": ["sqlalchemy.orm.decl_api.DeclarativeAttributeIntercept", "sqlalchemy.orm.decl_api.DeclarativeMeta", "builtins.type"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _EventsHold", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events._EventsHold._clear", "name": "_clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._EventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._EventsHold"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear of _EventsHold", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events._EventsHold._clear", "name": "_clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._EventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._EventsHold"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear of _EventsHold", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "all_holds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.events._EventsHold.all_holds", "name": "all_holds", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "weakref.<PERSON>eyDictionary"}}}, "class_": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.events._EventsHold.class_", "name": "class_", "type": {".class": "UnionType", "items": ["sqlalchemy.orm.decl_api.DeclarativeAttributeIntercept", "sqlalchemy.orm.decl_api.DeclarativeMeta", "builtins.type"]}}}, "populate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "class_", "subject"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.events._EventsHold.populate", "name": "populate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "class_", "subject"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._EventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._EventsHold"}}, {".class": "UnionType", "items": ["sqlalchemy.orm.decl_api.DeclarativeAttributeIntercept", "sqlalchemy.orm.decl_api.DeclarativeMeta", "builtins.type"]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "populate of _EventsHold", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.events._EventsHold.populate", "name": "populate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "class_", "subject"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._EventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._EventsHold"}}, {".class": "UnionType", "items": ["sqlalchemy.orm.decl_api.DeclarativeAttributeIntercept", "sqlalchemy.orm.decl_api.DeclarativeMeta", "builtins.type"]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "populate of _EventsHold", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event_key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events._EventsHold.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event_key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._EventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._EventsHold"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._EventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.event.registry._EventKey"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove of _EventsHold", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._EventsHold.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._EventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._EventsHold"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "_HasEventsDispatch": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.base._HasEventsDispatch", "kind": "Gdef"}, "_InstanceDict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._InstanceDict", "kind": "Gdef"}, "_InstanceEventsHold": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._InstanceEventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._EventsHold"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.events._InstanceEventsHold", "name": "_InstanceEventsHold", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._InstanceEventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.events._InstanceEventsHold", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.events", "mro": ["sqlalchemy.orm.events._InstanceEventsHold", "sqlalchemy.orm.events._EventsHold", "sqlalchemy.event.attr.RefCollection", "sqlalchemy.util.langhelpers.MemoizedSlots", "builtins.object"], "names": {".class": "SymbolTable", "HoldInstanceEvents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._InstanceEventsHold.HoldInstanceEvents", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._EventsHold.HoldEvents"}, "sqlalchemy.orm.events.InstanceEvents"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.events._InstanceEventsHold.HoldInstanceEvents", "name": "HoldInstanceEvents", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._InstanceEventsHold.HoldInstanceEvents", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.events._InstanceEventsHold.HoldInstanceEvents", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.events", "mro": ["sqlalchemy.orm.events._InstanceEventsHold.HoldInstanceEvents", "sqlalchemy.orm.events._EventsHold.HoldEvents", "sqlalchemy.orm.events.InstanceEvents", "sqlalchemy.event.base.Events", "sqlalchemy.event.base._HasEventsDispatch", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._InstanceEventsHold.HoldInstanceEvents.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._InstanceEventsHold.HoldInstanceEvents", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._InstanceEventsHold.HoldInstanceEvents"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "all_holds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.events._InstanceEventsHold.all_holds", "name": "all_holds", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "weakref.<PERSON>eyDictionary"}}}, "dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.events._InstanceEventsHold.dispatch", "name": "dispatch", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}], "type_ref": "sqlalchemy.event.base.dispatcher"}}}, "resolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events._InstanceEventsHold.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._InstanceEventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._InstanceEventsHold"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of _InstanceEventsHold", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._InstanceEventsHold.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._InstanceEventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._InstanceEventsHold"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "_InstrumentationEventsHold": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.events._InstrumentationEventsHold", "name": "_InstrumentationEventsHold", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.events._InstrumentationEventsHold", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.events", "mro": ["sqlalchemy.orm.events._InstrumentationEventsHold", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events._InstrumentationEventsHold.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "arg_types": ["sqlalchemy.orm.events._InstrumentationEventsHold", "builtins.type"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _InstrumentationEventsHold", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "class_": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.events._InstrumentationEventsHold.class_", "name": "class_", "type": "builtins.type"}}, "dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.events._InstrumentationEventsHold.dispatch", "name": "dispatch", "type": {".class": "Instance", "args": ["sqlalchemy.orm.instrumentation.InstrumentationFactory"], "type_ref": "sqlalchemy.event.base.dispatcher"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._InstrumentationEventsHold.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.events._InstrumentationEventsHold", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_InternalEntityType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._InternalEntityType", "kind": "Gdef"}, "_KT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._KT", "name": "_KT", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_MapperEventsHold": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._MapperEventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._EventsHold"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.events._MapperEventsHold", "name": "_MapperEventsHold", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._MapperEventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.events._MapperEventsHold", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.events", "mro": ["sqlalchemy.orm.events._MapperEventsHold", "sqlalchemy.orm.events._EventsHold", "sqlalchemy.event.attr.RefCollection", "sqlalchemy.util.langhelpers.MemoizedSlots", "builtins.object"], "names": {".class": "SymbolTable", "HoldMapperEvents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._MapperEventsHold.HoldMapperEvents", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._EventsHold.HoldEvents"}, "sqlalchemy.orm.events.MapperEvents"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.events._MapperEventsHold.HoldMapperEvents", "name": "HoldMapperEvents", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._MapperEventsHold.HoldMapperEvents", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.events._MapperEventsHold.HoldMapperEvents", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.events", "mro": ["sqlalchemy.orm.events._MapperEventsHold.HoldMapperEvents", "sqlalchemy.orm.events._EventsHold.HoldEvents", "sqlalchemy.orm.events.MapperEvents", "sqlalchemy.event.base.Events", "sqlalchemy.event.base._HasEventsDispatch", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._MapperEventsHold.HoldMapperEvents.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._MapperEventsHold.HoldMapperEvents", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._MapperEventsHold.HoldMapperEvents"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "all_holds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.events._MapperEventsHold.all_holds", "name": "all_holds", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "type_ref": "weakref.<PERSON>eyDictionary"}}}, "dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.events._MapperEventsHold.dispatch", "name": "dispatch", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "type_ref": "sqlalchemy.event.base.dispatcher"}}}, "resolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.events._MapperEventsHold.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._MapperEventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._MapperEventsHold"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of _MapperEventsHold", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.events._MapperEventsHold.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.orm.events._MapperEventsHold", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.events._MapperEventsHold"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "_O": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._O", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._T", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.events.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.events.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.events.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.events.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.events.__package__", "name": "__package__", "type": "builtins.str"}}, "_mapper_or_none": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._mapper_or_none", "kind": "Gdef"}, "_sessionevents_lifecycle_event_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.events._sessionevents_lifecycle_event_names", "name": "_sessionevents_lifecycle_event_names", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "event": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "inspect_getfullargspec": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.inspect_getfullargspec", "kind": "Gdef"}, "instrumentation": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.instrumentation", "kind": "Gdef"}, "interfaces": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces", "kind": "Gdef"}, "mapperlib": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper", "kind": "Gdef"}, "scoped_session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.scoping.scoped_session", "kind": "Gdef"}, "sessionmaker": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.sessionmaker", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/events.py"}