{".class": "MypyFile", "_fullname": "sqlalchemy.orm.strategy_options", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractEntityRegistry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "kind": "Gdef"}, "AliasedInsp": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.AliasedInsp", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CacheKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.cache_key.CacheKey", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Final", "kind": "Gdef"}, "InspectionAttr": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.InspectionAttr", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "Load": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.strategy_options._AbstractLoad"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategy_options.Load", "name": "Load", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.strategy_options.Load", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategy_options", "mro": ["sqlalchemy.orm.strategy_options.Load", "sqlalchemy.orm.strategy_options._AbstractLoad", "sqlalchemy.sql.traversals.GenerativeOnTraversal", "sqlalchemy.sql.traversals.HasShallowCopy", "sqlalchemy.orm.interfaces.LoaderOption", "sqlalchemy.orm.interfaces.CompileStateOption", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.orm.interfaces.ORMOption", "sqlalchemy.sql.base.ExecutableOption", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.HasTraverseInternals", "builtins.object"], "names": {".class": "SymbolTable", "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options.Load.__getstate__", "name": "__getstate__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "entity"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options.Load.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "entity"], "arg_types": ["sqlalchemy.orm.strategy_options.Load", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._EntityType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Load", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options.Load.__setstate__", "name": "__setstate__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategy_options.Load.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options.Load.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.orm.strategy_options.Load"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of Load", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_adapt_cached_option_to_uncached_option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "uncached_opt"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options.Load._adapt_cached_option_to_uncached_option", "name": "_adapt_cached_option_to_uncached_option", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "uncached_opt"], "arg_types": ["sqlalchemy.orm.strategy_options.Load", "sqlalchemy.orm.context.QueryContext", "sqlalchemy.orm.interfaces.ORMOption"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_adapt_cached_option_to_uncached_option of Load", "ret_type": "sqlalchemy.orm.interfaces.ORMOption", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_adjust_for_extra_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options.Load._adjust_for_extra_criteria", "name": "_adjust_for_extra_criteria", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["sqlalchemy.orm.strategy_options.Load", "sqlalchemy.orm.context.QueryContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_adjust_for_extra_criteria of Load", "ret_type": "sqlalchemy.orm.strategy_options.Load", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_apply_to_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options.Load._apply_to_parent", "name": "_apply_to_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "arg_types": ["sqlalchemy.orm.strategy_options.Load", "sqlalchemy.orm.strategy_options.Load"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_to_parent of Load", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_cache_key_traversal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options.Load._cache_key_traversal", "name": "_cache_key_traversal", "type": {".class": "NoneType"}}}, "_clone_for_bind_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "attrs", "strategy", "wildcard_key", "opts", "attr_group", "propagate_to_loaders", "reconcile_to_other", "extra_criteria"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options.Load._clone_for_bind_strategy", "name": "_clone_for_bind_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "attrs", "strategy", "wildcard_key", "opts", "attr_group", "propagate_to_loaders", "reconcile_to_other", "extra_criteria"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options.Load.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options.Load", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._StrategyKey"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "relationship"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "column"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._OptsType"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrGroupType"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clone_for_bind_strategy of Load", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options.Load.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options.Load", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options.Load.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options.Load", "values": [], "variance": 0}]}}}, "_construct_for_existing_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.Load._construct_for_existing_path", "name": "_construct_for_existing_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.strategy_options.Load"}, "sqlalchemy.orm.path_registry.AbstractEntityRegistry"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_construct_for_existing_path of Load", "ret_type": "sqlalchemy.orm.strategy_options.Load", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.Load._construct_for_existing_path", "name": "_construct_for_existing_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.strategy_options.Load"}, "sqlalchemy.orm.path_registry.AbstractEntityRegistry"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_construct_for_existing_path of Load", "ret_type": "sqlalchemy.orm.strategy_options.Load", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_prepend_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options.Load._prepend_path", "name": "_prepend_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["sqlalchemy.orm.strategy_options.Load", "sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepend_path of Load", "ret_type": "sqlalchemy.orm.strategy_options.Load", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "compile_state", "mapper_entities", "raiseerr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options.Load._process", "name": "_process", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "compile_state", "mapper_entities", "raiseerr"], "arg_types": ["sqlalchemy.orm.strategy_options.Load", "sqlalchemy.orm.context.ORMCompileState", {".class": "Instance", "args": ["sqlalchemy.orm.context._MapperEntity"], "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process of Load", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_reconcile_query_entities_with_us": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mapper_entities", "raiseerr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options.Load._reconcile_query_entities_with_us", "name": "_reconcile_query_entities_with_us", "type": null}}, "_traverse_internals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options.Load._traverse_internals", "name": "_traverse_internals", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.visitors.InternalTraversal"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "additional_source_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options.Load.additional_source_entities", "name": "additional_source_entities", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}], "type_ref": "builtins.tuple"}}}, "context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options.Load.context", "name": "context", "type": {".class": "Instance", "args": ["sqlalchemy.orm.strategy_options._LoadElement"], "type_ref": "builtins.tuple"}}}, "options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "opts"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.Load.options", "name": "options", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "opts"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options.Load.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options.Load", "values": [], "variance": 0}, "sqlalchemy.orm.strategy_options._AbstractLoad"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "options of Load", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options.Load.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options.Load", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options.Load.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options.Load", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.Load.options", "name": "options", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "opts"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options.Load.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options.Load", "values": [], "variance": 0}, "sqlalchemy.orm.strategy_options._AbstractLoad"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "options of Load", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options.Load.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options.Load", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options.Load.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options.Load", "values": [], "variance": 0}]}}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options.Load.path", "name": "path", "type": "sqlalchemy.orm.path_registry.PathRegistry"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options.Load.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options.Load", "values": [], "variance": 0}, "slots": ["additional_source_entities", "context", "path", "propagate_to_loaders"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LoaderOption": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.LoaderOption", "kind": "Gdef"}, "Mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.Mapper", "kind": "Gdef"}, "MapperProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.MapperProperty", "kind": "Gdef"}, "ORMCompileState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.ORMCompileState", "kind": "Gdef"}, "ORMOption": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.ORMOption", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PathRegistry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.path_registry.PathRegistry", "kind": "Gdef"}, "QueryContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.QueryContext", "kind": "Gdef"}, "QueryableAttribute": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes.QueryableAttribute", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Self", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TokenRegistry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.path_registry.TokenRegistry", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_AbstractLoad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.traversals.GenerativeOnTraversal", "sqlalchemy.orm.interfaces.LoaderOption"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad", "name": "_AbstractLoad", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategy_options", "mro": ["sqlalchemy.orm.strategy_options._AbstractLoad", "sqlalchemy.sql.traversals.GenerativeOnTraversal", "sqlalchemy.sql.traversals.HasShallowCopy", "sqlalchemy.orm.interfaces.LoaderOption", "sqlalchemy.orm.interfaces.CompileStateOption", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.orm.interfaces.ORMOption", "sqlalchemy.sql.base.ExecutableOption", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.HasTraverseInternals", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_apply_to_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._apply_to_parent", "name": "_apply_to_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "arg_types": ["sqlalchemy.orm.strategy_options._AbstractLoad", "sqlalchemy.orm.strategy_options.Load"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_to_parent of _AbstractLoad", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_chop_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "to_chop", "path", "debug"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._chop_path", "name": "_chop_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "to_chop", "path", "debug"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.strategy_options._AbstractLoad"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}, "sqlalchemy.orm.path_registry.PathRegistry", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_chop_path of _AbstractLoad", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._chop_path", "name": "_chop_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "to_chop", "path", "debug"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.strategy_options._AbstractLoad"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}, "sqlalchemy.orm.path_registry.PathRegistry", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_chop_path of _AbstractLoad", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.path_registry._PathRepresentation"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_clone_for_bind_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "attrs", "strategy", "wildcard_key", "opts", "attr_group", "propagate_to_loaders", "reconcile_to_other", "extra_criteria"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._clone_for_bind_strategy", "name": "_clone_for_bind_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "attrs", "strategy", "wildcard_key", "opts", "attr_group", "propagate_to_loaders", "reconcile_to_other", "extra_criteria"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._StrategyKey"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "relationship"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "column"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._OptsType"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrGroupType"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clone_for_bind_strategy of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}, "_coerce_strat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._coerce_strat", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "strategy"], "dataclass_transform_spec": null, "flags": ["is_overload"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._coerce_strat", "name": "_coerce_strat", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "strategy"], "arg_types": ["sqlalchemy.orm.strategy_options._AbstractLoad", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._StrategySpec"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_coerce_strat of _AbstractLoad", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._StrategyKey"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "strategy"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._coerce_strat", "name": "_coerce_strat", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "strategy"], "arg_types": ["sqlalchemy.orm.strategy_options._AbstractLoad", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._StrategySpec"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_coerce_strat of _AbstractLoad", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._StrategyKey"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._coerce_strat", "name": "_coerce_strat", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "strategy"], "arg_types": ["sqlalchemy.orm.strategy_options._AbstractLoad", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._StrategySpec"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_coerce_strat of _AbstractLoad", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._StrategyKey"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "strategy"], "dataclass_transform_spec": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._coerce_strat", "name": "_coerce_strat", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "strategy"], "arg_types": ["sqlalchemy.orm.strategy_options._AbstractLoad", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_coerce_strat of _AbstractLoad", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._coerce_strat", "name": "_coerce_strat", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "strategy"], "arg_types": ["sqlalchemy.orm.strategy_options._AbstractLoad", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_coerce_strat of _AbstractLoad", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "strategy"], "arg_types": ["sqlalchemy.orm.strategy_options._AbstractLoad", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._StrategySpec"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_coerce_strat of _AbstractLoad", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._StrategyKey"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "strategy"], "arg_types": ["sqlalchemy.orm.strategy_options._AbstractLoad", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_coerce_strat of _AbstractLoad", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "_is_strategy_option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._is_strategy_option", "name": "_is_strategy_option", "type": "builtins.bool"}}, "_process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "compile_state", "mapper_entities", "raiseerr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._process", "name": "_process", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "compile_state", "mapper_entities", "raiseerr"], "arg_types": ["sqlalchemy.orm.strategy_options._AbstractLoad", "sqlalchemy.orm.context.ORMCompileState", {".class": "Instance", "args": ["sqlalchemy.orm.context._MapperEntity"], "type_ref": "typing.Sequence"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process of _AbstractLoad", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_set_class_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "strategy", "opts"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._set_class_strategy", "name": "_set_class_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "strategy", "opts"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._StrategySpec"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._OptsType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_class_strategy of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._set_class_strategy", "name": "_set_class_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "strategy", "opts"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._StrategySpec"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._OptsType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_class_strategy of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}}, "_set_column_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "attrs", "strategy", "opts", "extra_criteria"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._set_column_strategy", "name": "_set_column_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "attrs", "strategy", "opts", "extra_criteria"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._StrategySpec"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._OptsType"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_column_strategy of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._set_column_strategy", "name": "_set_column_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "attrs", "strategy", "opts", "extra_criteria"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._StrategySpec"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._OptsType"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_column_strategy of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}}, "_set_generic_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "attrs", "strategy", "_reconcile_to_other"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._set_generic_strategy", "name": "_set_generic_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "attrs", "strategy", "_reconcile_to_other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "type_ref": "builtins.tuple"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._StrategySpec"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_generic_strategy of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._set_generic_strategy", "name": "_set_generic_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "attrs", "strategy", "_reconcile_to_other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "type_ref": "builtins.tuple"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._StrategySpec"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_generic_strategy of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}}, "_set_relationship_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "attr", "strategy", "propagate_to_loaders", "opts", "_reconcile_to_other"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._set_relationship_strategy", "name": "_set_relationship_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "attr", "strategy", "propagate_to_loaders", "opts", "_reconcile_to_other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._StrategySpec"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._OptsType"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_relationship_strategy of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad._set_relationship_strategy", "name": "_set_relationship_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "attr", "strategy", "propagate_to_loaders", "opts", "_reconcile_to_other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._StrategySpec"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._OptsType"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_relationship_strategy of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}}, "contains_eager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "attr", "alias", "_is_chain"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.contains_eager", "name": "contains_eager", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "attr", "alias", "_is_chain"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "UnionType", "items": ["sqlalchemy.sql.roles.FromClauseRole", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}, {".class": "Instance", "args": ["sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement"], "type_ref": "sqlalchemy.inspection.Inspectable"}, "sqlalchemy.sql._typing._Has<PERSON><PERSON>eElement", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contains_eager of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}, "defaultload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.defaultload", "name": "defaultload", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "defaultload of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}, "defer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "raiseload"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.defer", "name": "defer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "raiseload"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "defer of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}, "immediateload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "attr", "recursion_depth"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.immediateload", "name": "immediateload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "attr", "recursion_depth"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "immediateload of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}, "joinedload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "attr", "innerjoin"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.joinedload", "name": "joinedload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "attr", "innerjoin"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "joinedload of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}, "lazyload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.lazyload", "name": "lazyload", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lazyload of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}, "load_only": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5], "arg_names": ["self", "attrs", "raiseload"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.load_only", "name": "load_only", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5], "arg_names": ["self", "attrs", "raiseload"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_only of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}, "noload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.noload", "name": "noload", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "noload of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}, "options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "opts"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.options", "name": "options", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "opts"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "sqlalchemy.orm.strategy_options._AbstractLoad"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "options of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}, "process_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "compile_state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.process_compile_state", "name": "process_compile_state", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "compile_state"], "arg_types": ["sqlalchemy.orm.strategy_options._AbstractLoad", "sqlalchemy.orm.context.ORMCompileState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_compile_state of _AbstractLoad", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "process_compile_state_replaced_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "mapper_entities"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.process_compile_state_replaced_entities", "name": "process_compile_state_replaced_entities", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "mapper_entities"], "arg_types": ["sqlalchemy.orm.strategy_options._AbstractLoad", "sqlalchemy.orm.context.ORMCompileState", {".class": "Instance", "args": ["sqlalchemy.orm.context._MapperEntity"], "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_compile_state_replaced_entities of _AbstractLoad", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "propagate_to_loaders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.propagate_to_loaders", "name": "propagate_to_loaders", "type": "builtins.bool"}}, "raiseload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "attr", "sql_only"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.raiseload", "name": "raiseload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "attr", "sql_only"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raiseload of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}, "selectin_polymorphic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "classes"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.selectin_polymorphic", "name": "selectin_polymorphic", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "classes"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selectin_polymorphic of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}, "selectinload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "attr", "recursion_depth"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.selectinload", "name": "selectinload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "attr", "recursion_depth"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selectinload of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}, "subqueryload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.subqueryload", "name": "subqueryload", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subqueryload of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}, "undefer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.undefer", "name": "undefer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "undefer of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}, "undefer_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.undefer_group", "name": "undefer_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "undefer_group of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}, "with_expression": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "expression"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.with_expression", "name": "with_expression", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "expression"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_expression of _AbstractLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AbstractLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AbstractLoad", "values": [], "variance": 0}, "slots": ["propagate_to_loaders"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_AttrGroupType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.strategy_options._AttrGroupType", "line": 86, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "type_ref": "builtins.tuple"}}}, "_AttrType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.strategy_options._AttrType", "line": 81, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "*"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.orm.attributes.QueryableAttribute"}]}}}, "_AttributeStrategyLoad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.strategy_options._LoadElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad", "name": "_AttributeStrategyLoad", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategy_options", "mro": ["sqlalchemy.orm.strategy_options._AttributeStrategyLoad", "sqlalchemy.orm.strategy_options._LoadElement", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.sql.traversals.HasShallowCopy", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad.__getstate__", "name": "__getstate__", "type": null}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad.__setstate__", "name": "__setstate__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_generate_extra_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad._generate_extra_criteria", "name": "_generate_extra_criteria", "type": null}}, "_init_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "path", "attr", "wildcard_key", "attr_group", "raiseerr", "extra_criteria"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad._init_path", "name": "_init_path", "type": null}}, "_of_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad._of_type", "name": "_of_type", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "NoneType"}]}}}, "_path_with_polymorphic_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad._path_with_polymorphic_path", "name": "_path_with_polymorphic_path", "type": {".class": "UnionType", "items": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "NoneType"}]}}}, "_prepare_for_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "parent_loader", "compile_state", "mapper_entities", "reconciled_lead_entity", "raiseerr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad._prepare_for_compile_state", "name": "_prepare_for_compile_state", "type": null}}, "_set_of_type_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "current_path"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad._set_of_type_info", "name": "_set_of_type_info", "type": null}}, "_traverse_internals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad._traverse_internals", "name": "_traverse_internals", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.visitors.InternalTraversal"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "is_class_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad.is_class_strategy", "name": "is_class_strategy", "type": "builtins.bool"}}, "is_token_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad.is_token_strategy", "name": "is_token_strategy", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._AttributeStrategyLoad", "values": [], "variance": 0}, "slots": ["_extra_criteria", "_of_type", "_path_with_polymorphic_path", "_reconcile_to_other", "local_opts", "path", "propagate_to_loaders", "strategy"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_COLUMN_TOKEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "column", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._COLUMN_TOKEN", "name": "_COLUMN_TOKEN", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "column"}}}, "_CacheKeyTraversalType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.cache_key._CacheKeyTraversalType", "kind": "Gdef"}, "_ClassStrategyLoad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.strategy_options._LoadElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategy_options._ClassStrategyLoad", "name": "_ClassStrategyLoad", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.strategy_options._ClassStrategyLoad", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategy_options", "mro": ["sqlalchemy.orm.strategy_options._ClassStrategyLoad", "sqlalchemy.orm.strategy_options._LoadElement", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.sql.traversals.HasShallowCopy", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._ClassStrategyLoad.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_init_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "path", "attr", "wildcard_key", "attr_group", "raiseerr", "extra_criteria"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._ClassStrategyLoad._init_path", "name": "_init_path", "type": null}}, "_prepare_for_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "parent_loader", "compile_state", "mapper_entities", "reconciled_lead_entity", "raiseerr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._ClassStrategyLoad._prepare_for_compile_state", "name": "_prepare_for_compile_state", "type": null}}, "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._ClassStrategyLoad.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "is_class_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._ClassStrategyLoad.is_class_strategy", "name": "is_class_strategy", "type": "builtins.bool"}}, "is_token_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._ClassStrategyLoad.is_token_strategy", "name": "is_token_strategy", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._ClassStrategyLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._ClassStrategyLoad", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ColumnExpressionArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument", "kind": "Gdef"}, "_DEFAULT_TOKEN": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.path_registry._DEFAULT_TOKEN", "kind": "Gdef"}, "_EntityType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._EntityType", "kind": "Gdef"}, "_FN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._FN", "name": "_FN", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}}, "_FromClauseArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._FromClauseArgument", "kind": "Gdef"}, "_InternalEntityType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._InternalEntityType", "kind": "Gdef"}, "_LoadElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.sql.traversals.HasShallowCopy", "sqlalchemy.sql.visitors.Visitable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategy_options._LoadElement", "name": "_LoadElement", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.strategy_options._LoadElement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategy_options", "mro": ["sqlalchemy.orm.strategy_options._LoadElement", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.sql.traversals.HasShallowCopy", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.__eq__", "name": "__eq__", "type": null}}, "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.__getstate__", "name": "__getstate__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.strategy_options._LoadElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getstate__ of _LoadElement", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.strategy_options._LoadElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of _LoadElement", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.strategy_options._LoadElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _LoadElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.__setstate__", "name": "__setstate__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["sqlalchemy.orm.strategy_options._LoadElement", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setstate__ of _LoadElement", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_adjust_effective_path_for_current_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "effective_path", "current_path"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._LoadElement._adjust_effective_path_for_current_path", "name": "_adjust_effective_path_for_current_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "effective_path", "current_path"], "arg_types": ["sqlalchemy.orm.strategy_options._LoadElement", "sqlalchemy.orm.path_registry.PathRegistry", "sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_adjust_effective_path_for_current_path of _LoadElement", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.path_registry.PathRegistry", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_cache_key_traversal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement._cache_key_traversal", "name": "_cache_key_traversal", "type": {".class": "NoneType"}}}, "_clone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._LoadElement._clone", "name": "_clone", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "arg_types": ["sqlalchemy.orm.strategy_options._LoadElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clone of _LoadElement", "ret_type": "sqlalchemy.orm.strategy_options._LoadElement", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_extra_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement._extra_criteria", "name": "_extra_criteria", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}}, "_init_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "path", "attr", "wildcard_key", "attr_group", "raiseerr", "extra_criteria"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._LoadElement._init_path", "name": "_init_path", "type": null}}, "_prepare_for_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "parent_loader", "compile_state", "mapper_entities", "reconciled_lead_entity", "raiseerr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._LoadElement._prepare_for_compile_state", "name": "_prepare_for_compile_state", "type": null}}, "_prepend_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._LoadElement._prepend_path", "name": "_prepend_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["sqlalchemy.orm.strategy_options._LoadElement", "sqlalchemy.orm.path_registry.PathRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepend_path of _LoadElement", "ret_type": "sqlalchemy.orm.strategy_options._LoadElement", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_prepend_path_from": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._LoadElement._prepend_path_from", "name": "_prepend_path_from", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "arg_types": ["sqlalchemy.orm.strategy_options._LoadElement", "sqlalchemy.orm.strategy_options.Load"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepend_path_from of _LoadElement", "ret_type": "sqlalchemy.orm.strategy_options._LoadElement", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_raise_for_no_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent_loader", "mapper_entities"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._LoadElement._raise_for_no_match", "name": "_raise_for_no_match", "type": null}}, "_reconcile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["replacement", "existing"], "dataclass_transform_spec": null, "flags": ["is_static", "is_decorated"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement._reconcile", "name": "_reconcile", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["replacement", "existing"], "arg_types": ["sqlalchemy.orm.strategy_options._LoadElement", "sqlalchemy.orm.strategy_options._LoadElement"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reconcile of _LoadElement", "ret_type": "sqlalchemy.orm.strategy_options._LoadElement", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement._reconcile", "name": "_reconcile", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["replacement", "existing"], "arg_types": ["sqlalchemy.orm.strategy_options._LoadElement", "sqlalchemy.orm.strategy_options._LoadElement"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reconcile of _LoadElement", "ret_type": "sqlalchemy.orm.strategy_options._LoadElement", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_reconcile_to_other": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement._reconcile_to_other", "name": "_reconcile_to_other", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}, "_recurse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._LoadElement._recurse", "name": "_recurse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.strategy_options._LoadElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_recurse of _LoadElement", "ret_type": "sqlalchemy.orm.strategy_options._LoadElement", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_traverse_internals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement._traverse_internals", "name": "_traverse_internals", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.visitors.InternalTraversal"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "_update_opts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._LoadElement._update_opts", "name": "_update_opts", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "arg_types": ["sqlalchemy.orm.strategy_options._LoadElement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_opts of _LoadElement", "ret_type": "sqlalchemy.orm.strategy_options._LoadElement", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "path", "attr", "strategy", "wildcard_key", "local_opts", "propagate_to_loaders", "raiseerr", "attr_group", "reconcile_to_other", "extra_criteria"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "path", "attr", "strategy", "wildcard_key", "local_opts", "propagate_to_loaders", "raiseerr", "attr_group", "reconcile_to_other", "extra_criteria"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.strategy_options._LoadElement"}, "sqlalchemy.orm.path_registry.PathRegistry", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._StrategyKey"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "relationship"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "column"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._OptsType"}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrGroupType"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of _LoadElement", "ret_type": "sqlalchemy.orm.strategy_options._LoadElement", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "path", "attr", "strategy", "wildcard_key", "local_opts", "propagate_to_loaders", "raiseerr", "attr_group", "reconcile_to_other", "extra_criteria"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.strategy_options._LoadElement"}, "sqlalchemy.orm.path_registry.PathRegistry", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._StrategyKey"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "relationship"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "column"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._OptsType"}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrGroupType"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of _LoadElement", "ret_type": "sqlalchemy.orm.strategy_options._LoadElement", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_class_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.is_class_strategy", "name": "is_class_strategy", "type": "builtins.bool"}}, "is_opts_only": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.is_opts_only", "name": "is_opts_only", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.strategy_options._LoadElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_opts_only of _LoadElement", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.is_opts_only", "name": "is_opts_only", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.strategy_options._LoadElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_opts_only of _LoadElement", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_token_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.is_token_strategy", "name": "is_token_strategy", "type": "builtins.bool"}}, "local_opts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.local_opts", "name": "local_opts", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.path", "name": "path", "type": "sqlalchemy.orm.path_registry.PathRegistry"}}, "process_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "parent_loader", "compile_state", "mapper_entities", "reconciled_lead_entity", "raiseerr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.process_compile_state", "name": "process_compile_state", "type": null}}, "propagate_to_loaders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.propagate_to_loaders", "name": "propagate_to_loaders", "type": "builtins.bool"}}, "strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options._LoadElement.strategy", "name": "strategy", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._StrategyKey"}, {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._LoadElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._LoadElement", "values": [], "variance": 0}, "slots": ["_extra_criteria", "_reconcile_to_other", "local_opts", "path", "propagate_to_loaders", "strategy"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MapperEntity": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context._MapperEntity", "kind": "Gdef"}, "_OptsType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.strategy_options._OptsType", "line": 85, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_PathRepresentation": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.path_registry._PathRepresentation", "kind": "Gdef"}, "_RELATIONSHIP_TOKEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "relationship", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._RELATIONSHIP_TOKEN", "name": "_RELATIONSHIP_TOKEN", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "relationship"}}}, "_StrPathToken": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.path_registry._StrPathToken", "kind": "Gdef"}, "_StrategyKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces._StrategyKey", "kind": "Gdef"}, "_StrategySpec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.strategy_options._StrategySpec", "line": 84, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_TokenStrategyLoad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.strategy_options._LoadElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategy_options._TokenStrategyLoad", "name": "_TokenStrategyLoad", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.strategy_options._TokenStrategyLoad", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategy_options", "mro": ["sqlalchemy.orm.strategy_options._TokenStrategyLoad", "sqlalchemy.orm.strategy_options._LoadElement", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.sql.traversals.HasShallowCopy", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._TokenStrategyLoad.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_init_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "path", "attr", "wildcard_key", "attr_group", "raiseerr", "extra_criteria"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._TokenStrategyLoad._init_path", "name": "_init_path", "type": null}}, "_prepare_for_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "parent_loader", "compile_state", "mapper_entities", "reconciled_lead_entity", "raiseerr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._TokenStrategyLoad._prepare_for_compile_state", "name": "_prepare_for_compile_state", "type": null}}, "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._TokenStrategyLoad.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "is_class_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._TokenStrategyLoad.is_class_strategy", "name": "is_class_strategy", "type": "builtins.bool"}}, "is_token_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._TokenStrategyLoad.is_token_strategy", "name": "is_token_strategy", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._TokenStrategyLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._TokenStrategyLoad", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WILDCARD_TOKEN": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.path_registry._WILDCARD_TOKEN", "kind": "Gdef"}, "_WildcardKeyType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.strategy_options._WildcardKeyType", "line": 83, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "relationship"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "column"}]}}}, "_WildcardLoad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.strategy_options._AbstractLoad"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad", "name": "_WildcardLoad", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategy_options", "mro": ["sqlalchemy.orm.strategy_options._WildcardLoad", "sqlalchemy.orm.strategy_options._AbstractLoad", "sqlalchemy.sql.traversals.GenerativeOnTraversal", "sqlalchemy.sql.traversals.HasShallowCopy", "sqlalchemy.orm.interfaces.LoaderOption", "sqlalchemy.orm.interfaces.CompileStateOption", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.orm.interfaces.ORMOption", "sqlalchemy.sql.base.ExecutableOption", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.HasTraverseInternals", "builtins.object"], "names": {".class": "SymbolTable", "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad.__getstate__", "name": "__getstate__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.strategy_options._WildcardLoad"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getstate__ of _WildcardLoad", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.strategy_options._WildcardLoad"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _WildcardLoad", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad.__setstate__", "name": "__setstate__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["sqlalchemy.orm.strategy_options._WildcardLoad", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setstate__ of _WildcardLoad", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_apply_to_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad._apply_to_parent", "name": "_apply_to_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "arg_types": ["sqlalchemy.orm.strategy_options._WildcardLoad", "sqlalchemy.orm.strategy_options.Load"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_to_parent of _WildcardLoad", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_clone_for_bind_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "attrs", "strategy", "wildcard_key", "opts", "attr_group", "propagate_to_loaders", "reconcile_to_other", "extra_criteria"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad._clone_for_bind_strategy", "name": "_clone_for_bind_strategy", "type": null}}, "_find_entity_basestring": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "entities", "token", "raiseerr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad._find_entity_basestring", "name": "_find_entity_basestring", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "entities", "token", "raiseerr"], "arg_types": ["sqlalchemy.orm.strategy_options._WildcardLoad", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}], "type_ref": "typing.Iterable"}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_entity_basestring of _WildcardLoad", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "compile_state", "mapper_entities", "raiseerr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad._process", "name": "_process", "type": null}}, "_traverse_internals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad._traverse_internals", "name": "_traverse_internals", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "sqlalchemy.sql.visitors.InternalTraversal"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "cache_key_traversal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad.cache_key_traversal", "name": "cache_key_traversal", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.cache_key._CacheKeyTraversalType"}}}, "local_opts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad.local_opts", "name": "local_opts", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._OptsType"}}}, "options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "opts"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad.options", "name": "options", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "opts"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._WildcardLoad", "values": [], "variance": 0}, "sqlalchemy.orm.strategy_options._AbstractLoad"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "options of _WildcardLoad", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._WildcardLoad", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._WildcardLoad", "values": [], "variance": 0}]}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad.path", "name": "path", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}]}}}, "propagate_to_loaders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad.propagate_to_loaders", "name": "propagate_to_loaders", "type": "builtins.bool"}}, "strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad.strategy", "name": "strategy", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._WildcardLoad.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategy_options._WildcardLoad", "values": [], "variance": 0}, "slots": ["local_opts", "path", "propagate_to_loaders", "strategy"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.strategy_options.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.strategy_options.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.strategy_options.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.strategy_options.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.strategy_options.__package__", "name": "__package__", "type": "builtins.str"}}, "_generate_from_keys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["meth", "keys", "chained", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._generate_from_keys", "name": "_generate_from_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["meth", "keys", "chained", "kw"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "type_ref": "builtins.tuple"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_from_keys", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_generative": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._generative", "kind": "Gdef"}, "_orm_full_deannotate": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util._orm_full_deannotate", "kind": "Gdef"}, "_parse_attr_argument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["attr"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._parse_attr_argument", "name": "_parse_attr_argument", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["attr"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_attr_argument", "ret_type": {".class": "TupleType", "implicit": false, "items": ["sqlalchemy.orm.base.InspectionAttr", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_raise_for_does_not_link": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["path", "attrname", "parent_entity"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options._raise_for_does_not_link", "name": "_raise_for_does_not_link", "type": null}}, "and_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.and_", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cache_key": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.cache_key", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "coercions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.coercions", "kind": "Gdef"}, "contains_eager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["keys", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.contains_eager", "name": "contains_eager", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["keys", "kw"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contains_eager", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.contains_eager", "name": "contains_eager", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["keys", "kw"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contains_eager", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "defaultload": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["keys"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.defaultload", "name": "defaultload", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["keys"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "defaultload", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.defaultload", "name": "defaultload", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["keys"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "defaultload", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "defer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5], "arg_names": ["key", "addl_attrs", "raiseload"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.defer", "name": "defer", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5], "arg_names": ["key", "addl_attrs", "raiseload"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "defer", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.defer", "name": "defer", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5], "arg_names": ["key", "addl_attrs", "raiseload"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "defer", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "immediateload": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 5], "arg_names": ["keys", "recursion_depth"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.immediateload", "name": "immediateload", "type": {".class": "CallableType", "arg_kinds": [2, 5], "arg_names": ["keys", "recursion_depth"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "immediateload", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.immediateload", "name": "immediateload", "type": {".class": "CallableType", "arg_kinds": [2, 5], "arg_names": ["keys", "recursion_depth"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "immediateload", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "insp_is_aliased_class": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.insp_is_aliased_class", "kind": "Gdef"}, "insp_is_attribute": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.insp_is_attribute", "kind": "Gdef"}, "insp_is_mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.insp_is_mapper", "kind": "Gdef"}, "insp_is_mapper_property": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.insp_is_mapper_property", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.inspection.inspect", "kind": "Gdef"}, "joinedload": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["keys", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.joinedload", "name": "joinedload", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["keys", "kw"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "joinedload", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.joinedload", "name": "joinedload", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["keys", "kw"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "joinedload", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "lazyload": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["keys"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.lazyload", "name": "lazyload", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["keys"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lazyload", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.lazyload", "name": "lazyload", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["keys"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lazyload", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "load_only": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 5], "arg_names": ["attrs", "raiseload"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.load_only", "name": "load_only", "type": {".class": "CallableType", "arg_kinds": [2, 5], "arg_names": ["attrs", "raiseload"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_only", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.load_only", "name": "load_only", "type": {".class": "CallableType", "arg_kinds": [2, 5], "arg_names": ["attrs", "raiseload"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_only", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "loader_unbound_fn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.orm.strategy_options.loader_unbound_fn", "name": "loader_unbound_fn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["fn"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._FN", "id": -1, "name": "_FN", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loader_unbound_fn", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._FN", "id": -1, "name": "_FN", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategy_options._FN", "id": -1, "name": "_FN", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}}, "noload": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["keys"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.noload", "name": "noload", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["keys"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "noload", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.noload", "name": "noload", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["keys"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "noload", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "orm_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "path_is_property": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.path_registry.path_is_property", "kind": "Gdef"}, "raiseload": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["keys", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.raiseload", "name": "raiseload", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["keys", "kw"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raiseload", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.raiseload", "name": "raiseload", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["keys", "kw"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raiseload", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef"}, "sa_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "selectin_polymorphic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["base_cls", "classes"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.selectin_polymorphic", "name": "selectin_polymorphic", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["base_cls", "classes"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._EntityType"}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selectin_polymorphic", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.selectin_polymorphic", "name": "selectin_polymorphic", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["base_cls", "classes"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._EntityType"}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selectin_polymorphic", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "selectinload": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 5], "arg_names": ["keys", "recursion_depth"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.selectinload", "name": "selectinload", "type": {".class": "CallableType", "arg_kinds": [2, 5], "arg_names": ["keys", "recursion_depth"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selectinload", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.selectinload", "name": "selectinload", "type": {".class": "CallableType", "arg_kinds": [2, 5], "arg_names": ["keys", "recursion_depth"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selectinload", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "subqueryload": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["keys"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.subqueryload", "name": "subqueryload", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["keys"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subqueryload", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.subqueryload", "name": "subqueryload", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["keys"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subqueryload", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "traversals": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.traversals", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "undefer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["key", "addl_attrs"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.undefer", "name": "undefer", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["key", "addl_attrs"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "undefer", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.undefer", "name": "undefer", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["key", "addl_attrs"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "undefer", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "undefer_group": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.undefer_group", "name": "undefer_group", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "undefer_group", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.undefer_group", "name": "undefer_group", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "undefer_group", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "visitors": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors", "kind": "Gdef"}, "with_expression": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["key", "expression"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategy_options.with_expression", "name": "with_expression", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key", "expression"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_expression", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategy_options.with_expression", "name": "with_expression", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key", "expression"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.strategy_options._AttrType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_expression", "ret_type": "sqlalchemy.orm.strategy_options._AbstractLoad", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/strategy_options.py"}