{"data_mtime": 1754228260, "dep_lines": [53, 54, 55, 56, 57, 58, 59, 63, 72, 73, 74, 53, 60, 61, 34, 36, 51, 60, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 5, 25, 25, 25, 20, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.base", "sqlalchemy.orm.collections", "sqlalchemy.orm.exc", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.state", "sqlalchemy.orm._typing", "sqlalchemy.orm.attributes", "sqlalchemy.util.typing", "sqlalchemy.orm.decl_base", "sqlalchemy.orm.events", "sqlalchemy.orm.mapper", "sqlalchemy.orm", "sqlalchemy.util", "sqlalchemy.event", "__future__", "typing", "weakref", "sqlalchemy", "builtins", "_weakref", "abc", "enum", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.util", "sqlalchemy.sql", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "types"], "hash": "a35993bf9802825f5df9246f1175e397caf397cb816ac44bddba4381683d3f9f", "id": "sqlalchemy.orm.instrumentation", "ignore_all": true, "interface_hash": "57ec7e346445cec95cc1ee1f25d71e82d89ff45dcb18c56d074d4533edbb6c73", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/instrumentation.py", "plugin_data": null, "size": 24337, "suppressed": [], "version_id": "1.7.1"}