{"data_mtime": 1754228260, "dep_lines": [33, 34, 35, 36, 37, 38, 39, 40, 45, 50, 53, 56, 33, 44, 45, 55, 20, 22, 44, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 5, 5, 5, 10, 25, 25, 25, 20, 10, 20, 25, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.exc", "sqlalchemy.orm.relationships", "sqlalchemy.orm.util", "sqlalchemy.orm.base", "sqlalchemy.orm.query", "sqlalchemy.orm.session", "sqlalchemy.orm.writeonly", "sqlalchemy.engine.result", "sqlalchemy.orm.mapper", "sqlalchemy.orm.state", "sqlalchemy.sql.elements", "sqlalchemy.orm", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.event", "__future__", "typing", "sqlalchemy", "builtins", "abc", "enum", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm._typing", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.strategies", "sqlalchemy.sql", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "a5894c22ba69f34131e0a072a9dca1c74c74908ae5fdc2000f091e571bd8bb8b", "id": "sqlalchemy.orm.dynamic", "ignore_all": true, "interface_hash": "305a74ed7ff4f80e6df72a62c85f1c5a18235e2b2b879414383c1c8a2716c9ce", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/dynamic.py", "plugin_data": null, "size": 9798, "suppressed": [], "version_id": "1.7.1"}