{"data_mtime": 1754228260, "dep_lines": [23, 24, 26, 27, 29, 30, 34, 35, 42, 43, 45, 47, 48, 57, 71, 22, 39, 40, 41, 8, 10, 39, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 25, 5, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm._typing", "sqlalchemy.orm.descriptor_props", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.properties", "sqlalchemy.orm.query", "sqlalchemy.orm.relationships", "sqlalchemy.orm.session", "sqlalchemy.orm.util", "sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.util.typing", "sqlalchemy.orm.mapper", "sqlalchemy.sql.elements", "sqlalchemy.orm", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.exc", "__future__", "typing", "sqlalchemy", "builtins", "abc", "enum", "sqlalchemy.engine", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.attributes", "sqlalchemy.orm.base", "sqlalchemy.sql._elements_constructors", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers"], "hash": "ffbfc663aab0dac03e186fd65cbcf518e3bed2a0be48205e038dc6855b92d90c", "id": "sqlalchemy.orm._orm_constructors", "ignore_all": true, "interface_hash": "c9dac174707b50c3cf6c662201582c49bf88c08411cfc6ace7da32b68462963c", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/orm/_orm_constructors.py", "plugin_data": null, "size": 99803, "suppressed": [], "version_id": "1.7.1"}