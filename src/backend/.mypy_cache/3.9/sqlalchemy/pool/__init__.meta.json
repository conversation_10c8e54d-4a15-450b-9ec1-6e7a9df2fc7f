{"data_mtime": 1754228259, "dep_lines": [20, 21, 36, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 30, 30], "dependencies": ["sqlalchemy.pool.events", "sqlalchemy.pool.base", "sqlalchemy.pool.impl", "builtins", "abc", "typing"], "hash": "088bf86fa72db9e63bc37b0c2ff2f1c8b280765e7d7ac60e873dceed6e70ed61", "id": "sqlalchemy.pool", "ignore_all": true, "interface_hash": "30d5460dd608dd9e2fa46fd9415f64cfa94736dbec15584cfaba46db190f4705", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/pool/__init__.py", "plugin_data": null, "size": 1815, "suppressed": [], "version_id": "1.7.1"}