{"data_mtime": 1754228259, "dep_lines": [27, 38, 39, 42, 35, 36, 12, 14, 15, 16, 25, 35, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 25, 10, 5, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.pool.base", "sqlalchemy.util.queue", "sqlalchemy.util.typing", "sqlalchemy.engine.interfaces", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "threading", "traceback", "typing", "weakref", "sqlalchemy", "builtins", "_weakref", "abc", "enum", "logging", "re", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.util.langhelpers", "types"], "hash": "bd4d27f366bbbb1744df8a77854edcbd40c0e500f2f4c908bf508e4f891814ff", "id": "sqlalchemy.pool.impl", "ignore_all": true, "interface_hash": "b3126d9c9982c9ca7fab73e6fe2c7ac0438bae109151692e6e48102b54ba4ac3", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/pool/impl.py", "plugin_data": null, "size": 17724, "suppressed": [], "version_id": "1.7.1"}