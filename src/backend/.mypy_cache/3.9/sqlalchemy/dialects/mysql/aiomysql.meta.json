{"data_mtime": 1754228260, "dep_lines": [30, 34, 31, 32, 33, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 317], "dep_prios": [5, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["sqlalchemy.dialects.mysql.pymysql", "sqlalchemy.util.concurrency", "sqlalchemy.pool", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy", "builtins", "abc", "asyncio", "asyncio.events", "asyncio.locks", "sqlalchemy.dialects.mysql.base", "sqlalchemy.dialects.mysql.mysqldb", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.util._concurrency_py3k", "typing"], "hash": "65bfbf17d3f3974b7e7d3d5b6706cd3677ba8e3094a815f17a2cdb84c14faa5b", "id": "sqlalchemy.dialects.mysql.aiomysql", "ignore_all": true, "interface_hash": "c0a1a1aa3cf3310983f4348bbc5569e69e2b59a29ff986b1167c1c6615d26eaf", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/mysql/aiomysql.py", "plugin_data": null, "size": 9750, "suppressed": ["pymysql.constants"], "version_id": "1.7.1"}