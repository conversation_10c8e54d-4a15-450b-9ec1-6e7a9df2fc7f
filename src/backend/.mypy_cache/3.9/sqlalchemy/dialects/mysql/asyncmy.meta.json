{"data_mtime": 1754228260, "dep_lines": [30, 34, 31, 32, 33, 28, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 322], "dep_prios": [5, 5, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["sqlalchemy.dialects.mysql.pymysql", "sqlalchemy.util.concurrency", "sqlalchemy.pool", "sqlalchemy.util", "sqlalchemy.engine", "contextlib", "sqlalchemy", "builtins", "abc", "asyncio", "asyncio.events", "asyncio.locks", "sqlalchemy.dialects.mysql.base", "sqlalchemy.dialects.mysql.mysqldb", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.util._concurrency_py3k", "sqlalchemy.util.langhelpers", "typing"], "hash": "ceaba90f3ec0262863bf713cc3fe1702dabde5df2cb5dac44cdc7ad0c2cd56bd", "id": "sqlalchemy.dialects.mysql.asyncmy", "ignore_all": true, "interface_hash": "c02b9d563cb1277d50425df4b036abae7a45eeda7e062e0b39a683c4edff97bf", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/mysql/asyncmy.py", "plugin_data": null, "size": 9819, "suppressed": ["asyncmy.constants"], "version_id": "1.7.1"}