{"data_mtime": 1754228260, "dep_lines": [12, 14, 17, 18, 19, 10, 17, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.mysql.enumerated", "sqlalchemy.dialects.mysql.types", "sqlalchemy.log", "sqlalchemy.types", "sqlalchemy.util", "re", "sqlalchemy", "builtins", "_typeshed", "abc", "enum", "sqlalchemy.util.langhelpers", "typing", "typing_extensions"], "hash": "6a4e84f9e08ff77e3a8b19c82d834972b4586e559b213d2c8d77f812a99f06c6", "id": "sqlalchemy.dialects.mysql.reflection", "ignore_all": true, "interface_hash": "8421917b84ba890655a31b11388f19791c01ea94107c617170240501dc2300ef", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/mysql/reflection.py", "plugin_data": null, "size": 22556, "suppressed": [], "version_id": "1.7.1"}