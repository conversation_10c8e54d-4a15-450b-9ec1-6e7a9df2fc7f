{"data_mtime": 1754228260, "dep_lines": [10, 11, 12, 13, 14, 15, 16, 17, 18, 53, 55, 56, 56, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 10, 10, 10, 10, 10, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.mysql.aiomysql", "sqlalchemy.dialects.mysql.asyncmy", "sqlalchemy.dialects.mysql.base", "sqlalchemy.dialects.mysql.cymysql", "sqlalchemy.dialects.mysql.mariadbconnector", "sqlalchemy.dialects.mysql.mysqlconnector", "sqlalchemy.dialects.mysql.mysqldb", "sqlalchemy.dialects.mysql.pymysql", "sqlalchemy.dialects.mysql.pyodbc", "sqlalchemy.dialects.mysql.dml", "sqlalchemy.dialects.mysql.expression", "sqlalchemy.util.compat", "sqlalchemy.util", "builtins", "abc", "sqlalchemy.engine", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "types", "typing"], "hash": "6ed2c006236799b5adf73896f97815584075a87590712173ef3c59370e26fcb6", "id": "sqlalchemy.dialects.mysql", "ignore_all": true, "interface_hash": "3ca6b591f35dc68707d2d1d01898d4dc173b8b7b608df1c430ba40b58e3b95ee", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/mysql/__init__.py", "plugin_data": null, "size": 2144, "suppressed": [], "version_id": "1.7.1"}