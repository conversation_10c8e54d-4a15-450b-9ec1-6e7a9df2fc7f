{"data_mtime": 1754228260, "dep_lines": [53, 54, 51, 54, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 86], "dep_prios": [5, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["sqlalchemy.dialects.oracle.cx_oracle", "sqlalchemy.exc", "re", "sqlalchemy", "builtins", "_typeshed", "abc", "sqlalchemy.dialects.oracle.base", "sqlalchemy.engine", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "typing", "typing_extensions"], "hash": "ffe7d443de316a2f3407bbfd58b546a060c8bfcbb9e2756ca41772184c88efa8", "id": "sqlalchemy.dialects.oracle.oracledb", "ignore_all": true, "interface_hash": "51f1e4e587bd52d20c8a8e1f6b4bcb880f701b030d0a171a096d9b9dd0cf0f3d", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/oracle/oracledb.py", "plugin_data": null, "size": 3457, "suppressed": ["oracledb"], "version_id": "1.7.1"}