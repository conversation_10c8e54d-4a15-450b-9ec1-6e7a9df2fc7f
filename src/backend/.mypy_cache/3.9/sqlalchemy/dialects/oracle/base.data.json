{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.oracle.base", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BFILE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.BFILE", "kind": "Gdef"}, "BINARY_DOUBLE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.BINARY_DOUBLE", "kind": "Gdef"}, "BINARY_FLOAT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.BINARY_FLOAT", "kind": "Gdef"}, "BLOB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BLOB", "kind": "Gdef"}, "CHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.CHAR", "kind": "Gdef"}, "CLOB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.CLOB", "kind": "Gdef"}, "Computed": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Computed", "kind": "Gdef"}, "DATE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.DATE", "kind": "Gdef"}, "DOUBLE_PRECISION": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DOUBLE_PRECISION", "kind": "Gdef"}, "FLOAT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.FLOAT", "kind": "Gdef"}, "INTEGER": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.INTEGER", "kind": "Gdef"}, "INTERVAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.INTERVAL", "kind": "Gdef"}, "InternalTraversal": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors.InternalTraversal", "kind": "Gdef"}, "LONG": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.LONG", "kind": "Gdef"}, "NCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.NCHAR", "kind": "Gdef"}, "NCLOB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.NCLOB", "kind": "Gdef"}, "NO_ARG_FNS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.NO_ARG_FNS", "name": "NO_ARG_FNS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "NUMBER": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.NUMBER", "kind": "Gdef"}, "NVARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.NVARCHAR", "kind": "Gdef"}, "NVARCHAR2": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.NVARCHAR2", "kind": "Gdef"}, "ObjectKind": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.ObjectKind", "kind": "Gdef"}, "ObjectScope": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.ObjectScope", "kind": "Gdef"}, "OracleCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.SQLCompiler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler", "name": "OracleCompiler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.base", "mro": ["sqlalchemy.dialects.oracle.base.OracleCompiler", "sqlalchemy.sql.compiler.SQLCompiler", "sqlalchemy.sql.compiler.Compiled", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.__init__", "name": "__init__", "type": null}}, "__wheres": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.__wheres", "name": "__wheres", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_get_limit_or_fetch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "select"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler._get_limit_or_fetch", "name": "_get_limit_or_fetch", "type": null}}, "_get_nonansi_join_whereclause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "froms"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler._get_nonansi_join_whereclause", "name": "_get_nonansi_join_whereclause", "type": null}}, "_oracle_returning": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler._oracle_returning", "name": "_oracle_returning", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_row_limit_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "select", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler._row_limit_clause", "name": "_row_limit_clause", "type": null}}, "compound_keywords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.compound_keywords", "name": "compound_keywords", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "default_from": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.default_from", "name": "default_from", "type": null}}, "for_update_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "select", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.for_update_clause", "name": "for_update_clause", "type": null}}, "function_argspec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "fn", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.function_argspec", "name": "function_argspec", "type": null}}, "get_cte_preamble": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "recursive"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.get_cte_preamble", "name": "get_cte_preamble", "type": null}}, "get_render_as_alias_suffix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "alias_name_text"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.get_render_as_alias_suffix", "name": "get_render_as_alias_suffix", "type": null}}, "get_select_hint_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "byfroms"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.get_select_hint_text", "name": "get_select_hint_text", "type": null}}, "limit_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "select", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.limit_clause", "name": "limit_clause", "type": null}}, "returning_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 4], "arg_names": ["self", "stmt", "returning_cols", "populate_result_map", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.returning_clause", "name": "returning_clause", "type": null}}, "translate_select_structure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "select_stmt", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.translate_select_structure", "name": "translate_select_structure", "type": null}}, "visit_aggregate_strings_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "fn", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_aggregate_strings_func", "name": "visit_aggregate_strings_func", "type": null}}, "visit_char_length_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "fn", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_char_length_func", "name": "visit_char_length_func", "type": null}}, "visit_empty_set_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_empty_set_expr", "name": "visit_empty_set_expr", "type": null}}, "visit_false": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "expr", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_false", "name": "visit_false", "type": null}}, "visit_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "func", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_function", "name": "visit_function", "type": null}}, "visit_is_distinct_from_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_is_distinct_from_binary", "name": "visit_is_distinct_from_binary", "type": null}}, "visit_is_not_distinct_from_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_is_not_distinct_from_binary", "name": "visit_is_not_distinct_from_binary", "type": null}}, "visit_join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "join", "from_linter", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_join", "name": "visit_join", "type": null}}, "visit_match_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_match_op_binary", "name": "visit_match_op_binary", "type": null}}, "visit_mod_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_mod_binary", "name": "visit_mod_binary", "type": null}}, "visit_not_regexp_match_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_not_regexp_match_op_binary", "name": "visit_not_regexp_match_op_binary", "type": null}}, "visit_now_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "fn", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_now_func", "name": "visit_now_func", "type": null}}, "visit_outer_join_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "vc", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_outer_join_column", "name": "visit_outer_join_column", "type": null}}, "visit_regexp_match_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_regexp_match_op_binary", "name": "visit_regexp_match_op_binary", "type": null}}, "visit_regexp_replace_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_regexp_replace_op_binary", "name": "visit_regexp_replace_op_binary", "type": null}}, "visit_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "seq", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_sequence", "name": "visit_sequence", "type": null}}, "visit_table_valued_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_table_valued_column", "name": "visit_table_valued_column", "type": null}}, "visit_true": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "expr", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.visit_true", "name": "visit_true", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.base.OracleCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.base.OracleCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OracleDDLCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.DDLCompiler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.base.OracleDDLCompiler", "name": "OracleDDLCompiler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDDLCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.base", "mro": ["sqlalchemy.dialects.oracle.base.OracleDDLCompiler", "sqlalchemy.sql.compiler.DDLCompiler", "sqlalchemy.sql.compiler.Compiled", "builtins.object"], "names": {".class": "SymbolTable", "define_constraint_cascades": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "constraint"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDDLCompiler.define_constraint_cascades", "name": "define_constraint_cascades", "type": null}}, "get_identity_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "identity_options"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDDLCompiler.get_identity_options", "name": "get_identity_options", "type": null}}, "post_create_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDDLCompiler.post_create_table", "name": "post_create_table", "type": null}}, "visit_computed_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "generated", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDDLCompiler.visit_computed_column", "name": "visit_computed_column", "type": null}}, "visit_create_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "create", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDDLCompiler.visit_create_index", "name": "visit_create_index", "type": null}}, "visit_drop_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDDLCompiler.visit_drop_table_comment", "name": "visit_drop_table_comment", "type": null}}, "visit_identity_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "identity", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDDLCompiler.visit_identity_column", "name": "visit_identity_column", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.base.OracleDDLCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.base.OracleDDLCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OracleDialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.default.DefaultDialect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect", "name": "OracleDialect", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.base", "mro": ["sqlalchemy.dialects.oracle.base.OracleDialect", "sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "use_ansi", "optimize_limits", "use_binds_for_limits", "use_nchar_for_unicode", "exclude_tablespaces", "enable_offset_fetch", "kwargs"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.__init__", "name": "__init__", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "use_ansi", "optimize_limits", "use_binds_for_limits", "use_nchar_for_unicode", "exclude_tablespaces", "enable_offset_fetch", "kwargs"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_all_objects_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "owner", "scope", "kind", "has_filter_names", "has_mat_views"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._all_objects_query", "name": "_all_objects_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._all_objects_query", "name": "_all_objects_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "functools._lru_cache_wrapper"}}}}, "_check_max_identifier_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._check_max_identifier_length", "name": "_check_max_identifier_length", "type": null}}, "_column_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "owner"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._column_query", "name": "_column_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._column_query", "name": "_column_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "functools._lru_cache_wrapper"}}}}, "_comment_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "owner", "scope", "kind", "has_filter_names"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._comment_query", "name": "_comment_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._comment_query", "name": "_comment_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "functools._lru_cache_wrapper"}}}}, "_constraint_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "owner"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._constraint_query", "name": "_constraint_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._constraint_query", "name": "_constraint_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "functools._lru_cache_wrapper"}}}}, "_execute_reflection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "connection", "query", "dblink", "returns_long", "params"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._execute_reflection", "name": "_execute_reflection", "type": null}}, "_get_all_constraint_rows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "connection", "schema", "dblink", "all_objects", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._get_all_constraint_rows", "name": "_get_all_constraint_rows", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._get_all_constraint_rows", "name": "_get_all_constraint_rows", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "_get_all_constraint_rows of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_get_all_objects": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "connection", "schema", "scope", "kind", "filter_names", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._get_all_objects", "name": "_get_all_objects", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._get_all_objects", "name": "_get_all_objects", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "_get_all_objects of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_get_default_schema_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._get_default_schema_name", "name": "_get_default_schema_name", "type": null}}, "_get_effective_compat_server_version_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._get_effective_compat_server_version_info", "name": "_get_effective_compat_server_version_info", "type": null}}, "_get_indexes_rows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "connection", "schema", "dblink", "all_objects", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._get_indexes_rows", "name": "_get_indexes_rows", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._get_indexes_rows", "name": "_get_indexes_rows", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "_get_indexes_rows of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_get_synonyms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "connection", "schema", "filter_names", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._get_synonyms", "name": "_get_synonyms", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._get_synonyms", "name": "_get_synonyms", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "_get_synonyms of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_handle_synonyms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "fn", "connection", "args", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._handle_synonyms", "name": "_handle_synonyms", "type": null}}, "_handle_synonyms_decorator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._handle_synonyms_decorator", "name": "_handle_synonyms_decorator", "type": null}}, "_has_table_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._has_table_query", "name": "_has_table_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._has_table_query", "name": "_has_table_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_index_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "owner"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._index_query", "name": "_index_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._index_query", "name": "_index_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "functools._lru_cache_wrapper"}}}}, "_is_oracle_8": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._is_oracle_8", "name": "_is_oracle_8", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._is_oracle_8", "name": "_is_oracle_8", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_oracle_8 of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_list_dblinks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "connection", "dblink"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._list_dblinks", "name": "_list_dblinks", "type": null}}, "_parse_identity_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "identity_options", "default_on_null"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._parse_identity_options", "name": "_parse_identity_options", "type": null}}, "_prepare_filter_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filter_names"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._prepare_filter_names", "name": "_prepare_filter_names", "type": null}}, "_run_batches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "connection", "query", "dblink", "returns_long", "mappings", "all_objects"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._run_batches", "name": "_run_batches", "type": null}}, "_supports_char_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._supports_char_length", "name": "_supports_char_length", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._supports_char_length", "name": "_supports_char_length", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_supports_char_length of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_supports_except_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._supports_except_all", "name": "_supports_except_all", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._supports_except_all", "name": "_supports_except_all", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_supports_except_all of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_supports_offset_fetch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._supports_offset_fetch", "name": "_supports_offset_fetch", "type": "builtins.bool"}}, "_supports_table_compress_for": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._supports_table_compress_for", "name": "_supports_table_compress_for", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._supports_table_compress_for", "name": "_supports_table_compress_for", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_supports_table_compress_for of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_supports_table_compression": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._supports_table_compression", "name": "_supports_table_compression", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._supports_table_compression", "name": "_supports_table_compression", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_supports_table_compression of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_supports_update_returning_computed_cols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._supports_update_returning_computed_cols", "name": "_supports_update_returning_computed_cols", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._supports_update_returning_computed_cols", "name": "_supports_update_returning_computed_cols", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_supports_update_returning_computed_cols of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_table_options_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "owner", "scope", "kind", "has_filter_names", "has_mat_views"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._table_options_query", "name": "_table_options_query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._table_options_query", "name": "_table_options_query", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "functools._lru_cache_wrapper"}}}}, "_use_nchar_for_unicode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._use_nchar_for_unicode", "name": "_use_nchar_for_unicode", "type": "builtins.bool"}}, "_value_or_raise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "data", "table", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect._value_or_raise", "name": "_value_or_raise", "type": null}}, "colspecs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.colspecs", "name": "colspecs", "type": {".class": "Instance", "args": ["builtins.type", "builtins.type"], "type_ref": "builtins.dict"}}}, "construct_arguments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.construct_arguments", "name": "construct_arguments", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "sqlalchemy.sql.schema.SchemaItem"}, {".class": "TypeType", "item": "sqlalchemy.sql.elements.ClauseElement"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.list"}}}, "cte_follows_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.cte_follows_insert", "name": "cte_follows_insert", "type": "builtins.bool"}}, "ddl_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.ddl_compiler", "name": "ddl_compiler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["dialect", "statement", "schema_translate_map", "render_schema_translate", "compile_kwargs"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.sql.ddl.ExecutableDDLElement", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.SchemaTranslateMapType"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}], "bound_args": ["sqlalchemy.dialects.oracle.base.OracleDDLCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.oracle.base.OracleDDLCompiler", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "default_paramstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.default_paramstyle", "name": "default_paramstyle", "type": "builtins.str"}}, "delete_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.delete_returning", "name": "delete_returning", "type": "builtins.bool"}}, "denormalize_schema_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.denormalize_schema_name", "name": "denormalize_schema_name", "type": null}}, "div_is_floordiv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.div_is_floordiv", "name": "div_is_floordiv", "type": "builtins.bool"}}, "do_release_savepoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.do_release_savepoint", "name": "do_release_savepoint", "type": null}}, "enable_offset_fetch": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.enable_offset_fetch", "name": "enable_offset_fetch", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "exclude_tablespaces": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.exclude_tablespaces", "name": "exclude_tablespaces", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "execution_ctx_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.execution_ctx_cls", "name": "execution_ctx_cls", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["sqlalchemy.dialects.oracle.base.OracleExecutionContext"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.oracle.base.OracleExecutionContext", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_check_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "include_all", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_check_constraints", "name": "get_check_constraints", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_check_constraints", "name": "get_check_constraints", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "include_all", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_check_constraints of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_columns", "name": "get_columns", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_columns", "name": "get_columns", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_columns of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_default_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_conn"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_default_isolation_level", "name": "get_default_isolation_level", "type": null}}, "get_foreign_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_foreign_keys", "name": "get_foreign_keys", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_foreign_keys", "name": "get_foreign_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_foreign_keys of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_indexes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_indexes", "name": "get_indexes", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_indexes", "name": "get_indexes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_indexes of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_isolation_level_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_isolation_level_values", "name": "get_isolation_level_values", "type": null}}, "get_materialized_view_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "connection", "schema", "dblink", "_normalize", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_materialized_view_names", "name": "get_materialized_view_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_materialized_view_names", "name": "get_materialized_view_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "connection", "schema", "dblink", "_normalize", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_materialized_view_names of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_multi_check_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 5, 3, 3, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "dblink", "scope", "kind", "include_all", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_multi_check_constraints", "name": "get_multi_check_constraints", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_multi_check_constraints", "name": "get_multi_check_constraints", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_multi_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3, 3, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "scope", "kind", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_multi_columns", "name": "get_multi_columns", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_multi_columns", "name": "get_multi_columns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_multi_foreign_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3, 3, 5, 4], "arg_names": ["self", "connection", "scope", "schema", "filter_names", "kind", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_multi_foreign_keys", "name": "get_multi_foreign_keys", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_multi_foreign_keys", "name": "get_multi_foreign_keys", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_multi_indexes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3, 3, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "scope", "kind", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_multi_indexes", "name": "get_multi_indexes", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_multi_indexes", "name": "get_multi_indexes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_multi_pk_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3, 3, 5, 4], "arg_names": ["self", "connection", "scope", "schema", "filter_names", "kind", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_multi_pk_constraint", "name": "get_multi_pk_constraint", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_multi_pk_constraint", "name": "get_multi_pk_constraint", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_multi_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3, 3, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "scope", "kind", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_multi_table_comment", "name": "get_multi_table_comment", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_multi_table_comment", "name": "get_multi_table_comment", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_multi_table_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3, 3, 5, 4], "arg_names": ["self", "connection", "schema", "filter_names", "scope", "kind", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_multi_table_options", "name": "get_multi_table_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_multi_table_options", "name": "get_multi_table_options", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_multi_unique_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3, 3, 5, 4], "arg_names": ["self", "connection", "scope", "schema", "filter_names", "kind", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_multi_unique_constraints", "name": "get_multi_unique_constraints", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_multi_unique_constraints", "name": "get_multi_unique_constraints", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_pk_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_pk_constraint", "name": "get_pk_constraint", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_pk_constraint", "name": "get_pk_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_pk_constraint of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_schema_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_schema_names", "name": "get_schema_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_schema_names", "name": "get_schema_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "dblink", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_schema_names of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_sequence_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "connection", "schema", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_sequence_names", "name": "get_sequence_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_sequence_names", "name": "get_sequence_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "connection", "schema", "dblink", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sequence_names of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_table_comment", "name": "get_table_comment", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_table_comment", "name": "get_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_table_comment of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_table_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "connection", "schema", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_table_names", "name": "get_table_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_table_names", "name": "get_table_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "connection", "schema", "dblink", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_table_names of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_table_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_table_options", "name": "get_table_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_table_options", "name": "get_table_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_table_options of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_temp_table_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_temp_table_names", "name": "get_temp_table_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_temp_table_names", "name": "get_temp_table_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "connection", "dblink", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_temp_table_names of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_unique_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_unique_constraints", "name": "get_unique_constraints", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_unique_constraints", "name": "get_unique_constraints", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unique_constraints of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_view_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "connection", "view_name", "schema", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_view_definition", "name": "get_view_definition", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_view_definition", "name": "get_view_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "connection", "view_name", "schema", "dblink", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_view_definition of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "get_view_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "connection", "schema", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_view_names", "name": "get_view_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.get_view_names", "name": "get_view_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "connection", "schema", "dblink", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_view_names of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "has_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "connection", "sequence_name", "schema", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.has_sequence", "name": "has_sequence", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.has_sequence", "name": "has_sequence", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "connection", "sequence_name", "schema", "dblink", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_sequence of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "has_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "dblink", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.has_table", "name": "has_table", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.has_table", "name": "has_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "connection", "table_name", "schema", "dblink", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_table of OracleDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.initialize", "name": "initialize", "type": null}}, "insert_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.insert_returning", "name": "insert_returning", "type": "builtins.bool"}}, "ischema_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.ischema_names", "name": "ischema_names", "type": {".class": "Instance", "args": ["builtins.str", "builtins.type"], "type_ref": "builtins.dict"}}}, "max_identifier_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.max_identifier_length", "name": "max_identifier_length", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.name", "name": "name", "type": "builtins.str"}}, "optimize_limits": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.optimize_limits", "name": "optimize_limits", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "postfetch_lastrowid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.postfetch_lastrowid", "name": "postfetch_lastrowid", "type": "builtins.bool"}}, "preparer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.preparer", "name": "preparer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["dialect", "initial_quote", "final_quote", "escape_quote", "quote_case_sensitive_collations", "omit_schema"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["sqlalchemy.dialects.oracle.base.OracleIdentifierPreparer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.oracle.base.OracleIdentifierPreparer", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "reflection_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.reflection_options", "name": "reflection_options", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "requires_name_normalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.requires_name_normalize", "name": "requires_name_normalize", "type": "builtins.bool"}}, "returns_native_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.returns_native_bytes", "name": "returns_native_bytes", "type": "builtins.bool"}}, "sequences_optional": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.sequences_optional", "name": "sequences_optional", "type": "builtins.bool"}}, "statement_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.statement_compiler", "name": "statement_compiler", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["sqlalchemy.dialects.oracle.base.OracleCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.oracle.base.OracleCompiler", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "supports_alter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.supports_alter", "name": "supports_alter", "type": "builtins.bool"}}, "supports_comments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.supports_comments", "name": "supports_comments", "type": "builtins.bool"}}, "supports_default_metavalue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.supports_default_metavalue", "name": "supports_default_metavalue", "type": "builtins.bool"}}, "supports_default_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.supports_default_values", "name": "supports_default_values", "type": "builtins.bool"}}, "supports_empty_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.supports_empty_insert", "name": "supports_empty_insert", "type": "builtins.bool"}}, "supports_identity_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.supports_identity_columns", "name": "supports_identity_columns", "type": "builtins.bool"}}, "supports_sequences": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.supports_sequences", "name": "supports_sequences", "type": "builtins.bool"}}, "supports_simple_order_by_label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.supports_simple_order_by_label", "name": "supports_simple_order_by_label", "type": "builtins.bool"}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}, "type_compiler_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.type_compiler_cls", "name": "type_compiler_cls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dialect"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect"], "bound_args": ["sqlalchemy.dialects.oracle.base.OracleTypeCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "update_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.update_returning", "name": "update_returning", "type": "builtins.bool"}}, "use_ansi": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.use_ansi", "name": "use_ansi", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.base.OracleDialect.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.base.OracleDialect", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OracleExecutionContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.default.DefaultExecutionContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.base.OracleExecutionContext", "name": "OracleExecutionContext", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleExecutionContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.base", "mro": ["sqlalchemy.dialects.oracle.base.OracleExecutionContext", "sqlalchemy.engine.default.DefaultExecutionContext", "sqlalchemy.engine.interfaces.ExecutionContext", "builtins.object"], "names": {".class": "SymbolTable", "fire_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "seq", "type_"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleExecutionContext.fire_sequence", "name": "fire_sequence", "type": null}}, "pre_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleExecutionContext.pre_exec", "name": "pre_exec", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.base.OracleExecutionContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.base.OracleExecutionContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OracleIdentifierPreparer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.IdentifierPreparer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.base.OracleIdentifierPreparer", "name": "OracleIdentifierPreparer", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleIdentifierPreparer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.base", "mro": ["sqlalchemy.dialects.oracle.base.OracleIdentifierPreparer", "sqlalchemy.sql.compiler.IdentifierPreparer", "builtins.object"], "names": {".class": "SymbolTable", "_bindparam_requires_quotes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleIdentifierPreparer._bindparam_requires_quotes", "name": "_bindparam_requires_quotes", "type": null}}, "format_savepoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "savepoint"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleIdentifierPreparer.format_savepoint", "name": "format_savepoint", "type": null}}, "illegal_initial_characters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleIdentifierPreparer.illegal_initial_characters", "name": "illegal_initial_characters", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "reserved_words": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleIdentifierPreparer.reserved_words", "name": "reserved_words", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.base.OracleIdentifierPreparer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.base.OracleIdentifierPreparer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OracleRaw": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.OracleRaw", "kind": "Gdef"}, "OracleTypeCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.GenericTypeCompiler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler", "name": "OracleTypeCompiler", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.base", "mro": ["sqlalchemy.dialects.oracle.base.OracleTypeCompiler", "sqlalchemy.sql.compiler.GenericTypeCompiler", "sqlalchemy.sql.compiler.TypeCompiler", "sqlalchemy.util.langhelpers.EnsureKWArg", "builtins.object"], "names": {".class": "SymbolTable", "_generate_numeric": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "type_", "name", "precision", "scale", "_requires_binary_precision", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler._generate_numeric", "name": "_generate_numeric", "type": null}}, "_visit_varchar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "type_", "n", "num"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler._visit_varchar", "name": "_visit_varchar", "type": null}}, "visit_BINARY_DOUBLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_BINARY_DOUBLE", "name": "visit_BINARY_DOUBLE", "type": null}}, "visit_BINARY_FLOAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_BINARY_FLOAT", "name": "visit_BINARY_FLOAT", "type": null}}, "visit_DOUBLE_PRECISION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_DOUBLE_PRECISION", "name": "visit_DOUBLE_PRECISION", "type": null}}, "visit_FLOAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_FLOAT", "name": "visit_FLOAT", "type": null}}, "visit_INTERVAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_INTERVAL", "name": "visit_INTERVAL", "type": null}}, "visit_LONG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_LONG", "name": "visit_LONG", "type": null}}, "visit_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_NUMBER", "name": "visit_NUMBER", "type": null}}, "visit_NVARCHAR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_NVARCHAR", "name": "visit_NVARCHAR", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "arg_types": ["sqlalchemy.dialects.oracle.base.OracleTypeCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "visit_NVARCHAR2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_NVARCHAR2", "name": "visit_NVARCHAR2", "type": null}}, "visit_RAW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_RAW", "name": "visit_RAW", "type": null}}, "visit_ROWID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_ROWID", "name": "visit_ROWID", "type": null}}, "visit_TIMESTAMP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_TIMESTAMP", "name": "visit_TIMESTAMP", "type": null}}, "visit_VARCHAR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_VARCHAR", "name": "visit_VARCHAR", "type": null}}, "visit_VARCHAR2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_VARCHAR2", "name": "visit_VARCHAR2", "type": null}}, "visit_big_integer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_big_integer", "name": "visit_big_integer", "type": null}}, "visit_boolean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_boolean", "name": "visit_boolean", "type": null}}, "visit_datetime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_datetime", "name": "visit_datetime", "type": null}}, "visit_double": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_double", "name": "visit_double", "type": null}}, "visit_float": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_float", "name": "visit_float", "type": null}}, "visit_large_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_large_binary", "name": "visit_large_binary", "type": null}}, "visit_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_string", "name": "visit_string", "type": null}}, "visit_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_text", "name": "visit_text", "type": null}}, "visit_unicode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_unicode", "name": "visit_unicode", "type": null}}, "visit_unicode_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.visit_unicode_text", "name": "visit_unicode_text", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.base.OracleTypeCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RAW": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.RAW", "kind": "Gdef"}, "REAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.REAL", "kind": "Gdef"}, "RESERVED_WORDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.RESERVED_WORDS", "name": "RESERVED_WORDS", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}}}, "ROWID": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.ROWID", "kind": "Gdef"}, "ReflectionDefaults": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.ReflectionDefaults", "kind": "Gdef"}, "TIMESTAMP": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.TIMESTAMP", "kind": "Gdef"}, "VARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.VARCHAR", "kind": "Gdef"}, "VARCHAR2": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.VARCHAR2", "kind": "Gdef"}, "_OracleBoolean": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types._OracleBoolean", "kind": "Gdef"}, "_OracleDate": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types._OracleDate", "kind": "Gdef"}, "_OuterJoinColumn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.elements.ClauseElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.base._OuterJoinColumn", "name": "_OuterJoinColumn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.base._OuterJoinColumn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.base", "mro": ["sqlalchemy.dialects.oracle.base._OuterJoinColumn", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "column"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.base._OuterJoinColumn.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base._OuterJoinColumn.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.oracle.base._OuterJoinColumn.column", "name": "column", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.base._OuterJoinColumn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.base._OuterJoinColumn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.base.__package__", "name": "__package__", "type": "builtins.str"}}, "and_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.and_", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "bindparam": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.bindparam", "kind": "Gdef"}, "colspecs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.colspecs", "name": "colspecs", "type": {".class": "Instance", "args": ["builtins.type", "builtins.type"], "type_ref": "builtins.dict"}}}, "compiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler", "kind": "Gdef"}, "default": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.default", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "dictionary": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.dictionary", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "expression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.expression", "kind": "Gdef"}, "func": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.func", "kind": "Gdef"}, "ischema_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.base.ischema_names", "name": "ischema_names", "type": {".class": "Instance", "args": ["builtins.str", "builtins.type"], "type_ref": "builtins.dict"}}}, "lru_cache": {".class": "SymbolTableNode", "cross_ref": "functools.lru_cache", "kind": "Gdef"}, "null": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.null", "kind": "Gdef"}, "or_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.or_", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "reflection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection", "kind": "Gdef"}, "sa_schema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.schema", "kind": "Gdef"}, "select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.select", "kind": "Gdef"}, "sql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql", "kind": "Gdef"}, "sql_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.util", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "visitors": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors", "kind": "Gdef"}, "wraps": {".class": "SymbolTableNode", "cross_ref": "functools.wraps", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/oracle/base.py"}