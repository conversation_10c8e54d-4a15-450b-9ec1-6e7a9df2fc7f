{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.oracle.types", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BFILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.LargeBinary"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.types.BFILE", "name": "BFILE", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.BFILE", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.types", "mro": ["sqlalchemy.dialects.oracle.types.BFILE", "sqlalchemy.sql.sqltypes.LargeBinary", "sqlalchemy.sql.sqltypes._Binary", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.types.BFILE.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.types.BFILE.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.types.BFILE", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BINARY_DOUBLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.sqltypes.Double"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.types.BINARY_DOUBLE", "name": "BINARY_DOUBLE", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.BINARY_DOUBLE", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.types", "mro": ["sqlalchemy.dialects.oracle.types.BINARY_DOUBLE", "sqlalchemy.sql.sqltypes.Double", "sqlalchemy.sql.sqltypes.Float", "sqlalchemy.sql.sqltypes.Numeric", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.types.BINARY_DOUBLE.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.types.BINARY_DOUBLE.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.types.BINARY_DOUBLE", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BINARY_FLOAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.sqltypes.Float"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.types.BINARY_FLOAT", "name": "BINARY_FLOAT", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.BINARY_FLOAT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.types", "mro": ["sqlalchemy.dialects.oracle.types.BINARY_FLOAT", "sqlalchemy.sql.sqltypes.Float", "sqlalchemy.sql.sqltypes.Numeric", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.types.BINARY_FLOAT.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.types.BINARY_FLOAT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.types.BINARY_FLOAT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.oracle.types._OracleDateLiteralRender", "sqlalchemy.sql.sqltypes.DateTime"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.types.DATE", "name": "DATE", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.DATE", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.types", "mro": ["sqlalchemy.dialects.oracle.types.DATE", "sqlalchemy.dialects.oracle.types._OracleDateLiteralRender", "sqlalchemy.sql.sqltypes.DateTime", "sqlalchemy.sql.sqltypes._RenderISO8601NoT", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.types.DATE.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_compare_type_affinity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.DATE._compare_type_affinity", "name": "_compare_type_affinity", "type": null}}, "literal_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.DATE.literal_processor", "name": "literal_processor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.types.DATE.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.types.DATE", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Dialect", "kind": "Gdef"}, "FLOAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.sqltypes.FLOAT"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.types.FLOAT", "name": "FLOAT", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.FLOAT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.types", "mro": ["sqlalchemy.dialects.oracle.types.FLOAT", "sqlalchemy.sql.sqltypes.FLOAT", "sqlalchemy.sql.sqltypes.Float", "sqlalchemy.sql.sqltypes.Numeric", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "binary_precision", "asdecimal", "decimal_return_scale"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.FLOAT.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.types.FLOAT.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "binary_precision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.oracle.types.FLOAT.binary_precision", "name": "binary_precision", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.types.FLOAT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.types.FLOAT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "INTERVAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.type_api.NativeForEmulated", "sqlalchemy.sql.sqltypes._AbstractInterval"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL", "name": "INTERVAL", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.types", "mro": ["sqlalchemy.dialects.oracle.types.INTERVAL", "sqlalchemy.sql.type_api.NativeForEmulated", "sqlalchemy.sql.sqltypes._AbstractInterval", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "day_precision", "second_precision"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_adapt_from_generic_interval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "interval"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL._adapt_from_generic_interval", "name": "_adapt_from_generic_interval", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL._adapt_from_generic_interval", "name": "_adapt_from_generic_interval", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "interval"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.oracle.types.INTERVAL"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_adapt_from_generic_interval of INTERVAL", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_type_affinity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL._type_affinity", "name": "_type_affinity", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL._type_affinity", "name": "_type_affinity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.oracle.types.INTERVAL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_type_affinity of INTERVAL", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "adapt_emulated_to_native": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "interval", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL.adapt_emulated_to_native", "name": "adapt_emulated_to_native", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "interval", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.oracle.types.INTERVAL"}, "sqlalchemy.sql.sqltypes.Interval", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adapt_emulated_to_native of INTERVAL", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL.adapt_emulated_to_native", "name": "adapt_emulated_to_native", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "interval", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.oracle.types.INTERVAL"}, "sqlalchemy.sql.sqltypes.Interval", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adapt_emulated_to_native of INTERVAL", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "as_generic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "allow_nulltype"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL.as_generic", "name": "as_generic", "type": null}}, "day_precision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL.day_precision", "name": "day_precision", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "literal_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL.literal_processor", "name": "literal_processor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "arg_types": ["sqlalchemy.dialects.oracle.types.INTERVAL", "sqlalchemy.engine.interfaces.Dialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "literal_processor of INTERVAL", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["datetime.<PERSON><PERSON><PERSON>"], "type_ref": "sqlalchemy.sql.type_api._LiteralProcessorType"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "python_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL.python_type", "name": "python_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.oracle.types.INTERVAL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "python_type of INTERVAL", "ret_type": {".class": "TypeType", "item": "datetime.<PERSON><PERSON><PERSON>"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL.python_type", "name": "python_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.oracle.types.INTERVAL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "python_type of INTERVAL", "ret_type": {".class": "TypeType", "item": "datetime.<PERSON><PERSON><PERSON>"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "second_precision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL.second_precision", "name": "second_precision", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.types.INTERVAL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.types.INTERVAL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LONG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.Text"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.types.LONG", "name": "LONG", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.LONG", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.types", "mro": ["sqlalchemy.dialects.oracle.types.LONG", "sqlalchemy.sql.sqltypes.Text", "sqlalchemy.sql.sqltypes.String", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.types.LONG.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.types.LONG.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.types.LONG", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NCLOB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.Text"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.types.NCLOB", "name": "NCLOB", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.NCLOB", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.types", "mro": ["sqlalchemy.dialects.oracle.types.NCLOB", "sqlalchemy.sql.sqltypes.Text", "sqlalchemy.sql.sqltypes.String", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.types.NCLOB.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.types.NCLOB.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.types.NCLOB", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NUMBER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.sqltypes.Numeric"}, "sqlalchemy.sql.sqltypes.Integer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.types.NUMBER", "name": "NUMBER", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.NUMBER", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.types", "mro": ["sqlalchemy.dialects.oracle.types.NUMBER", "sqlalchemy.sql.sqltypes.Numeric", "sqlalchemy.sql.sqltypes.Integer", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "precision", "scale", "asdecimal"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.NUMBER.__init__", "name": "__init__", "type": null}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.types.NUMBER.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_type_affinity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.oracle.types.NUMBER._type_affinity", "name": "_type_affinity", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.types.NUMBER._type_affinity", "name": "_type_affinity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.oracle.types.NUMBER"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_type_affinity of NUMBER", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "adapt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "impltype"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.NUMBER.adapt", "name": "adapt", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.types.NUMBER.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.types.NUMBER", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NVARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.NVARCHAR", "kind": "Gdef"}, "NVARCHAR2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.oracle.types.NVARCHAR2", "line": 39, "no_args": true, "normalized": false, "target": "sqlalchemy.sql.sqltypes.NVARCHAR"}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OracleRaw": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.oracle.types.OracleRaw", "line": 28, "no_args": true, "normalized": false, "target": "sqlalchemy.dialects.oracle.types.RAW"}}, "RAW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes._Binary"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.types.RAW", "name": "RAW", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.RAW", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.types", "mro": ["sqlalchemy.dialects.oracle.types.RAW", "sqlalchemy.sql.sqltypes._Binary", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.types.RAW.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.types.RAW.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.types.RAW", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ROWID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.types.ROWID", "name": "ROWID", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.ROWID", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.types", "mro": ["sqlalchemy.dialects.oracle.types.ROWID", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.types.ROWID.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.types.ROWID.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.types.ROWID", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TIMESTAMP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.TIMESTAMP"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.types.TIMESTAMP", "name": "TIMESTAMP", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.TIMESTAMP", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.types", "mro": ["sqlalchemy.dialects.oracle.types.TIMESTAMP", "sqlalchemy.sql.sqltypes.TIMESTAMP", "sqlalchemy.sql.sqltypes.DateTime", "sqlalchemy.sql.sqltypes._RenderISO8601NoT", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "timezone", "local_timezone"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.TIMESTAMP.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "timezone", "local_timezone"], "arg_types": ["sqlalchemy.dialects.oracle.types.TIMESTAMP", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TIMESTAMP", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "local_timezone": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.oracle.types.TIMESTAMP.local_timezone", "name": "local_timezone", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.types.TIMESTAMP.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.types.TIMESTAMP", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "VARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.VARCHAR", "kind": "Gdef"}, "VARCHAR2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.VARCHAR"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.types.VARCHAR2", "name": "VARCHAR2", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.types.VARCHAR2", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.types", "mro": ["sqlalchemy.dialects.oracle.types.VARCHAR2", "sqlalchemy.sql.sqltypes.VARCHAR", "sqlalchemy.sql.sqltypes.String", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.types.VARCHAR2.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.types.VARCHAR2.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.types.VARCHAR2", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_LiteralProcessorType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api._LiteralProcessorType", "kind": "Gdef"}, "_OracleBoolean": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.Boolean"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.types._OracleBoolean", "name": "_OracleBoolean", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.types._OracleBoolean", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.types", "mro": ["sqlalchemy.dialects.oracle.types._OracleBoolean", "sqlalchemy.sql.sqltypes.Boolean", "sqlalchemy.sql.sqltypes.SchemaType", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.type_api.Emulated", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "get_dbapi_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.types._OracleBoolean.get_dbapi_type", "name": "get_dbapi_type", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.types._OracleBoolean.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.types._OracleBoolean", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_OracleDate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.oracle.types._OracleDateLiteralRender", "sqlalchemy.sql.sqltypes.Date"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.types._OracleDate", "name": "_OracleDate", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.types._OracleDate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.types", "mro": ["sqlalchemy.dialects.oracle.types._OracleDate", "sqlalchemy.dialects.oracle.types._OracleDateLiteralRender", "sqlalchemy.sql.sqltypes.Date", "sqlalchemy.sql.sqltypes._RenderISO8601NoT", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "literal_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.types._OracleDate.literal_processor", "name": "literal_processor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.types._OracleDate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.types._OracleDate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_OracleDateLiteralRender": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.types._OracleDateLiteralRender", "name": "_OracleDateLiteralRender", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.types._OracleDateLiteralRender", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.types", "mro": ["sqlalchemy.dialects.oracle.types._OracleDateLiteralRender", "builtins.object"], "names": {".class": "SymbolTable", "_literal_processor_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.types._OracleDateLiteralRender._literal_processor_date", "name": "_literal_processor_date", "type": null}}, "_literal_processor_datetime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.types._OracleDateLiteralRender._literal_processor_datetime", "name": "_literal_processor_datetime", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.types._OracleDateLiteralRender.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.types._OracleDateLiteralRender", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.types.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.types.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.types.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.types.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.types.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "dt": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/oracle/types.py"}