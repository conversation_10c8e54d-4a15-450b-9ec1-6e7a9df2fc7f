{"data_mtime": 1754228260, "dep_lines": [430, 434, 430, 437, 438, 439, 440, 441, 435, 436, 437, 440, 424, 426, 427, 428, 435, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1098], "dep_prios": [5, 5, 20, 10, 10, 10, 10, 5, 10, 10, 20, 20, 5, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["sqlalchemy.dialects.oracle.base", "sqlalchemy.dialects.oracle.types", "sqlalchemy.dialects.oracle", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.processors", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql._typing", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.sql", "__future__", "decimal", "random", "re", "sqlalchemy", "builtins", "_decimal", "_typeshed", "abc", "enum", "sqlalchemy.engine.default", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "typing", "typing_extensions", "uuid"], "hash": "2f41af701eb16f4fb3cafe5dc776e94027a9b69d0a4aa1fa83d154438cbe77e8", "id": "sqlalchemy.dialects.oracle.cx_oracle", "ignore_all": true, "interface_hash": "59e94c482900897b5160aac03585a4cc5d880a1869b70cbed90543c18576f0b5", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/oracle/cx_oracle.py", "plugin_data": null, "size": 55108, "suppressed": ["cx_Oracle"], "version_id": "1.7.1"}