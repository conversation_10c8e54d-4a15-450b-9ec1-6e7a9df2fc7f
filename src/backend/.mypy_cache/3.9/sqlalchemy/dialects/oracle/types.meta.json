{"data_mtime": 1754228260, "dep_lines": [15, 20, 21, 14, 15, 16, 7, 9, 10, 14, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 25, 25, 10, 20, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.sqltypes", "sqlalchemy.engine.interfaces", "sqlalchemy.sql.type_api", "sqlalchemy.exc", "sqlalchemy.sql", "sqlalchemy.types", "__future__", "datetime", "typing", "sqlalchemy", "builtins", "abc", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.base", "sqlalchemy.sql.visitors"], "hash": "4de3a1516e56f6a642f12689fbd6f7bba3af38f39aacdab8326090ee5df0597d", "id": "sqlalchemy.dialects.oracle.types", "ignore_all": true, "interface_hash": "71866be01a6ceacea8bbd52c9ddd51abcd3e19ccb29888fa24cbb0bb0313e070", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/oracle/types.py", "plugin_data": null, "size": 8204, "suppressed": [], "version_id": "1.7.1"}