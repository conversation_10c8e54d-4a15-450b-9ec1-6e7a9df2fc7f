{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.oracle.oracledb", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "OracleDialect_oracledb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.oracle.cx_oracle.OracleDialect_cx_oracle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb", "name": "OracleDialect_oracledb", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.oracle.oracledb", "mro": ["sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb", "sqlalchemy.dialects.oracle.cx_oracle.OracleDialect_cx_oracle", "sqlalchemy.dialects.oracle.base.OracleDialect", "sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "auto_convert_lobs", "coerce_to_decimal", "arraysize", "encoding_errors", "thick_mode", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb.__init__", "name": "__init__", "type": null}}, "_load_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_module"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb._load_version", "name": "_load_version", "type": null}}, "driver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb.driver", "name": "driver", "type": "builtins.str"}}, "import_dbapi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb.import_dbapi", "name": "import_dbapi", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb.import_dbapi", "name": "import_dbapi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_dbapi of OracleDialect_oracledb", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_thin_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "connection"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb.is_thin_mode", "name": "is_thin_mode", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb.is_thin_mode", "name": "is_thin_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "connection"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_thin_mode of OracleDialect_oracledb", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "oracledb_ver": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb.oracledb_ver", "name": "oracledb_ver", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_OracleDialect_cx_oracle": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.cx_oracle.OracleDialect_cx_oracle", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.oracledb.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.oracledb.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.oracledb.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.oracledb.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.oracledb.__package__", "name": "__package__", "type": "builtins.str"}}, "dialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.oracle.oracledb.dialect", "line": 109, "no_args": true, "normalized": false, "target": "sqlalchemy.dialects.oracle.oracledb.OracleDialect_oracledb"}}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/oracle/oracledb.py"}