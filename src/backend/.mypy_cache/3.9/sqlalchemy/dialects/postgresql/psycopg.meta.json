{"data_mtime": 1754228260, "dep_lines": [61, 62, 64, 68, 71, 61, 75, 76, 72, 73, 74, 75, 54, 56, 57, 58, 72, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 316, 326, 365, 413, 419, 309, 316, 407, 316], "dep_prios": [10, 5, 5, 5, 5, 20, 10, 5, 10, 10, 5, 20, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20, 20, 20, 20, 20, 20, 20], "dependencies": ["sqlalchemy.dialects.postgresql.ranges", "sqlalchemy.dialects.postgresql._psycopg_common", "sqlalchemy.dialects.postgresql.base", "sqlalchemy.dialects.postgresql.json", "sqlalchemy.dialects.postgresql.types", "sqlalchemy.dialects.postgresql", "sqlalchemy.sql.sqltypes", "sqlalchemy.util.concurrency", "sqlalchemy.pool", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.sql", "__future__", "logging", "re", "typing", "sqlalchemy", "builtins", "_typeshed", "abc", "enum", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._concurrency_py3k", "sqlalchemy.util.langhelpers", "typing_extensions"], "hash": "60cb9bcd01cc60dd41cbc40949c20f6ff3f0362002bfab2b75d43a9d7e9696d4", "id": "sqlalchemy.dialects.postgresql.psycopg", "ignore_all": true, "interface_hash": "34ad186918ca4b8eed1a76b9efc12508cc3e9b0f2ad60abeb24c5e551a4cc602", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/psycopg.py", "plugin_data": null, "size": 22238, "suppressed": ["psycopg.types.string", "psycopg.types.json", "psycopg.types.hstore", "psycopg.types.range", "psycopg.types.multirange", "psycopg.adapt", "psycopg.types", "psycopg.pq", "psycopg"], "version_id": "1.7.1"}