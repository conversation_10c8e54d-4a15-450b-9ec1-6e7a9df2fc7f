{"data_mtime": 1754228260, "dep_lines": [1415, 1416, 1417, 1418, 1419, 1420, 1422, 1429, 1415, 1459, 1460, 1461, 1464, 1468, 1469, 1470, 1471, 1472, 1473, 1474, 1476, 1490, 1454, 1455, 1457, 1458, 1459, 1477, 1402, 1404, 1405, 1406, 1407, 1454, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 5, 20, 10, 10, 10, 5, 10, 5, 10, 10, 10, 10, 10, 5, 5, 10, 10, 5, 10, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.array", "sqlalchemy.dialects.postgresql.hstore", "sqlalchemy.dialects.postgresql.json", "sqlalchemy.dialects.postgresql.pg_catalog", "sqlalchemy.dialects.postgresql.ranges", "sqlalchemy.dialects.postgresql.ext", "sqlalchemy.dialects.postgresql.named_types", "sqlalchemy.dialects.postgresql.types", "sqlalchemy.dialects.postgresql", "sqlalchemy.engine.characteristics", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.reflection", "sqlalchemy.sql.coercions", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.expression", "sqlalchemy.sql.roles", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.util", "sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.exc", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.types", "__future__", "collections", "functools", "re", "typing", "sqlalchemy", "builtins", "_decimal", "_typeshed", "abc", "enum", "sqlalchemy.engine.base", "sqlalchemy.engine.url", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types", "typing_extensions", "uuid"], "hash": "0c685aaae14958340bef022f436104e7b2f10fbcc6474e81290c6f3591c52e06", "id": "sqlalchemy.dialects.postgresql.base", "ignore_all": true, "interface_hash": "76c5d2e75906de01a7902f76bc07137f7ca174b5a8bcfaed6663ebe2deb70710", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/base.py", "plugin_data": null, "size": 175634, "suppressed": [], "version_id": "1.7.1"}