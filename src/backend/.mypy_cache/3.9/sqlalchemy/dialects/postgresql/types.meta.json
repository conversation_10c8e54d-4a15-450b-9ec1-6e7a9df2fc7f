{"data_mtime": 1754228260, "dep_lines": [16, 17, 18, 21, 22, 16, 6, 8, 9, 14, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 25, 25, 20, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "sqlalchemy.util.typing", "sqlalchemy.engine.interfaces", "sqlalchemy.sql.operators", "sqlalchemy.sql", "__future__", "datetime", "typing", "uuid", "builtins", "abc", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.base", "sqlalchemy.sql.visitors"], "hash": "976e2bb3cfe72b8bea2f2402d1a5247f84bb79cc3a4ffecf82ae7421c739081b", "id": "sqlalchemy.dialects.postgresql.types", "ignore_all": true, "interface_hash": "d3c9d5785b4aa6000c979cab2ab13b45e5cfa8312e5d7b59807a647f5538edc6", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/types.py", "plugin_data": null, "size": 7292, "suppressed": [], "version_id": "1.7.1"}