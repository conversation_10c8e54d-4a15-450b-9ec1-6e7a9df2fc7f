{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.postgresql", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ARRAY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.array.ARRAY", "kind": "Gdef"}, "AbstractMultiRange": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ranges.AbstractMultiRange", "kind": "Gdef", "module_public": false}, "AbstractRange": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ranges.AbstractRange", "kind": "Gdef", "module_public": false}, "All": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.array.All", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BIGINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BIGINT", "kind": "Gdef"}, "BIT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.BIT", "kind": "Gdef"}, "BOOLEAN": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BOOLEAN", "kind": "Gdef"}, "BYTEA": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.BYTEA", "kind": "Gdef"}, "CHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.CHAR", "kind": "Gdef"}, "CIDR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.CIDR", "kind": "Gdef"}, "CITEXT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.CITEXT", "kind": "Gdef"}, "CreateDomainType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.named_types.CreateDomainType", "kind": "Gdef"}, "CreateEnumType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.named_types.CreateEnumType", "kind": "Gdef"}, "DATE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DATE", "kind": "Gdef"}, "DATEMULTIRANGE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ranges.DATEMULTIRANGE", "kind": "Gdef"}, "DATERANGE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ranges.DATERANGE", "kind": "Gdef"}, "DOMAIN": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.named_types.DOMAIN", "kind": "Gdef"}, "DOUBLE_PRECISION": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DOUBLE_PRECISION", "kind": "Gdef"}, "DropDomainType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.named_types.DropDomainType", "kind": "Gdef"}, "DropEnumType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.named_types.DropEnumType", "kind": "Gdef"}, "ENUM": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.named_types.ENUM", "kind": "Gdef"}, "ExcludeConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ext.ExcludeConstraint", "kind": "Gdef"}, "FLOAT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.FLOAT", "kind": "Gdef"}, "HSTORE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.hstore.HSTORE", "kind": "Gdef"}, "INET": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.INET", "kind": "Gdef"}, "INT4MULTIRANGE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ranges.INT4MULTIRANGE", "kind": "Gdef"}, "INT4RANGE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ranges.INT4RANGE", "kind": "Gdef"}, "INT8MULTIRANGE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ranges.INT8MULTIRANGE", "kind": "Gdef"}, "INT8RANGE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ranges.INT8RANGE", "kind": "Gdef"}, "INTEGER": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.INTEGER", "kind": "Gdef"}, "INTERVAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.INTERVAL", "kind": "Gdef"}, "Insert": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.dml.Insert", "kind": "Gdef"}, "JSON": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.json.JSON", "kind": "Gdef"}, "JSONB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.json.JSONB", "kind": "Gdef"}, "JSONPATH": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.json.JSONPATH", "kind": "Gdef"}, "MACADDR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.MACADDR", "kind": "Gdef"}, "MACADDR8": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.MACADDR8", "kind": "Gdef"}, "MONEY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.MONEY", "kind": "Gdef"}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef", "module_public": false}, "NUMERIC": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.NUMERIC", "kind": "Gdef"}, "NUMMULTIRANGE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ranges.NUMMULTIRANGE", "kind": "Gdef"}, "NUMRANGE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ranges.NUMRANGE", "kind": "Gdef"}, "NamedType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.named_types.NamedType", "kind": "Gdef"}, "OID": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.OID", "kind": "Gdef"}, "REAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.REAL", "kind": "Gdef"}, "REGCLASS": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.REGCLASS", "kind": "Gdef"}, "REGCONFIG": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.REGCONFIG", "kind": "Gdef"}, "Range": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ranges.Range", "kind": "Gdef"}, "SMALLINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.SMALLINT", "kind": "Gdef"}, "TEXT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.TEXT", "kind": "Gdef"}, "TIME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.TIME", "kind": "Gdef"}, "TIMESTAMP": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.TIMESTAMP", "kind": "Gdef"}, "TSMULTIRANGE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ranges.TSMULTIRANGE", "kind": "Gdef"}, "TSQUERY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.TSQUERY", "kind": "Gdef"}, "TSRANGE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ranges.TSRANGE", "kind": "Gdef"}, "TSTZMULTIRANGE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ranges.TSTZMULTIRANGE", "kind": "Gdef"}, "TSTZRANGE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ranges.TSTZRANGE", "kind": "Gdef"}, "TSVECTOR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types.TSVECTOR", "kind": "Gdef"}, "UUID": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.UUID", "kind": "Gdef"}, "VARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.VARCHAR", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "aggregate_order_by": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ext.aggregate_order_by", "kind": "Gdef"}, "array": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.array.array", "kind": "Gdef"}, "array_agg": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ext.array_agg", "kind": "Gdef"}, "asyncpg": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.asyncpg", "kind": "Gdef", "module_public": false}, "base": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.base", "kind": "Gdef", "module_public": false}, "dialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.dialect", "name": "dialect", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["executemany_mode", "executemany_batch_page_size", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["sqlalchemy.dialects.postgresql.psycopg2.PGDialect_psycopg2"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.postgresql.psycopg2.PGDialect_psycopg2", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "hstore": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.hstore.hstore", "kind": "Gdef"}, "insert": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.dml.insert", "kind": "Gdef"}, "pg8000": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.pg8000", "kind": "Gdef", "module_public": false}, "phraseto_tsquery": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ext.phraseto_tsquery", "kind": "Gdef", "module_public": false}, "plainto_tsquery": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ext.plainto_tsquery", "kind": "Gdef", "module_public": false}, "psycopg": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.psycopg", "kind": "Gdef", "module_public": false}, "psycopg2": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.psycopg2", "kind": "Gdef", "module_public": false}, "psycopg2cffi": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.psycopg2cffi", "kind": "Gdef", "module_public": false}, "psycopg_async": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql.psycopg_async", "name": "psycopg_async", "type": "builtins.type"}}, "to_tsquery": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ext.to_tsquery", "kind": "Gdef", "module_public": false}, "to_tsvector": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ext.to_tsvector", "kind": "Gdef", "module_public": false}, "ts_headline": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ext.ts_headline", "kind": "Gdef", "module_public": false}, "websearch_to_tsquery": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ext.websearch_to_tsquery", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/__init__.py"}