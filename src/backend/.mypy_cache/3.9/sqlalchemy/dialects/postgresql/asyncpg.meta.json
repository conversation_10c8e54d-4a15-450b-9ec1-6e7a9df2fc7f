{"data_mtime": 1754228260, "dep_lines": [193, 194, 195, 196, 208, 193, 215, 216, 217, 211, 212, 213, 214, 216, 183, 185, 186, 187, 188, 189, 190, 211, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 989], "dep_prios": [10, 10, 5, 5, 5, 20, 10, 10, 5, 10, 10, 10, 5, 20, 5, 10, 10, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["sqlalchemy.dialects.postgresql.json", "sqlalchemy.dialects.postgresql.ranges", "sqlalchemy.dialects.postgresql.array", "sqlalchemy.dialects.postgresql.base", "sqlalchemy.dialects.postgresql.types", "sqlalchemy.dialects.postgresql", "sqlalchemy.engine.processors", "sqlalchemy.sql.sqltypes", "sqlalchemy.util.concurrency", "sqlalchemy.exc", "sqlalchemy.pool", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.sql", "__future__", "collections", "decimal", "json", "re", "time", "typing", "sqlalchemy", "builtins", "_decimal", "_typeshed", "abc", "asyncio", "asyncio.events", "asyncio.locks", "enum", "sqlalchemy.dialects.postgresql.named_types", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._concurrency_py3k", "sqlalchemy.util.langhelpers", "typing_extensions"], "hash": "5cd6a8399e436b8fa35136844b5cc4393116dd61bc50ac950a8212dcbb058731", "id": "sqlalchemy.dialects.postgresql.asyncpg", "ignore_all": true, "interface_hash": "78258e23c7daeb86f39fdef4a13180aeadaaa42e37f6fd5fec7f3c5952694692", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", "plugin_data": null, "size": 39967, "suppressed": ["asyncpg"], "version_id": "1.7.1"}