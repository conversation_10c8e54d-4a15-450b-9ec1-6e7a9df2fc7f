{"data_mtime": 1754228260, "dep_lines": [14, 15, 14, 16, 17, 18, 19, 20, 21, 23, 24, 16, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 10, 10, 10, 10, 5, 5, 5, 20, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.types", "sqlalchemy.dialects.postgresql.array", "sqlalchemy.dialects.postgresql", "sqlalchemy.sql.coercions", "sqlalchemy.sql.elements", "sqlalchemy.sql.expression", "sqlalchemy.sql.functions", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.visitors", "sqlalchemy.sql", "__future__", "typing", "builtins", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util", "sqlalchemy.util.langhelpers", "typing_extensions"], "hash": "a0f3f6d8fabe9f694c990f1a8627d89ac9b34614a24afd5157ec6b4d3d20c9cc", "id": "sqlalchemy.dialects.postgresql.ext", "ignore_all": true, "interface_hash": "46ce441890eddcbd42378a625c78eb3b4743c51083a421712415b42859bbbf2a", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/ext.py", "plugin_data": null, "size": 16253, "suppressed": [], "version_id": "1.7.1"}