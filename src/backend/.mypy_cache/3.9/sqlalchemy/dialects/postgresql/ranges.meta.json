{"data_mtime": 1754228260, "dep_lines": [25, 34, 35, 37, 40, 33, 34, 36, 7, 9, 10, 13, 14, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 25, 10, 20, 5, 5, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.operators", "sqlalchemy.sql.operators", "sqlalchemy.sql.type_api", "sqlalchemy.util.typing", "sqlalchemy.sql.elements", "sqlalchemy.types", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "dataclasses", "datetime", "decimal", "typing", "sqlalchemy", "builtins", "_decimal", "abc", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util.compat", "sqlalchemy.util.langhelpers"], "hash": "c87075051954ade40f641dd5127d0a30c2dfd36ce37f98e361d9a0e0de12d92c", "id": "sqlalchemy.dialects.postgresql.ranges", "ignore_all": true, "interface_hash": "4fe1dc1c487b1e94ed1c55e1b6db2a6a3dcaacd3c4f6bbc6d49f97d1d50700d4", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/ranges.py", "plugin_data": null, "size": 30220, "suppressed": [], "version_id": "1.7.1"}