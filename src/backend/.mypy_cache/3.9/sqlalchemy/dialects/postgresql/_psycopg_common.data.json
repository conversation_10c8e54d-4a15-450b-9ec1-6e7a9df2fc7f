{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.postgresql._psycopg_common", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "HSTORE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.hstore.HSTORE", "kind": "Gdef"}, "INT2VECTOR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.pg_catalog.INT2VECTOR", "kind": "Gdef"}, "OIDVECTOR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.pg_catalog.OIDVECTOR", "kind": "Gdef"}, "PGARRAY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.array.ARRAY", "kind": "Gdef"}, "PGDialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.base.PGDialect", "kind": "Gdef"}, "PGExecutionContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.base.PGExecutionContext", "kind": "Gdef"}, "_DECIMAL_TYPES": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types._DECIMAL_TYPES", "kind": "Gdef"}, "_FLOAT_TYPES": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types._FLOAT_TYPES", "kind": "Gdef"}, "_INT_TYPES": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.types._INT_TYPES", "kind": "Gdef"}, "_PGDialect_common_psycopg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.postgresql.base.PGDialect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg", "name": "_PGDialect_common_psycopg", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql._psycopg_common", "mro": ["sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg", "sqlalchemy.dialects.postgresql.base.PGDialect", "sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "client_encoding", "use_native_hstore", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg.__init__", "name": "__init__", "type": null}}, "_do_autocommit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg._do_autocommit", "name": "_do_autocommit", "type": null}}, "_has_native_hstore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg._has_native_hstore", "name": "_has_native_hstore", "type": "builtins.bool"}}, "client_encoding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg.client_encoding", "name": "client_encoding", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "colspecs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg.colspecs", "name": "colspecs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create_connect_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg.create_connect_args", "name": "create_connect_args", "type": null}}, "default_paramstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg.default_paramstyle", "name": "default_paramstyle", "type": "builtins.str"}}, "do_ping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg.do_ping", "name": "do_ping", "type": null}}, "get_deferrable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg.get_deferrable", "name": "get_deferrable", "type": null}}, "get_isolation_level_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg.get_isolation_level_values", "name": "get_isolation_level_values", "type": null}}, "set_deferrable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg.set_deferrable", "name": "set_deferrable", "type": null}}, "supports_server_side_cursors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg.supports_server_side_cursors", "name": "supports_server_side_cursors", "type": "builtins.bool"}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}, "use_native_hstore": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg.use_native_hstore", "name": "use_native_hstore", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql._psycopg_common._PGDialect_common_psycopg", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_PGExecutionContext_common_psycopg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.postgresql.base.PGExecutionContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGExecutionContext_common_psycopg", "name": "_PGExecutionContext_common_psycopg", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGExecutionContext_common_psycopg", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql._psycopg_common", "mro": ["sqlalchemy.dialects.postgresql._psycopg_common._PGExecutionContext_common_psycopg", "sqlalchemy.dialects.postgresql.base.PGExecutionContext", "sqlalchemy.engine.default.DefaultExecutionContext", "sqlalchemy.engine.interfaces.ExecutionContext", "builtins.object"], "names": {".class": "SymbolTable", "create_server_side_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGExecutionContext_common_psycopg.create_server_side_cursor", "name": "create_server_side_cursor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PGExecutionContext_common_psycopg.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql._psycopg_common._PGExecutionContext_common_psycopg", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_PsycopgARRAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.postgresql.array.ARRAY"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgARRAY", "name": "_PsycopgARRAY", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgARRAY", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql._psycopg_common", "mro": ["sqlalchemy.dialects.postgresql._psycopg_common._PsycopgARRAY", "sqlalchemy.dialects.postgresql.array.ARRAY", "sqlalchemy.sql.sqltypes.ARRAY", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.sqltypes.Indexable", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "render_bind_cast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgARRAY.render_bind_cast", "name": "render_bind_cast", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgARRAY.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgARRAY", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_PsycopgFloat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.postgresql._psycopg_common._PsycopgNumeric"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgFloat", "name": "_PsycopgFloat", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgFloat", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql._psycopg_common", "mro": ["sqlalchemy.dialects.postgresql._psycopg_common._PsycopgFloat", "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgNumeric", "sqlalchemy.sql.sqltypes.Numeric", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgFloat.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgFloat.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgFloat", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_PsycopgHStore": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.postgresql.hstore.HSTORE"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgHStore", "name": "_PsycopgHStore", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgHStore", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql._psycopg_common", "mro": ["sqlalchemy.dialects.postgresql._psycopg_common._PsycopgHStore", "sqlalchemy.dialects.postgresql.hstore.HSTORE", "sqlalchemy.sql.sqltypes.Indexable", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "bind_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgHStore.bind_processor", "name": "bind_processor", "type": null}}, "result_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "coltype"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgHStore.result_processor", "name": "result_processor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgHStore.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgHStore", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_PsycopgINT2VECTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.postgresql.pg_catalog._SpaceVector", "sqlalchemy.dialects.postgresql.pg_catalog.INT2VECTOR"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgINT2VECTOR", "name": "_PsycopgINT2VECTOR", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgINT2VECTOR", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql._psycopg_common", "mro": ["sqlalchemy.dialects.postgresql._psycopg_common._PsycopgINT2VECTOR", "sqlalchemy.dialects.postgresql.pg_catalog._SpaceVector", "sqlalchemy.dialects.postgresql.pg_catalog.INT2VECTOR", "sqlalchemy.sql.type_api.TypeDecorator", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.type_api.ExternalType", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgINT2VECTOR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgINT2VECTOR", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_PsycopgNumeric": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.sqltypes.Numeric"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgNumeric", "name": "_PsycopgNumeric", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgNumeric", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql._psycopg_common", "mro": ["sqlalchemy.dialects.postgresql._psycopg_common._PsycopgNumeric", "sqlalchemy.sql.sqltypes.Numeric", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "bind_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgNumeric.bind_processor", "name": "bind_processor", "type": null}}, "result_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "coltype"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgNumeric.result_processor", "name": "result_processor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgNumeric.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgNumeric", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_PsycopgOIDVECTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.postgresql.pg_catalog._SpaceVector", "sqlalchemy.dialects.postgresql.pg_catalog.OIDVECTOR"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgOIDVECTOR", "name": "_PsycopgOIDVECTOR", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgOIDVECTOR", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.postgresql._psycopg_common", "mro": ["sqlalchemy.dialects.postgresql._psycopg_common._PsycopgOIDVECTOR", "sqlalchemy.dialects.postgresql.pg_catalog._SpaceVector", "sqlalchemy.dialects.postgresql.pg_catalog.OIDVECTOR", "sqlalchemy.sql.type_api.TypeDecorator", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "sqlalchemy.sql.type_api.ExternalType", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgOIDVECTOR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.postgresql._psycopg_common._PsycopgOIDVECTOR", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SpaceVector": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.pg_catalog._SpaceVector", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common.__package__", "name": "__package__", "type": "builtins.str"}}, "_server_side_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.postgresql._psycopg_common._server_side_id", "name": "_server_side_id", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "decimal": {".class": "SymbolTableNode", "cross_ref": "decimal", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "processors": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.processors", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.types", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/postgresql/_psycopg_common.py"}