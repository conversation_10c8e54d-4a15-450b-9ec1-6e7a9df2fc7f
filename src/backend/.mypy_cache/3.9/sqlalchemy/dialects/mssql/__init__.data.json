{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.mssql", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BIGINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BIGINT", "kind": "Gdef"}, "BINARY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BINARY", "kind": "Gdef"}, "BIT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.BIT", "kind": "Gdef"}, "CHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.CHAR", "kind": "Gdef"}, "DATE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DATE", "kind": "Gdef"}, "DATETIME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DATETIME", "kind": "Gdef"}, "DATETIME2": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.DATETIME2", "kind": "Gdef"}, "DATETIMEOFFSET": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.DATETIMEOFFSET", "kind": "Gdef"}, "DECIMAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DECIMAL", "kind": "Gdef"}, "DOUBLE_PRECISION": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.DOUBLE_PRECISION", "kind": "Gdef"}, "FLOAT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.FLOAT", "kind": "Gdef"}, "IMAGE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.IMAGE", "kind": "Gdef"}, "INTEGER": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.INTEGER", "kind": "Gdef"}, "JSON": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.json.JSON", "kind": "Gdef"}, "MONEY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.MONEY", "kind": "Gdef"}, "NCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.NCHAR", "kind": "Gdef"}, "NTEXT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.NTEXT", "kind": "Gdef"}, "NUMERIC": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.NUMERIC", "kind": "Gdef"}, "NVARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.NVARCHAR", "kind": "Gdef"}, "REAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.REAL", "kind": "Gdef"}, "ROWVERSION": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.ROWVERSION", "kind": "Gdef"}, "SMALLDATETIME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.SMALLDATETIME", "kind": "Gdef"}, "SMALLINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.SMALLINT", "kind": "Gdef"}, "SMALLMONEY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.SMALLMONEY", "kind": "Gdef"}, "SQL_VARIANT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.SQL_VARIANT", "kind": "Gdef"}, "TEXT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.TEXT", "kind": "Gdef"}, "TIME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.TIME", "kind": "Gdef"}, "TIMESTAMP": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.TIMESTAMP", "kind": "Gdef"}, "TINYINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.TINYINT", "kind": "Gdef"}, "UNIQUEIDENTIFIER": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.UNIQUEIDENTIFIER", "kind": "Gdef"}, "VARBINARY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.VARBINARY", "kind": "Gdef"}, "VARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.VARCHAR", "kind": "Gdef"}, "XML": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.XML", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "aioodbc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.aioodbc", "kind": "Gdef", "module_public": false}, "base": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base", "kind": "Gdef", "module_public": false}, "dialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.dialect", "name": "dialect", "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["fast_executemany", "use_setinputsizes", "params"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "pymssql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.pymssql", "kind": "Gdef", "module_public": false}, "pyodbc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.pyodbc", "kind": "Gdef", "module_public": false}, "try_cast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.try_cast", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/mssql/__init__.py"}