{"data_mtime": 1754228260, "dep_lines": [944, 945, 944, 955, 956, 957, 959, 960, 961, 962, 965, 966, 968, 969, 987, 990, 991, 948, 950, 952, 954, 955, 972, 934, 936, 937, 938, 939, 940, 942, 948, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 10, 5, 10, 5, 5, 10, 10, 10, 10, 5, 5, 25, 25, 10, 10, 5, 5, 20, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.mssql.information_schema", "sqlalchemy.dialects.mssql.json", "sqlalchemy.dialects.mssql", "sqlalchemy.engine.cursor", "sqlalchemy.engine.default", "sqlalchemy.engine.reflection", "sqlalchemy.sql.coercions", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.expression", "sqlalchemy.sql.roles", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.util", "sqlalchemy.sql._typing", "sqlalchemy.util.typing", "sqlalchemy.sql.dml", "sqlalchemy.sql.selectable", "sqlalchemy.exc", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.types", "__future__", "codecs", "datetime", "operator", "re", "typing", "uuid", "sqlalchemy", "builtins", "_decimal", "_typeshed", "abc", "enum", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._elements_constructors", "sqlalchemy.sql._selectable_constructors", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.functions", "sqlalchemy.sql.operators", "sqlalchemy.sql.schema", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "types", "typing_extensions"], "hash": "97dbd7e9f2ba0c9118034d0df6e0e7bd26ea7e0bf15df6149f60b8005e53f76d", "id": "sqlalchemy.dialects.mssql.base", "ignore_all": true, "interface_hash": "5d4c00accc75cbc5a9f681fdaed0ced76d730b82159f7c79a16cdf27b718a2e9", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/mssql/base.py", "plugin_data": null, "size": 133649, "suppressed": [], "version_id": "1.7.1"}