{"data_mtime": 1754228260, "dep_lines": [353, 361, 367, 368, 364, 365, 366, 368, 348, 349, 350, 351, 364, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 20, 10, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.mssql.base", "sqlalchemy.dialects.mssql.json", "sqlalchemy.connectors.pyodbc", "sqlalchemy.engine.cursor", "sqlalchemy.exc", "sqlalchemy.types", "sqlalchemy.util", "sqlalchemy.engine", "datetime", "decimal", "re", "struct", "sqlalchemy", "builtins", "_decimal", "_typeshed", "abc", "sqlalchemy.connectors", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql", "sqlalchemy.sql.base", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "typing", "typing_extensions"], "hash": "ab067c07239367558e6e3c5e3ab6afa09052057face1127f3d9e5527f0a1e56b", "id": "sqlalchemy.dialects.mssql.pyodbc", "ignore_all": true, "interface_hash": "7c3b3e1343c41435064180d9fb99fd88eb2d414a5ac87e86dac9fdb415df9675", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/mssql/pyodbc.py", "plugin_data": null, "size": 27048, "suppressed": [], "version_id": "1.7.1"}