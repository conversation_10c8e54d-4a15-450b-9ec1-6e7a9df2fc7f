{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.mssql.pyodbc", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BINARY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BINARY", "kind": "Gdef"}, "DATETIMEOFFSET": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.DATETIMEOFFSET", "kind": "Gdef"}, "MSDialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.MSDialect", "kind": "Gdef"}, "MSDialect_pyodbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.connectors.pyodbc.PyODBCConnector", "sqlalchemy.dialects.mssql.base.MSDialect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc", "name": "MSDialect_pyodbc", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc", "sqlalchemy.connectors.pyodbc.PyODBCConnector", "sqlalchemy.connectors.Connector", "sqlalchemy.dialects.mssql.base.MSDialect", "sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "fast_executemany", "use_setinputsizes", "params"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc.__init__", "name": "__init__", "type": null}}, "_get_server_version_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc._get_server_version_info", "name": "_get_server_version_info", "type": null}}, "_need_decimal_fix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc._need_decimal_fix", "name": "_need_decimal_fix", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_setup_timestampoffset_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc._setup_timestampoffset_type", "name": "_setup_timestampoffset_type", "type": null}}, "colspecs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc.colspecs", "name": "colspecs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "do_executemany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "cursor", "statement", "parameters", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc.do_executemany", "name": "do_executemany", "type": null}}, "execution_ctx_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc.execution_ctx_cls", "name": "execution_ctx_cls", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["sqlalchemy.dialects.mssql.pyodbc.MSExecutionContext_pyodbc"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.mssql.pyodbc.MSExecutionContext_pyodbc", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "is_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "e", "connection", "cursor"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc.is_disconnect", "name": "is_disconnect", "type": null}}, "on_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc.on_connect", "name": "on_connect", "type": null}}, "supports_sane_rowcount_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc.supports_sane_rowcount_returning", "name": "supports_sane_rowcount_returning", "type": "builtins.bool"}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MSExecutionContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.MSExecutionContext", "kind": "Gdef"}, "MSExecutionContext_pyodbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mssql.base.MSExecutionContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSExecutionContext_pyodbc", "name": "MSExecutionContext_pyodbc", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSExecutionContext_pyodbc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc.MSExecutionContext_pyodbc", "sqlalchemy.dialects.mssql.base.MSExecutionContext", "sqlalchemy.engine.default.DefaultExecutionContext", "sqlalchemy.engine.interfaces.ExecutionContext", "builtins.object"], "names": {".class": "SymbolTable", "_embedded_scope_identity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSExecutionContext_pyodbc._embedded_scope_identity", "name": "_embedded_scope_identity", "type": "builtins.bool"}}, "post_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSExecutionContext_pyodbc.post_exec", "name": "post_exec", "type": null}}, "pre_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSExecutionContext_pyodbc.pre_exec", "name": "pre_exec", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc.MSExecutionContext_pyodbc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc.MSExecutionContext_pyodbc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PyODBCConnector": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.connectors.pyodbc.PyODBCConnector", "kind": "Gdef"}, "VARBINARY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.VARBINARY", "kind": "Gdef"}, "_BINARY_pyodbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mssql.pyodbc._ms_binary_pyodbc", "sqlalchemy.sql.sqltypes.BINARY"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc._BINARY_pyodbc", "name": "_BINARY_pyodbc", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._BINARY_pyodbc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc._BINARY_pyodbc", "sqlalchemy.dialects.mssql.pyodbc._ms_binary_pyodbc", "sqlalchemy.sql.sqltypes.BINARY", "sqlalchemy.sql.sqltypes._Binary", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc._BINARY_pyodbc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc._BINARY_pyodbc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_JSONIndexType_pyodbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mssql.json.JSONIndexType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc._JSONIndexType_pyodbc", "name": "_JSONIndexType_pyodbc", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._JSONIndexType_pyodbc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc._JSONIndexType_pyodbc", "sqlalchemy.dialects.mssql.json.JSONIndexType", "sqlalchemy.dialects.mssql.json._FormatTypeMixin", "sqlalchemy.sql.sqltypes.JSON.JSONIndexType", "sqlalchemy.sql.sqltypes.JSON.JSONElementType", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "get_dbapi_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._JSONIndexType_pyodbc.get_dbapi_type", "name": "get_dbapi_type", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc._JSONIndexType_pyodbc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc._JSONIndexType_pyodbc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_JSONPathType_pyodbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mssql.json.JSONPathType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc._JSONPathType_pyodbc", "name": "_JSONPathType_pyodbc", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._JSONPathType_pyodbc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc._JSONPathType_pyodbc", "sqlalchemy.dialects.mssql.json.JSONPathType", "sqlalchemy.dialects.mssql.json._FormatTypeMixin", "sqlalchemy.sql.sqltypes.JSON.JSONPathType", "sqlalchemy.sql.sqltypes.JSON.JSONElementType", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "get_dbapi_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._JSONPathType_pyodbc.get_dbapi_type", "name": "get_dbapi_type", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc._JSONPathType_pyodbc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc._JSONPathType_pyodbc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_JSON_pyodbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mssql.json.JSON"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc._JSON_pyodbc", "name": "_JSON_pyodbc", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._JSON_pyodbc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc._JSON_pyodbc", "sqlalchemy.dialects.mssql.json.JSON", "sqlalchemy.sql.sqltypes.JSON", "sqlalchemy.sql.sqltypes.Indexable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "get_dbapi_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._JSON_pyodbc.get_dbapi_type", "name": "get_dbapi_type", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc._JSON_pyodbc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc._JSON_pyodbc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MSDateTime": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base._MSDateTime", "kind": "Gdef"}, "_MSFloat_pyodbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mssql.pyodbc._ms_numeric_pyodbc", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.sqltypes.Float"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc._MSFloat_pyodbc", "name": "_MSFloat_pyodbc", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._MSFloat_pyodbc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc._MSFloat_pyodbc", "sqlalchemy.dialects.mssql.pyodbc._ms_numeric_pyodbc", "sqlalchemy.sql.sqltypes.Float", "sqlalchemy.sql.sqltypes.Numeric", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc._MSFloat_pyodbc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc._MSFloat_pyodbc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MSJson": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.json.JSON", "kind": "Gdef"}, "_MSJsonIndexType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.json.JSONIndexType", "kind": "Gdef"}, "_MSJsonPathType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.json.JSONPathType", "kind": "Gdef"}, "_MSNumeric_pyodbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mssql.pyodbc._ms_numeric_pyodbc", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.sqltypes.Numeric"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc._MSNumeric_pyodbc", "name": "_MSNumeric_pyodbc", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._MSNumeric_pyodbc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc._MSNumeric_pyodbc", "sqlalchemy.dialects.mssql.pyodbc._ms_numeric_pyodbc", "sqlalchemy.sql.sqltypes.Numeric", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc._MSNumeric_pyodbc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc._MSNumeric_pyodbc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MSUnicode": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base._MSUnicode", "kind": "Gdef"}, "_MSUnicodeText": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base._MSUnicodeText", "kind": "Gdef"}, "_ODBCDATETIMEOFFSET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mssql.pyodbc._ODBCDateTimeBindProcessor", "sqlalchemy.dialects.mssql.base.DATETIMEOFFSET"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc._ODBCDATETIMEOFFSET", "name": "_ODBCDATETIMEOFFSET", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._ODBCDATETIMEOFFSET", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc._ODBCDATETIMEOFFSET", "sqlalchemy.dialects.mssql.pyodbc._ODBCDateTimeBindProcessor", "sqlalchemy.dialects.mssql.base.DATETIMEOFFSET", "sqlalchemy.dialects.mssql.base._DateTimeBase", "sqlalchemy.sql.sqltypes.DateTime", "sqlalchemy.sql.sqltypes._RenderISO8601NoT", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "has_tz": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.pyodbc._ODBCDATETIMEOFFSET.has_tz", "name": "has_tz", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc._ODBCDATETIMEOFFSET.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc._ODBCDATETIMEOFFSET", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ODBCDateTime": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mssql.pyodbc._ODBCDateTimeBindProcessor", "sqlalchemy.dialects.mssql.base._MSDateTime"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc._ODBCDateTime", "name": "_ODBCDateTime", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._ODBCDateTime", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc._ODBCDateTime", "sqlalchemy.dialects.mssql.pyodbc._ODBCDateTimeBindProcessor", "sqlalchemy.dialects.mssql.base._MSDateTime", "sqlalchemy.dialects.mssql.base._DateTimeBase", "sqlalchemy.sql.sqltypes.DateTime", "sqlalchemy.sql.sqltypes._RenderISO8601NoT", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc._ODBCDateTime.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc._ODBCDateTime", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ODBCDateTimeBindProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc._ODBCDateTimeBindProcessor", "name": "_ODBCDateTimeBindProcessor", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._ODBCDateTimeBindProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc._ODBCDateTimeBindProcessor", "builtins.object"], "names": {".class": "SymbolTable", "bind_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._ODBCDateTimeBindProcessor.bind_processor", "name": "bind_processor", "type": null}}, "has_tz": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mssql.pyodbc._ODBCDateTimeBindProcessor.has_tz", "name": "has_tz", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc._ODBCDateTimeBindProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc._ODBCDateTimeBindProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_String_pyodbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.String"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc._String_pyodbc", "name": "_String_pyodbc", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._String_pyodbc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc._String_pyodbc", "sqlalchemy.sql.sqltypes.String", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "get_dbapi_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._String_pyodbc.get_dbapi_type", "name": "get_dbapi_type", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc._String_pyodbc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc._String_pyodbc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_UnicodeText_pyodbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mssql.base._MSUnicodeText"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc._UnicodeText_pyodbc", "name": "_UnicodeText_pyodbc", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._UnicodeText_pyodbc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc._UnicodeText_pyodbc", "sqlalchemy.dialects.mssql.base._MSUnicodeText", "sqlalchemy.dialects.mssql.base._UnicodeLiteral", "sqlalchemy.sql.sqltypes.UnicodeText", "sqlalchemy.sql.sqltypes.Text", "sqlalchemy.sql.sqltypes.String", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "get_dbapi_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._UnicodeText_pyodbc.get_dbapi_type", "name": "get_dbapi_type", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc._UnicodeText_pyodbc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc._UnicodeText_pyodbc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Unicode_pyodbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mssql.base._MSUnicode"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc._Unicode_pyodbc", "name": "_Unicode_pyodbc", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._Unicode_pyodbc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc._Unicode_pyodbc", "sqlalchemy.dialects.mssql.base._MSUnicode", "sqlalchemy.dialects.mssql.base._UnicodeLiteral", "sqlalchemy.sql.sqltypes.Unicode", "sqlalchemy.sql.sqltypes.String", "sqlalchemy.sql.sqltypes.Concatenable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "get_dbapi_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._Unicode_pyodbc.get_dbapi_type", "name": "get_dbapi_type", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc._Unicode_pyodbc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc._Unicode_pyodbc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_VARBINARY_pyodbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mssql.pyodbc._ms_binary_pyodbc", "sqlalchemy.dialects.mssql.base.VARBINARY"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc._VARBINARY_pyodbc", "name": "_VARBINARY_pyodbc", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._VARBINARY_pyodbc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc._VARBINARY_pyodbc", "sqlalchemy.dialects.mssql.pyodbc._ms_binary_pyodbc", "sqlalchemy.dialects.mssql.base.VARBINARY", "sqlalchemy.sql.sqltypes.VARBINARY", "sqlalchemy.sql.sqltypes.LargeBinary", "sqlalchemy.sql.sqltypes._Binary", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc._VARBINARY_pyodbc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc._VARBINARY_pyodbc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.pyodbc.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.pyodbc.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.pyodbc.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.pyodbc.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mssql.pyodbc.__package__", "name": "__package__", "type": "builtins.str"}}, "_cursor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor", "kind": "Gdef"}, "_ms_binary_pyodbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc._ms_binary_pyodbc", "name": "_ms_binary_pyodbc", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._ms_binary_pyodbc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc._ms_binary_pyodbc", "builtins.object"], "names": {".class": "SymbolTable", "bind_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._ms_binary_pyodbc.bind_processor", "name": "bind_processor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc._ms_binary_pyodbc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc._ms_binary_pyodbc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ms_numeric_pyodbc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mssql.pyodbc._ms_numeric_pyodbc", "name": "_ms_numeric_pyodbc", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._ms_numeric_pyodbc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mssql.pyodbc", "mro": ["sqlalchemy.dialects.mssql.pyodbc._ms_numeric_pyodbc", "builtins.object"], "names": {".class": "SymbolTable", "_large_dec_to_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._ms_numeric_pyodbc._large_dec_to_string", "name": "_large_dec_to_string", "type": null}}, "_small_dec_to_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._ms_numeric_pyodbc._small_dec_to_string", "name": "_small_dec_to_string", "type": null}}, "bind_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.dialects.mssql.pyodbc._ms_numeric_pyodbc.bind_processor", "name": "bind_processor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mssql.pyodbc._ms_numeric_pyodbc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mssql.pyodbc._ms_numeric_pyodbc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "decimal": {".class": "SymbolTableNode", "cross_ref": "decimal", "kind": "Gdef"}, "dialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mssql.pyodbc.dialect", "line": 746, "no_args": true, "normalized": false, "target": "sqlalchemy.dialects.mssql.pyodbc.MSDialect_pyodbc"}}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.types", "kind": "Gdef"}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/dialects/mssql/pyodbc.py"}