{".class": "MypyFile", "_fullname": "sqlalchemy.connectors.aioodbc", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncAdaptFallback_aioodbc_connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.connectors.asyncio.AsyncAdaptFallback_dbapi_connection", "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdaptFallback_aioodbc_connection", "name": "AsyncAdaptFallback_aioodbc_connection", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdaptFallback_aioodbc_connection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.connectors.aioodbc", "mro": ["sqlalchemy.connectors.aioodbc.AsyncAdaptFallback_aioodbc_connection", "sqlalchemy.connectors.asyncio.AsyncAdaptFallback_dbapi_connection", "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection", "sqlalchemy.connectors.asyncio.AsyncAdapt_dbapi_connection", "sqlalchemy.engine.interfaces.AdaptedConnection", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdaptFallback_aioodbc_connection.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdaptFallback_aioodbc_connection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.connectors.aioodbc.AsyncAdaptFallback_aioodbc_connection", "values": [], "variance": 0}, "slots": ["_connection", "_execute_mutex", "dbapi"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncAdaptFallback_dbapi_connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.connectors.asyncio.AsyncAdaptFallback_dbapi_connection", "kind": "Gdef"}, "AsyncAdapt_aioodbc_connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.connectors.asyncio.AsyncAdapt_dbapi_connection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection", "name": "AsyncAdapt_aioodbc_connection", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.connectors.aioodbc", "mro": ["sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection", "sqlalchemy.connectors.asyncio.AsyncAdapt_dbapi_connection", "sqlalchemy.engine.interfaces.AdaptedConnection", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_cursor_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection._cursor_cls", "name": "_cursor_cls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["adapt_connection"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_cursor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_cursor", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_ss_cursor_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection._ss_cursor_cls", "name": "_ss_cursor_cls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["adapt_connection"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_ss_cursor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_ss_cursor", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "autocommit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "flags": ["is_property"], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection.autocommit", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection.autocommit", "name": "autocommit", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection.autocommit", "name": "autocommit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "autocommit of AsyncAdapt_aioodbc_connection", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection.autocommit", "name": "autocommit", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "autocommit", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "autocommit", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}]}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection.close", "name": "close", "type": null}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection.commit", "name": "commit", "type": null}}, "cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "server_side"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection.cursor", "name": "cursor", "type": null}}, "rollback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection.rollback", "name": "rollback", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_connection", "values": [], "variance": 0}, "slots": ["_connection", "_execute_mutex", "dbapi"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncAdapt_aioodbc_cursor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.connectors.asyncio.AsyncAdapt_dbapi_cursor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_cursor", "name": "AsyncAdapt_aioodbc_cursor", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_cursor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.connectors.aioodbc", "mro": ["sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_cursor", "sqlalchemy.connectors.asyncio.AsyncAdapt_dbapi_cursor", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_cursor.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "setinputsizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "inputsizes"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_cursor.setinputsizes", "name": "setinputsizes", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_cursor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_cursor", "values": [], "variance": 0}, "slots": ["_adapt_connection", "_connection", "_cursor", "_rows", "await_"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncAdapt_aioodbc_dbapi": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_dbapi", "name": "AsyncAdapt_aioodbc_dbapi", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_dbapi", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.connectors.aioodbc", "mro": ["sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_dbapi", "builtins.object"], "names": {".class": "SymbolTable", "Cursor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_dbapi.Cursor", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "aioodbc", "pyodbc"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_dbapi.__init__", "name": "__init__", "type": null}}, "_init_dbapi_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_dbapi._init_dbapi_attributes", "name": "_init_dbapi_attributes", "type": null}}, "aioodbc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_dbapi.aioodbc", "name": "aioodbc", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_dbapi.connect", "name": "connect", "type": null}}, "paramstyle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_dbapi.paramstyle", "name": "paramstyle", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pyodbc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_dbapi.pyodbc", "name": "pyodbc", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_dbapi.version", "name": "version", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_dbapi.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_dbapi", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncAdapt_aioodbc_ss_cursor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_cursor", "sqlalchemy.connectors.asyncio.AsyncAdapt_dbapi_ss_cursor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_ss_cursor", "name": "AsyncAdapt_aioodbc_ss_cursor", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_ss_cursor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.connectors.aioodbc", "mro": ["sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_ss_cursor", "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_cursor", "sqlalchemy.connectors.asyncio.AsyncAdapt_dbapi_ss_cursor", "sqlalchemy.connectors.asyncio.AsyncAdapt_dbapi_cursor", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_ss_cursor.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_ss_cursor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.connectors.aioodbc.AsyncAdapt_aioodbc_ss_cursor", "values": [], "variance": 0}, "slots": ["_adapt_connection", "_connection", "_cursor", "_rows", "await_"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncAdapt_dbapi_connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.connectors.asyncio.AsyncAdapt_dbapi_connection", "kind": "Gdef"}, "AsyncAdapt_dbapi_cursor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.connectors.asyncio.AsyncAdapt_dbapi_cursor", "kind": "Gdef"}, "AsyncAdapt_dbapi_ss_cursor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.connectors.asyncio.AsyncAdapt_dbapi_ss_cursor", "kind": "Gdef"}, "ConnectArgsType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ConnectArgsType", "kind": "Gdef"}, "PyODBCConnector": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.connectors.pyodbc.PyODBCConnector", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "URL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url.URL", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.connectors.aioodbc.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.connectors.aioodbc.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.connectors.aioodbc.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.connectors.aioodbc.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.connectors.aioodbc.__package__", "name": "__package__", "type": "builtins.str"}}, "aiodbcConnector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.connectors.pyodbc.PyODBCConnector"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.connectors.aioodbc.aiodbcConnector", "name": "aiodbcConnector", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.aiodbcConnector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.connectors.aioodbc", "mro": ["sqlalchemy.connectors.aioodbc.aiodbcConnector", "sqlalchemy.connectors.pyodbc.PyODBCConnector", "sqlalchemy.connectors.Connector", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "_do_autocommit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.aiodbcConnector._do_autocommit", "name": "_do_autocommit", "type": null}}, "_do_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "connection", "autocommit", "isolation_level"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.aiodbcConnector._do_isolation_level", "name": "_do_isolation_level", "type": null}}, "create_connect_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.aiodbcConnector.create_connect_args", "name": "create_connect_args", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["sqlalchemy.connectors.aioodbc.aiodbcConnector", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_connect_args of aiodbcConnector", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ConnectArgsType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_driver_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.aiodbcConnector.get_driver_connection", "name": "get_driver_connection", "type": null}}, "get_pool_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.connectors.aioodbc.aiodbcConnector.get_pool_class", "name": "get_pool_class", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.connectors.aioodbc.aiodbcConnector.get_pool_class", "name": "get_pool_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.connectors.aioodbc.aiodbcConnector"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_pool_class of aiodbcConnector", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "import_dbapi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.connectors.aioodbc.aiodbcConnector.import_dbapi", "name": "import_dbapi", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.connectors.aioodbc.aiodbcConnector.import_dbapi", "name": "import_dbapi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.connectors.aioodbc.aiodbcConnector"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_dbapi of aiodbcConnector", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.connectors.aioodbc.aiodbcConnector.is_async", "name": "is_async", "type": "builtins.bool"}}, "set_deferrable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.aiodbcConnector.set_deferrable", "name": "set_deferrable", "type": null}}, "set_readonly": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "value"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.connectors.aioodbc.aiodbcConnector.set_readonly", "name": "set_readonly", "type": null}}, "supports_server_side_cursors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.connectors.aioodbc.aiodbcConnector.supports_server_side_cursors", "name": "supports_server_side_cursors", "type": "builtins.bool"}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.connectors.aioodbc.aiodbcConnector.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.connectors.aioodbc.aiodbcConnector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.connectors.aioodbc.aiodbcConnector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "await_fallback": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.await_fallback", "kind": "Gdef"}, "await_only": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.await_only", "kind": "Gdef"}, "pool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/connectors/aioodbc.py"}