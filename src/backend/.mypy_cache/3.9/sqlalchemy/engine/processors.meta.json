{"data_mtime": 1754228257, "dep_lines": [19, 20, 15, 17, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 30], "dependencies": ["sqlalchemy.engine._py_processors", "sqlalchemy.util._has_cy", "__future__", "typing", "builtins", "abc"], "hash": "10d37a5f09ddc493d6f9a5cfbbfdcdcc066ccb94af3739c7a1ad509f6f44440c", "id": "sqlalchemy.engine.processors", "ignore_all": true, "interface_hash": "7f54095f4784fe9563c28ce51ef8fb0cacffb45c51a12ba19260789da65bbd0d", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/engine/processors.py", "plugin_data": null, "size": 2383, "suppressed": [], "version_id": "1.7.1"}