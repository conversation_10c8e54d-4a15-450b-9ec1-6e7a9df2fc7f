{".class": "MypyFile", "_fullname": "sqlalchemy.engine.url", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Dialect", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "URL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.url.URL", "name": "URL", "type_vars": []}, "deletable_attributes": [], "flags": ["is_named_tuple"], "fullname": "sqlalchemy.engine.url.URL", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["drivername", "username", "password", "host", "port", "database", "query"]}}, "module_name": "sqlalchemy.engine.url", "mro": ["sqlalchemy.engine.url.URL", "builtins.tuple", "typing.Sequence", "typing.Collection", "typing.Reversible", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.url.URL._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "builtins.str", "builtins.int"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.url.URL.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__copy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL.__copy__", "name": "__copy__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__copy__ of URL", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__deepcopy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "memo"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL.__deepcopy__", "name": "__deepcopy__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "memo"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__deepcopy__ of URL", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.url.URL.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of URL", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of URL", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__ne__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL.__ne__", "name": "__ne__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ne__ of URL", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "drivername", "username", "password", "host", "port", "database", "query"], "dataclass_transform_spec": null, "flags": ["is_static"], "fullname": "sqlalchemy.engine.url.URL.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "drivername", "username", "password", "host", "port", "database", "query"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.url.URL._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "builtins.str", "builtins.int"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of URL", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.url.URL._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "builtins.str", "builtins.int"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.url.URL._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "builtins.str", "builtins.int"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__to_string__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "hide_password"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.url.URL.__to_string__", "name": "__to_string__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "hide_password"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__to_string__ of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.url.URL.__to_string__", "name": "__to_string__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "hide_password"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__to_string__ of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.url.URL._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "builtins.str", "builtins.int"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of URL", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.url.URL._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "builtins.str", "builtins.int"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_assert_none_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "v", "paramname"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.url.URL._assert_none_str", "name": "_assert_none_str", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "v", "paramname"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assert_none_str of URL", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.url.URL._assert_none_str", "name": "_assert_none_str", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "v", "paramname"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assert_none_str of URL", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_assert_port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "port"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.url.URL._assert_port", "name": "_assert_port", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "port"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assert_port of URL", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.url.URL._assert_port", "name": "_assert_port", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "port"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assert_port of URL", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_assert_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL._assert_replace", "name": "_assert_replace", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assert_replace of URL", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_assert_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "v", "paramname"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.url.URL._assert_str", "name": "_assert_str", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "v", "paramname"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assert_str of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.url.URL._assert_str", "name": "_assert_str", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "v", "paramname"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assert_str of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.url.URL._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.url.URL._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.url.URL._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "_get_entrypoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL._get_entrypoint", "name": "_get_entrypoint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_entrypoint of URL", "ret_type": {".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_instantiate_plugins": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL._instantiate_plugins", "name": "_instantiate_plugins", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_instantiate_plugins of URL", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.url.URL._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.url.URL._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "builtins.str", "builtins.int"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of URL", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.url.URL._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "builtins.str", "builtins.int"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.url.URL._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "builtins.str", "builtins.int"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "sqlalchemy.engine.url.URL._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.url.URL._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "builtins.str", "builtins.int"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of URL", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.url.URL._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "builtins.str", "builtins.int"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.url.URL._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "builtins.str", "builtins.int"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "drivername", "username", "password", "host", "port", "database", "query"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "drivername", "username", "password", "host", "port", "database", "query"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.url.URL._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "builtins.str", "builtins.int"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of URL", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.url.URL._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "builtins.str", "builtins.int"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.url.URL._NT", "id": -1, "name": "_NT", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "builtins.str", "builtins.int"]}], "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.url.URL._source", "name": "_source", "type": "builtins.str"}}, "_str_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "dict_"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.url.URL._str_dict", "name": "_str_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "dict_"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, "builtins.str"]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, "builtins.str"]}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_str_dict of URL", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.url.URL._str_dict", "name": "_str_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "dict_"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, "builtins.str"]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, "builtins.str"]}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_str_dict of URL", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["cls", "drivername", "username", "password", "host", "port", "database", "query"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.url.URL.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["cls", "drivername", "username", "password", "host", "port", "database", "query"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, "builtins.str"]}], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of URL", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.url.URL.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["cls", "drivername", "username", "password", "host", "port", "database", "query"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, "builtins.str"]}], "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create of URL", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "database": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.engine.url.URL.database", "name": "database", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "database-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url.URL.database", "kind": "<PERSON><PERSON><PERSON>"}, "difference_update_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "names"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL.difference_update_query", "name": "difference_update_query", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "difference_update_query of URL", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "drivername": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.engine.url.URL.drivername", "name": "drivername", "type": "builtins.str"}}, "drivername-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url.URL.drivername", "kind": "<PERSON><PERSON><PERSON>"}, "get_backend_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL.get_backend_name", "name": "get_backend_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_backend_name of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_dialect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "_is_async"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL.get_dialect", "name": "get_dialect", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "_is_async"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_dialect of URL", "ret_type": {".class": "TypeType", "item": "sqlalchemy.engine.interfaces.Dialect"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_driver_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL.get_driver_name", "name": "get_driver_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_driver_name of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.engine.url.URL.host", "name": "host", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "host-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url.URL.host", "kind": "<PERSON><PERSON><PERSON>"}, "normalized_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.url.URL.normalized_query", "name": "normalized_query", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalized_query of URL", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], "type_ref": "typing.Mapping"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.url.URL.normalized_query", "name": "normalized_query", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalized_query of URL", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}], "type_ref": "typing.Mapping"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.engine.url.URL.password", "name": "password", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "password-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url.URL.password", "kind": "<PERSON><PERSON><PERSON>"}, "port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.engine.url.URL.port", "name": "port", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}}}, "port-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url.URL.port", "kind": "<PERSON><PERSON><PERSON>"}, "query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.engine.url.URL.query", "name": "query", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "query-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url.URL.query", "kind": "<PERSON><PERSON><PERSON>"}, "render_as_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "hide_password"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL.render_as_string", "name": "render_as_string", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "hide_password"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_as_string of URL", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "drivername", "username", "password", "host", "port", "database", "query"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "drivername", "username", "password", "host", "port", "database", "query"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, "builtins.str"]}], "type_ref": "typing.Mapping"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of URL", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "translate_connect_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "names", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL.translate_connect_args", "name": "translate_connect_args", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "names", "kw"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "translate_connect_args of URL", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "update_query_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "query_parameters", "append"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL.update_query_dict", "name": "update_query_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "query_parameters", "append"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}]}], "type_ref": "typing.Mapping"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_query_dict of URL", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "update_query_pairs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "key_value_pairs", "append"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL.update_query_pairs", "name": "update_query_pairs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "key_value_pairs", "append"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Iterable"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_query_pairs of URL", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "update_query_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "query_string", "append"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.URL.update_query_string", "name": "update_query_string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "query_string", "append"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_query_string of URL", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "username": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.engine.url.URL.username", "name": "username", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "username-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url.URL.username", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.url.URL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": "sqlalchemy.engine.url.URL"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, "builtins.str"]}], "type_ref": "sqlalchemy.util._py_collections.immutabledict"}, "builtins.str", "builtins.int"]}], "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.url.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.url.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.url.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.url.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.url.__package__", "name": "__package__", "type": "builtins.str"}}, "_parse_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url._parse_url", "name": "_parse_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_url", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_sqla_url_quote": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url._sqla_url_quote", "name": "_sqla_url_quote", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sqla_url_quote", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_sqla_url_unquote": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.url._sqla_url_unquote", "name": "_sqla_url_unquote", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["string", "encoding", "errors"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"]}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "collections_abc": {".class": "SymbolTableNode", "cross_ref": "collections.abc", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "make_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name_or_url"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.engine.url.make_url", "name": "make_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name_or_url"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_url", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "parse_qsl": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.parse_qsl", "kind": "Gdef"}, "plugins": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.plugins", "kind": "Gdef"}, "quote_plus": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.quote_plus", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "registry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.registry", "kind": "Gdef"}, "unquote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.unquote", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/engine/url.py"}