{".class": "MypyFile", "_fullname": "sqlalchemy.engine.processors", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "HAS_CYEXTENSION": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._has_cy.HAS_CYEXTENSION", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.processors.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.processors.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.processors.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.processors.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.processors.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "int_to_boolean": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine._py_processors.int_to_boolean", "kind": "Gdef"}, "str_to_date": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine._py_processors.str_to_date", "kind": "Gdef"}, "str_to_datetime": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine._py_processors.str_to_datetime", "kind": "Gdef"}, "str_to_datetime_processor_factory": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine._py_processors.str_to_datetime_processor_factory", "kind": "Gdef"}, "str_to_time": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine._py_processors.str_to_time", "kind": "Gdef"}, "to_decimal_processor_factory": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine._py_processors.to_decimal_processor_factory", "kind": "Gdef"}, "to_float": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine._py_processors.to_float", "kind": "Gdef"}, "to_str": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine._py_processors.to_str", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/engine/processors.py"}