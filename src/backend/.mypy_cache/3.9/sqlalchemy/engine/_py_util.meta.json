{"data_mtime": 1754228260, "dep_lines": [12, 9, 1, 3, 9, 1, 1], "dep_prios": [25, 10, 5, 5, 20, 5, 30], "dependencies": ["sqlalchemy.engine.interfaces", "sqlalchemy.exc", "__future__", "typing", "sqlalchemy", "builtins", "abc"], "hash": "e66dcc65b12a9d4c0fe642bf8218ac169cdc5e00704b14d290110507a69f883f", "id": "sqlalchemy.engine._py_util", "ignore_all": true, "interface_hash": "66fbc89e07e84898bb4d50b38db703b8ae859d716995d8178c534cbc5ed65804", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/engine/_py_util.py", "plugin_data": null, "size": 2245, "suppressed": [], "version_id": "1.7.1"}