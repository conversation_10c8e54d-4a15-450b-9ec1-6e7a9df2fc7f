{"data_mtime": 1754228260, "dep_lines": [18, 19, 20, 26, 29, 31, 42, 43, 46, 56, 59, 62, 62, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30], "dependencies": ["sqlalchemy.engine.events", "sqlalchemy.engine.util", "sqlalchemy.engine.base", "sqlalchemy.engine.create", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.mock", "sqlalchemy.engine.reflection", "sqlalchemy.engine.result", "sqlalchemy.engine.row", "sqlalchemy.engine.url", "sqlalchemy.sql.ddl", "sqlalchemy.sql", "builtins", "abc", "typing"], "hash": "7c90809793fb247f62c23b96a3bdbfdcb388cd65a14e7bd7ab3a409a6fd3d1f6", "id": "sqlalchemy.engine", "ignore_all": true, "interface_hash": "f8ca645b4017641e5b8aedcd56d00bdbc0bdf4a28faf604d243ae9e71fb49d54", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/engine/__init__.py", "plugin_data": null, "size": 2818, "suppressed": [], "version_id": "1.7.1"}