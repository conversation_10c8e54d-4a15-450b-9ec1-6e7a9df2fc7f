{"data_mtime": 1754228260, "dep_lines": [19, 21, 27, 38, 41, 25, 26, 39, 40, 9, 11, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 10, 10, 25, 25, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.util.typing", "sqlalchemy.engine.result", "sqlalchemy.sql.elements", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.pool", "sqlalchemy.sql", "__future__", "typing", "sqlalchemy", "builtins", "abc", "sqlalchemy.event.base", "sqlalchemy.event.legacy", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.pool.base", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "3d0c9cfec6e6aa4b3aa6ac8def18ad3bae7c29d2b3cc959f5b54ca63011de6fa", "id": "sqlalchemy.engine.events", "ignore_all": true, "interface_hash": "46522192649986738b3624743609217c7f62bb64b823f6328f94fddd0bca69b5", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/engine/events.py", "plugin_data": null, "size": 37392, "suppressed": [], "version_id": "1.7.1"}