{"data_mtime": 1754228259, "dep_lines": [18, 19, 22, 16, 17, 8, 10, 16, 1, 1, 1], "dep_prios": [5, 5, 25, 10, 10, 5, 5, 20, 5, 30, 30], "dependencies": ["sqlalchemy.util._has_cy", "sqlalchemy.util.typing", "sqlalchemy.engine._py_util", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "abc", "sqlalchemy.util.langhelpers"], "hash": "dfe10d23d4bedca2d6af4196dbbb9641fb2f089c0c0464ca6f292428f5208801", "id": "sqlalchemy.engine.util", "ignore_all": true, "interface_hash": "0164865b1f5a78711fe39b94bf7b904275f10a4742636eeff29b1bd225cae7bc", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/engine/util.py", "plugin_data": null, "size": 5667, "suppressed": [], "version_id": "1.7.1"}