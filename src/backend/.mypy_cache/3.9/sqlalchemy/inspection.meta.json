{"data_mtime": 1754228259, "dep_lines": [44, 43, 31, 33, 43, 1, 1], "dep_prios": [5, 10, 5, 5, 20, 5, 30], "dependencies": ["sqlalchemy.util.typing", "sqlalchemy.exc", "__future__", "typing", "sqlalchemy", "builtins", "abc"], "hash": "8b7691f88575d35614f03f5303c3f16f6c22d3c419b89df8b0ccba2f933ffeb6", "id": "sqlalchemy.inspection", "ignore_all": true, "interface_hash": "8018b7e6c8a7d90e6c4cb3d7143a5d301873148edab00a05aaaa055f16a7869b", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/inspection.py", "plugin_data": null, "size": 5145, "suppressed": [], "version_id": "1.7.1"}