{"data_mtime": 1754228259, "dep_lines": [26, 27, 30, 32, 34, 26, 15, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 25, 25, 25, 20, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util.compat", "sqlalchemy.util.preloaded", "sqlalchemy.engine.interfaces", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.util", "__future__", "typing", "builtins", "abc", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util.langhelpers", "typing_extensions"], "hash": "e1230a389b73eff496b79bec90249e5d28b86651722f88e1e7743cb24e3e3834", "id": "sqlalchemy.exc", "ignore_all": true, "interface_hash": "be8891eaa979b1da0c0d87cb1579ca7131434aa2440f3a173bcadeaa85162211", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/exc.py", "plugin_data": null, "size": 24011, "suppressed": [], "version_id": "1.7.1"}