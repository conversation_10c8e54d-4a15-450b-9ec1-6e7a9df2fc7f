{"data_mtime": 1754228259, "dep_lines": [34, 32, 20, 22, 23, 24, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 30, 30], "dependencies": ["sqlalchemy.util.typing", "sqlalchemy.util", "__future__", "logging", "sys", "typing", "builtins", "abc", "sqlalchemy.util.compat"], "hash": "692959f0314790eb88f8032668e51462d4bdcc63da762ffbb40a3df10a543a26", "id": "sqlalchemy.log", "ignore_all": true, "interface_hash": "2d79576cfa2de9f84dd6b98f9b2fafa2be6a9d9c3d5e31e27d82902f13caf8b3", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/log.py", "plugin_data": null, "size": 8634, "suppressed": [], "version_id": "1.7.1"}