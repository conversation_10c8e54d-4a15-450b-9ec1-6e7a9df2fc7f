{"data_mtime": 1754228260, "dep_lines": [13, 1, 1, 1], "dep_prios": [5, 5, 30, 30], "dependencies": ["sqlalchemy.engine", "builtins", "abc", "typing"], "hash": "eae3a939e748aa24f5fb7a89489225bfdfeb68c254f0d4e4850c0dfcd820f242", "id": "sqlalchemy.future.engine", "ignore_all": true, "interface_hash": "57c3882d52524ffb4b6d0214c7cefeca3797fd618f5a6afd14ce498aa661584d", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/future/engine.py", "plugin_data": null, "size": 499, "suppressed": [], "version_id": "1.7.1"}