{"data_mtime": 1754228259, "dep_lines": [25, 29, 41, 42, 45, 47, 53, 56, 57, 68, 70, 25, 26, 27, 28, 8, 10, 11, 26, 34, 38, 39, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 20, 10, 10, 5, 5, 10, 5, 20, 25, 25, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.roles", "sqlalchemy.util.typing", "sqlalchemy.sql.base", "sqlalchemy.sql.compiler", "sqlalchemy.sql.dml", "sqlalchemy.sql.elements", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.inspection", "__future__", "operator", "typing", "sqlalchemy", "datetime", "decimal", "uuid", "builtins", "_typeshed", "abc", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "0bc90d650dd322933e435d8e7f7b53684481d54c487d1304fe55e8baa830313f", "id": "sqlalchemy.sql._typing", "ignore_all": true, "interface_hash": "e90e2fc4a35575729ed4e74d0aae44f72e746976e76aa2f7096c39c1fd062eaa", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/sql/_typing.py", "plugin_data": null, "size": 12252, "suppressed": [], "version_id": "1.7.1"}