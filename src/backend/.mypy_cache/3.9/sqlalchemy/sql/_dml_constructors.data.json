{".class": "MypyFile", "_fullname": "sqlalchemy.sql._dml_constructors", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Delete": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.Delete", "kind": "Gdef"}, "Insert": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.Insert", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Update": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.Update", "kind": "Gdef"}, "_DMLTableArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._DMLTableArgument", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._dml_constructors.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._dml_constructors.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._dml_constructors.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._dml_constructors.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._dml_constructors.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "delete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql._dml_constructors.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["table"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._DMLTableArgument"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete", "ret_type": "sqlalchemy.sql.dml.Delete", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "insert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql._dml_constructors.insert", "name": "insert", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["table"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._DMLTableArgument"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert", "ret_type": "sqlalchemy.sql.dml.Insert", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["table"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql._dml_constructors.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["table"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._DMLTableArgument"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update", "ret_type": "sqlalchemy.sql.dml.Update", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/sql/_dml_constructors.py"}