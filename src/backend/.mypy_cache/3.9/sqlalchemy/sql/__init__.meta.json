{"data_mtime": 1754228260, "dep_lines": [10, 13, 14, 18, 22, 108, 7, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30], "dependencies": ["sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.compiler", "sqlalchemy.sql.ddl", "sqlalchemy.sql.expression", "sqlalchemy.sql.visitors", "typing", "builtins", "abc"], "hash": "8c0431f6bc21c8f8684a39ed33505948494988c4662e8c064e12550c6bc4c525", "id": "sqlalchemy.sql", "ignore_all": true, "interface_hash": "58238a475d0144c6efbf4f9a02e279b066bd63461c58d6943249b660bab42238", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/sql/__init__.py", "plugin_data": null, "size": 5820, "suppressed": [], "version_id": "1.7.1"}