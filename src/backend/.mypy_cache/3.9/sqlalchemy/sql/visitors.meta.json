{"data_mtime": 1754228259, "dep_lines": [39, 40, 41, 46, 47, 50, 37, 38, 13, 15, 16, 17, 18, 19, 37, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 25, 25, 25, 10, 10, 5, 5, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util.langhelpers", "sqlalchemy.util._has_cy", "sqlalchemy.util.typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.elements", "sqlalchemy.sql._py_util", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "collections", "enum", "itertools", "operator", "typing", "sqlalchemy", "builtins", "_operator", "abc", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.util._py_collections", "sqlalchemy.util.preloaded", "types", "typing_extensions"], "hash": "283d6a3989ba45d7ed0ae7d5181f2aea3153219210292df33c283bf1c555d264", "id": "sqlalchemy.sql.visitors", "ignore_all": true, "interface_hash": "30e7b7fd4a7686c369a84846d63e36858b4f50c6647c7a113554e48567181a8e", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/sql/visitors.py", "plugin_data": null, "size": 36427, "suppressed": [], "version_id": "1.7.1"}