{"data_mtime": 1754228259, "dep_lines": [45, 46, 47, 49, 59, 65, 66, 67, 68, 70, 79, 81, 86, 45, 54, 55, 56, 84, 14, 16, 17, 18, 20, 21, 22, 54, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 20, 10, 10, 5, 25, 5, 10, 5, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.roles", "sqlalchemy.sql.visitors", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.traversals", "sqlalchemy.util.typing", "sqlalchemy.sql.coercions", "sqlalchemy.sql.elements", "sqlalchemy.sql.type_api", "sqlalchemy.sql._orm_types", "sqlalchemy.sql._typing", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.engine.interfaces", "sqlalchemy.sql", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "collections", "enum", "itertools", "operator", "re", "typing", "sqlalchemy", "builtins", "_collections_abc", "_operator", "_typeshed", "abc", "sqlalchemy.engine.base", "sqlalchemy.engine.cursor", "sqlalchemy.engine.result", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.compiler", "sqlalchemy.sql.operators", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types", "typing_extensions"], "hash": "755bd9a0fa1ade96fa8aec135384280af55640fc8766d85a7a4979276cd5fd25", "id": "sqlalchemy.sql.base", "ignore_all": true, "interface_hash": "1b47107ba56cc2d91b25502537c0f8228498fbe0efbf9644c96c071c1d2ad377", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/sql/base.py", "plugin_data": null, "size": 73928, "suppressed": [], "version_id": "1.7.1"}