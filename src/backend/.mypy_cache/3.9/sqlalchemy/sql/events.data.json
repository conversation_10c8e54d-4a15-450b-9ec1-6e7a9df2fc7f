{".class": "MypyFile", "_fullname": "sqlalchemy.sql.events", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "Constraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Constraint", "kind": "Gdef"}, "DDLEvents": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["sqlalchemy.sql.base.SchemaEventTarget"], "type_ref": "sqlalchemy.event.base.Events"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.events.DDLEvents", "name": "DDLEvents", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "sqlalchemy.sql.events.DDLEvents", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.events", "mro": ["sqlalchemy.sql.events.DDLEvents", "sqlalchemy.event.base.Events", "sqlalchemy.event.base._HasEventsDispatch", "builtins.object"], "names": {".class": "SymbolTable", "_dispatch_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.events.DDLEvents._dispatch_target", "name": "_dispatch_target", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["sqlalchemy.sql.base.SchemaEventTarget"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.sql.base.SchemaEventTarget", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_sa_event_column_added_to_pk_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "const", "col"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.events.DDLEvents._sa_event_column_added_to_pk_constraint", "name": "_sa_event_column_added_to_pk_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "const", "col"], "arg_types": ["sqlalchemy.sql.events.DDLEvents", "sqlalchemy.sql.schema.Constraint", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sa_event_column_added_to_pk_constraint of DDLEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_target_class_doc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.events.DDLEvents._target_class_doc", "name": "_target_class_doc", "type": "builtins.str"}}, "after_create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "target", "connection", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.events.DDLEvents.after_create", "name": "after_create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "target", "connection", "kw"], "arg_types": ["sqlalchemy.sql.events.DDLEvents", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.engine.base.Connection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_create of DDLEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "after_drop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "target", "connection", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.events.DDLEvents.after_drop", "name": "after_drop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "target", "connection", "kw"], "arg_types": ["sqlalchemy.sql.events.DDLEvents", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.engine.base.Connection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_drop of DDLEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "after_parent_attach": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "parent"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.events.DDLEvents.after_parent_attach", "name": "after_parent_attach", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "parent"], "arg_types": ["sqlalchemy.sql.events.DDLEvents", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.sql.schema.SchemaItem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "after_parent_attach of DDLEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "before_create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "target", "connection", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.events.DDLEvents.before_create", "name": "before_create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "target", "connection", "kw"], "arg_types": ["sqlalchemy.sql.events.DDLEvents", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.engine.base.Connection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_create of DDLEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "before_drop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "target", "connection", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.events.DDLEvents.before_drop", "name": "before_drop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "target", "connection", "kw"], "arg_types": ["sqlalchemy.sql.events.DDLEvents", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.engine.base.Connection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_drop of DDLEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "before_parent_attach": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "parent"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.events.DDLEvents.before_parent_attach", "name": "before_parent_attach", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "parent"], "arg_types": ["sqlalchemy.sql.events.DDLEvents", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.sql.schema.SchemaItem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_parent_attach of DDLEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "column_reflect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "inspector", "table", "column_info"], "dataclass_transform_spec": null, "flags": [], "fullname": "sqlalchemy.sql.events.DDLEvents.column_reflect", "name": "column_reflect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "inspector", "table", "column_info"], "arg_types": ["sqlalchemy.sql.events.DDLEvents", "sqlalchemy.engine.reflection.Inspector", "sqlalchemy.sql.schema.Table", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.ReflectedColumn"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "column_reflect of DDLEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.events.DDLEvents.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.events.DDLEvents", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Inspector": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.Inspector", "kind": "Gdef"}, "ReflectedColumn": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ReflectedColumn", "kind": "Gdef"}, "SchemaEventTarget": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.SchemaEventTarget", "kind": "Gdef"}, "SchemaItem": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.SchemaItem", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.events.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.events.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.events.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.events.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.events.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "event": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/sql/events.py"}