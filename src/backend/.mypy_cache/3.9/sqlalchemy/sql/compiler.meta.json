{"data_mtime": 1754228260, "dep_lines": [59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 82, 83, 88, 93, 96, 97, 98, 118, 119, 2311, 29, 59, 85, 86, 2311, 26, 28, 30, 31, 32, 33, 34, 35, 36, 85, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 10, 10, 10, 5, 10, 5, 10, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 20, 10, 20, 10, 5, 20, 5, 10, 10, 5, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.base", "sqlalchemy.sql.coercions", "sqlalchemy.sql.crud", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.util", "sqlalchemy.sql._typing", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.dml", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.result", "collections.abc", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "collections", "contextlib", "enum", "itertools", "operator", "re", "time", "typing", "sqlalchemy", "builtins", "_collections_abc", "_operator", "_typeshed", "abc", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._py_util", "sqlalchemy.sql.traversals", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "typing_extensions"], "hash": "f56c78db71fbd98abb34747c7263001fa1a9302266821b352fce5826ab3f2e78", "id": "sqlalchemy.sql.compiler", "ignore_all": true, "interface_hash": "9b113a0a102876beda24c63f53999e09ba8374f837f377d347994d43c211138c", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/sql/compiler.py", "plugin_data": null, "size": 268763, "suppressed": [], "version_id": "1.7.1"}