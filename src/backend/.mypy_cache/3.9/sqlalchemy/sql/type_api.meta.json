{"data_mtime": 1754228259, "dep_lines": [33, 34, 36, 37, 40, 47, 48, 51, 60, 38, 39, 12, 14, 15, 16, 38, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 25, 25, 25, 10, 10, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.sql._typing", "sqlalchemy.sql.elements", "sqlalchemy.sql.sqltypes", "sqlalchemy.engine.interfaces", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "enum", "types", "typing", "sqlalchemy", "builtins", "abc", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded"], "hash": "0fd2aafa9a7066f94d9b1687aaf56633c2158389fafdeaf3269562a327bd58a1", "id": "sqlalchemy.sql.type_api", "ignore_all": true, "interface_hash": "81852132c080f19d17f3bd238cad332e6b9881360e174e667c3d9e1a3229b8be", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/sql/type_api.py", "plugin_data": null, "size": 83823, "suppressed": [], "version_id": "1.7.1"}