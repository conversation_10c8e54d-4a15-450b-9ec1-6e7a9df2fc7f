{"data_mtime": 1754228260, "dep_lines": [16, 19, 8, 10, 1, 1, 1], "dep_prios": [5, 25, 5, 5, 5, 30, 30], "dependencies": ["sqlalchemy.util.typing", "sqlalchemy.sql.cache_key", "__future__", "typing", "builtins", "abc", "enum"], "hash": "8a2c205f775084e8dd079fb5d23b601cf21d89b52a1a4e3d6c2d6a759301a582", "id": "sqlalchemy.sql._py_util", "ignore_all": true, "interface_hash": "706b014f4369a3d4e802c5fdd459cc6f521069015c7e8bfb9be45c3c19ce0be2", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/sql/_py_util.py", "plugin_data": null, "size": 2173, "suppressed": [], "version_id": "1.7.1"}