{"data_mtime": 1754228259, "dep_lines": [35, 36, 37, 38, 39, 40, 43, 54, 59, 60, 62, 67, 70, 75, 14, 35, 55, 56, 57, 58, 59, 12, 14, 15, 16, 17, 18, 19, 20, 33, 55, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 5, 5, 5, 5, 10, 10, 5, 25, 25, 25, 10, 20, 10, 10, 10, 5, 20, 5, 20, 10, 10, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.coercions", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.type_api", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.visitors", "sqlalchemy.engine.processors", "sqlalchemy.util.langhelpers", "sqlalchemy.util.typing", "sqlalchemy.sql._typing", "sqlalchemy.sql.schema", "sqlalchemy.engine.interfaces", "collections.abc", "sqlalchemy.sql", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "collections", "datetime", "decimal", "enum", "json", "pickle", "typing", "uuid", "sqlalchemy", "builtins", "_decimal", "_typeshed", "abc", "sqlalchemy.event.api", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.preloaded", "types", "typing_extensions"], "hash": "ff416914b1f4005b9e6f74c807955cc7d9d7583363df55c54133f4bbc3979d2a", "id": "sqlalchemy.sql.sqltypes", "ignore_all": true, "interface_hash": "e77bd02a6c6d8a36cc5c5967d1aa9548331fde498c6c6fc2bfd4b9fe4227ee61", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/sql/sqltypes.py", "plugin_data": null, "size": 126540, "suppressed": [], "version_id": "1.7.1"}