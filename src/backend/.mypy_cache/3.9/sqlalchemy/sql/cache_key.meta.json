{"data_mtime": 1754228259, "dep_lines": [25, 33, 37, 40, 30, 31, 8, 10, 11, 12, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 25, 5, 5, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.sql.elements", "sqlalchemy.engine.interfaces", "sqlalchemy.util", "sqlalchemy.inspection", "__future__", "enum", "itertools", "typing", "sqlalchemy", "builtins", "abc", "sqlalchemy.sql._py_util", "sqlalchemy.sql.annotation", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types"], "hash": "0e5d7adea1e34e43026b92d38a966e77c5f7c3477c0ef748bc6befe00aab8871", "id": "sqlalchemy.sql.cache_key", "ignore_all": true, "interface_hash": "40edd8a1517c9b5fd173d9b3f83c0d20d5069a4ff7f3a58e1ce660c728402fa6", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/sql/cache_key.py", "plugin_data": null, "size": 32823, "suppressed": [], "version_id": "1.7.1"}