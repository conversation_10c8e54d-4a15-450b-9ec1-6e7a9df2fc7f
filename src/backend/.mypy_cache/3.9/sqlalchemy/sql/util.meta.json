{"data_mtime": 1754228259, "dep_lines": [35, 36, 37, 38, 39, 40, 43, 45, 46, 47, 60, 61, 71, 86, 90, 35, 69, 70, 12, 14, 15, 16, 17, 69, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 25, 20, 10, 10, 5, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.coercions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.visitors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.elements", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.util.typing", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.row", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "collections", "copy", "itertools", "typing", "sqlalchemy", "builtins", "_typeshed", "abc", "sqlalchemy.engine", "sqlalchemy.engine._py_row", "sqlalchemy.inspection", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util._collections", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types"], "hash": "9411007fff9e45ea5312b3810a9d4f6c43190d8749a8088ad467a642d8288d8a", "id": "sqlalchemy.sql.util", "ignore_all": true, "interface_hash": "6bcbd3f34f289e0bf20f699e532e61f8e7ca8cc8ded910b44b4beabfd17f66ed", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/sql/util.py", "plugin_data": null, "size": 48175, "suppressed": [], "version_id": "1.7.1"}