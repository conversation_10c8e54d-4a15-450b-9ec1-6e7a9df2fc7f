{"data_mtime": 1754228259, "dep_lines": [27, 28, 29, 35, 36, 12, 27, 34, 9, 11, 13, 15, 16, 34, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 5, 10, 20, 10, 5, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.operators", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.visitors", "sqlalchemy.util.langhelpers", "sqlalchemy.util.typing", "collections.abc", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "collections", "itertools", "operator", "typing", "sqlalchemy", "builtins", "abc", "enum", "sqlalchemy.util.preloaded", "types"], "hash": "edbf7c25278bc6a79c9861e184b5d3ff633840c91ee96fb109c8b94579dd8712", "id": "sqlalchemy.sql.traversals", "ignore_all": true, "interface_hash": "e2ccddf3ee7a2aca48bee592b6ac042b70413f0d55651bdbfc7b10f7af3aeea7", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/sql/traversals.py", "plugin_data": null, "size": 33521, "suppressed": [], "version_id": "1.7.1"}