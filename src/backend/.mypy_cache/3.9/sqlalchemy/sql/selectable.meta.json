{"data_mtime": 1754228259, "dep_lines": [41, 42, 43, 44, 45, 46, 47, 48, 56, 58, 80, 93, 100, 133, 134, 141, 142, 41, 97, 98, 14, 16, 17, 18, 19, 97, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 25, 25, 25, 25, 20, 10, 5, 5, 10, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.cache_key", "sqlalchemy.sql.coercions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.sql.sqltypes", "sqlalchemy.util.typing", "sqlalchemy.sql.compiler", "sqlalchemy.sql.dml", "sqlalchemy.sql.functions", "sqlalchemy.sql.schema", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "collections", "enum", "itertools", "typing", "sqlalchemy", "builtins", "_collections_abc", "_decimal", "abc", "datetime", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.util", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types", "uuid"], "hash": "f5d3b6ca137cdf38e76bb9cf8ce13585cbc6c891a373f963e52033ed23f90814", "id": "sqlalchemy.sql.selectable", "ignore_all": true, "interface_hash": "5e82785c8cd8e7df55d6eb8cde5b04d46d387040fb9107248411254593d41545", "mtime": 1754207015, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/sqlalchemy/sql/selectable.py", "plugin_data": null, "size": 233041, "suppressed": [], "version_id": "1.7.1"}