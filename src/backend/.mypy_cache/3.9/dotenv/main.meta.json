{"data_mtime": 1754228257, "dep_lines": [12, 13, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 30, 30], "dependencies": ["dotenv.parser", "dotenv.variables", "io", "logging", "os", "shutil", "sys", "tempfile", "collections", "contextlib", "typing", "builtins", "_typeshed", "abc"], "hash": "ea3d465bc90d799028a2a7df75a8cb9defddabf4c92e2d8c93adc344d2635cb9", "id": "dotenv.main", "ignore_all": true, "interface_hash": "08eef4cd19bffe15fdafceaf3c2c47c29408f1c151f8c3b25a8ff741170b2b5d", "mtime": 1754207014, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/dotenv/main.py", "plugin_data": null, "size": 11932, "suppressed": [], "version_id": "1.7.1"}