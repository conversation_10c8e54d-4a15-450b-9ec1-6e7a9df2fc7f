{"data_mtime": 1754228257, "dep_lines": [36, 36, 36, 37, 38, 39, 42, 8, 20, 30, 31, 32, 34, 35, 36, 5, 7, 9, 10, 11, 12, 13, 14, 15, 16, 19, 20, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 25, 10, 10, 5, 5, 5, 5, 5, 20, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["pydantic._internal._known_annotated_metadata", "pydantic._internal._typing_extra", "pydantic._internal._validators", "pydantic._internal._core_utils", "pydantic._internal._internal_dataclass", "pydantic._internal._schema_generation_shared", "pydantic._internal._generate_schema", "collections.abc", "pydantic_core.core_schema", "pydantic.errors", "pydantic.fields", "pydantic.types", "pydantic.config", "pydantic.json_schema", "pydantic._internal", "__future__", "collections", "dataclasses", "decimal", "inspect", "os", "typing", "enum", "functools", "ipaddress", "typing_extensions", "pydantic_core", "builtins", "_typeshed", "abc", "pydantic.annotated_handlers"], "hash": "e90e641867bfec62f7683521e4f9301f524068d59eb40133b96676324b5049fc", "id": "pydantic._internal._std_types_schema", "ignore_all": true, "interface_hash": "c5a3a85411836bd777db70eb1f507136456c04784e4275b0cbebb4ee3f66126c", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic/_internal/_std_types_schema.py", "plugin_data": null, "size": 29085, "suppressed": [], "version_id": "1.7.1"}