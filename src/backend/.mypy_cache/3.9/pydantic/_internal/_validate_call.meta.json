{"data_mtime": 1754228257, "dep_lines": [11, 12, 12, 13, 10, 12, 1, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 20, 5, 10, 5, 5, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["pydantic.plugin._schema_validator", "pydantic._internal._generate_schema", "pydantic._internal._typing_extra", "pydantic._internal._config", "pydantic.config", "pydantic._internal", "__future__", "inspect", "dataclasses", "functools", "typing", "pydantic_core", "builtins", "abc", "pydantic.plugin", "pydantic_core._pydantic_core", "pydantic_core.core_schema"], "hash": "d866ae9b53c3b37e93fc27677d3fb6b46caaeb1f2eb11023a23a3515bdddcb07", "id": "pydantic._internal._validate_call", "ignore_all": true, "interface_hash": "3d1b3a68e868a91a0d556519e47cb25cfc3a34c26fddcb0e2621abf282db019c", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic/_internal/_validate_call.py", "plugin_data": null, "size": 5755, "suppressed": [], "version_id": "1.7.1"}