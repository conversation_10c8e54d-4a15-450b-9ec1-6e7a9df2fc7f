{"data_mtime": 1754228257, "dep_lines": [18, 18, 18, 23, 5, 7, 8, 9, 10, 11, 12, 13, 16, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 25, 5, 10, 5, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["pydantic._internal._repr", "pydantic._internal._typing_extra", "pydantic._internal", "pydantic.main", "__future__", "keyword", "typing", "weakref", "collections", "copy", "itertools", "types", "typing_extensions", "builtins", "_typeshed", "_weakref", "abc"], "hash": "69f25fc70e24666a6737ad836e6b5062263b9d4cb4ed6f8a8556b88b8bb9ba0c", "id": "pydantic._internal._utils", "ignore_all": true, "interface_hash": "9e27bfd8ce9c086aca329426bcb9e125e9402a2abaf846c6d2ac282f3914bf5d", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic/_internal/_utils.py", "plugin_data": null, "size": 11714, "suppressed": [], "version_id": "1.7.1"}