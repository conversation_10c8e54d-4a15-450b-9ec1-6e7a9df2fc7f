{"data_mtime": 1754228257, "dep_lines": [13, 14, 6, 9, 12, 2, 4, 6, 7, 1, 1], "dep_prios": [25, 25, 10, 5, 25, 5, 5, 20, 5, 5, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic._internal._generate_schema", "pydantic_core.core_schema", "pydantic.annotated_handlers", "pydantic.json_schema", "__future__", "typing", "pydantic_core", "typing_extensions", "builtins", "abc"], "hash": "791c19f391a3d057da6d896f33dec8e7df7bbe1638324dc06102658cae41deda", "id": "pydantic._internal._schema_generation_shared", "ignore_all": true, "interface_hash": "85df46c706f0814d37475df78d33ff1aa6b8037f294a1494fb29e0258b42c0b6", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic/_internal/_schema_generation_shared.py", "plugin_data": null, "size": 4855, "suppressed": [], "version_id": "1.7.1"}