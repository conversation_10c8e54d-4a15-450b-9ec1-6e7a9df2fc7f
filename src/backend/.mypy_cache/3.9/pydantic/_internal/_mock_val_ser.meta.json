{"data_mtime": 1754228257, "dep_lines": [8, 11, 12, 1, 3, 5, 6, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["pydantic.errors", "pydantic.dataclasses", "pydantic.main", "__future__", "typing", "pydantic_core", "typing_extensions", "builtins", "abc", "pydantic._internal._dataclasses", "pydantic._internal._model_construction", "pydantic_core._pydantic_core", "pydantic_core.core_schema"], "hash": "e43aabb687c5c38c1499958d932eb3685c320806e34d30fd5d073c14ad89cca7", "id": "pydantic._internal._mock_val_ser", "ignore_all": true, "interface_hash": "a67402f6a9823a19a3c00a61a1c7a9419f219a2c62fc3c83729dcaf19a9752be", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic/_internal/_mock_val_ser.py", "plugin_data": null, "size": 5180, "suppressed": [], "version_id": "1.7.1"}