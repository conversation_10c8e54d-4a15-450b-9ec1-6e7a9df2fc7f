{"data_mtime": 1754228257, "dep_lines": [11, 9, 14, 1, 3, 4, 5, 6, 8, 1, 1, 1], "dep_prios": [5, 10, 25, 5, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["pydantic._internal._fields", "pydantic_core.core_schema", "pydantic.annotated_handlers", "__future__", "collections", "copy", "functools", "typing", "pydantic_core", "builtins", "_typeshed", "abc"], "hash": "0e891145935c160c94a1e00ece0077269d670f96f7f47c7076713d76bb9a2a07", "id": "pydantic._internal._known_annotated_metadata", "ignore_all": true, "interface_hash": "f5a066768778b3617bf34d9c31a449c5680f686b83bbc68965b44a2e6a10e35d", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic/_internal/_known_annotated_metadata.py", "plugin_data": null, "size": 16415, "suppressed": [], "version_id": "1.7.1"}