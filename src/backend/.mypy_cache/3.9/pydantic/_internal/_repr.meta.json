{"data_mtime": 1754228257, "dep_lines": [10, 10, 2, 4, 5, 8, 1, 1], "dep_prios": [10, 20, 5, 10, 5, 10, 5, 30], "dependencies": ["pydantic._internal._typing_extra", "pydantic._internal", "__future__", "types", "typing", "typing_extensions", "builtins", "abc"], "hash": "00f0e53b0acfbb4ee14d64dfd4f82f594da37a5a3cd011ccf2801c892d5598fe", "id": "pydantic._internal._repr", "ignore_all": true, "interface_hash": "b348969c65e69c661d9786ab4c128c234707e00fb0ce3e9b08156176b6d60287", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic/_internal/_repr.py", "plugin_data": null, "size": 4485, "suppressed": [], "version_id": "1.7.1"}