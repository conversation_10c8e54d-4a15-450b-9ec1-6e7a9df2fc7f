{"data_mtime": 1754228257, "dep_lines": [14, 15, 6, 8, 9, 10, 11, 14, 1, 1], "dep_prios": [10, 5, 5, 10, 10, 5, 5, 5, 5, 30], "dependencies": ["pydantic_core.core_schema", "pydantic_core._pydantic_core", "__future__", "math", "re", "typing", "ipaddress", "pydantic_core", "builtins", "abc"], "hash": "19bc84f6f52431a7cff3d8518fc65d9b64d74a903aca7319cfca8f2aa5217652", "id": "pydantic._internal._validators", "ignore_all": true, "interface_hash": "eae3ae947f4662eb04e4fac3901e7a9fd7585a88b698510b7c8a8732ea25b3e1", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic/_internal/_validators.py", "plugin_data": null, "size": 10054, "suppressed": [], "version_id": "1.7.1"}