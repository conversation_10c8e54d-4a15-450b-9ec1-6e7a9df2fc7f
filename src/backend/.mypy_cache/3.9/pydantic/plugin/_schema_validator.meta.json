{"data_mtime": 1754228257, "dep_lines": [11, 2, 4, 5, 7, 8, 1, 1, 1, 1], "dep_prios": [25, 5, 10, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["pydantic.plugin", "__future__", "functools", "typing", "pydantic_core", "typing_extensions", "builtins", "abc", "pydantic_core._pydantic_core", "pydantic_core.core_schema"], "hash": "dde569e7ee08b081c442bb02b21df8a0f7f66cdc8c253561de71801fb2110b53", "id": "pydantic.plugin._schema_validator", "ignore_all": true, "interface_hash": "93bf13fe5dc04c188db341705c46e48436c22a5ea7b04fd68baa1dc69cb9b4e0", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic/plugin/_schema_validator.py", "plugin_data": null, "size": 5228, "suppressed": [], "version_id": "1.7.1"}