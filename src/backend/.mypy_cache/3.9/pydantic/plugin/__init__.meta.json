{"data_mtime": 1754228257, "dep_lines": [5, 7, 9, 10, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["__future__", "typing", "pydantic_core", "typing_extensions", "builtins", "abc", "pydantic_core._pydantic_core", "pydantic_core.core_schema"], "hash": "8a0fa609a2ab5e6ff90e0f16fa76ea220f40824f8e1fa0d8026c68dd116f97ff", "id": "pydantic.plugin", "ignore_all": true, "interface_hash": "3c8b5b402ace36e9d9527d5e9337b18cb7ef025b2d12247429c154bb868b8548", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic/plugin/__init__.py", "plugin_data": null, "size": 6115, "suppressed": [], "version_id": "1.7.1"}