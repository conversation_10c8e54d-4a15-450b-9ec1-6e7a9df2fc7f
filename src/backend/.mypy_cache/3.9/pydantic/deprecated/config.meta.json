{"data_mtime": 1754228257, "dep_lines": [8, 8, 9, 1, 3, 4, 6, 1, 1], "dep_prios": [10, 20, 5, 5, 10, 5, 5, 5, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal", "pydantic.warnings", "__future__", "warnings", "typing", "typing_extensions", "builtins", "abc"], "hash": "ce06855b19a0e64e9c594b3b8abfce1984b6e8c409c5189b969e873e60b2d2ee", "id": "pydantic.deprecated.config", "ignore_all": true, "interface_hash": "00c55c7d9005b35ce729565a2331ff9a913e8c254a1b97dd8abadd68f9936997", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic/deprecated/config.py", "plugin_data": null, "size": 2612, "suppressed": [], "version_id": "1.7.1"}