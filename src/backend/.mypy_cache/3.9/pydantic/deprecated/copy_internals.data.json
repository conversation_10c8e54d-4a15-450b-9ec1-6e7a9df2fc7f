{".class": "MypyFile", "_fullname": "pydantic.deprecated.copy_internals", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractSetIntStr": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.AbstractSetIntStr", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyClassMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.deprecated.copy_internals.AnyClassMethod", "line": 20, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.classmethod"}}}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "IncEx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.deprecated.copy_internals.IncEx", "line": 24, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "type_ref": "builtins.set"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}, {".class": "Instance", "args": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}}}, "MappingIntStrAny": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.MappingIntStrAny", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.copy_internals.Model", "name": "Model", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TupleGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.deprecated.copy_internals.TupleGenerator", "line": 21, "no_args": false, "normalized": false, "target": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}, {".class": "NoneType"}, {".class": "NoneType"}], "type_ref": "typing.Generator"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.deprecated.copy_internals.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.deprecated.copy_internals.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.deprecated.copy_internals.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.deprecated.copy_internals.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.deprecated.copy_internals.__package__", "name": "__package__", "type": "builtins.str"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "_calculate_keys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "include", "exclude", "exclude_unset", "update"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.deprecated.copy_internals._calculate_keys", "name": "_calculate_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "include", "exclude", "exclude_unset", "update"], "arg_types": ["pydantic.main.BaseModel", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._utils.MappingIntStrAny"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._utils.MappingIntStrAny"}, {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_keys", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.AbstractSet"}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_copy_and_set_values": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 3], "arg_names": ["self", "values", "fields_set", "extra", "private", "deep"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.deprecated.copy_internals._copy_and_set_values", "name": "_copy_and_set_values", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 3], "arg_names": ["self", "values", "fields_set", "extra", "private", "deep"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.copy_internals.Model", "id": -1, "name": "Model", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy_and_set_values", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.copy_internals.Model", "id": -1, "name": "Model", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.deprecated.copy_internals.Model", "id": -1, "name": "Model", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}, "_get_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "v", "to_dict", "by_alias", "include", "exclude", "exclude_unset", "exclude_defaults", "exclude_none"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "pydantic.deprecated.copy_internals._get_value", "name": "_get_value", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.deprecated.copy_internals._get_value", "name": "_get_value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_iter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "to_dict", "by_alias", "include", "exclude", "exclude_unset", "exclude_defaults", "exclude_none"], "dataclass_transform_spec": null, "flags": [], "fullname": "pydantic.deprecated.copy_internals._iter", "name": "_iter", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "to_dict", "by_alias", "include", "exclude", "exclude_unset", "exclude_defaults", "exclude_none"], "arg_types": ["pydantic.main.BaseModel", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._utils.AbstractSetIntStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._utils.MappingIntStrAny"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._utils.AbstractSetIntStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._utils.MappingIntStrAny"}, {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_iter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.deprecated.copy_internals.TupleGenerator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_model_construction": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._model_construction", "kind": "Gdef"}, "_object_setattr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.deprecated.copy_internals._object_setattr", "name": "_object_setattr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", null, null], "arg_types": ["builtins.object", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_typing_extra": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._typing_extra", "kind": "Gdef"}, "_utils": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils", "kind": "Gdef"}, "deepcopy": {".class": "SymbolTableNode", "cross_ref": "copy.deepcopy", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing_extensions", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic/deprecated/copy_internals.py"}