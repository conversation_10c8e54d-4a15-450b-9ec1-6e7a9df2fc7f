{"data_mtime": 1754228257, "dep_lines": [25, 25, 25, 25, 25, 25, 25, 24, 25, 26, 27, 28, 2, 4, 5, 6, 7, 8, 12, 17, 19, 20, 21, 24, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 20, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._decorators", "pydantic._internal._fields", "pydantic._internal._generics", "pydantic._internal._internal_dataclass", "pydantic._internal._repr", "pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic.types", "pydantic._internal", "pydantic.config", "pydantic.errors", "pydantic.warnings", "__future__", "dataclasses", "inspect", "sys", "typing", "copy", "functools", "warnings", "annotated_types", "typing_extensions", "pydantic_core", "pydantic", "builtins", "_collections_abc", "_typeshed", "abc", "pydantic_core._pydantic_core", "types"], "hash": "2a5b515237ab54e03f7ff23b31b8b119f921bee88b79b81353d0fcef5772c7ea", "id": "pydantic.fields", "ignore_all": true, "interface_hash": "5b88b2b60e20ffd83460c01dad77d6659b87e723f2178dd758bc3cde564beb0c", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic/fields.py", "plugin_data": null, "size": 46237, "suppressed": [], "version_id": "1.7.1"}