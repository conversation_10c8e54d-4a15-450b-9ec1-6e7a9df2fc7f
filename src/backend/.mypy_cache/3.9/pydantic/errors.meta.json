{"data_mtime": 1754228257, "dep_lines": [8, 9, 2, 4, 6, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 30, 30], "dependencies": ["pydantic._migration", "pydantic.version", "__future__", "re", "typing_extensions", "builtins", "abc", "typing"], "hash": "9d1708c9ecb6148b403453560a4e97d52c8c125070d22cd760864a2fcafe7770", "id": "pydantic.errors", "ignore_all": true, "interface_hash": "102a3f746249d1b54e0d766b3fe9f816c4ce058f53fef6b687909df6e28fe1c0", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic/errors.py", "plugin_data": null, "size": 4632, "suppressed": [], "version_id": "1.7.1"}