{"data_mtime": 1754228257, "dep_lines": [6, 9, 2, 4, 6, 1, 1], "dep_prios": [10, 25, 5, 5, 20, 5, 30], "dependencies": ["pydantic_core.core_schema", "pydantic.json_schema", "__future__", "typing", "pydantic_core", "builtins", "abc"], "hash": "8b239d32fcf6f86fa97ba1c9d5ad44a51627dc49e4b4dca9a66948d1878cf92b", "id": "pydantic.annotated_handlers", "ignore_all": true, "interface_hash": "d1849142edd30b7a3294ccaa5b2b633630843a5a5247b8979ce8ccb614809d3b", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic/annotated_handlers.py", "plugin_data": null, "size": 4346, "suppressed": [], "version_id": "1.7.1"}