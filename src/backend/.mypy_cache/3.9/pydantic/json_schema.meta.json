{"data_mtime": 1754228257, "dep_lines": [43, 43, 43, 43, 43, 43, 43, 43, 60, 39, 43, 53, 54, 55, 62, 11, 13, 14, 15, 16, 17, 18, 19, 21, 22, 38, 41, 58, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 25, 5, 20, 5, 5, 5, 25, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._core_metadata", "pydantic._internal._core_utils", "pydantic._internal._decorators", "pydantic._internal._internal_dataclass", "pydantic._internal._mock_val_ser", "pydantic._internal._schema_generation_shared", "pydantic._internal._typing_extra", "pydantic._internal._dataclasses", "pydantic_core.core_schema", "pydantic._internal", "pydantic.annotated_handlers", "pydantic.config", "pydantic.errors", "pydantic.main", "__future__", "dataclasses", "inspect", "math", "re", "warnings", "collections", "copy", "enum", "typing", "pydantic_core", "typing_extensions", "pydantic", "builtins", "_collections_abc", "_decimal", "_operator", "_typeshed", "abc", "datetime", "pydantic._internal._generate_schema", "pydantic._internal._model_construction"], "hash": "caaf349fbc9b4779a4982cb2521e7a14fc72bb491c1ea5b1839a2588761850be", "id": "pydantic.json_schema", "ignore_all": true, "interface_hash": "6ca79ec82ffc1406e4b2b6e9cf16eb51d7c41b2c6ef51c74bd4640fd3f727886", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/pydantic/json_schema.py", "plugin_data": null, "size": 100969, "suppressed": [], "version_id": "1.7.1"}