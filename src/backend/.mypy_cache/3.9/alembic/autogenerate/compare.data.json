{".class": "MypyFile", "_fullname": "alembic.autogenerate.compare", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AlterColumnOp": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.AlterColumnOp", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AutogenContext": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.api.AutogenContext", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "DefaultImpl": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.impl.DefaultImpl", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ForeignKeyConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ForeignKeyConstraint", "kind": "Gdef"}, "Index": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Index", "kind": "Gdef"}, "Inspector": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.Inspector", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MigrationScript": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.MigrationScript", "kind": "Gdef"}, "ModifyTableOps": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.ModifyTableOps", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._py_collections.OrderedSet", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "TextClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TextClause", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UniqueConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.UniqueConstraint", "kind": "Gdef"}, "UpgradeOps": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.UpgradeOps", "kind": "Gdef"}, "_IndexColumnSortingOps": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "alembic.autogenerate.compare._IndexColumnSortingOps", "name": "_IndexColumnSortingOps", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.compare.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.compare.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.compare.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.compare.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.compare.__package__", "name": "__package__", "type": "builtins.str"}}, "_autogen_for_tables": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["autogen_context", "upgrade_ops", "schemas"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._autogen_for_tables", "name": "_autogen_for_tables", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["autogen_context", "upgrade_ops", "schemas"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.UpgradeOps", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "type_ref": "builtins.set"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_autogen_for_tables", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._autogen_for_tables", "name": "_autogen_for_tables", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_compare_column_comment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._compare_column_comment", "name": "_compare_column_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, "sqlalchemy.sql.elements.quoted_name", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_column_comment", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._compare_column_comment", "name": "_compare_column_comment", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_compare_columns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["schema", "tname", "conn_table", "metadata_table", "modify_table_ops", "autogen_context", "inspector"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._compare_columns", "name": "_compare_columns", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["schema", "tname", "conn_table", "metadata_table", "modify_table_ops", "autogen_context", "inspector"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, "sqlalchemy.sql.schema.Table", "sqlalchemy.sql.schema.Table", "alembic.operations.ops.ModifyTableOps", "alembic.autogenerate.api.AutogenContext", "sqlalchemy.engine.reflection.Inspector"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_columns", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._compare_columns", "name": "_compare_columns", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["schema", "tname", "conn_table", "metadata_table", "modify_table_ops", "autogen_context", "inspector"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, "sqlalchemy.sql.schema.Table", "sqlalchemy.sql.schema.Table", "alembic.operations.ops.ModifyTableOps", "alembic.autogenerate.api.AutogenContext", "sqlalchemy.engine.reflection.Inspector"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_columns", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_compare_computed_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._compare_computed_default", "name": "_compare_computed_default", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_computed_default", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_compare_foreign_keys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "modify_table_ops", "schema", "tname", "conn_table", "metadata_table"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._compare_foreign_keys", "name": "_compare_foreign_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "modify_table_ops", "schema", "tname", "conn_table", "metadata_table"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.ModifyTableOps", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_foreign_keys", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._compare_foreign_keys", "name": "_compare_foreign_keys", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_compare_identity_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._compare_identity_default", "name": "_compare_identity_default", "type": null}}, "_compare_indexes_and_uniques": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "modify_ops", "schema", "tname", "conn_table", "metadata_table"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._compare_indexes_and_uniques", "name": "_compare_indexes_and_uniques", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "modify_ops", "schema", "tname", "conn_table", "metadata_table"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.ModifyTableOps", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_indexes_and_uniques", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._compare_indexes_and_uniques", "name": "_compare_indexes_and_uniques", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_compare_nullable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._compare_nullable", "name": "_compare_nullable", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_nullable", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._compare_nullable", "name": "_compare_nullable", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_compare_server_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._compare_server_default", "name": "_compare_server_default", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_server_default", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._compare_server_default", "name": "_compare_server_default", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_compare_table_comment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "modify_table_ops", "schema", "tname", "conn_table", "metadata_table"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._compare_table_comment", "name": "_compare_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "modify_table_ops", "schema", "tname", "conn_table", "metadata_table"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.ModifyTableOps", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_table_comment", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._compare_table_comment", "name": "_compare_table_comment", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_compare_tables": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["conn_table_names", "metadata_table_names", "inspector", "upgrade_ops", "autogen_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._compare_tables", "name": "_compare_tables", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["conn_table_names", "metadata_table_names", "inspector", "upgrade_ops", "autogen_context"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "builtins.set"}, "sqlalchemy.engine.reflection.Inspector", "alembic.operations.ops.UpgradeOps", "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_tables", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_compare_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._compare_type", "name": "_compare_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_type", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._compare_type", "name": "_compare_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_constraint_sig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.autogenerate.compare._constraint_sig", "name": "_constraint_sig", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.autogenerate.compare._constraint_sig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.autogenerate.compare", "mro": ["alembic.autogenerate.compare._constraint_sig", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._constraint_sig.__eq__", "name": "__eq__", "type": null}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._constraint_sig.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.autogenerate.compare._constraint_sig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of _constraint_sig", "ret_type": "builtins.int", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "__ne__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._constraint_sig.__ne__", "name": "__ne__", "type": null}}, "const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.autogenerate.compare._constraint_sig.const", "name": "const", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.UniqueConstraint", "sqlalchemy.sql.schema.ForeignKeyConstraint", "sqlalchemy.sql.schema.Index"]}}}, "md_name_to_sql_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._constraint_sig.md_name_to_sql_name", "name": "md_name_to_sql_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["alembic.autogenerate.compare._constraint_sig", "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "md_name_to_sql_name of _constraint_sig", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.autogenerate.compare._constraint_sig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.autogenerate.compare._constraint_sig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_correct_for_uq_duplicates_uix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["conn_unique_constraints", "conn_indexes", "metadata_unique_constraints", "metadata_indexes", "dialect", "impl"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._correct_for_uq_duplicates_uix", "name": "_correct_for_uq_duplicates_uix", "type": null}}, "_fk_constraint_sig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.autogenerate.compare._constraint_sig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.autogenerate.compare._fk_constraint_sig", "name": "_fk_constraint_sig", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.autogenerate.compare._fk_constraint_sig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.autogenerate.compare", "mro": ["alembic.autogenerate.compare._fk_constraint_sig", "alembic.autogenerate.compare._constraint_sig", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "const", "include_options"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._fk_constraint_sig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "const", "include_options"], "arg_types": ["alembic.autogenerate.compare._fk_constraint_sig", "sqlalchemy.sql.schema.ForeignKeyConstraint", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _fk_constraint_sig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.compare._fk_constraint_sig.name", "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.base._NoneName", {".class": "NoneType"}]}}}, "sig": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._fk_constraint_sig.sig", "name": "sig", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}}}, "source_columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.compare._fk_constraint_sig.source_columns", "name": "source_columns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "source_schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.compare._fk_constraint_sig.source_schema", "name": "source_schema", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "source_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.compare._fk_constraint_sig.source_table", "name": "source_table", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "target_columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.compare._fk_constraint_sig.target_columns", "name": "target_columns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "target_schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.compare._fk_constraint_sig.target_schema", "name": "target_schema", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "target_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.compare._fk_constraint_sig.target_table", "name": "target_table", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.autogenerate.compare._fk_constraint_sig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.autogenerate.compare._fk_constraint_sig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_fk_spec": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._fk_spec", "kind": "Gdef"}, "_ix_constraint_sig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.autogenerate.compare._constraint_sig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.autogenerate.compare._ix_constraint_sig", "name": "_ix_constraint_sig", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.autogenerate.compare._ix_constraint_sig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.autogenerate.compare", "mro": ["alembic.autogenerate.compare._ix_constraint_sig", "alembic.autogenerate.compare._constraint_sig", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "const", "impl"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._ix_constraint_sig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "const", "impl"], "arg_types": ["alembic.autogenerate.compare._ix_constraint_sig", "sqlalchemy.sql.schema.Index", "alembic.ddl.impl.DefaultImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ix_constraint_sig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "column_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.autogenerate.compare._ix_constraint_sig.column_names", "name": "column_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.autogenerate.compare._ix_constraint_sig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "column_names of _ix_constraint_sig", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.sql.elements.quoted_name"], "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "builtins.list"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._ix_constraint_sig.column_names", "name": "column_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.autogenerate.compare._ix_constraint_sig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "column_names of _ix_constraint_sig", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.sql.elements.quoted_name"], "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "NoneType"}], "type_ref": "builtins.list"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "alembic.autogenerate.compare._ix_constraint_sig.is_index", "name": "is_index", "type": "builtins.bool"}}, "is_unique": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.compare._ix_constraint_sig.is_unique", "name": "is_unique", "type": "builtins.bool"}}, "md_name_to_sql_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._ix_constraint_sig.md_name_to_sql_name", "name": "md_name_to_sql_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["alembic.autogenerate.compare._ix_constraint_sig", "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "md_name_to_sql_name of _ix_constraint_sig", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.compare._ix_constraint_sig.name", "name": "name", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", {".class": "NoneType"}]}}}, "sig": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.compare._ix_constraint_sig.sig", "name": "sig", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.autogenerate.compare._ix_constraint_sig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.autogenerate.compare._ix_constraint_sig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_make_foreign_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["params", "conn_table"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._make_foreign_key", "name": "_make_foreign_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["params", "conn_table"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_foreign_key", "ret_type": "sqlalchemy.sql.schema.ForeignKeyConstraint", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_make_index": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["impl", "params", "conn_table"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._make_index", "name": "_make_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["impl", "params", "conn_table"], "arg_types": ["alembic.ddl.impl.DefaultImpl", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_index", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Index", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_make_unique_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["impl", "params", "conn_table"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._make_unique_constraint", "name": "_make_unique_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["impl", "params", "conn_table"], "arg_types": ["alembic.ddl.impl.DefaultImpl", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_unique_constraint", "ret_type": "sqlalchemy.sql.schema.UniqueConstraint", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_normalize_computed_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sqltext"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._normalize_computed_default", "name": "_normalize_computed_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sqltext"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_computed_default", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_populate_migration_script": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "migration_script"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._populate_migration_script", "name": "_populate_migration_script", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "migration_script"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.MigrationScript"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_populate_migration_script", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_produce_net_changes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "upgrade_ops"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._produce_net_changes", "name": "_produce_net_changes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "upgrade_ops"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.UpgradeOps"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_produce_net_changes", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_render_server_default_for_compare": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["metadata_default", "autogen_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._render_server_default_for_compare", "name": "_render_server_default_for_compare", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["metadata_default", "autogen_context"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}]}, "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_server_default_for_compare", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_setup_autoincrement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._setup_autoincrement", "name": "_setup_autoincrement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, "sqlalchemy.sql.elements.quoted_name", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setup_autoincrement", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._setup_autoincrement", "name": "_setup_autoincrement", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_uq_constraint_sig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.autogenerate.compare._constraint_sig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.autogenerate.compare._uq_constraint_sig", "name": "_uq_constraint_sig", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.autogenerate.compare._uq_constraint_sig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.autogenerate.compare", "mro": ["alembic.autogenerate.compare._uq_constraint_sig", "alembic.autogenerate.compare._constraint_sig", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "const", "impl"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._uq_constraint_sig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "const", "impl"], "arg_types": ["alembic.autogenerate.compare._uq_constraint_sig", "sqlalchemy.sql.schema.UniqueConstraint", "alembic.ddl.impl.DefaultImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _uq_constraint_sig", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "column_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.autogenerate.compare._uq_constraint_sig.column_names", "name": "column_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.autogenerate.compare._uq_constraint_sig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "column_names of _uq_constraint_sig", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._uq_constraint_sig.column_names", "name": "column_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.autogenerate.compare._uq_constraint_sig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "column_names of _uq_constraint_sig", "ret_type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "is_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "alembic.autogenerate.compare._uq_constraint_sig.is_index", "name": "is_index", "type": "builtins.bool"}}, "is_unique": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "alembic.autogenerate.compare._uq_constraint_sig.is_unique", "name": "is_unique", "type": "builtins.bool"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.compare._uq_constraint_sig.name", "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.base._NoneName", {".class": "NoneType"}]}}}, "sig": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.compare._uq_constraint_sig.sig", "name": "sig", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.autogenerate.compare._uq_constraint_sig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.autogenerate.compare._uq_constraint_sig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_warn_computed_not_supported": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tname", "cname"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.autogenerate.compare._warn_computed_not_supported", "name": "_warn_computed_not_supported", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["tname", "cname"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_warn_computed_not_supported", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "comparators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.autogenerate.compare.comparators", "name": "comparators", "type": "alembic.util.langhelpers.Dispatcher"}}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "event": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event", "kind": "Gdef"}, "expression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.expression", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.inspection.inspect", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.autogenerate.compare.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "ops": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops", "kind": "Gdef"}, "quoted_name": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.quoted_name", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "sa_schema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.schema", "kind": "Gdef"}, "sqla_compat": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.types", "kind": "Gdef"}, "text": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.text", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "alembic.util", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/autogenerate/compare.py"}