{"data_mtime": 1754228260, "dep_lines": [23, 26, 28, 29, 34, 35, 37, 43, 44, 18, 20, 22, 23, 24, 27, 28, 1, 3, 4, 5, 6, 18, 27, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 25, 25, 25, 25, 25, 10, 10, 10, 20, 5, 10, 20, 5, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.expression", "alembic.ddl.base", "alembic.operations.ops", "alembic.util.sqla_compat", "sqlalchemy.engine.reflection", "sqlalchemy.sql.elements", "sqlalchemy.sql.schema", "alembic.autogenerate.api", "alembic.ddl.impl", "sqlalchemy.event", "sqlalchemy.schema", "sqlalchemy.types", "sqlalchemy.sql", "sqlalchemy.util", "alembic.util", "alembic.operations", "__future__", "contextlib", "logging", "re", "typing", "sqlalchemy", "alembic", "builtins", "abc", "alembic.ddl", "alembic.util.langhelpers", "enum", "sqlalchemy.engine", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._elements_constructors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "8120a3c6b910025d2b243ea8f4b9fcc0dc4654d53a16b5b3299615907e5399a7", "id": "alembic.autogenerate.compare", "ignore_all": true, "interface_hash": "62c8627729fa8a7333b76d98b71b2e02d141609f975c61da126372cf1335059c", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/autogenerate/compare.py", "plugin_data": null, "size": 47042, "suppressed": [], "version_id": "1.7.1"}