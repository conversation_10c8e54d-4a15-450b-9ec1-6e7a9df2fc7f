{".class": "MypyFile", "_fullname": "alembic.autogenerate", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "RevisionContext": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.api.RevisionContext", "kind": "Gdef"}, "Rewriter": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.rewriter.Rewriter", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "_produce_net_changes": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.compare._produce_net_changes", "kind": "Gdef"}, "_render_migration_diffs": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.api._render_migration_diffs", "kind": "Gdef"}, "comparators": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.compare.comparators", "kind": "Gdef"}, "compare_metadata": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.api.compare_metadata", "kind": "Gdef"}, "produce_migrations": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.api.produce_migrations", "kind": "Gdef"}, "render_op_text": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.render.render_op_text", "kind": "Gdef"}, "render_python_code": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.api.render_python_code", "kind": "Gdef"}, "renderers": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.render.renderers", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/autogenerate/__init__.py"}