{"data_mtime": 1754228260, "dep_lines": [1, 6, 8, 10, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30], "dependencies": ["alembic.autogenerate.api", "alembic.autogenerate.compare", "alembic.autogenerate.render", "alembic.autogenerate.rewriter", "builtins", "abc", "typing"], "hash": "e081e0587f3da45a2b46afb20836611a38d5b6c7c65f910258d3ee52cf271949", "id": "alembic.autogenerate", "ignore_all": true, "interface_hash": "f53eccfe47a45f034498b1858e789934874d209c93fd7a762bf67284d486c8cd", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/autogenerate/__init__.py", "plugin_data": null, "size": 351, "suppressed": [], "version_id": "1.7.1"}