{"data_mtime": 1754228260, "dep_lines": [16, 17, 19, 28, 35, 39, 40, 42, 16, 18, 19, 25, 31, 1, 3, 4, 14, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 25, 25, 25, 25, 25, 20, 10, 20, 25, 25, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["alembic.autogenerate.compare", "alembic.autogenerate.render", "alembic.operations.ops", "sqlalchemy.sql.schema", "alembic.runtime.environment", "alembic.runtime.migration", "alembic.script.base", "alembic.script.revision", "alembic.autogenerate", "alembic.util", "alembic.operations", "sqlalchemy.engine", "alembic.config", "__future__", "contextlib", "typing", "sqlalchemy", "alembic", "builtins", "abc", "alembic.runtime", "alembic.script", "alembic.util.exc", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.reflection", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql", "sqlalchemy.sql.base", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "30d9f45ed9a3e3868c1637e24742cc91bc4eca71f28b2691067ae3e449089a6e", "id": "alembic.autogenerate.api", "ignore_all": true, "interface_hash": "9a3c0969fe27665eaef18d8f6a661b66ae5b693f87c57a665b4edf0ab73a381e", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/autogenerate/api.py", "plugin_data": null, "size": 21967, "suppressed": [], "version_id": "1.7.1"}