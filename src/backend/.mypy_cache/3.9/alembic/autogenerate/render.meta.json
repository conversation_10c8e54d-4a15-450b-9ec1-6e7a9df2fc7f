{"data_mtime": 1754228260, "dep_lines": [18, 22, 23, 28, 31, 41, 42, 44, 15, 16, 17, 21, 22, 45, 1, 3, 4, 5, 15, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 14], "dep_prios": [5, 10, 10, 25, 25, 25, 25, 25, 10, 10, 10, 10, 20, 25, 5, 5, 10, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["sqlalchemy.sql.elements", "alembic.operations.ops", "alembic.util.sqla_compat", "sqlalchemy.sql.base", "sqlalchemy.sql.schema", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "alembic.autogenerate.api", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.types", "alembic.util", "alembic.operations", "alembic.config", "__future__", "io", "re", "typing", "sqlalchemy", "alembic", "builtins", "abc", "alembic.util.langhelpers", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "16293668f6442313a54c2ac17745223f19d7e52146fff0af7d7a8c5a826bea5c", "id": "alembic.autogenerate.render", "ignore_all": true, "interface_hash": "088b7e9a6dd53eec48f17c89cd4a73f5ee6ce679d31cf88a18c676bff39149f4", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/autogenerate/render.py", "plugin_data": null, "size": 34475, "suppressed": ["mako.pygen"], "version_id": "1.7.1"}