{"data_mtime": 1754228260, "dep_lines": [13, 23, 24, 12, 13, 1, 3, 12, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 25, 25, 10, 20, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["alembic.operations.ops", "alembic.runtime.environment", "alembic.runtime.migration", "alembic.util", "alembic.operations", "__future__", "typing", "alembic", "builtins", "_typeshed", "abc", "alembic.runtime", "alembic.util.langhelpers", "sqlalchemy", "sqlalchemy.util", "sqlalchemy.util._collections"], "hash": "3ac6daf061557aa897d72a46256ec0c6dd2e8b61113a5685b5565d3056e1cd9d", "id": "alembic.autogenerate.rewriter", "ignore_all": true, "interface_hash": "aaf4a131ebfe763a3e13b3e336a6273b9e7c83fbaf07bad117419cf6b520c306", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/autogenerate/rewriter.py", "plugin_data": null, "size": 7384, "suppressed": [], "version_id": "1.7.1"}