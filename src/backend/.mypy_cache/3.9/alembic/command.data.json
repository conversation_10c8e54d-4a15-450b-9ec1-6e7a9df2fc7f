{".class": "MypyFile", "_fullname": "alembic.command", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Config": {".class": "SymbolTableNode", "cross_ref": "alembic.config.Config", "kind": "Gdef"}, "EnvironmentContext": {".class": "SymbolTableNode", "cross_ref": "alembic.runtime.environment.EnvironmentContext", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ProcessRevisionDirectiveFn": {".class": "SymbolTableNode", "cross_ref": "alembic.runtime.environment.ProcessRevisionDirectiveFn", "kind": "Gdef"}, "Script": {".class": "SymbolTableNode", "cross_ref": "alembic.script.base.Script", "kind": "Gdef"}, "ScriptDirectory": {".class": "SymbolTableNode", "cross_ref": "alembic.script.base.ScriptDirectory", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_RevIdType": {".class": "SymbolTableNode", "cross_ref": "alembic.script.revision._RevIdType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.command.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.command.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.command.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.command.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.command.__package__", "name": "__package__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "autogen": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate", "kind": "Gdef"}, "branches": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["config", "verbose"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.command.branches", "name": "branches", "type": null}}, "check": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.command.check", "name": "check", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["alembic.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "current": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["config", "verbose"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.command.current", "name": "current", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["config", "verbose"], "arg_types": ["alembic.config.Config", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "downgrade": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["config", "revision", "sql", "tag"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.command.downgrade", "name": "downgrade", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["config", "revision", "sql", "tag"], "arg_types": ["alembic.config.Config", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "downgrade", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "edit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["config", "rev"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.command.edit", "name": "edit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["config", "rev"], "arg_types": ["alembic.config.Config", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "edit", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "ensure_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["config", "sql"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.command.ensure_version", "name": "ensure_version", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["config", "sql"], "arg_types": ["alembic.config.Config", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_version", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "heads": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["config", "verbose", "resolve_dependencies"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.command.heads", "name": "heads", "type": null}}, "history": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["config", "rev_range", "verbose", "indicate_current"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.command.history", "name": "history", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["config", "rev_range", "verbose", "indicate_current"], "arg_types": ["alembic.config.Config", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "history", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "init": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["config", "directory", "template", "package"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.command.init", "name": "init", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["config", "directory", "template", "package"], "arg_types": ["alembic.config.Config", "builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "list_templates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.command.list_templates", "name": "list_templates", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["alembic.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_templates", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "merge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["config", "revisions", "message", "branch_label", "rev_id"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.command.merge", "name": "merge", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["config", "revisions", "message", "branch_label", "rev_id"], "arg_types": ["alembic.config.Config", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevIdType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge", "ret_type": {".class": "UnionType", "items": ["alembic.script.base.Script", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "revision": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["config", "message", "autogenerate", "sql", "head", "splice", "branch_label", "version_path", "rev_id", "depends_on", "process_revision_directives"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.command.revision", "name": "revision", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["config", "message", "autogenerate", "sql", "head", "splice", "branch_label", "version_path", "rev_id", "depends_on", "process_revision_directives"], "arg_types": ["alembic.config.Config", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", "builtins.bool", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.runtime.environment.ProcessRevisionDirectiveFn"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "revision", "ret_type": {".class": "UnionType", "items": ["alembic.script.base.Script", {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["alembic.script.base.Script", {".class": "NoneType"}]}], "type_ref": "builtins.list"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "show": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["config", "rev"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.command.show", "name": "show", "type": null}}, "stamp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["config", "revision", "sql", "tag", "purge"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.command.stamp", "name": "stamp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["config", "revision", "sql", "tag", "purge"], "arg_types": ["alembic.config.Config", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._RevIdType"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stamp", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "upgrade": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["config", "revision", "sql", "tag"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.command.upgrade", "name": "upgrade", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["config", "revision", "sql", "tag"], "arg_types": ["alembic.config.Config", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "upgrade", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "util": {".class": "SymbolTableNode", "cross_ref": "alembic.util", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/command.py"}