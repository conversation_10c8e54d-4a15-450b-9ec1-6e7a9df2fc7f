{"data_mtime": 1754228260, "dep_lines": [11, 14, 16, 25, 28, 29, 30, 32, 8, 10, 12, 1, 3, 4, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 25, 25, 25, 10, 10, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.ext.compiler", "sqlalchemy.sql.elements", "alembic.util.sqla_compat", "sqlalchemy.sql.compiler", "sqlalchemy.sql.functions", "sqlalchemy.sql.schema", "sqlalchemy.sql.type_api", "alembic.ddl.impl", "sqlalchemy.exc", "sqlalchemy.types", "sqlalchemy.schema", "__future__", "functools", "typing", "sqlalchemy", "builtins", "abc", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.ext", "sqlalchemy.inspection", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "70263736574c46082b29df5b67498544113d18d0da032d1425c3377b2e14b60c", "id": "alembic.ddl.base", "ignore_all": true, "interface_hash": "1efcc39a0735d57bd9c19575e40d9f8830e2805f38a52e11f2f0c2b6f6064295", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/ddl/base.py", "plugin_data": null, "size": 9638, "suppressed": [], "version_id": "1.7.1"}