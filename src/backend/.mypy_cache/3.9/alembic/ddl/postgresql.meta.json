{"data_mtime": 1754228260, "dep_lines": [55, 56, 57, 58, 20, 24, 25, 28, 31, 41, 43, 44, 45, 46, 48, 63, 65, 68, 70, 19, 23, 24, 42, 43, 44, 1, 3, 4, 5, 15, 42, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 5, 10, 5, 5, 5, 5, 10, 10, 10, 5, 10, 25, 25, 25, 25, 5, 5, 20, 10, 20, 20, 5, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.array", "sqlalchemy.dialects.postgresql.base", "sqlalchemy.dialects.postgresql.hstore", "sqlalchemy.dialects.postgresql.json", "sqlalchemy.dialects.postgresql", "sqlalchemy.sql.operators", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "alembic.ddl.base", "alembic.ddl.impl", "alembic.autogenerate.render", "alembic.operations.ops", "alembic.operations.schemaobj", "alembic.operations.base", "alembic.util.sqla_compat", "sqlalchemy.sql.schema", "sqlalchemy.sql.type_api", "alembic.autogenerate.api", "alembic.runtime.migration", "sqlalchemy.types", "sqlalchemy.schema", "sqlalchemy.sql", "alembic.util", "alembic.autogenerate", "alembic.operations", "__future__", "logging", "re", "typing", "sqlalchemy", "alembic", "builtins", "abc", "alembic.operations.batch", "alembic.runtime", "alembic.util.langhelpers", "enum", "sqlalchemy.dialects", "sqlalchemy.dialects.postgresql.ext", "sqlalchemy.engine", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.ext", "sqlalchemy.ext.compiler", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.ddl", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "68ef29715379c9cc35c06da6d5146df1d4140f52a045ae93e2b4b383d14f0a45", "id": "alembic.ddl.postgresql", "ignore_all": true, "interface_hash": "c4fc5a6bdaaa49357261a18351af4ac83524fc6e620dd5106bfabfa8c637a7b2", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/ddl/postgresql.py", "plugin_data": null, "size": 26457, "suppressed": [], "version_id": "1.7.1"}