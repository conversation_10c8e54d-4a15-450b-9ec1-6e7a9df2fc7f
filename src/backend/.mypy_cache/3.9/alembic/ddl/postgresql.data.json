{".class": "MypyFile", "_fullname": "alembic.ddl.postgresql", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ARRAY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.array.ARRAY", "kind": "Gdef"}, "AlterColumn": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.AlterColumn", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AutogenContext": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.api.AutogenContext", "kind": "Gdef"}, "BIGINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BIGINT", "kind": "Gdef"}, "BatchOperations": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.base.BatchOperations", "kind": "Gdef"}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "ColumnClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnClause", "kind": "Gdef"}, "ColumnComment": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.ColumnComment", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "CreateExcludeConstraintOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.ops.AddConstraintOp"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp", "name": "CreateExcludeConstraintOp", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl.postgresql", "mro": ["alembic.ddl.postgresql.CreateExcludeConstraintOp", "alembic.operations.ops.AddConstraintOp", "alembic.operations.ops.MigrateOperation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "constraint_name", "table_name", "elements", "where", "schema", "_orig_constraint", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "constraint_name", "table_name", "elements", "where", "schema", "_orig_constraint", "kw"], "arg_types": ["alembic.ddl.postgresql.CreateExcludeConstraintOp", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintName"}, {".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.elements.quoted_name"]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnClause"}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Sequence"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.dialects.postgresql.ext.ExcludeConstraint", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CreateExcludeConstraintOp", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_orig_constraint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp._orig_constraint", "name": "_orig_constraint", "type": {".class": "UnionType", "items": ["sqlalchemy.dialects.postgresql.ext.ExcludeConstraint", {".class": "NoneType"}]}}}, "batch_create_exclude_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["cls", "operations", "constraint_name", "elements", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp.batch_create_exclude_constraint", "name": "batch_create_exclude_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["cls", "operations", "constraint_name", "elements", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.ddl.postgresql.CreateExcludeConstraintOp"}, "alembic.operations.base.BatchOperations", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_create_exclude_constraint of CreateExcludeConstraintOp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp.batch_create_exclude_constraint", "name": "batch_create_exclude_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["cls", "operations", "constraint_name", "elements", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.ddl.postgresql.CreateExcludeConstraintOp"}, "alembic.operations.base.BatchOperations", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_create_exclude_constraint of CreateExcludeConstraintOp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "constraint_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp.constraint_name", "name": "constraint_name", "type": {".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintName"}}}, "constraint_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp.constraint_type", "name": "constraint_type", "type": "builtins.str"}}, "create_exclude_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 2, 4], "arg_names": ["cls", "operations", "constraint_name", "table_name", "elements", "kw"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp.create_exclude_constraint", "name": "create_exclude_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 2, 4], "arg_names": ["cls", "operations", "constraint_name", "table_name", "elements", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.ddl.postgresql.CreateExcludeConstraintOp"}, "alembic.operations.base.Operations", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_exclude_constraint of CreateExcludeConstraintOp", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp.create_exclude_constraint", "name": "create_exclude_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 2, 4], "arg_names": ["cls", "operations", "constraint_name", "table_name", "elements", "kw"], "arg_types": [{".class": "TypeType", "item": "alembic.ddl.postgresql.CreateExcludeConstraintOp"}, "alembic.operations.base.Operations", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_exclude_constraint of CreateExcludeConstraintOp", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "elements": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp.elements", "name": "elements", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnClause"}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "typing.Sequence"}]}}}, "from_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp.from_constraint", "name": "from_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "arg_types": [{".class": "TypeType", "item": "alembic.ddl.postgresql.CreateExcludeConstraintOp"}, "sqlalchemy.dialects.postgresql.ext.ExcludeConstraint"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_constraint of CreateExcludeConstraintOp", "ret_type": "alembic.ddl.postgresql.CreateExcludeConstraintOp", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp.from_constraint", "name": "from_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "constraint"], "arg_types": [{".class": "TypeType", "item": "alembic.ddl.postgresql.CreateExcludeConstraintOp"}, "sqlalchemy.dialects.postgresql.ext.ExcludeConstraint"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_constraint of CreateExcludeConstraintOp", "ret_type": "alembic.ddl.postgresql.CreateExcludeConstraintOp", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "kw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp.kw", "name": "kw", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "type_ref": "builtins.dict"}}}, "schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp.schema", "name": "schema", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp.table_name", "name": "table_name", "type": {".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.elements.quoted_name"]}}}, "to_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp.to_constraint", "name": "to_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "migration_context"], "arg_types": ["alembic.ddl.postgresql.CreateExcludeConstraintOp", {".class": "UnionType", "items": ["alembic.runtime.migration.MigrationContext", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_constraint of CreateExcludeConstraintOp", "ret_type": "sqlalchemy.dialects.postgresql.ext.ExcludeConstraint", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "where": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp.where", "name": "where", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "builtins.str", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.postgresql.CreateExcludeConstraintOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.postgresql.CreateExcludeConstraintOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CreateIndex": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.CreateIndex", "kind": "Gdef"}, "DefaultImpl": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.impl.DefaultImpl", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ExcludeConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.ext.ExcludeConstraint", "kind": "Gdef"}, "FunctionElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.FunctionElement", "kind": "Gdef"}, "HSTORE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.hstore.HSTORE", "kind": "Gdef"}, "INTEGER": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.INTEGER", "kind": "Gdef"}, "IdentityColumnDefault": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.IdentityColumnDefault", "kind": "Gdef"}, "Index": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Index", "kind": "Gdef"}, "JSON": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.json.JSON", "kind": "Gdef"}, "JSONB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.json.JSONB", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "MetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.MetaData", "kind": "Gdef"}, "MigrationContext": {".class": "SymbolTableNode", "cross_ref": "alembic.runtime.migration.MigrationContext", "kind": "Gdef"}, "NULLTYPE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.NULLTYPE", "kind": "Gdef"}, "Numeric": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Numeric", "kind": "Gdef"}, "Operations": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.base.Operations", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PGDDLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.postgresql.base.PGDDLCompiler", "kind": "Gdef"}, "PostgresqlColumnType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.ddl.base.AlterColumn"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.postgresql.PostgresqlColumnType", "name": "PostgresqlColumnType", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlColumnType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl.postgresql", "mro": ["alembic.ddl.postgresql.PostgresqlColumnType", "alembic.ddl.base.AlterColumn", "alembic.ddl.base.AlterTable", "sqlalchemy.sql.ddl.ExecutableDDLElement", "sqlalchemy.sql.roles.DDLRole", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.ddl.BaseDDLElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "name", "column_name", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlColumnType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "name", "column_name", "type_", "kw"], "arg_types": ["alembic.ddl.postgresql.PostgresqlColumnType", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PostgresqlColumnType", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "type_": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.postgresql.PostgresqlColumnType.type_", "name": "type_", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}}, "using": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.postgresql.PostgresqlColumnType.using", "name": "using", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.postgresql.PostgresqlColumnType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.postgresql.PostgresqlColumnType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PostgresqlImpl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.ddl.impl.DefaultImpl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.postgresql.PostgresqlImpl", "name": "PostgresqlImpl", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl", "has_param_spec_type": false, "metaclass_type": "alembic.ddl.impl.ImplMeta", "metadata": {}, "module_name": "alembic.ddl.postgresql", "mro": ["alembic.ddl.postgresql.PostgresqlImpl", "alembic.ddl.impl.DefaultImpl", "builtins.object"], "names": {".class": "SymbolTable", "__dialect__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl.postgresql.PostgresqlImpl.__dialect__", "name": "__dialect__", "type": "builtins.str"}}, "_cleanup_index_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "index", "expr", "remove_suffix"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl._cleanup_index_expr", "name": "_cleanup_index_expr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "index", "expr", "remove_suffix"], "arg_types": ["alembic.ddl.postgresql.PostgresqlImpl", "sqlalchemy.sql.schema.Index", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cleanup_index_expr of PostgresqlImpl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_compile_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "element"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl._compile_element", "name": "_compile_element", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "element"], "arg_types": ["alembic.ddl.postgresql.PostgresqlImpl", "sqlalchemy.sql.elements.ClauseElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compile_element of PostgresqlImpl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_default_modifiers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exp"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl._default_modifiers", "name": "_default_modifiers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exp"], "arg_types": ["alembic.ddl.postgresql.PostgresqlImpl", "sqlalchemy.sql.elements.ClauseElement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_default_modifiers of PostgresqlImpl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_dialect_sig": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl._dialect_sig", "name": "_dialect_sig", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["alembic.ddl.postgresql.PostgresqlImpl", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Index", "sqlalchemy.sql.schema.UniqueConstraint"]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dialect_sig of PostgresqlImpl", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_render_ARRAY_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "type_", "autogen_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl._render_ARRAY_type", "name": "_render_ARRAY_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "type_", "autogen_context"], "arg_types": ["alembic.ddl.postgresql.PostgresqlImpl", "sqlalchemy.dialects.postgresql.array.ARRAY", "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_ARRAY_type of PostgresqlImpl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_render_HSTORE_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "type_", "autogen_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl._render_HSTORE_type", "name": "_render_HSTORE_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "type_", "autogen_context"], "arg_types": ["alembic.ddl.postgresql.PostgresqlImpl", "sqlalchemy.dialects.postgresql.hstore.HSTORE", "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_HSTORE_type of PostgresqlImpl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_render_JSONB_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "type_", "autogen_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl._render_JSONB_type", "name": "_render_JSONB_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "type_", "autogen_context"], "arg_types": ["alembic.ddl.postgresql.PostgresqlImpl", "sqlalchemy.dialects.postgresql.json.JSONB", "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_JSONB_type of PostgresqlImpl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_render_JSON_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "type_", "autogen_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl._render_JSON_type", "name": "_render_JSON_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "type_", "autogen_context"], "arg_types": ["alembic.ddl.postgresql.PostgresqlImpl", "sqlalchemy.dialects.postgresql.json.JSON", "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_JSON_type of PostgresqlImpl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "adjust_reflected_dialect_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "reflected_options", "kind"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl.adjust_reflected_dialect_options", "name": "adjust_reflected_dialect_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "reflected_options", "kind"], "arg_types": ["alembic.ddl.postgresql.PostgresqlImpl", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adjust_reflected_dialect_options of PostgresqlImpl", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "alter_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "table_name", "column_name", "nullable", "server_default", "name", "type_", "schema", "autoincrement", "existing_type", "existing_server_default", "existing_nullable", "existing_autoincrement", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl.alter_column", "name": "alter_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "table_name", "column_name", "nullable", "server_default", "name", "type_", "schema", "autoincrement", "existing_type", "existing_server_default", "existing_nullable", "existing_autoincrement", "kw"], "arg_types": ["alembic.ddl.postgresql.PostgresqlImpl", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.ddl.base._ServerDefault"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.TextClause", "sqlalchemy.sql.schema.FetchedValue", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.sql.functions.Function"}, "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alter_column of PostgresqlImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "autogen_column_reflect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "inspector", "table", "column_info"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl.autogen_column_reflect", "name": "autogen_column_reflect", "type": null}}, "compare_server_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "inspector_column", "metadata_column", "rendered_metadata_default", "rendered_inspector_default"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl.compare_server_default", "name": "compare_server_default", "type": null}}, "correct_for_autogen_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "conn_unique_constraints", "conn_indexes", "metadata_unique_constraints", "metadata_indexes"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl.correct_for_autogen_constraints", "name": "correct_for_autogen_constraints", "type": null}}, "create_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "index", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl.create_index", "name": "create_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "index", "kw"], "arg_types": ["alembic.ddl.postgresql.PostgresqlImpl", "sqlalchemy.sql.schema.Index", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_index of PostgresqlImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_index_sig": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl.create_index_sig", "name": "create_index_sig", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": ["alembic.ddl.postgresql.PostgresqlImpl", "sqlalchemy.sql.schema.Index"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_index_sig of PostgresqlImpl", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_unique_constraint_sig": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "const"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl.create_unique_constraint_sig", "name": "create_unique_constraint_sig", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "const"], "arg_types": ["alembic.ddl.postgresql.PostgresqlImpl", "sqlalchemy.sql.schema.UniqueConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unique_constraint_sig of PostgresqlImpl", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "prep_table_for_batch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "batch_impl", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl.prep_table_for_batch", "name": "prep_table_for_batch", "type": null}}, "render_ddl_sql_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "expr", "is_server_default", "is_index", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl.render_ddl_sql_expr", "name": "render_ddl_sql_expr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "expr", "is_server_default", "is_index", "kw"], "arg_types": ["alembic.ddl.postgresql.PostgresqlImpl", "sqlalchemy.sql.elements.ClauseElement", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_ddl_sql_expr of PostgresqlImpl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "render_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "type_", "autogen_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql.PostgresqlImpl.render_type", "name": "render_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "type_", "autogen_context"], "arg_types": ["alembic.ddl.postgresql.PostgresqlImpl", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_type of PostgresqlImpl", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "transactional_ddl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl.postgresql.PostgresqlImpl.transactional_ddl", "name": "transactional_ddl", "type": "builtins.bool"}}, "type_synonyms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl.postgresql.PostgresqlImpl.type_synonyms", "name": "type_synonyms", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.set"}], "type_ref": "builtins.tuple"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.postgresql.PostgresqlImpl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.postgresql.PostgresqlImpl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RenameTable": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.RenameTable", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "TextClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TextClause", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "UnaryExpression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.UnaryExpression", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UniqueConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.UniqueConstraint", "kind": "Gdef"}, "_ServerDefault": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base._ServerDefault", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.postgresql.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.postgresql.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.postgresql.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.postgresql.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.postgresql.__package__", "name": "__package__", "type": "builtins.str"}}, "_add_exclude_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.postgresql._add_exclude_constraint", "name": "_add_exclude_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.ddl.postgresql.CreateExcludeConstraintOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_exclude_constraint", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.postgresql._add_exclude_constraint", "name": "_add_exclude_constraint", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_exclude_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "alter"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql._exclude_constraint", "name": "_exclude_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "alter"], "arg_types": ["sqlalchemy.dialects.postgresql.ext.ExcludeConstraint", "alembic.autogenerate.api.AutogenContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_exclude_constraint", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_f_name": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.render._f_name", "kind": "Gdef"}, "_postgresql_autogenerate_prefix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["autogen_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql._postgresql_autogenerate_prefix", "name": "_postgresql_autogenerate_prefix", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["autogen_context"], "arg_types": ["alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_postgresql_autogenerate_prefix", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_render_inline_exclude_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "namespace_metadata"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.postgresql._render_inline_exclude_constraint", "name": "_render_inline_exclude_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "namespace_metadata"], "arg_types": ["sqlalchemy.dialects.postgresql.ext.ExcludeConstraint", "alembic.autogenerate.api.AutogenContext", "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_inline_exclude_constraint", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.postgresql._render_inline_exclude_constraint", "name": "_render_inline_exclude_constraint", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_render_potential_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["value", "autogen_context"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.postgresql._render_potential_column", "name": "_render_potential_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["value", "autogen_context"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnClause"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, "sqlalchemy.sql.elements.TextClause", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.functions.FunctionElement"}]}, "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_potential_column", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "alter_column": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.alter_column", "kind": "Gdef"}, "alter_table": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.alter_table", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "compiles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.compiler.compiles", "kind": "Gdef"}, "format_column_name": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.format_column_name", "kind": "Gdef"}, "format_table_name": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.format_table_name", "kind": "Gdef"}, "format_type": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.format_type", "kind": "Gdef"}, "literal_column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.literal_column", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.ddl.postgresql.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "operators": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators", "kind": "Gdef"}, "ops": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops", "kind": "Gdef"}, "quoted_name": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.quoted_name", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "render": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.render", "kind": "Gdef"}, "schemaobj": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.schemaobj", "kind": "Gdef"}, "sqla_compat": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.types", "kind": "Gdef"}, "text": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.text", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "alembic.util", "kind": "Gdef"}, "visit_column_comment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.postgresql.visit_column_comment", "name": "visit_column_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.ColumnComment", "sqlalchemy.dialects.postgresql.base.PGDDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_column_comment", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.postgresql.visit_column_comment", "name": "visit_column_comment", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "visit_column_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.postgresql.visit_column_type", "name": "visit_column_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.postgresql.PostgresqlColumnType", "sqlalchemy.dialects.postgresql.base.PGDDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_column_type", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.postgresql.visit_column_type", "name": "visit_column_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "visit_identity_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.postgresql.visit_identity_column", "name": "visit_identity_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.IdentityColumnDefault", "sqlalchemy.dialects.postgresql.base.PGDDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_identity_column", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.postgresql.visit_identity_column", "name": "visit_identity_column", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "visit_rename_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.postgresql.visit_rename_table", "name": "visit_rename_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.RenameTable", "sqlalchemy.dialects.postgresql.base.PGDDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_rename_table", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.postgresql.visit_rename_table", "name": "visit_rename_table", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/ddl/postgresql.py"}