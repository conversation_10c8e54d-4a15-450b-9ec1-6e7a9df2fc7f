{"data_mtime": 1754228260, "dep_lines": [23, 25, 33, 34, 37, 39, 45, 46, 49, 50, 20, 23, 24, 31, 35, 1, 3, 4, 5, 19, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 25, 25, 25, 25, 25, 25, 25, 25, 10, 20, 10, 25, 25, 5, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["alembic.ddl.base", "alembic.util.sqla_compat", "sqlalchemy.engine.cursor", "sqlalchemy.engine.reflection", "sqlalchemy.sql.elements", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.type_api", "alembic.autogenerate.api", "alembic.operations.batch", "sqlalchemy.schema", "alembic.ddl", "alembic.util", "sqlalchemy.engine", "sqlalchemy.sql", "__future__", "collections", "re", "typing", "sqlalchemy", "alembic", "builtins", "abc", "alembic.autogenerate", "alembic.operations", "alembic.util.exc", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.result", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.functions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "6771a934cd8ac01a5f9755026a6d58b186d2774990cd18a038a4215022c83e01", "id": "alembic.ddl.impl", "ignore_all": true, "interface_hash": "f6a7ef692ced03a4fb540517cb83b82f5a527c43d3b839eba8503fcf65925400", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/ddl/impl.py", "plugin_data": null, "size": 25564, "suppressed": [], "version_id": "1.7.1"}