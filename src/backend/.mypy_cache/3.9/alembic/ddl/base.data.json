{".class": "MypyFile", "_fullname": "alembic.ddl.base", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AddColumn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.ddl.base.AlterTable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.base.AddColumn", "name": "AddColumn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.ddl.base.AddColumn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl.base", "mro": ["alembic.ddl.base.AddColumn", "alembic.ddl.base.AlterTable", "sqlalchemy.sql.ddl.ExecutableDDLElement", "sqlalchemy.sql.roles.DDLRole", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.ddl.BaseDDLElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "column", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.AddColumn.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "column", "schema"], "arg_types": ["alembic.ddl.base.AddColumn", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AddColumn", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.AddColumn.column", "name": "column", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.base.AddColumn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.base.AddColumn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AlterColumn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.ddl.base.AlterTable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.base.AlterColumn", "name": "AlterColumn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.ddl.base.AlterColumn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl.base", "mro": ["alembic.ddl.base.AlterColumn", "alembic.ddl.base.AlterTable", "sqlalchemy.sql.ddl.ExecutableDDLElement", "sqlalchemy.sql.roles.DDLRole", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.ddl.BaseDDLElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "column_name", "schema", "existing_type", "existing_nullable", "existing_server_default", "existing_comment"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.AlterColumn.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "column_name", "schema", "existing_type", "existing_nullable", "existing_server_default", "existing_comment"], "arg_types": ["alembic.ddl.base.AlterColumn", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.TextClause", "sqlalchemy.sql.schema.FetchedValue", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.sql.functions.Function"}, "builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AlterColumn", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "column_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.AlterColumn.column_name", "name": "column_name", "type": "builtins.str"}}, "existing_comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.AlterColumn.existing_comment", "name": "existing_comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}, "existing_nullable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.AlterColumn.existing_nullable", "name": "existing_nullable", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}}}, "existing_server_default": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.AlterColumn.existing_server_default", "name": "existing_server_default", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.elements.TextClause", "sqlalchemy.sql.schema.FetchedValue", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.sql.functions.Function"}, "builtins.str", {".class": "NoneType"}]}}}, "existing_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.AlterColumn.existing_type", "name": "existing_type", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.base.AlterColumn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.base.AlterColumn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AlterTable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.ddl.ExecutableDDLElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.base.AlterTable", "name": "AlterTable", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.ddl.base.AlterTable", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl.base", "mro": ["alembic.ddl.base.AlterTable", "sqlalchemy.sql.ddl.ExecutableDDLElement", "sqlalchemy.sql.roles.DDLRole", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.ddl.BaseDDLElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "table_name", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.AlterTable.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "table_name", "schema"], "arg_types": ["alembic.ddl.base.AlterTable", "builtins.str", {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AlterTable", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.AlterTable.schema", "name": "schema", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str", {".class": "NoneType"}]}}}, "table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.AlterTable.table_name", "name": "table_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.base.AlterTable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.base.AlterTable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "ColumnComment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.ddl.base.AlterColumn"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.base.ColumnComment", "name": "ColumnComment", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.ddl.base.ColumnComment", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl.base", "mro": ["alembic.ddl.base.ColumnComment", "alembic.ddl.base.AlterColumn", "alembic.ddl.base.AlterTable", "sqlalchemy.sql.ddl.ExecutableDDLElement", "sqlalchemy.sql.roles.DDLRole", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.ddl.BaseDDLElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "name", "column_name", "comment", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.ColumnComment.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "name", "column_name", "comment", "kw"], "arg_types": ["alembic.ddl.base.ColumnComment", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ColumnComment", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.ColumnComment.comment", "name": "comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.base.ColumnComment.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.base.ColumnComment", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColumnDefault": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.ddl.base.AlterColumn"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.base.ColumnDefault", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.ddl.base.ColumnDefault", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl.base", "mro": ["alembic.ddl.base.ColumnDefault", "alembic.ddl.base.AlterColumn", "alembic.ddl.base.AlterTable", "sqlalchemy.sql.ddl.ExecutableDDLElement", "sqlalchemy.sql.roles.DDLRole", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.ddl.BaseDDLElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "name", "column_name", "default", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.ColumnDefault.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "name", "column_name", "default", "kw"], "arg_types": ["alembic.ddl.base.ColumnDefault", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["sqlalchemy.sql.elements.TextClause", "sqlalchemy.sql.schema.FetchedValue", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.sql.functions.Function"}, "builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ColumnDefault", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "default": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.ColumnDefault.default", "name": "default", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.elements.TextClause", "sqlalchemy.sql.schema.FetchedValue", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.sql.functions.Function"}, "builtins.str", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.base.ColumnDefault.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.base.ColumnDefault", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColumnName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.ddl.base.AlterColumn"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.base.ColumnName", "name": "ColumnName", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.ddl.base.ColumnName", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl.base", "mro": ["alembic.ddl.base.ColumnName", "alembic.ddl.base.AlterColumn", "alembic.ddl.base.AlterTable", "sqlalchemy.sql.ddl.ExecutableDDLElement", "sqlalchemy.sql.roles.DDLRole", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.ddl.BaseDDLElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "name", "column_name", "newname", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.ColumnName.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "name", "column_name", "newname", "kw"], "arg_types": ["alembic.ddl.base.ColumnName", "builtins.str", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ColumnName", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "newname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.ColumnName.newname", "name": "newname", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.base.ColumnName.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.base.ColumnName", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColumnNullable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.ddl.base.AlterColumn"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.base.ColumnNullable", "name": "ColumnNullable", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.ddl.base.ColumnNullable", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl.base", "mro": ["alembic.ddl.base.ColumnNullable", "alembic.ddl.base.AlterColumn", "alembic.ddl.base.AlterTable", "sqlalchemy.sql.ddl.ExecutableDDLElement", "sqlalchemy.sql.roles.DDLRole", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.ddl.BaseDDLElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "name", "column_name", "nullable", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.ColumnNullable.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "name", "column_name", "nullable", "kw"], "arg_types": ["alembic.ddl.base.ColumnNullable", "builtins.str", "builtins.str", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ColumnNullable", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "nullable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.ColumnNullable.nullable", "name": "nullable", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.base.ColumnNullable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.base.ColumnNullable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColumnType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.ddl.base.AlterColumn"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.base.ColumnType", "name": "ColumnType", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.ddl.base.ColumnType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl.base", "mro": ["alembic.ddl.base.ColumnType", "alembic.ddl.base.AlterColumn", "alembic.ddl.base.AlterTable", "sqlalchemy.sql.ddl.ExecutableDDLElement", "sqlalchemy.sql.roles.DDLRole", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.ddl.BaseDDLElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "name", "column_name", "type_", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.ColumnType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "name", "column_name", "type_", "kw"], "arg_types": ["alembic.ddl.base.ColumnType", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ColumnType", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "type_": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.ColumnType.type_", "name": "type_", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.base.ColumnType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.base.ColumnType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Compiled": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.Compiled", "kind": "Gdef"}, "Computed": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Computed", "kind": "Gdef"}, "ComputedColumnDefault": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.ddl.base.AlterColumn"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.base.ComputedColumnDefault", "name": "ComputedColumnDefault", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.ddl.base.ComputedColumnDefault", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl.base", "mro": ["alembic.ddl.base.ComputedColumnDefault", "alembic.ddl.base.AlterColumn", "alembic.ddl.base.AlterTable", "sqlalchemy.sql.ddl.ExecutableDDLElement", "sqlalchemy.sql.roles.DDLRole", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.ddl.BaseDDLElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "name", "column_name", "default", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.ComputedColumnDefault.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "name", "column_name", "default", "kw"], "arg_types": ["alembic.ddl.base.ComputedColumnDefault", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Computed", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ComputedColumnDefault", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "default": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.ComputedColumnDefault.default", "name": "default", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Computed", {".class": "NoneType"}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.base.ComputedColumnDefault.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.base.ComputedColumnDefault", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DDLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.DDLCompiler", "kind": "Gdef"}, "DDLElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.DDLElement", "kind": "Gdef"}, "DefaultImpl": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.impl.DefaultImpl", "kind": "Gdef"}, "DropColumn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.ddl.base.AlterTable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.base.DropColumn", "name": "DropColumn", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.ddl.base.DropColumn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl.base", "mro": ["alembic.ddl.base.DropColumn", "alembic.ddl.base.AlterTable", "sqlalchemy.sql.ddl.ExecutableDDLElement", "sqlalchemy.sql.roles.DDLRole", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.ddl.BaseDDLElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "column", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.DropColumn.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "column", "schema"], "arg_types": ["alembic.ddl.base.DropColumn", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DropColumn", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.DropColumn.column", "name": "column", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.base.DropColumn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.base.DropColumn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FetchedValue": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.FetchedValue", "kind": "Gdef"}, "Function": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.Function", "kind": "Gdef"}, "Identity": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Identity", "kind": "Gdef"}, "IdentityColumnDefault": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.ddl.base.AlterColumn"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.base.IdentityColumnDefault", "name": "IdentityColumnDefault", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.ddl.base.IdentityColumnDefault", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl.base", "mro": ["alembic.ddl.base.IdentityColumnDefault", "alembic.ddl.base.AlterColumn", "alembic.ddl.base.AlterTable", "sqlalchemy.sql.ddl.ExecutableDDLElement", "sqlalchemy.sql.roles.DDLRole", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.ddl.BaseDDLElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "name", "column_name", "default", "impl", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.IdentityColumnDefault.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "name", "column_name", "default", "impl", "kw"], "arg_types": ["alembic.ddl.base.IdentityColumnDefault", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Identity", {".class": "NoneType"}]}, "alembic.ddl.impl.DefaultImpl", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IdentityColumnDefault", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "default": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.IdentityColumnDefault.default", "name": "default", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Identity", {".class": "NoneType"}]}}}, "impl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.IdentityColumnDefault.impl", "name": "impl", "type": "alembic.ddl.impl.DefaultImpl"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.base.IdentityColumnDefault.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.base.IdentityColumnDefault", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Integer": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Integer", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RenameTable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.ddl.base.AlterTable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.base.RenameTable", "name": "RenameTable", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.ddl.base.RenameTable", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl.base", "mro": ["alembic.ddl.base.RenameTable", "alembic.ddl.base.AlterTable", "sqlalchemy.sql.ddl.ExecutableDDLElement", "sqlalchemy.sql.roles.DDLRole", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.ddl.BaseDDLElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "old_table_name", "new_table_name", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.RenameTable.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "old_table_name", "new_table_name", "schema"], "arg_types": ["alembic.ddl.base.RenameTable", "builtins.str", {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RenameTable", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "new_table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.base.RenameTable.new_table_name", "name": "new_table_name", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.base.RenameTable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.base.RenameTable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TextClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TextClause", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_ServerDefault": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "alembic.ddl.base._ServerDefault", "line": 36, "no_args": false, "normalized": false, "target": {".class": "UnionType", "items": ["sqlalchemy.sql.elements.TextClause", "sqlalchemy.sql.schema.FetchedValue", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.sql.functions.Function"}, "builtins.str"]}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.base.__package__", "name": "__package__", "type": "builtins.str"}}, "_columns_for_constraint": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._columns_for_constraint", "kind": "Gdef"}, "_find_columns": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._find_columns", "kind": "Gdef"}, "_fk_spec": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._fk_spec", "kind": "Gdef"}, "_is_type_bound": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._is_type_bound", "kind": "Gdef"}, "_table_for_constraint": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._table_for_constraint", "kind": "Gdef"}, "add_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["compiler", "column", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.add_column", "name": "add_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["compiler", "column", "kw"], "arg_types": ["sqlalchemy.sql.compiler.DDLCompiler", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_column", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "alter_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["compiler", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.alter_column", "name": "alter_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["compiler", "name"], "arg_types": ["sqlalchemy.sql.compiler.DDLCompiler", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alter_column", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "alter_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["compiler", "name", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.alter_table", "name": "alter_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["compiler", "name", "schema"], "arg_types": ["sqlalchemy.sql.compiler.DDLCompiler", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alter_table", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "compiles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.compiler.compiles", "kind": "Gdef"}, "drop_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["compiler", "name", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.drop_column", "name": "drop_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["compiler", "name", "kw"], "arg_types": ["sqlalchemy.sql.compiler.DDLCompiler", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_column", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "format_column_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["compiler", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.format_column_name", "name": "format_column_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["compiler", "name"], "arg_types": ["sqlalchemy.sql.compiler.DDLCompiler", {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_column_name", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "format_server_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["compiler", "default"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.format_server_default", "name": "format_server_default", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["compiler", "default"], "arg_types": ["sqlalchemy.sql.compiler.DDLCompiler", {".class": "UnionType", "items": ["sqlalchemy.sql.elements.TextClause", "sqlalchemy.sql.schema.FetchedValue", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "sqlalchemy.sql.functions.Function"}, "builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_server_default", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "format_table_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["compiler", "name", "schema"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.format_table_name", "name": "format_table_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["compiler", "name", "schema"], "arg_types": ["sqlalchemy.sql.compiler.Compiled", {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_table_name", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "format_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["compiler", "type_"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.format_type", "name": "format_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["compiler", "type_"], "arg_types": ["sqlalchemy.sql.compiler.DDLCompiler", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_type", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "quote_dotted": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["name", "quote"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.ddl.base.quote_dotted", "name": "quote_dotted", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["name", "quote"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "functools.partial"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "quote_dotted", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "quoted_name": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.quoted_name", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.types", "kind": "Gdef"}, "visit_add_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.base.visit_add_column", "name": "visit_add_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.AddColumn", "sqlalchemy.sql.compiler.DDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_add_column", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.base.visit_add_column", "name": "visit_add_column", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "visit_column_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.base.visit_column_default", "name": "visit_column_default", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.ColumnDefault", "sqlalchemy.sql.compiler.DDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_column_default", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.base.visit_column_default", "name": "visit_column_default", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "visit_column_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.base.visit_column_name", "name": "visit_column_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.ColumnName", "sqlalchemy.sql.compiler.DDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_column_name", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.base.visit_column_name", "name": "visit_column_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "visit_column_nullable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.base.visit_column_nullable", "name": "visit_column_nullable", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.ColumnNullable", "sqlalchemy.sql.compiler.DDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_column_nullable", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.base.visit_column_nullable", "name": "visit_column_nullable", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "visit_column_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.base.visit_column_type", "name": "visit_column_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.ColumnType", "sqlalchemy.sql.compiler.DDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_column_type", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.base.visit_column_type", "name": "visit_column_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "visit_computed_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.base.visit_computed_column", "name": "visit_computed_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.ComputedColumnDefault", "sqlalchemy.sql.compiler.DDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_computed_column", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.base.visit_computed_column", "name": "visit_computed_column", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "visit_drop_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.base.visit_drop_column", "name": "visit_drop_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.DropColumn", "sqlalchemy.sql.compiler.DDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_drop_column", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.base.visit_drop_column", "name": "visit_drop_column", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "visit_identity_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.base.visit_identity_column", "name": "visit_identity_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.IdentityColumnDefault", "sqlalchemy.sql.compiler.DDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_identity_column", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.base.visit_identity_column", "name": "visit_identity_column", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "visit_rename_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.base.visit_rename_table", "name": "visit_rename_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.RenameTable", "sqlalchemy.sql.compiler.DDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_rename_table", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.base.visit_rename_table", "name": "visit_rename_table", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/ddl/base.py"}