{"data_mtime": 1754228260, "dep_lines": [1, 2, 3, 4, 5, 6, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 30, 30], "dependencies": ["alembic.ddl.mssql", "alembic.ddl.mysql", "alembic.ddl.oracle", "alembic.ddl.postgresql", "alembic.ddl.sqlite", "alembic.ddl.impl", "builtins", "abc", "typing"], "hash": "c57af55ba3de3ded2008bc11e36b9d7b413a8abbbd9a25057357c279037860ff", "id": "alembic.ddl", "ignore_all": true, "interface_hash": "e58b5648b46cc8b211c1cc4a70de35f3c05bef257e4c3ef96e9cc26ccc72f7b6", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/ddl/__init__.py", "plugin_data": null, "size": 137, "suppressed": [], "version_id": "1.7.1"}