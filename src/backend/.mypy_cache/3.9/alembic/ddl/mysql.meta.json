{"data_mtime": 1754228260, "dep_lines": [31, 11, 13, 21, 23, 24, 32, 33, 34, 9, 10, 22, 23, 1, 3, 4, 9, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 10, 5, 25, 25, 25, 10, 10, 10, 20, 5, 10, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.mysql.base", "sqlalchemy.ext.compiler", "alembic.ddl.base", "alembic.ddl.impl", "alembic.autogenerate.compare", "alembic.util.sqla_compat", "sqlalchemy.sql.ddl", "sqlalchemy.sql.schema", "sqlalchemy.sql.type_api", "sqlalchemy.schema", "sqlalchemy.types", "alembic.util", "alembic.autogenerate", "__future__", "re", "typing", "sqlalchemy", "alembic", "builtins", "abc", "alembic.util.exc", "sqlalchemy.dialects", "sqlalchemy.dialects.mysql", "sqlalchemy.engine", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.ext", "sqlalchemy.inspection", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "7dff0e134cd0f1862302096d05bb634240d1fa0f73eb90cd7858c4326e2c5fa7", "id": "alembic.ddl.mysql", "ignore_all": true, "interface_hash": "2628c954f5f50a008052a4beefae2f5ceb9a4e31ebc6dbd8bd0855d141157279", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/ddl/mysql.py", "plugin_data": null, "size": 16675, "suppressed": [], "version_id": "1.7.1"}