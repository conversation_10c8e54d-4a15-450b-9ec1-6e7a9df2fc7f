{"data_mtime": 1754228260, "dep_lines": [3, 4, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 30, 30], "dependencies": ["alembic.context", "alembic.op", "sys", "builtins", "abc", "typing"], "hash": "81ccea803801470dda57bd1a35e1fa586bb459d01241ffd8882855d76a824512", "id": "alembic", "ignore_all": true, "interface_hash": "989f43837547cf26b9e5e6c9b96f8f15a692994ea38acaa98a4b98c2f5cdefb9", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/__init__.py", "plugin_data": null, "size": 75, "suppressed": [], "version_id": "1.7.1"}