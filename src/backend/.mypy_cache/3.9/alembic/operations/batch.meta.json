{"data_mtime": 1754228260, "dep_lines": [22, 24, 25, 43, 45, 46, 47, 49, 17, 19, 20, 21, 24, 42, 1, 3, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 25, 25, 25, 25, 25, 10, 10, 5, 5, 20, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util.topological", "alembic.util.exc", "alembic.util.sqla_compat", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "sqlalchemy.sql.schema", "sqlalchemy.sql.type_api", "alembic.ddl.impl", "sqlalchemy.schema", "sqlalchemy.types", "sqlalchemy.events", "sqlalchemy.util", "alembic.util", "sqlalchemy.engine", "__future__", "typing", "sqlalchemy", "builtins", "_collections_abc", "_typeshed", "abc", "alembic.ddl", "enum", "sqlalchemy.engine.base", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.result", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql", "sqlalchemy.sql._selectable_constructors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.dml", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "b8cbc624395c4ecd064876ac8381ac76fd5871778b38aff5964465de39357b36", "id": "alembic.operations.batch", "ignore_all": true, "interface_hash": "acc389a89db70ae62dfc5184e6168722548c0b405896a3d2b50208c5a68a7967", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/operations/batch.py", "plugin_data": null, "size": 26954, "suppressed": [], "version_id": "1.7.1"}