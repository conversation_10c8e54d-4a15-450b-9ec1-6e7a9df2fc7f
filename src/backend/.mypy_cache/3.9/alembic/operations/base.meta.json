{"data_mtime": 1754228260, "dep_lines": [21, 23, 24, 26, 27, 39, 42, 43, 50, 52, 525, 23, 25, 37, 38, 47, 51, 526, 1, 3, 4, 5, 6, 25, 36, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 25, 25, 25, 25, 25, 20, 20, 10, 25, 25, 25, 25, 20, 5, 5, 10, 10, 5, 20, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.elements", "alembic.operations.batch", "alembic.operations.schemaobj", "alembic.util.sqla_compat", "alembic.util.compat", "sqlalchemy.sql.expression", "sqlalchemy.sql.functions", "sqlalchemy.sql.schema", "alembic.operations.ops", "alembic.runtime.migration", "sqlalchemy.ext.asyncio", "alembic.operations", "alembic.util", "sqlalchemy.engine", "sqlalchemy.sql", "sqlalchemy.types", "alembic.ddl", "sqlalchemy.util", "__future__", "contextlib", "re", "textwrap", "typing", "alembic", "sqlalchemy", "builtins", "abc", "alembic.ddl.impl", "alembic.runtime", "alembic.util.langhelpers", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "daca382a2b0336e38bc34091899aa8ac81eb86e7a7a55a056e7dc1d2c36f0e27", "id": "alembic.operations.base", "ignore_all": true, "interface_hash": "d0730ae4bfa74d45f0c6c6f22b041ff2fd9ca3b01442cc54131ffa591316c573", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/operations/base.py", "plugin_data": null, "size": 72471, "suppressed": [], "version_id": "1.7.1"}