{"data_mtime": 1754228260, "dep_lines": [22, 23, 26, 32, 36, 37, 49, 50, 52, 53, 54, 20, 22, 25, 31, 1, 3, 4, 5, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 25, 25, 25, 25, 25, 25, 25, 25, 5, 20, 10, 25, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["alembic.operations.schemaobj", "alembic.operations.base", "alembic.util.sqla_compat", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.type_api", "alembic.autogenerate.rewriter", "alembic.runtime.migration", "alembic.script.revision", "sqlalchemy.types", "alembic.operations", "alembic.util", "sqlalchemy.sql", "__future__", "abc", "re", "typing", "alembic", "builtins", "alembic.autogenerate", "alembic.operations.batch", "alembic.runtime", "alembic.util.langhelpers", "enum", "sqlalchemy", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util._collections", "sqlalchemy.util.langhelpers"], "hash": "68ff54cf7ea4f7c3bf63e9e32880227f2bf2862d20db353afe280a328b3dd7fb", "id": "alembic.operations.ops", "ignore_all": true, "interface_hash": "ca4458936ef8c9887c043e6cd393a50fe53f5a782c7ec3ac3090a7f9d1bc627f", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/operations/ops.py", "plugin_data": null, "size": 93539, "suppressed": [], "version_id": "1.7.1"}