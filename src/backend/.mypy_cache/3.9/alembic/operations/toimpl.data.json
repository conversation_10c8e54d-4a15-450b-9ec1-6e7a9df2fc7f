{".class": "MypyFile", "_fullname": "alembic.operations.toimpl", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Operations": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.base.Operations", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.toimpl.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.toimpl.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.toimpl.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.toimpl.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.toimpl.__package__", "name": "__package__", "type": "builtins.str"}}, "_copy": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._copy", "kind": "Gdef"}, "add_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.toimpl.add_column", "name": "add_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "arg_types": ["alembic.operations.base.Operations", "alembic.operations.ops.AddColumnOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_column", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.operations.toimpl.add_column", "name": "add_column", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "alter_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.toimpl.alter_column", "name": "alter_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "arg_types": ["alembic.operations.base.Operations", "alembic.operations.ops.AlterColumnOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alter_column", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.operations.toimpl.alter_column", "name": "alter_column", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "bulk_insert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.toimpl.bulk_insert", "name": "bulk_insert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "arg_types": ["alembic.operations.base.Operations", "alembic.operations.ops.BulkInsertOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bulk_insert", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.operations.toimpl.bulk_insert", "name": "bulk_insert", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "create_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.toimpl.create_constraint", "name": "create_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "arg_types": ["alembic.operations.base.Operations", "alembic.operations.ops.AddConstraintOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_constraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.operations.toimpl.create_constraint", "name": "create_constraint", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "create_index": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.toimpl.create_index", "name": "create_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "arg_types": ["alembic.operations.base.Operations", "alembic.operations.ops.CreateIndexOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_index", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.operations.toimpl.create_index", "name": "create_index", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "create_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.toimpl.create_table", "name": "create_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "arg_types": ["alembic.operations.base.Operations", "alembic.operations.ops.CreateTableOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_table", "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.operations.toimpl.create_table", "name": "create_table", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "create_table_comment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.toimpl.create_table_comment", "name": "create_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "arg_types": ["alembic.operations.base.Operations", "alembic.operations.ops.CreateTableCommentOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_table_comment", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.operations.toimpl.create_table_comment", "name": "create_table_comment", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "drop_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.toimpl.drop_column", "name": "drop_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "arg_types": ["alembic.operations.base.Operations", "alembic.operations.ops.DropColumnOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_column", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.operations.toimpl.drop_column", "name": "drop_column", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "drop_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.toimpl.drop_constraint", "name": "drop_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "arg_types": ["alembic.operations.base.Operations", "alembic.operations.ops.DropConstraintOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_constraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.operations.toimpl.drop_constraint", "name": "drop_constraint", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "drop_index": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.toimpl.drop_index", "name": "drop_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "arg_types": ["alembic.operations.base.Operations", "alembic.operations.ops.DropIndexOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_index", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.operations.toimpl.drop_index", "name": "drop_index", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "drop_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.toimpl.drop_table", "name": "drop_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "arg_types": ["alembic.operations.base.Operations", "alembic.operations.ops.DropTableOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_table", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.operations.toimpl.drop_table", "name": "drop_table", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "drop_table_comment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.toimpl.drop_table_comment", "name": "drop_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "arg_types": ["alembic.operations.base.Operations", "alembic.operations.ops.DropTableCommentOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_table_comment", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.operations.toimpl.drop_table_comment", "name": "drop_table_comment", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "execute_sql": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.toimpl.execute_sql", "name": "execute_sql", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "arg_types": ["alembic.operations.base.Operations", "alembic.operations.ops.ExecuteSQLOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute_sql", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.operations.toimpl.execute_sql", "name": "execute_sql", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "ops": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops", "kind": "Gdef"}, "rename_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.toimpl.rename_table", "name": "rename_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["operations", "operation"], "arg_types": ["alembic.operations.base.Operations", "alembic.operations.ops.RenameTableOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rename_table", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.operations.toimpl.rename_table", "name": "rename_table", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "sa_schema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.schema", "kind": "Gdef"}, "sqla_2": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat.sqla_2", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/operations/toimpl.py"}