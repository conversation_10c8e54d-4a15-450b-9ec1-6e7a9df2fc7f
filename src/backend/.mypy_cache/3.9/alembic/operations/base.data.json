{".class": "MypyFile", "_fullname": "alembic.operations.base", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractOperations": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.util.langhelpers.ModuleClsProxy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.base.AbstractOperations", "name": "AbstractOperations", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.base.AbstractOperations", "has_param_spec_type": false, "metaclass_type": "alembic.util.langhelpers._ModuleClsMeta", "metadata": {}, "module_name": "alembic.operations.base", "mro": ["alembic.operations.base.AbstractOperations", "alembic.util.langhelpers.ModuleClsProxy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "migration_context", "impl"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.base.AbstractOperations.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "migration_context", "impl"], "arg_types": ["alembic.operations.base.AbstractOperations", "alembic.runtime.migration.MigrationContext", {".class": "UnionType", "items": ["alembic.operations.batch.BatchOperationsImpl", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AbstractOperations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_to_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.operations.base.AbstractOperations._to_impl", "name": "_to_impl", "type": "alembic.util.langhelpers.Dispatcher"}}, "batch_alter_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "table_name", "schema", "recreate", "partial_reordering", "copy_from", "table_args", "table_kwargs", "reflect_args", "reflect_kwargs", "naming_convention"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.operations.base.AbstractOperations.batch_alter_table", "name": "batch_alter_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "table_name", "schema", "recreate", "partial_reordering", "copy_from", "table_args", "table_kwargs", "reflect_args", "reflect_kwargs", "naming_convention"], "arg_types": ["alembic.operations.base.AbstractOperations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "never"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}]}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_alter_table of AbstractOperations", "ret_type": {".class": "Instance", "args": ["alembic.operations.base.BatchOperations"], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.operations.base.AbstractOperations.batch_alter_table", "name": "batch_alter_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "table_name", "schema", "recreate", "partial_reordering", "copy_from", "table_args", "table_kwargs", "reflect_args", "reflect_kwargs", "naming_convention"], "arg_types": ["alembic.operations.base.AbstractOperations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "never"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "builtins.tuple"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}]}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_alter_table of AbstractOperations", "ret_type": {".class": "Instance", "args": ["alembic.operations.base.BatchOperations"], "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "migration_context"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.base.AbstractOperations.context", "name": "context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "migration_context"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.base.AbstractOperations"}, "alembic.runtime.migration.MigrationContext"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "context of AbstractOperations", "ret_type": {".class": "Instance", "args": ["alembic.operations.base.Operations"], "type_ref": "typing.Iterator"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.base.AbstractOperations.context", "name": "context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "migration_context"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.base.AbstractOperations"}, "alembic.runtime.migration.MigrationContext"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "context of AbstractOperations", "ret_type": {".class": "Instance", "args": ["alembic.operations.base.Operations"], "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "f": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.base.AbstractOperations.f", "name": "f", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["alembic.operations.base.AbstractOperations", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "f of AbstractOperations", "ret_type": "sqlalchemy.sql.elements.conv", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.base.AbstractOperations.get_bind", "name": "get_bind", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.base.AbstractOperations"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bind of AbstractOperations", "ret_type": "sqlalchemy.engine.base.Connection", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "get_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.base.AbstractOperations.get_context", "name": "get_context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.base.AbstractOperations"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_context of AbstractOperations", "ret_type": "alembic.runtime.migration.MigrationContext", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.operations.base.AbstractOperations.impl", "name": "impl", "type": {".class": "UnionType", "items": ["alembic.ddl.impl.DefaultImpl", "alembic.operations.batch.BatchOperationsImpl"]}}}, "implementation_for": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "op_cls"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.base.AbstractOperations.implementation_for", "name": "implementation_for", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "op_cls"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.base.AbstractOperations"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "implementation_for of AbstractOperations", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.base.AbstractOperations.implementation_for", "name": "implementation_for", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "op_cls"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.base.AbstractOperations"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "implementation_for of AbstractOperations", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "inline_literal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "value", "type_"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.base.AbstractOperations.inline_literal", "name": "inline_literal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "value", "type_"], "arg_types": ["alembic.operations.base.AbstractOperations", {".class": "UnionType", "items": ["builtins.str", "builtins.int"]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inline_literal of AbstractOperations", "ret_type": "alembic.util.sqla_compat._literal_bindparam", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "invoke": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.base.AbstractOperations.invoke", "name": "invoke", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operation"], "arg_types": ["alembic.operations.base.AbstractOperations", "alembic.operations.ops.MigrateOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invoke of AbstractOperations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "migration_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.base.AbstractOperations.migration_context", "name": "migration_context", "type": "alembic.runtime.migration.MigrationContext"}}, "register_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "name", "sourcename"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.base.AbstractOperations.register_operation", "name": "register_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "name", "sourcename"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.base.AbstractOperations"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_operation of AbstractOperations", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.base._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.base._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.base._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.base.AbstractOperations.register_operation", "name": "register_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "name", "sourcename"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.base.AbstractOperations"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_operation of AbstractOperations", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.base._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.base._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.base._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "run_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "async_function", "args", "kw_args"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.base.AbstractOperations.run_async", "name": "run_async", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "async_function", "args", "kw_args"], "arg_types": ["alembic.operations.base.AbstractOperations", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.base._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "typing.Awaitable"}, "type_guard": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_async of AbstractOperations", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.base._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.base._T", "id": -1, "name": "_T", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "schema_obj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.base.AbstractOperations.schema_obj", "name": "schema_obj", "type": "alembic.operations.schemaobj.SchemaObjects"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.base.AbstractOperations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.base.AbstractOperations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef", "module_public": false}, "BatchOperations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.base.AbstractOperations"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.base.BatchOperations", "name": "BatchOperations", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.base.BatchOperations", "has_param_spec_type": false, "metaclass_type": "alembic.util.langhelpers._ModuleClsMeta", "metadata": {}, "module_name": "alembic.operations.base", "mro": ["alembic.operations.base.BatchOperations", "alembic.operations.base.AbstractOperations", "alembic.util.langhelpers.ModuleClsProxy", "builtins.object"], "names": {".class": "SymbolTable", "_noop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operation"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.base.BatchOperations._noop", "name": "_noop", "type": null}}, "add_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "column", "insert_before", "insert_after"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.BatchOperations.add_column", "name": "add_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "column", "insert_before", "insert_after"], "arg_types": ["alembic.operations.base.BatchOperations", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_column of BatchOperations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "alter_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "column_name", "nullable", "comment", "server_default", "new_column_name", "type_", "existing_type", "existing_server_default", "existing_nullable", "existing_comment", "insert_before", "insert_after", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.BatchOperations.alter_column", "name": "alter_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "column_name", "nullable", "comment", "server_default", "new_column_name", "type_", "existing_type", "existing_server_default", "existing_nullable", "existing_comment", "insert_before", "insert_after", "kw"], "arg_types": ["alembic.operations.base.BatchOperations", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bool", "sqlalchemy.sql.schema.Identity", "sqlalchemy.sql.schema.Computed", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alter_column of BatchOperations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_check_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "constraint_name", "condition", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.BatchOperations.create_check_constraint", "name": "create_check_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "constraint_name", "condition", "kw"], "arg_types": ["alembic.operations.base.BatchOperations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.elements.TextClause"]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_check_constraint of BatchOperations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_exclude_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "constraint_name", "elements", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.BatchOperations.create_exclude_constraint", "name": "create_exclude_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "constraint_name", "elements", "kw"], "arg_types": ["alembic.operations.base.BatchOperations", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_exclude_constraint of BatchOperations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_foreign_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "constraint_name", "referent_table", "local_cols", "remote_cols", "referent_schema", "onupdate", "ondelete", "deferrable", "initially", "match", "dialect_kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.BatchOperations.create_foreign_key", "name": "create_foreign_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "constraint_name", "referent_table", "local_cols", "remote_cols", "referent_schema", "onupdate", "ondelete", "deferrable", "initially", "match", "dialect_kw"], "arg_types": ["alembic.operations.base.BatchOperations", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_foreign_key of BatchOperations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "index_name", "columns", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.BatchOperations.create_index", "name": "create_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "index_name", "columns", "kw"], "arg_types": ["alembic.operations.base.BatchOperations", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_index of BatchOperations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_primary_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "constraint_name", "columns"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.BatchOperations.create_primary_key", "name": "create_primary_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "constraint_name", "columns"], "arg_types": ["alembic.operations.base.BatchOperations", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_primary_key of BatchOperations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "comment", "existing_comment"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.BatchOperations.create_table_comment", "name": "create_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "comment", "existing_comment"], "arg_types": ["alembic.operations.base.BatchOperations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_table_comment of BatchO<PERSON>ations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_unique_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "constraint_name", "columns", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.BatchOperations.create_unique_constraint", "name": "create_unique_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "constraint_name", "columns", "kw"], "arg_types": ["alembic.operations.base.BatchOperations", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unique_constraint of BatchOperations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "drop_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "column_name", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.BatchOperations.drop_column", "name": "drop_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "column_name", "kw"], "arg_types": ["alembic.operations.base.BatchOperations", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_column of BatchOperations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "drop_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "constraint_name", "type_"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.BatchOperations.drop_constraint", "name": "drop_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "constraint_name", "type_"], "arg_types": ["alembic.operations.base.BatchOperations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_constraint of BatchOperations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "drop_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "index_name", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.BatchOperations.drop_index", "name": "drop_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "index_name", "kw"], "arg_types": ["alembic.operations.base.BatchOperations", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_index of BatchOperations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "drop_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "existing_comment"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.BatchOperations.drop_table_comment", "name": "drop_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "existing_comment"], "arg_types": ["alembic.operations.base.BatchOperations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_table_comment of <PERSON><PERSON><PERSON><PERSON>ations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "sqltext", "execution_options"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.BatchOperations.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "sqltext", "execution_options"], "arg_types": ["alembic.operations.base.BatchOperations", {".class": "UnionType", "items": ["sqlalchemy.sql.base.Executable", "builtins.str"]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of BatchOperations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.operations.base.BatchOperations.impl", "name": "impl", "type": "alembic.operations.batch.BatchOperationsImpl"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.base.BatchOperations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.base.BatchOperations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchOperationsImpl": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.batch.BatchOperationsImpl", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef", "module_public": false}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef", "module_public": false}, "Computed": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Computed", "kind": "Gdef", "module_public": false}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef", "module_public": false}, "DefaultImpl": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.impl.DefaultImpl", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "Executable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Executable", "kind": "Gdef", "module_public": false}, "Function": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.Function", "kind": "Gdef", "module_public": false}, "Identity": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Identity", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "MigrateOperation": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.MigrateOperation", "kind": "Gdef", "module_public": false}, "MigrationContext": {".class": "SymbolTableNode", "cross_ref": "alembic.runtime.migration.MigrationContext", "kind": "Gdef", "module_public": false}, "Operations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.operations.base.AbstractOperations"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.base.Operations", "name": "Operations", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.base.Operations", "has_param_spec_type": false, "metaclass_type": "alembic.util.langhelpers._ModuleClsMeta", "metadata": {}, "module_name": "alembic.operations.base", "mro": ["alembic.operations.base.Operations", "alembic.operations.base.AbstractOperations", "alembic.util.langhelpers.ModuleClsProxy", "builtins.object"], "names": {".class": "SymbolTable", "add_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "table_name", "column", "schema"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.add_column", "name": "add_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "table_name", "column", "schema"], "arg_types": ["alembic.operations.base.Operations", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_column of Operations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "alter_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "table_name", "column_name", "nullable", "comment", "server_default", "new_column_name", "type_", "existing_type", "existing_server_default", "existing_nullable", "existing_comment", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.alter_column", "name": "alter_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "table_name", "column_name", "nullable", "comment", "server_default", "new_column_name", "type_", "existing_type", "existing_server_default", "existing_nullable", "existing_comment", "schema", "kw"], "arg_types": ["alembic.operations.base.Operations", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", "builtins.bool", "sqlalchemy.sql.schema.Identity", "sqlalchemy.sql.schema.Computed", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alter_column of Operations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "bulk_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "table", "rows", "multiinsert"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.bulk_insert", "name": "bulk_insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "table", "rows", "multiinsert"], "arg_types": ["alembic.operations.base.Operations", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", "sqlalchemy.sql.selectable.TableClause"]}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "builtins.dict"}], "type_ref": "builtins.list"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bulk_insert of Operations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_check_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["self", "constraint_name", "table_name", "condition", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.create_check_constraint", "name": "create_check_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["self", "constraint_name", "table_name", "condition", "schema", "kw"], "arg_types": ["alembic.operations.base.Operations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.elements.TextClause"]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_check_constraint of Operations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_exclude_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "constraint_name", "table_name", "elements", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.create_exclude_constraint", "name": "create_exclude_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "constraint_name", "table_name", "elements", "kw"], "arg_types": ["alembic.operations.base.Operations", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_exclude_constraint of Operations", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}]}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_foreign_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "constraint_name", "source_table", "referent_table", "local_cols", "remote_cols", "onupdate", "ondelete", "deferrable", "initially", "match", "source_schema", "referent_schema", "dialect_kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.create_foreign_key", "name": "create_foreign_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "constraint_name", "source_table", "referent_table", "local_cols", "remote_cols", "onupdate", "ondelete", "deferrable", "initially", "match", "source_schema", "referent_schema", "dialect_kw"], "arg_types": ["alembic.operations.base.Operations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_foreign_key of Operations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 4], "arg_names": ["self", "index_name", "table_name", "columns", "schema", "unique", "if_not_exists", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.create_index", "name": "create_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 4], "arg_names": ["self", "index_name", "table_name", "columns", "schema", "unique", "if_not_exists", "kw"], "arg_types": ["alembic.operations.base.Operations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.elements.TextClause", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.functions.Function"}]}], "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_index of Operations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_primary_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "constraint_name", "table_name", "columns", "schema"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.create_primary_key", "name": "create_primary_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "constraint_name", "table_name", "columns", "schema"], "arg_types": ["alembic.operations.base.Operations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_primary_key of Operations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "table_name", "columns", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.create_table", "name": "create_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "table_name", "columns", "kw"], "arg_types": ["alembic.operations.base.Operations", "builtins.str", "sqlalchemy.sql.schema.SchemaItem", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_table of Operations", "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "table_name", "comment", "existing_comment", "schema"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.create_table_comment", "name": "create_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "table_name", "comment", "existing_comment", "schema"], "arg_types": ["alembic.operations.base.Operations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_table_comment of Operations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_unique_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["self", "constraint_name", "table_name", "columns", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.create_unique_constraint", "name": "create_unique_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 4], "arg_names": ["self", "constraint_name", "table_name", "columns", "schema", "kw"], "arg_types": ["alembic.operations.base.Operations", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unique_constraint of Operations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "drop_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 4], "arg_names": ["self", "table_name", "column_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.drop_column", "name": "drop_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 4], "arg_names": ["self", "table_name", "column_name", "schema", "kw"], "arg_types": ["alembic.operations.base.Operations", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_column of Operations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "drop_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 5], "arg_names": ["self", "constraint_name", "table_name", "type_", "schema"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.drop_constraint", "name": "drop_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5], "arg_names": ["self", "constraint_name", "table_name", "type_", "schema"], "arg_types": ["alembic.operations.base.Operations", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_constraint of Operations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "drop_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "index_name", "table_name", "schema", "if_exists", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.drop_index", "name": "drop_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "index_name", "table_name", "schema", "if_exists", "kw"], "arg_types": ["alembic.operations.base.Operations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_index of Operations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "drop_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "table_name", "schema", "kw"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.drop_table", "name": "drop_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "table_name", "schema", "kw"], "arg_types": ["alembic.operations.base.Operations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_table of Operations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "drop_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "table_name", "existing_comment", "schema"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.drop_table_comment", "name": "drop_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "table_name", "existing_comment", "schema"], "arg_types": ["alembic.operations.base.Operations", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_table_comment of Operations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "sqltext", "execution_options"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "sqltext", "execution_options"], "arg_types": ["alembic.operations.base.Operations", {".class": "UnionType", "items": ["sqlalchemy.sql.base.Executable", "builtins.str"]}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of Operations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "rename_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "old_table_name", "new_table_name", "schema"], "dataclass_transform_spec": null, "flags": ["is_mypy_only"], "fullname": "alembic.operations.base.Operations.rename_table", "name": "rename_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "old_table_name", "new_table_name", "schema"], "arg_types": ["alembic.operations.base.Operations", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rename_table of Operations", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.base.Operations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.base.Operations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "SchemaItem": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.SchemaItem", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef", "module_public": false}, "TableClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TableClause", "kind": "Gdef", "module_public": false}, "TextClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TextClause", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.base._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.operations.base.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.base.__package__", "name": "__package__", "type": "builtins.str"}}, "_literal_bindparam": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._literal_bindparam", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "batch": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.batch", "kind": "Gdef", "module_public": false}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef", "module_public": false}, "conv": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.conv", "kind": "Gdef", "module_public": false}, "formatannotation_fwdref": {".class": "SymbolTableNode", "cross_ref": "alembic.util.compat.formatannotation_fwdref", "kind": "Gdef", "module_public": false}, "inspect_formatargspec": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.inspect_formatargspec", "kind": "Gdef", "module_public": false}, "inspect_getfullargspec": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.compat.inspect_getfullargspec", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "schemaobj": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.schemaobj", "kind": "Gdef", "module_public": false}, "sqla_compat": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat", "kind": "Gdef", "module_public": false}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef", "module_public": false}, "util": {".class": "SymbolTableNode", "cross_ref": "alembic.util", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/operations/base.py"}