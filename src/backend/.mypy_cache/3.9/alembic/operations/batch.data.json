{".class": "MypyFile", "_fullname": "alembic.operations.batch", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "sqlalchemy.sql.base.SchemaEventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.batch.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "name": "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "alembic.operations.batch.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.batch", "mro": ["alembic.operations.batch.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "sqlalchemy.sql.base.SchemaEventTarget"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.batch.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">1", "name": "<subclass of \"TypeEngine\" and \"SchemaEventTarget\">", "type_vars": []}, "deletable_attributes": [], "flags": ["is_intersection"], "fullname": "alembic.operations.batch.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">1", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.batch", "mro": ["alembic.operations.batch.<subclass of \"TypeEngine\" and \"SchemaEventTarget\">1", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "sqlalchemy.sql.base.SchemaEventTarget", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ApplyBatchImpl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.batch.ApplyBatchImpl", "name": "ApplyBatchImpl", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.batch", "mro": ["alembic.operations.batch.ApplyBatchImpl", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "impl", "table", "table_args", "table_kwargs", "reflected", "partial_reordering"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "impl", "table", "table_args", "table_kwargs", "reflected", "partial_reordering"], "arg_types": ["alembic.operations.batch.ApplyBatchImpl", "alembic.ddl.impl.DefaultImpl", "sqlalchemy.sql.schema.Table", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}, "builtins.bool", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ApplyBatchImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_adjust_self_columns_for_partial_reordering": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl._adjust_self_columns_for_partial_reordering", "name": "_adjust_self_columns_for_partial_reordering", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.batch.ApplyBatchImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_adjust_self_columns_for_partial_reordering of ApplyBatchImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_calc_temp_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "tablename"], "dataclass_transform_spec": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.operations.batch.ApplyBatchImpl._calc_temp_name", "name": "_calc_temp_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tablename"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.batch.ApplyBatchImpl"}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calc_temp_name of ApplyBatchImpl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl._calc_temp_name", "name": "_calc_temp_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tablename"], "arg_types": [{".class": "TypeType", "item": "alembic.operations.batch.ApplyBatchImpl"}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"]}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calc_temp_name of ApplyBatchImpl", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "_create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op_impl"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl._create", "name": "_create", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op_impl"], "arg_types": ["alembic.operations.batch.ApplyBatchImpl", "alembic.ddl.impl.DefaultImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create of ApplyBatchImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_gather_indexes_from_both_tables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl._gather_indexes_from_both_tables", "name": "_gather_indexes_from_both_tables", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.batch.ApplyBatchImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_gather_indexes_from_both_tables of ApplyBatchImpl", "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.Index"], "type_ref": "builtins.list"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_grab_table_elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl._grab_table_elements", "name": "_grab_table_elements", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.batch.ApplyBatchImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_grab_table_elements of ApplyBatchImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_setup_dependencies_for_add_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "colname", "insert_before", "insert_after"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl._setup_dependencies_for_add_column", "name": "_setup_dependencies_for_add_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "colname", "insert_before", "insert_after"], "arg_types": ["alembic.operations.batch.ApplyBatchImpl", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setup_dependencies_for_add_column of ApplyBatchImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_setup_referent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "metadata", "constraint"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl._setup_referent", "name": "_setup_referent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "metadata", "constraint"], "arg_types": ["alembic.operations.batch.ApplyBatchImpl", "sqlalchemy.sql.schema.MetaData", "sqlalchemy.sql.schema.ForeignKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setup_referent of ApplyBatchImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "_transfer_elements_to_new_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl._transfer_elements_to_new_table", "name": "_transfer_elements_to_new_table", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.batch.ApplyBatchImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_transfer_elements_to_new_table of ApplyBatchImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "add_col_ordering": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.add_col_ordering", "name": "add_col_ordering", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.tuple"}}}, "add_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "table_name", "column", "insert_before", "insert_after", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl.add_column", "name": "add_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "table_name", "column", "insert_before", "insert_after", "kw"], "arg_types": ["alembic.operations.batch.ApplyBatchImpl", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_column of ApplyBatchImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "add_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "const"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl.add_constraint", "name": "add_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "const"], "arg_types": ["alembic.operations.batch.ApplyBatchImpl", "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_constraint of ApplyBatchImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "alter_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "table_name", "column_name", "nullable", "server_default", "name", "type_", "autoincrement", "comment", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl.alter_column", "name": "alter_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "table_name", "column_name", "nullable", "server_default", "name", "type_", "autoincrement", "comment", "kw"], "arg_types": ["alembic.operations.batch.ApplyBatchImpl", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.functions.Function"}, "builtins.str", "builtins.bool", {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}]}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "NoneType"}]}, {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alter_column of ApplyBatchImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "col_named_constraints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.col_named_constraints", "name": "col_named_constraints", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.base._NoneName"]}, {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}, "sqlalchemy.sql.schema.Constraint"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.tuple"}}], "type_ref": "builtins.dict"}}}, "column_transfers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.column_transfers", "name": "column_transfers", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}], "type_ref": "builtins.dict"}], "type_ref": "builtins.dict"}}}, "columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.columns", "name": "columns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}], "type_ref": "builtins.dict"}}}, "create_column_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "column"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl.create_column_comment", "name": "create_column_comment", "type": null}}, "create_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "idx"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl.create_index", "name": "create_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "idx"], "arg_types": ["alembic.operations.batch.ApplyBatchImpl", "sqlalchemy.sql.schema.Index"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_index of ApplyBatchImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl.create_table_comment", "name": "create_table_comment", "type": null}}, "drop_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "table_name", "column", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl.drop_column", "name": "drop_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "table_name", "column", "kw"], "arg_types": ["alembic.operations.batch.ApplyBatchImpl", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.elements.ColumnClause"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql.schema.Column"}]}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_column of ApplyBatchImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "drop_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "const"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl.drop_constraint", "name": "drop_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "const"], "arg_types": ["alembic.operations.batch.ApplyBatchImpl", "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_constraint of ApplyBatchImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "drop_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "idx"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl.drop_index", "name": "drop_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "idx"], "arg_types": ["alembic.operations.batch.ApplyBatchImpl", "sqlalchemy.sql.schema.Index"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_index of ApplyBatchImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "drop_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl.drop_table_comment", "name": "drop_table_comment", "type": null}}, "existing_ordering": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.existing_ordering", "name": "existing_ordering", "type": {".class": "Instance", "args": ["builtins.str"], "type_ref": "builtins.list"}}}, "impl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.impl", "name": "impl", "type": "alembic.ddl.impl.DefaultImpl"}}, "indexes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.indexes", "name": "indexes", "type": {".class": "Instance", "args": ["builtins.str", "sqlalchemy.sql.schema.Index"], "type_ref": "builtins.dict"}}}, "named_constraints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.named_constraints", "name": "named_constraints", "type": {".class": "Instance", "args": ["builtins.str", "sqlalchemy.sql.schema.Constraint"], "type_ref": "builtins.dict"}}}, "new_indexes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.new_indexes", "name": "new_indexes", "type": {".class": "Instance", "args": ["builtins.str", "sqlalchemy.sql.schema.Index"], "type_ref": "builtins.dict"}}}, "new_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.new_table", "name": "new_table", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}]}}}, "partial_reordering": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.partial_reordering", "name": "partial_reordering", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "builtins.tuple"}}}, "reflected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.reflected", "name": "reflected", "type": "builtins.bool"}}, "rename_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.ApplyBatchImpl.rename_table", "name": "rename_table", "type": null}}, "table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.table", "name": "table", "type": "sqlalchemy.sql.schema.Table"}}, "table_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.table_args", "name": "table_args", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "builtins.tuple"}}}, "table_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.table_kwargs", "name": "table_kwargs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "builtins.dict"}}}, "temp_table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.temp_table_name", "name": "temp_table_name", "type": "builtins.str"}}, "unnamed_constraints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.ApplyBatchImpl.unnamed_constraints", "name": "unnamed_constraints", "type": {".class": "Instance", "args": ["sqlalchemy.sql.schema.Constraint"], "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.batch.ApplyBatchImpl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.batch.ApplyBatchImpl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchOperationsImpl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.operations.batch.BatchOperationsImpl", "name": "BatchOperationsImpl", "type_vars": []}, "deletable_attributes": [], "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.operations.batch", "mro": ["alembic.operations.batch.BatchOperationsImpl", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "operations", "table_name", "schema", "recreate", "copy_from", "table_args", "table_kwargs", "reflect_args", "reflect_kwargs", "naming_convention", "partial_reordering"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl.__init__", "name": "__init__", "type": null}}, "_should_recreate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl._should_recreate", "name": "_should_recreate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.batch.BatchOperationsImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_should_recreate of BatchOperationsImpl", "ret_type": "builtins.bool", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "add_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl.add_column", "name": "add_column", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "arg_types": ["alembic.operations.batch.BatchOperationsImpl", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_column of BatchOperationsImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "add_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "const"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl.add_constraint", "name": "add_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "const"], "arg_types": ["alembic.operations.batch.BatchOperationsImpl", "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_constraint of BatchOperationsImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "alter_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl.alter_column", "name": "alter_column", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "arg_types": ["alembic.operations.batch.BatchOperationsImpl", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alter_column of BatchOperationsImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "batch": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.BatchOperationsImpl.batch", "name": "batch", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "copy_from": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.BatchOperationsImpl.copy_from", "name": "copy_from", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create_column_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "column"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl.create_column_comment", "name": "create_column_comment", "type": null}}, "create_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "idx", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl.create_index", "name": "create_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "idx", "kw"], "arg_types": ["alembic.operations.batch.BatchOperationsImpl", "sqlalchemy.sql.schema.Index", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_index of BatchOperationsImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "create_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl.create_table", "name": "create_table", "type": null}}, "create_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl.create_table_comment", "name": "create_table_comment", "type": null}}, "dialect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.operations.batch.BatchOperationsImpl.dialect", "name": "dialect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.batch.BatchOperationsImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dialect of BatchOperationsImpl", "ret_type": "sqlalchemy.engine.interfaces.Dialect", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.operations.batch.BatchOperationsImpl.dialect", "name": "dialect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.batch.BatchOperationsImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dialect of BatchOperationsImpl", "ret_type": "sqlalchemy.engine.interfaces.Dialect", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "drop_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl.drop_column", "name": "drop_column", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "arg_types": ["alembic.operations.batch.BatchOperationsImpl", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_column of BatchOperationsImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "drop_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "const"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl.drop_constraint", "name": "drop_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "const"], "arg_types": ["alembic.operations.batch.BatchOperationsImpl", "sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_constraint of BatchOperationsImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "drop_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "idx", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl.drop_index", "name": "drop_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "idx", "kw"], "arg_types": ["alembic.operations.batch.BatchOperationsImpl", "sqlalchemy.sql.schema.Index", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_index of BatchOperationsImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "drop_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl.drop_table", "name": "drop_table", "type": null}}, "drop_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl.drop_table_comment", "name": "drop_table_comment", "type": null}}, "flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl.flush", "name": "flush", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.batch.BatchOperationsImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flush of BatchOperationsImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.operations.batch.BatchOperationsImpl.impl", "name": "impl", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.batch.BatchOperationsImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "impl of BatchOperationsImpl", "ret_type": "alembic.ddl.impl.DefaultImpl", "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.operations.batch.BatchOperationsImpl.impl", "name": "impl", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.operations.batch.BatchOperationsImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "impl of BatchOperationsImpl", "ret_type": "alembic.ddl.impl.DefaultImpl", "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "naming_convention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.BatchOperationsImpl.naming_convention", "name": "naming_convention", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "operations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.BatchOperationsImpl.operations", "name": "operations", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "partial_reordering": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.BatchOperationsImpl.partial_reordering", "name": "partial_reordering", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "recreate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.BatchOperationsImpl.recreate", "name": "recreate", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "reflect_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.BatchOperationsImpl.reflect_args", "name": "reflect_args", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "reflect_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.BatchOperationsImpl.reflect_kwargs", "name": "reflect_kwargs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "rename_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.operations.batch.BatchOperationsImpl.rename_table", "name": "rename_table", "type": null}}, "schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.BatchOperationsImpl.schema", "name": "schema", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "table_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.BatchOperationsImpl.table_args", "name": "table_args", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "table_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.BatchOperationsImpl.table_kwargs", "name": "table_kwargs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.operations.batch.BatchOperationsImpl.table_name", "name": "table_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.operations.batch.BatchOperationsImpl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.operations.batch.BatchOperationsImpl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CheckConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.CheckConstraint", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "ColumnClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnClause", "kind": "Gdef"}, "Constraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Constraint", "kind": "Gdef"}, "DefaultImpl": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.impl.DefaultImpl", "kind": "Gdef"}, "Dialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Dialect", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ForeignKeyConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ForeignKeyConstraint", "kind": "Gdef"}, "Function": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.Function", "kind": "Gdef"}, "Index": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Index", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "MetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.MetaData", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.OrderedDict", "kind": "Gdef"}, "PrimaryKeyConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.PrimaryKeyConstraint", "kind": "Gdef"}, "SchemaEventTarget": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.SchemaEventTarget", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.batch.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.batch.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.batch.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.batch.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.batch.__package__", "name": "__package__", "type": "builtins.str"}}, "_columns_for_constraint": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._columns_for_constraint", "kind": "Gdef"}, "_copy": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._copy", "kind": "Gdef"}, "_copy_expression": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._copy_expression", "kind": "Gdef"}, "_ensure_scope_for_ddl": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._ensure_scope_for_ddl", "kind": "Gdef"}, "_fk_is_self_referential": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._fk_is_self_referential", "kind": "Gdef"}, "_idx_table_bound_expressions": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._idx_table_bound_expressions", "kind": "Gdef"}, "_insert_inline": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._insert_inline", "kind": "Gdef"}, "_is_type_bound": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._is_type_bound", "kind": "Gdef"}, "_remove_column_from_collection": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._remove_column_from_collection", "kind": "Gdef"}, "_resolve_for_variant": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat._resolve_for_variant", "kind": "Gdef"}, "_select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.select", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "constraint_name_defined": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat.constraint_name_defined", "kind": "Gdef"}, "constraint_name_string": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat.constraint_name_string", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "alembic.util.exc", "kind": "Gdef"}, "quoted_name": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.quoted_name", "kind": "Gdef"}, "sql_schema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.schema", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.types", "kind": "Gdef"}, "topological": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.topological", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/operations/batch.py"}