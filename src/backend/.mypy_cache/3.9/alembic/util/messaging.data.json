{".class": "MypyFile", "_fullname": "alembic.util.messaging", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TERMWIDTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.util.messaging.TERMWIDTH", "name": "TERMWIDTH", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "TextIO": {".class": "SymbolTableNode", "cross_ref": "typing.TextIO", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.messaging.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.messaging.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.messaging.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.messaging.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.messaging.__package__", "name": "__package__", "type": "builtins.str"}}, "_h": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.util.messaging._h", "name": "_h", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_hp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.util.messaging._hp", "name": "_hp", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_wp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.util.messaging._wp", "name": "_wp", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "err": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["message", "quiet"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.util.messaging.err", "name": "err", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["message", "quiet"], "arg_types": ["builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "err", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "fcntl": {".class": "SymbolTableNode", "cross_ref": "fcntl", "kind": "Gdef"}, "format_as_comma": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.util.messaging.format_as_comma", "name": "format_as_comma", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "type_ref": "typing.Iterable"}, {".class": "NoneType"}]}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_as_comma", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "ioctl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.util.messaging.ioctl", "name": "ioctl", "type": "builtins.bytes"}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.util.messaging.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "msg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["msg", "newline", "flush", "quiet"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.util.messaging.msg", "name": "msg", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["msg", "newline", "flush", "quiet"], "arg_types": ["builtins.str", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "msg", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "obfuscate_url_pw": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["input_url"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.util.messaging.obfuscate_url_pw", "name": "obfuscate_url_pw", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["input_url"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obfuscate_url_pw", "ret_type": "builtins.str", "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "sqla_compat": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat", "kind": "Gdef"}, "status": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["status_msg", "newline", "quiet"], "dataclass_transform_spec": null, "flags": ["is_decorated"], "fullname": "alembic.util.messaging.status", "name": "status", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["status_msg", "newline", "quiet"], "arg_types": ["builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "status", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.util.messaging.status", "name": "status", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["status_msg", "newline", "quiet"], "arg_types": ["builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "status", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "termios": {".class": "SymbolTableNode", "cross_ref": "termios", "kind": "Gdef"}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef"}, "url": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url", "kind": "Gdef"}, "warn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["msg", "stacklevel"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.util.messaging.warn", "name": "warn", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["msg", "stacklevel"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "warn", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "write_outstream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5], "arg_names": ["stream", "text", "quiet"], "dataclass_transform_spec": null, "flags": [], "fullname": "alembic.util.messaging.write_outstream", "name": "write_outstream", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5], "arg_names": ["stream", "text", "quiet"], "arg_types": ["typing.TextIO", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"]}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_outstream", "ret_type": {".class": "NoneType"}, "type_guard": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/util/messaging.py"}