{"data_mtime": 1754228260, "dep_lines": [13, 14, 4, 1, 3, 7, 8, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 30], "dependencies": ["alembic.util.compat", "alembic.util.exc", "os.path", "__future__", "os", "subprocess", "typing", "builtins", "abc"], "hash": "248cfafc176057cfe82ad9e1791e8366807baa7ac7ae54605a3c74f40b13b14c", "id": "alembic.util.editor", "ignore_all": true, "interface_hash": "84d3aa47162ed33e476acf9a80d1776db01c73f232c7dd5207fe778f7e5e9dbb", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/util/editor.py", "plugin_data": null, "size": 2546, "suppressed": [], "version_id": "1.7.1"}