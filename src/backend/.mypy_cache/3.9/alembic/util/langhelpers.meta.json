{"data_mtime": 1754228260, "dep_lines": [26, 4, 20, 1, 3, 5, 6, 17, 18, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 10, 10, 5, 30, 30, 30, 30], "dependencies": ["alembic.util.compat", "collections.abc", "sqlalchemy.util", "__future__", "collections", "textwrap", "typing", "uuid", "warnings", "builtins", "_typeshed", "abc", "sqlalchemy", "sqlalchemy.util._py_collections"], "hash": "6451b21b280745bced39e6a3a5a8e9a7285df86a783c1e6c94cbaf08556b9e68", "id": "alembic.util.langhelpers", "ignore_all": true, "interface_hash": "38277d611f4ca7455d5ca6bf462b5d29b15a2f6d093a87dc96ab5edb16980a14", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/util/langhelpers.py", "plugin_data": null, "size": 8591, "suppressed": [], "version_id": "1.7.1"}