{"data_mtime": 1754228260, "dep_lines": [13, 15, 3, 13, 15, 1, 4, 5, 6, 7, 8, 11, 24, 25, 26, 1, 1, 1], "dep_prios": [10, 10, 5, 20, 20, 5, 5, 10, 10, 10, 5, 10, 10, 10, 10, 5, 30, 30], "dependencies": ["sqlalchemy.engine.url", "alembic.util.sqla_compat", "collections.abc", "sqlalchemy.engine", "alembic.util", "__future__", "contextlib", "logging", "sys", "textwrap", "typing", "warnings", "fcntl", "termios", "struct", "builtins", "abc", "typing_extensions"], "hash": "07a4fe96832120e6373936c6e3b630a750dff4b667d7c3e08f0a702eb0f554d8", "id": "alembic.util.messaging", "ignore_all": true, "interface_hash": "887ee0f87ae1f3b2a25612ff178a3b23a1a76c9203a6e1ae18e326fe331ef099", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/util/messaging.py", "plugin_data": null, "size": 3042, "suppressed": [], "version_id": "1.7.1"}