{"data_mtime": 1754228260, "dep_lines": [16, 17, 6, 7, 16, 1, 3, 4, 5, 8, 9, 10, 11, 1, 1, 14, 13], "dep_prios": [10, 5, 10, 10, 20, 5, 10, 5, 10, 10, 10, 10, 5, 5, 30, 5, 5], "dependencies": ["alembic.util.compat", "alembic.util.exc", "importlib.machinery", "importlib.util", "alembic.util", "__future__", "atexit", "contextlib", "importlib", "os", "re", "tempfile", "typing", "builtins", "abc"], "hash": "f79274d450a13748f6b8fde9ef69a368e42f879c02e976dd1ad4c32bca04cec4", "id": "alembic.util.pyfiles", "ignore_all": true, "interface_hash": "518ecbd255d9958a019124f3df35d34908eb5accc19294531816a35b0a672b44", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/util/pyfiles.py", "plugin_data": null, "size": 3373, "suppressed": ["mako.template", "mako"], "version_id": "1.7.1"}