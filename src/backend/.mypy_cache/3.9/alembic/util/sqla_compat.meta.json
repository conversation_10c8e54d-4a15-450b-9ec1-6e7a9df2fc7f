{"data_mtime": 1754228260, "dep_lines": [20, 21, 25, 26, 27, 41, 43, 44, 46, 48, 73, 17, 18, 19, 20, 152, 1, 3, 4, 5, 15, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 25, 25, 25, 25, 25, 5, 5, 10, 10, 20, 5, 5, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.url", "sqlalchemy.ext.compiler", "sqlalchemy.sql.visitors", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.engine.reflection", "sqlalchemy.sql.compiler", "sqlalchemy.sql.dml", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.naming", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.types", "sqlalchemy.engine", "sqlalchemy.util", "__future__", "contextlib", "re", "typing", "sqlalchemy", "typing_extensions", "builtins", "abc", "enum", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.util", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.ext", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "f783079648f8df2f904324b3e5d09488950d38faf7045f5343f06b3fa7b2fbcc", "id": "alembic.util.sqla_compat", "ignore_all": true, "interface_hash": "a841ade01bd425a1fcfcf901bc5e493ae50409e44a04210558e28fc7d7b738da", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/util/sqla_compat.py", "plugin_data": null, "size": 18906, "suppressed": [], "version_id": "1.7.1"}