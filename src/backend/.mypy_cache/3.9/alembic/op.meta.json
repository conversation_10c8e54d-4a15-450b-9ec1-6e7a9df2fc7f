{"data_mtime": 1754228260, "dep_lines": [25, 28, 29, 30, 35, 38, 40, 41, 23, 24, 36, 3, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.elements", "sqlalchemy.sql.expression", "sqlalchemy.sql.functions", "sqlalchemy.sql.schema", "sqlalchemy.sql.type_api", "alembic.operations.ops", "alembic.runtime.migration", "alembic.util.sqla_compat", "sqlalchemy.engine", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "contextlib", "typing", "builtins", "abc", "alembic.operations", "alembic.operations.base", "alembic.runtime", "alembic.util", "alembic.util.langhelpers", "sqlalchemy", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "95d401c007f39bff99b02de78b332e1a80f7e218cc29be15ffe435ad1f2af0b2", "id": "alembic.op", "ignore_all": true, "interface_hash": "83c2d8b1a7885aa70dfc0751e0e94f11f986a56b8fa49fbf36d42fb9235c7157", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/op.pyi", "plugin_data": null, "size": 48591, "suppressed": [], "version_id": "1.7.1"}