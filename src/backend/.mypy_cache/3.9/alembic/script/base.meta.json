{"data_mtime": 1754228260, "dep_lines": [22, 23, 25, 22, 24, 25, 32, 1, 3, 4, 5, 6, 7, 8, 9, 10, 24, 1, 1, 1, 1, 1, 1, 1, 1, 38], "dep_prios": [10, 10, 10, 20, 5, 20, 25, 5, 5, 10, 10, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["alembic.script.revision", "alembic.script.write_hooks", "alembic.runtime.migration", "alembic.script", "alembic.util", "alembic.runtime", "alembic.config", "__future__", "contextlib", "datetime", "os", "re", "shutil", "sys", "types", "typing", "alembic", "builtins", "abc", "alembic.util.exc", "alembic.util.langhelpers", "posixpath", "sqlalchemy", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "f744a94fcc324cc4d4b92d12beccb9608a2a252ad1fba0ad612cd2b6646f153d", "id": "alembic.script.base", "ignore_all": true, "interface_hash": "bc643f5fe1230c0f2cd501f47d7ee9517402e00036a6228466bd2b5240826e98", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/script/base.py", "plugin_data": null, "size": 37174, "suppressed": ["dateutil"], "version_id": "1.7.1"}