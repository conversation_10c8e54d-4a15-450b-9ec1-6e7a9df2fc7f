{"data_mtime": 1754228260, "dep_lines": [24, 26, 1, 3, 4, 5, 24, 26, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 10, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util", "alembic.util", "__future__", "collections", "re", "typing", "sqlalchemy", "alembic", "builtins", "_collections_abc", "_typeshed", "abc", "alembic.util.langhelpers", "alembic.util.messaging", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "0c4d27c2f0cecdd168f38ddbaef9e1b350df3f48d10b9115407acd8a10bb3d44", "id": "alembic.script.revision", "ignore_all": true, "interface_hash": "3cff4ea9bb50bed170208d325957dd21aed507f275d03f30d0d34b71afe6c5c4", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/script/revision.py", "plugin_data": null, "size": 61471, "suppressed": [], "version_id": "1.7.1"}