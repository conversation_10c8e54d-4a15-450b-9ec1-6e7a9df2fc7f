{"data_mtime": 1754228260, "dep_lines": [15, 14, 1, 3, 4, 5, 6, 14, 1, 1], "dep_prios": [10, 10, 5, 10, 10, 10, 5, 20, 5, 30], "dependencies": ["alembic.util.compat", "alembic.util", "__future__", "shlex", "subprocess", "sys", "typing", "alembic", "builtins", "abc"], "hash": "36a8f8cf3dec9bdee400f3a92b59be8b6ce725c862c9b3bf4d64f9d28963949c", "id": "alembic.script.write_hooks", "ignore_all": true, "interface_hash": "ee52c9c6175ae34cbedf15fe35875b0110af5871c562a02271524daf9c0cf3b1", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/script/write_hooks.py", "plugin_data": null, "size": 4917, "suppressed": [], "version_id": "1.7.1"}