{"data_mtime": 1754228260, "dep_lines": [29, 30, 34, 35, 40, 42, 45, 47, 49, 28, 32, 33, 43, 46, 1, 3, 5, 6, 7, 22, 32, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 25, 25, 25, 25, 25, 5, 10, 10, 25, 25, 5, 5, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.url", "sqlalchemy.engine.strategies", "alembic.util.sqla_compat", "alembic.util.compat", "sqlalchemy.engine.base", "sqlalchemy.engine.mock", "alembic.runtime.environment", "alembic.script.base", "alembic.script.revision", "sqlalchemy.engine", "alembic.ddl", "alembic.util", "sqlalchemy.sql", "alembic.config", "__future__", "contextlib", "logging", "sys", "typing", "sqlalchemy", "alembic", "builtins", "abc", "alembic.ddl.impl", "alembic.script", "alembic.util.langhelpers", "enum", "io", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.util", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "e54b5323f4f426d633b7a66979485a9e73193af5d6884ca62853a978279f68f6", "id": "alembic.runtime.migration", "ignore_all": true, "interface_hash": "9edac98fce18a2215f5c349581539abe6206aa396990a2f77a78ef6b5fef7415", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/runtime/migration.py", "plugin_data": null, "size": 49407, "suppressed": [], "version_id": "1.7.1"}