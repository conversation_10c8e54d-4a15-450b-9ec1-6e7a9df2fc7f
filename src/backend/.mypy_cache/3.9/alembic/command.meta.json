{"data_mtime": 1754228260, "dep_lines": [11, 16, 17, 9, 10, 12, 15, 1, 3, 4, 9, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 10, 10, 5, 25, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["alembic.runtime.environment", "alembic.script.base", "alembic.script.revision", "alembic.autogenerate", "alembic.util", "alembic.script", "alembic.config", "__future__", "os", "typing", "alembic", "builtins", "abc", "alembic.operations", "alembic.operations.ops", "alembic.runtime", "alembic.runtime.migration"], "hash": "cdee2962f2a907e16d17caddb98e85ea7dd71ea780fb5e625da6840de3475732", "id": "alembic.command", "ignore_all": true, "interface_hash": "f347ceeaaefc12166f18fec7e3903cadd53f047480e542f4ecbd5018344fc55f", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/command.py", "plugin_data": null, "size": 21588, "suppressed": [], "version_id": "1.7.1"}