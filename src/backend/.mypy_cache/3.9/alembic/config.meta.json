{"data_mtime": 1754228260, "dep_lines": [23, 21, 22, 1, 3, 5, 6, 7, 8, 9, 18, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["alembic.util.compat", "alembic.command", "alembic.util", "__future__", "<PERSON><PERSON><PERSON><PERSON>", "configparser", "inspect", "os", "sys", "typing", "typing_extensions", "alembic", "builtins", "_typeshed", "abc", "sqlalchemy", "sqlalchemy.util", "sqlalchemy.util._py_collections", "sqlalchemy.util.compat", "sqlalchemy.util.langhelpers", "types"], "hash": "ebc7b59e6614e4d7e1d1b351a91594ca04a2943975a741bf535cd9f227e0983f", "id": "alembic.config", "ignore_all": true, "interface_hash": "b738565dbfb40dc00e1e45c740688974dedc3dd0d44063dca09f97ece0885a1c", "mtime": 1754207016, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/alembic/config.py", "plugin_data": null, "size": 21931, "suppressed": [], "version_id": "1.7.1"}