{"data_mtime": 1754228257, "dep_lines": [1, 2, 3, 6, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 30, 30], "dependencies": ["sys", "threading", "typing", "types", "builtins", "_typeshed", "abc"], "hash": "52f97b3b29e2499ba75dcfa91b065c808da80bdfe097d8f7e96b67334ea3117c", "id": "queue", "ignore_all": true, "interface_hash": "b130aaf107ee87bfa72bf32d84157a6b6d663f0edc578b7627ca0c7e024815bc", "mtime": 1754207148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/venv/lib/python3.9/site-packages/mypy/typeshed/stdlib/queue.pyi", "plugin_data": null, "size": 2042, "suppressed": [], "version_id": "1.7.1"}