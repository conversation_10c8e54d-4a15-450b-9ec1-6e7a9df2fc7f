<?xml version="1.0" ?>
<coverage version="7.10.1" timestamp="1754244848081" lines-valid="1361" lines-covered="668" line-rate="0.4908" branches-covered="0" branches-valid="0" branch-rate="0" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.10.1 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source>/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/backend/app</source>
	</sources>
	<packages>
		<package name="." line-rate="0.6463" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
					</lines>
				</class>
				<class name="main.py" filename="main.py" complexity="0" line-rate="0.6329" branch-rate="0">
					<methods/>
					<lines>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="42" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="57" hits="0"/>
						<line number="59" hits="0"/>
						<line number="61" hits="0"/>
						<line number="62" hits="0"/>
						<line number="65" hits="0"/>
						<line number="66" hits="0"/>
						<line number="68" hits="0"/>
						<line number="70" hits="0"/>
						<line number="72" hits="0"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="82" hits="1"/>
						<line number="91" hits="1"/>
						<line number="103" hits="1"/>
						<line number="106" hits="1"/>
						<line number="109" hits="1"/>
						<line number="111" hits="1"/>
						<line number="114" hits="1"/>
						<line number="123" hits="1"/>
						<line number="124" hits="0"/>
						<line number="127" hits="1"/>
						<line number="137" hits="1"/>
						<line number="138" hits="1"/>
						<line number="139" hits="1"/>
						<line number="140" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="0"/>
						<line number="147" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="1"/>
						<line number="160" hits="0"/>
						<line number="171" hits="1"/>
						<line number="172" hits="1"/>
						<line number="173" hits="0"/>
						<line number="174" hits="0"/>
						<line number="184" hits="1"/>
						<line number="185" hits="1"/>
						<line number="186" hits="0"/>
						<line number="197" hits="1"/>
						<line number="200" hits="1"/>
						<line number="209" hits="1"/>
						<line number="210" hits="1"/>
						<line number="217" hits="0"/>
						<line number="219" hits="0"/>
						<line number="221" hits="0"/>
						<line number="229" hits="0"/>
						<line number="230" hits="0"/>
						<line number="231" hits="0"/>
						<line number="241" hits="1"/>
						<line number="242" hits="1"/>
						<line number="249" hits="0"/>
						<line number="258" hits="1"/>
						<line number="262" hits="1"/>
						<line number="265" hits="1"/>
						<line number="266" hits="0"/>
						<line number="268" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api" line-rate="1" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
			</classes>
		</package>
		<package name="api.v1" line-rate="1" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/v1/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="api.py" filename="api/v1/api.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="23" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api.v1.endpoints" line-rate="0.33" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/v1/endpoints/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="channels.py" filename="api/v1/endpoints/channels.py" complexity="0" line-rate="0.3234" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="32" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="39" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="50" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="72" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0"/>
						<line number="79" hits="0"/>
						<line number="85" hits="1"/>
						<line number="86" hits="0"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="95" hits="1"/>
						<line number="96" hits="0"/>
						<line number="102" hits="1"/>
						<line number="105" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="0"/>
						<line number="112" hits="0"/>
						<line number="113" hits="0"/>
						<line number="114" hits="0"/>
						<line number="120" hits="0"/>
						<line number="127" hits="0"/>
						<line number="135" hits="0"/>
						<line number="136" hits="0"/>
						<line number="139" hits="0"/>
						<line number="147" hits="0"/>
						<line number="148" hits="0"/>
						<line number="151" hits="0"/>
						<line number="153" hits="0"/>
						<line number="166" hits="0"/>
						<line number="167" hits="0"/>
						<line number="168" hits="0"/>
						<line number="169" hits="0"/>
						<line number="170" hits="0"/>
						<line number="176" hits="1"/>
						<line number="177" hits="1"/>
						<line number="192" hits="1"/>
						<line number="195" hits="1"/>
						<line number="196" hits="0"/>
						<line number="198" hits="0"/>
						<line number="199" hits="0"/>
						<line number="204" hits="0"/>
						<line number="205" hits="0"/>
						<line number="207" hits="0"/>
						<line number="219" hits="1"/>
						<line number="220" hits="1"/>
						<line number="234" hits="0"/>
						<line number="237" hits="0"/>
						<line number="238" hits="0"/>
						<line number="240" hits="0"/>
						<line number="241" hits="0"/>
						<line number="247" hits="0"/>
						<line number="248" hits="0"/>
						<line number="249" hits="0"/>
						<line number="251" hits="0"/>
						<line number="264" hits="1"/>
						<line number="265" hits="1"/>
						<line number="282" hits="0"/>
						<line number="285" hits="0"/>
						<line number="286" hits="0"/>
						<line number="288" hits="0"/>
						<line number="289" hits="0"/>
						<line number="291" hits="0"/>
						<line number="292" hits="0"/>
						<line number="294" hits="0"/>
						<line number="295" hits="0"/>
						<line number="297" hits="0"/>
						<line number="311" hits="0"/>
						<line number="317" hits="1"/>
						<line number="318" hits="1"/>
						<line number="335" hits="0"/>
						<line number="338" hits="0"/>
						<line number="339" hits="0"/>
						<line number="341" hits="0"/>
						<line number="342" hits="0"/>
						<line number="348" hits="0"/>
						<line number="349" hits="0"/>
						<line number="350" hits="0"/>
						<line number="351" hits="0"/>
						<line number="352" hits="0"/>
						<line number="353" hits="0"/>
						<line number="355" hits="0"/>
						<line number="356" hits="0"/>
						<line number="357" hits="0"/>
						<line number="359" hits="0"/>
						<line number="361" hits="0"/>
						<line number="362" hits="0"/>
						<line number="364" hits="0"/>
						<line number="368" hits="0"/>
						<line number="371" hits="0"/>
						<line number="372" hits="0"/>
						<line number="378" hits="0"/>
						<line number="380" hits="0"/>
						<line number="383" hits="0"/>
						<line number="384" hits="0"/>
						<line number="386" hits="0"/>
						<line number="389" hits="0"/>
						<line number="390" hits="0"/>
						<line number="391" hits="0"/>
						<line number="393" hits="0"/>
						<line number="407" hits="1"/>
						<line number="408" hits="1"/>
						<line number="423" hits="0"/>
						<line number="426" hits="0"/>
						<line number="427" hits="0"/>
						<line number="429" hits="0"/>
						<line number="430" hits="0"/>
						<line number="436" hits="0"/>
						<line number="439" hits="0"/>
						<line number="446" hits="0"/>
						<line number="449" hits="0"/>
						<line number="450" hits="0"/>
						<line number="456" hits="0"/>
						<line number="458" hits="0"/>
						<line number="461" hits="0"/>
						<line number="464" hits="0"/>
						<line number="465" hits="0"/>
						<line number="467" hits="0"/>
						<line number="480" hits="1"/>
						<line number="481" hits="1"/>
						<line number="496" hits="0"/>
						<line number="499" hits="0"/>
						<line number="500" hits="0"/>
						<line number="502" hits="0"/>
						<line number="503" hits="0"/>
						<line number="509" hits="0"/>
						<line number="512" hits="0"/>
						<line number="519" hits="0"/>
						<line number="522" hits="0"/>
						<line number="523" hits="0"/>
						<line number="529" hits="0"/>
						<line number="531" hits="0"/>
						<line number="534" hits="0"/>
						<line number="537" hits="0"/>
						<line number="538" hits="0"/>
						<line number="540" hits="0"/>
						<line number="553" hits="1"/>
						<line number="554" hits="1"/>
						<line number="569" hits="1"/>
						<line number="572" hits="1"/>
						<line number="573" hits="0"/>
						<line number="575" hits="0"/>
						<line number="576" hits="0"/>
						<line number="582" hits="0"/>
						<line number="583" hits="0"/>
						<line number="587" hits="0"/>
						<line number="588" hits="0"/>
						<line number="591" hits="0"/>
						<line number="592" hits="0"/>
						<line number="593" hits="0"/>
						<line number="604" hits="0"/>
						<line number="610" hits="1"/>
						<line number="611" hits="1"/>
						<line number="627" hits="0"/>
						<line number="629" hits="0"/>
						<line number="630" hits="0"/>
						<line number="632" hits="0"/>
						<line number="633" hits="0"/>
						<line number="638" hits="0"/>
						<line number="639" hits="0"/>
						<line number="643" hits="0"/>
						<line number="650" hits="0"/>
						<line number="652" hits="0"/>
						<line number="653" hits="0"/>
						<line number="658" hits="0"/>
						<line number="661" hits="0"/>
						<line number="663" hits="0"/>
					</lines>
				</class>
				<class name="health.py" filename="api/v1/endpoints/health.py" complexity="0" line-rate="0.3" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="33" hits="0"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="60" hits="0"/>
						<line number="61" hits="0"/>
						<line number="71" hits="0"/>
						<line number="72" hits="0"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="76" hits="0"/>
						<line number="85" hits="0"/>
						<line number="86" hits="0"/>
						<line number="88" hits="0"/>
						<line number="89" hits="0"/>
						<line number="90" hits="0"/>
						<line number="91" hits="0"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0"/>
						<line number="98" hits="0"/>
						<line number="100" hits="0"/>
						<line number="105" hits="0"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="110" hits="0"/>
						<line number="111" hits="0"/>
						<line number="114" hits="0"/>
						<line number="116" hits="0"/>
						<line number="121" hits="0"/>
						<line number="122" hits="0"/>
						<line number="123" hits="0"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0"/>
						<line number="130" hits="0"/>
						<line number="131" hits="0"/>
						<line number="135" hits="0"/>
						<line number="138" hits="1"/>
						<line number="139" hits="1"/>
						<line number="152" hits="0"/>
						<line number="154" hits="0"/>
						<line number="156" hits="0"/>
						<line number="157" hits="0"/>
						<line number="163" hits="0"/>
						<line number="169" hits="0"/>
						<line number="170" hits="0"/>
						<line number="171" hits="0"/>
						<line number="178" hits="1"/>
						<line number="179" hits="1"/>
						<line number="189" hits="0"/>
					</lines>
				</class>
				<class name="monitoring.py" filename="api/v1/endpoints/monitoring.py" complexity="0" line-rate="0.2933" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="0"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="44" hits="0"/>
						<line number="47" hits="0"/>
						<line number="48" hits="0"/>
						<line number="51" hits="0"/>
						<line number="53" hits="0"/>
						<line number="85" hits="1"/>
						<line number="86" hits="1"/>
						<line number="96" hits="1"/>
						<line number="99" hits="1"/>
						<line number="100" hits="0"/>
						<line number="102" hits="0"/>
						<line number="103" hits="0"/>
						<line number="105" hits="0"/>
						<line number="108" hits="0"/>
						<line number="109" hits="0"/>
						<line number="112" hits="0"/>
						<line number="114" hits="0"/>
						<line number="127" hits="1"/>
						<line number="128" hits="0"/>
						<line number="130" hits="0"/>
						<line number="131" hits="0"/>
						<line number="132" hits="0"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="135" hits="0"/>
						<line number="137" hits="0"/>
						<line number="139" hits="1"/>
						<line number="151" hits="1"/>
						<line number="152" hits="1"/>
						<line number="169" hits="1"/>
						<line number="170" hits="1"/>
						<line number="171" hits="1"/>
						<line number="172" hits="1"/>
						<line number="173" hits="1"/>
						<line number="175" hits="0"/>
						<line number="178" hits="1"/>
						<line number="179" hits="1"/>
						<line number="180" hits="0"/>
						<line number="181" hits="0"/>
						<line number="183" hits="0"/>
						<line number="186" hits="1"/>
						<line number="189" hits="1"/>
						<line number="190" hits="0"/>
						<line number="192" hits="0"/>
						<line number="195" hits="0"/>
						<line number="196" hits="0"/>
						<line number="198" hits="0"/>
						<line number="201" hits="0"/>
						<line number="202" hits="0"/>
						<line number="205" hits="0"/>
						<line number="211" hits="0"/>
						<line number="212" hits="0"/>
						<line number="215" hits="0"/>
						<line number="218" hits="0"/>
						<line number="219" hits="0"/>
						<line number="220" hits="0"/>
						<line number="221" hits="0"/>
						<line number="227" hits="0"/>
						<line number="229" hits="0"/>
						<line number="251" hits="1"/>
						<line number="252" hits="1"/>
						<line number="267" hits="0"/>
						<line number="268" hits="0"/>
						<line number="269" hits="0"/>
						<line number="271" hits="0"/>
						<line number="272" hits="0"/>
						<line number="277" hits="0"/>
						<line number="289" hits="1"/>
						<line number="290" hits="1"/>
						<line number="305" hits="0"/>
						<line number="306" hits="0"/>
						<line number="307" hits="0"/>
						<line number="309" hits="0"/>
						<line number="310" hits="0"/>
						<line number="315" hits="0"/>
						<line number="327" hits="1"/>
						<line number="328" hits="1"/>
						<line number="345" hits="0"/>
						<line number="346" hits="0"/>
						<line number="347" hits="0"/>
						<line number="349" hits="0"/>
						<line number="350" hits="0"/>
						<line number="355" hits="0"/>
						<line number="356" hits="0"/>
						<line number="359" hits="0"/>
						<line number="367" hits="0"/>
						<line number="380" hits="1"/>
						<line number="381" hits="1"/>
						<line number="396" hits="1"/>
						<line number="397" hits="1"/>
						<line number="398" hits="0"/>
						<line number="400" hits="0"/>
						<line number="401" hits="0"/>
						<line number="407" hits="0"/>
						<line number="408" hits="0"/>
						<line number="409" hits="0"/>
						<line number="410" hits="0"/>
						<line number="411" hits="0"/>
						<line number="413" hits="0"/>
						<line number="415" hits="0"/>
						<line number="416" hits="0"/>
						<line number="418" hits="0"/>
						<line number="419" hits="0"/>
						<line number="421" hits="0"/>
						<line number="424" hits="0"/>
						<line number="425" hits="0"/>
						<line number="426" hits="0"/>
						<line number="427" hits="0"/>
						<line number="428" hits="0"/>
						<line number="429" hits="0"/>
						<line number="430" hits="0"/>
						<line number="431" hits="0"/>
						<line number="432" hits="0"/>
						<line number="433" hits="0"/>
						<line number="434" hits="0"/>
						<line number="435" hits="0"/>
						<line number="436" hits="0"/>
						<line number="437" hits="0"/>
						<line number="438" hits="0"/>
						<line number="439" hits="0"/>
						<line number="440" hits="0"/>
						<line number="441" hits="0"/>
						<line number="448" hits="0"/>
						<line number="449" hits="0"/>
						<line number="452" hits="0"/>
						<line number="453" hits="0"/>
						<line number="460" hits="0"/>
						<line number="461" hits="0"/>
						<line number="477" hits="0"/>
						<line number="489" hits="0"/>
					</lines>
				</class>
				<class name="notifications.py" filename="api/v1/endpoints/notifications.py" complexity="0" line-rate="0.5185" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="37" hits="0"/>
						<line number="39" hits="0"/>
						<line number="41" hits="0"/>
						<line number="75" hits="0"/>
						<line number="89" hits="0"/>
						<line number="90" hits="0"/>
						<line number="93" hits="0"/>
						<line number="95" hits="0"/>
						<line number="102" hits="1"/>
						<line number="103" hits="1"/>
						<line number="117" hits="0"/>
						<line number="127" hits="1"/>
						<line number="128" hits="1"/>
						<line number="142" hits="0"/>
						<line number="152" hits="1"/>
						<line number="153" hits="1"/>
						<line number="167" hits="0"/>
						<line number="169" hits="0"/>
						<line number="180" hits="0"/>
					</lines>
				</class>
				<class name="system.py" filename="api/v1/endpoints/system.py" complexity="0" line-rate="0.3929" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="33" hits="0"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="65" hits="0"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="68" hits="0"/>
						<line number="69" hits="0"/>
						<line number="70" hits="0"/>
						<line number="73" hits="0"/>
						<line number="76" hits="0"/>
						<line number="79" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="85" hits="0"/>
						<line number="120" hits="1"/>
						<line number="121" hits="1"/>
						<line number="130" hits="0"/>
						<line number="182" hits="0"/>
						<line number="185" hits="1"/>
						<line number="186" hits="1"/>
						<line number="195" hits="0"/>
						<line number="198" hits="0"/>
						<line number="211" hits="0"/>
						<line number="212" hits="0"/>
						<line number="214" hits="0"/>
						<line number="216" hits="0"/>
						<line number="217" hits="0"/>
						<line number="219" hits="0"/>
						<line number="221" hits="0"/>
						<line number="230" hits="1"/>
						<line number="231" hits="1"/>
						<line number="239" hits="0"/>
						<line number="240" hits="0"/>
						<line number="242" hits="0"/>
						<line number="243" hits="0"/>
						<line number="244" hits="0"/>
						<line number="253" hits="0"/>
						<line number="255" hits="0"/>
						<line number="261" hits="0"/>
						<line number="262" hits="0"/>
						<line number="263" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="connectors" line-rate="0.5294" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="connectors/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="douyin_connector.py" filename="connectors/douyin_connector.py" complexity="0" line-rate="0.4118" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="0"/>
						<line number="16" hits="1"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="30" hits="1"/>
						<line number="42" hits="0"/>
						<line number="43" hits="0"/>
						<line number="45" hits="0"/>
						<line number="54" hits="1"/>
						<line number="64" hits="0"/>
						<line number="66" hits="1"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
					</lines>
				</class>
				<class name="xianyu_connector.py" filename="connectors/xianyu_connector.py" complexity="0" line-rate="0.6471" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="30" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="45" hits="1"/>
						<line number="56" hits="1"/>
						<line number="66" hits="0"/>
						<line number="68" hits="1"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="core" line-rate="0.5332" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="core/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="config.py" filename="core/config.py" complexity="0" line-rate="0.9186" branch-rate="0">
					<methods/>
					<lines>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="37" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="50" hits="1"/>
						<line number="53" hits="1"/>
						<line number="56" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="65" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="72" hits="1"/>
						<line number="76" hits="1"/>
						<line number="79" hits="1"/>
						<line number="84" hits="1"/>
						<line number="85" hits="1"/>
						<line number="86" hits="1"/>
						<line number="89" hits="1"/>
						<line number="90" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1"/>
						<line number="99" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="1"/>
						<line number="111" hits="1"/>
						<line number="112" hits="1"/>
						<line number="115" hits="1"/>
						<line number="118" hits="1"/>
						<line number="123" hits="1"/>
						<line number="124" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1"/>
						<line number="129" hits="1"/>
						<line number="130" hits="1"/>
						<line number="131" hits="0"/>
						<line number="132" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
						<line number="137" hits="1"/>
						<line number="138" hits="1"/>
						<line number="139" hits="0"/>
						<line number="140" hits="1"/>
						<line number="142" hits="1"/>
						<line number="143" hits="1"/>
						<line number="145" hits="1"/>
						<line number="146" hits="0"/>
						<line number="147" hits="1"/>
						<line number="149" hits="1"/>
						<line number="150" hits="1"/>
						<line number="152" hits="1"/>
						<line number="153" hits="0"/>
						<line number="154" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="1"/>
						<line number="159" hits="0"/>
						<line number="161" hits="1"/>
						<line number="162" hits="1"/>
						<line number="164" hits="0"/>
						<line number="166" hits="1"/>
						<line number="167" hits="1"/>
						<line number="169" hits="0"/>
						<line number="171" hits="1"/>
						<line number="172" hits="1"/>
						<line number="173" hits="1"/>
						<line number="174" hits="1"/>
						<line number="175" hits="1"/>
						<line number="178" hits="1"/>
						<line number="179" hits="1"/>
						<line number="186" hits="1"/>
					</lines>
				</class>
				<class name="database.py" filename="core/database.py" complexity="0" line-rate="0.2479" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="33" hits="1"/>
						<line number="36" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="48" hits="1"/>
						<line number="54" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="62" hits="0"/>
						<line number="73" hits="0"/>
						<line number="81" hits="0"/>
						<line number="82" hits="0"/>
						<line number="84" hits="0"/>
						<line number="85" hits="0"/>
						<line number="86" hits="0"/>
						<line number="88" hits="1"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0"/>
						<line number="97" hits="0"/>
						<line number="98" hits="0"/>
						<line number="100" hits="0"/>
						<line number="101" hits="0"/>
						<line number="102" hits="0"/>
						<line number="104" hits="0"/>
						<line number="105" hits="0"/>
						<line number="107" hits="0"/>
						<line number="109" hits="0"/>
						<line number="110" hits="0"/>
						<line number="112" hits="1"/>
						<line number="121" hits="0"/>
						<line number="122" hits="0"/>
						<line number="124" hits="0"/>
						<line number="125" hits="0"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0"/>
						<line number="128" hits="0"/>
						<line number="129" hits="0"/>
						<line number="130" hits="0"/>
						<line number="132" hits="0"/>
						<line number="134" hits="1"/>
						<line number="141" hits="0"/>
						<line number="142" hits="0"/>
						<line number="143" hits="0"/>
						<line number="145" hits="0"/>
						<line number="146" hits="0"/>
						<line number="147" hits="0"/>
						<line number="148" hits="0"/>
						<line number="150" hits="0"/>
						<line number="151" hits="0"/>
						<line number="152" hits="0"/>
						<line number="154" hits="0"/>
						<line number="155" hits="0"/>
						<line number="157" hits="0"/>
						<line number="158" hits="0"/>
						<line number="159" hits="0"/>
						<line number="161" hits="1"/>
						<line number="172" hits="0"/>
						<line number="173" hits="0"/>
						<line number="175" hits="0"/>
						<line number="176" hits="0"/>
						<line number="177" hits="0"/>
						<line number="178" hits="0"/>
						<line number="180" hits="0"/>
						<line number="181" hits="0"/>
						<line number="182" hits="0"/>
						<line number="184" hits="1"/>
						<line number="185" hits="1"/>
						<line number="187" hits="0"/>
						<line number="189" hits="1"/>
						<line number="190" hits="1"/>
						<line number="192" hits="0"/>
						<line number="196" hits="1"/>
						<line number="199" hits="1"/>
						<line number="208" hits="0"/>
						<line number="209" hits="0"/>
						<line number="212" hits="1"/>
						<line number="218" hits="0"/>
						<line number="219" hits="0"/>
						<line number="221" hits="0"/>
						<line number="222" hits="0"/>
						<line number="224" hits="0"/>
						<line number="225" hits="0"/>
						<line number="227" hits="0"/>
						<line number="229" hits="0"/>
						<line number="230" hits="0"/>
						<line number="231" hits="0"/>
						<line number="234" hits="1"/>
						<line number="240" hits="0"/>
						<line number="241" hits="0"/>
						<line number="243" hits="0"/>
						<line number="244" hits="0"/>
						<line number="246" hits="0"/>
						<line number="247" hits="0"/>
						<line number="249" hits="0"/>
						<line number="251" hits="0"/>
						<line number="252" hits="0"/>
						<line number="253" hits="0"/>
						<line number="257" hits="1"/>
						<line number="266" hits="0"/>
						<line number="267" hits="0"/>
						<line number="268" hits="0"/>
						<line number="269" hits="0"/>
						<line number="271" hits="0"/>
						<line number="272" hits="0"/>
					</lines>
				</class>
				<class name="exceptions.py" filename="core/exceptions.py" complexity="0" line-rate="0.3718" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
						<line number="23" hits="1"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
						<line number="35" hits="1"/>
						<line number="42" hits="1"/>
						<line number="48" hits="0"/>
						<line number="49" hits="0"/>
						<line number="50" hits="0"/>
						<line number="53" hits="1"/>
						<line number="60" hits="1"/>
						<line number="63" hits="0"/>
						<line number="66" hits="1"/>
						<line number="73" hits="1"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="84" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="0"/>
						<line number="95" hits="1"/>
						<line number="102" hits="1"/>
						<line number="108" hits="0"/>
						<line number="109" hits="0"/>
						<line number="110" hits="0"/>
						<line number="113" hits="1"/>
						<line number="120" hits="1"/>
						<line number="127" hits="0"/>
						<line number="128" hits="0"/>
						<line number="129" hits="0"/>
						<line number="130" hits="0"/>
						<line number="131" hits="0"/>
						<line number="132" hits="0"/>
						<line number="133" hits="0"/>
						<line number="136" hits="1"/>
						<line number="143" hits="1"/>
						<line number="149" hits="0"/>
						<line number="150" hits="0"/>
						<line number="151" hits="0"/>
						<line number="154" hits="1"/>
						<line number="161" hits="1"/>
						<line number="168" hits="0"/>
						<line number="169" hits="0"/>
						<line number="170" hits="0"/>
						<line number="171" hits="0"/>
						<line number="172" hits="0"/>
						<line number="173" hits="0"/>
						<line number="174" hits="0"/>
						<line number="177" hits="1"/>
						<line number="184" hits="1"/>
						<line number="190" hits="0"/>
						<line number="191" hits="0"/>
						<line number="192" hits="0"/>
						<line number="195" hits="1"/>
						<line number="202" hits="1"/>
						<line number="208" hits="0"/>
						<line number="209" hits="0"/>
						<line number="210" hits="0"/>
						<line number="213" hits="1"/>
						<line number="228" hits="0"/>
						<line number="231" hits="0"/>
						<line number="242" hits="0"/>
						<line number="243" hits="0"/>
						<line number="254" hits="0"/>
						<line number="264" hits="1"/>
						<line number="285" hits="0"/>
						<line number="290" hits="0"/>
						<line number="291" hits="0"/>
						<line number="293" hits="0"/>
						<line number="294" hits="0"/>
						<line number="296" hits="0"/>
					</lines>
				</class>
				<class name="logging.py" filename="core/logging.py" complexity="0" line-rate="0.6479" branch-rate="0">
					<methods/>
					<lines>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="1"/>
						<line number="23" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="37" hits="1"/>
						<line number="43" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="52" hits="0"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="0"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="72" hits="1"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="76" hits="0"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="85" hits="1"/>
						<line number="91" hits="1"/>
						<line number="113" hits="1"/>
						<line number="114" hits="1"/>
						<line number="116" hits="0"/>
						<line number="119" hits="1"/>
						<line number="127" hits="1"/>
						<line number="142" hits="1"/>
						<line number="150" hits="1"/>
						<line number="153" hits="1"/>
						<line number="164" hits="0"/>
						<line number="167" hits="1"/>
						<line number="174" hits="1"/>
						<line number="175" hits="1"/>
						<line number="177" hits="0"/>
						<line number="180" hits="1"/>
						<line number="189" hits="0"/>
						<line number="190" hits="0"/>
						<line number="193" hits="1"/>
						<line number="203" hits="0"/>
						<line number="204" hits="0"/>
						<line number="207" hits="1"/>
						<line number="221" hits="0"/>
						<line number="222" hits="0"/>
						<line number="230" hits="1"/>
						<line number="240" hits="0"/>
						<line number="241" hits="0"/>
						<line number="244" hits="1"/>
						<line number="254" hits="0"/>
						<line number="255" hits="0"/>
						<line number="258" hits="1"/>
						<line number="267" hits="0"/>
						<line number="268" hits="0"/>
						<line number="271" hits="1"/>
						<line number="280" hits="0"/>
						<line number="281" hits="0"/>
						<line number="284" hits="1"/>
						<line number="297" hits="0"/>
						<line number="298" hits="0"/>
					</lines>
				</class>
				<class name="middleware.py" filename="core/middleware.py" complexity="0" line-rate="0.58" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="29" hits="1"/>
						<line number="41" hits="1"/>
						<line number="44" hits="1"/>
						<line number="47" hits="1"/>
						<line number="50" hits="1"/>
						<line number="52" hits="1"/>
						<line number="55" hits="1"/>
						<line number="62" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="79" hits="1"/>
						<line number="82" hits="1"/>
						<line number="85" hits="1"/>
						<line number="96" hits="1"/>
						<line number="98" hits="1"/>
						<line number="101" hits="1"/>
						<line number="104" hits="1"/>
						<line number="107" hits="1"/>
						<line number="117" hits="1"/>
						<line number="119" hits="0"/>
						<line number="121" hits="0"/>
						<line number="124" hits="0"/>
						<line number="135" hits="0"/>
						<line number="137" hits="1"/>
						<line number="149" hits="1"/>
						<line number="150" hits="1"/>
						<line number="151" hits="0"/>
						<line number="153" hits="1"/>
						<line number="154" hits="1"/>
						<line number="155" hits="0"/>
						<line number="158" hits="1"/>
						<line number="161" hits="1"/>
						<line number="168" hits="1"/>
						<line number="171" hits="0"/>
						<line number="172" hits="0"/>
						<line number="173" hits="0"/>
						<line number="174" hits="0"/>
						<line number="176" hits="1"/>
						<line number="189" hits="0"/>
						<line number="190" hits="0"/>
						<line number="193" hits="0"/>
						<line number="194" hits="0"/>
						<line number="197" hits="0"/>
						<line number="200" hits="0"/>
						<line number="201" hits="0"/>
						<line number="204" hits="0"/>
						<line number="210" hits="0"/>
						<line number="212" hits="0"/>
						<line number="220" hits="0"/>
						<line number="231" hits="0"/>
						<line number="234" hits="0"/>
						<line number="236" hits="0"/>
						<line number="238" hits="1"/>
						<line number="240" hits="0"/>
						<line number="241" hits="0"/>
						<line number="242" hits="0"/>
						<line number="244" hits="0"/>
						<line number="245" hits="0"/>
						<line number="246" hits="0"/>
						<line number="248" hits="0"/>
						<line number="250" hits="1"/>
						<line number="252" hits="0"/>
						<line number="254" hits="0"/>
						<line number="256" hits="0"/>
						<line number="262" hits="0"/>
						<line number="263" hits="0"/>
						<line number="265" hits="0"/>
						<line number="268" hits="0"/>
						<line number="269" hits="0"/>
						<line number="272" hits="1"/>
						<line number="279" hits="1"/>
						<line number="291" hits="1"/>
						<line number="294" hits="1"/>
						<line number="302" hits="1"/>
						<line number="303" hits="1"/>
						<line number="305" hits="1"/>
						<line number="308" hits="1"/>
						<line number="315" hits="1"/>
						<line number="327" hits="1"/>
						<line number="330" hits="1"/>
						<line number="332" hits="1"/>
						<line number="334" hits="0"/>
						<line number="335" hits="1"/>
						<line number="337" hits="1"/>
						<line number="338" hits="1"/>
						<line number="339" hits="1"/>
						<line number="340" hits="0"/>
						<line number="342" hits="0"/>
						<line number="344" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="models" line-rate="0.7202" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="models/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
					</lines>
				</class>
				<class name="base.py" filename="models/base.py" complexity="0" line-rate="0.7143" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="0"/>
						<line number="28" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="0"/>
						<line number="36" hits="1"/>
						<line number="37" hits="0"/>
						<line number="40" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="0"/>
						<line number="52" hits="1"/>
						<line number="60" hits="1"/>
						<line number="64" hits="1"/>
						<line number="71" hits="0"/>
						<line number="75" hits="1"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="86" hits="1"/>
						<line number="93" hits="0"/>
						<line number="96" hits="0"/>
						<line number="97" hits="0"/>
						<line number="98" hits="0"/>
						<line number="101" hits="0"/>
						<line number="103" hits="0"/>
						<line number="106" hits="1"/>
						<line number="113" hits="1"/>
						<line number="118" hits="1"/>
						<line number="125" hits="1"/>
						<line number="129" hits="1"/>
						<line number="137" hits="1"/>
						<line number="144" hits="1"/>
						<line number="148" hits="1"/>
						<line number="149" hits="1"/>
						<line number="156" hits="0"/>
						<line number="158" hits="1"/>
						<line number="162" hits="0"/>
						<line number="164" hits="1"/>
						<line number="168" hits="0"/>
						<line number="171" hits="1"/>
						<line number="178" hits="1"/>
						<line number="180" hits="1"/>
						<line number="184" hits="0"/>
						<line number="187" hits="1"/>
						<line number="194" hits="1"/>
						<line number="198" hits="1"/>
					</lines>
				</class>
				<class name="channel.py" filename="models/channel.py" complexity="0" line-rate="0.9444" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="35" hits="1"/>
						<line number="43" hits="1"/>
						<line number="46" hits="1"/>
						<line number="53" hits="1"/>
						<line number="60" hits="1"/>
						<line number="67" hits="1"/>
						<line number="74" hits="1"/>
						<line number="81" hits="1"/>
						<line number="87" hits="1"/>
						<line number="88" hits="0"/>
						<line number="90" hits="1"/>
						<line number="92" hits="0"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1"/>
						<line number="99" hits="1"/>
						<line number="103" hits="1"/>
					</lines>
				</class>
				<class name="channel_connection_log.py" filename="models/channel_connection_log.py" complexity="0" line-rate="0.8" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="33" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="48" hits="1"/>
						<line number="55" hits="1"/>
						<line number="58" hits="1"/>
						<line number="66" hits="1"/>
						<line number="72" hits="1"/>
						<line number="79" hits="1"/>
						<line number="85" hits="1"/>
						<line number="92" hits="1"/>
						<line number="98" hits="1"/>
						<line number="105" hits="1"/>
						<line number="111" hits="1"/>
						<line number="118" hits="1"/>
						<line number="124" hits="1"/>
						<line number="131" hits="1"/>
						<line number="137" hits="1"/>
						<line number="143" hits="1"/>
						<line number="152" hits="1"/>
						<line number="153" hits="0"/>
						<line number="155" hits="1"/>
						<line number="157" hits="0"/>
						<line number="165" hits="1"/>
						<line number="167" hits="0"/>
						<line number="174" hits="1"/>
						<line number="176" hits="0"/>
						<line number="190" hits="0"/>
						<line number="203" hits="0"/>
						<line number="204" hits="0"/>
						<line number="206" hits="0"/>
						<line number="208" hits="1"/>
						<line number="210" hits="0"/>
						<line number="211" hits="0"/>
						<line number="212" hits="0"/>
						<line number="214" hits="1"/>
						<line number="216" hits="0"/>
						<line number="217" hits="0"/>
						<line number="218" hits="0"/>
						<line number="220" hits="1"/>
						<line number="221" hits="1"/>
						<line number="239" hits="1"/>
					</lines>
				</class>
				<class name="notification.py" filename="models/notification.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="12" hits="0"/>
						<line number="15" hits="0"/>
						<line number="21" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="33" hits="0"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0"/>
						<line number="38" hits="0"/>
						<line number="40" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="services" line-rate="0.5532" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="services/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="monitoring_service.py" filename="services/monitoring_service.py" complexity="0" line-rate="0.5532" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="26" hits="0"/>
						<line number="28" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="45" hits="1"/>
						<line number="47" hits="1"/>
						<line number="57" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="62" hits="1"/>
						<line number="72" hits="0"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="77" hits="1"/>
						<line number="87" hits="0"/>
						<line number="88" hits="0"/>
						<line number="89" hits="0"/>
						<line number="90" hits="0"/>
						<line number="91" hits="0"/>
						<line number="93" hits="1"/>
						<line number="103" hits="0"/>
						<line number="104" hits="0"/>
						<line number="106" hits="1"/>
						<line number="116" hits="0"/>
						<line number="117" hits="0"/>
						<line number="120" hits="1"/>
						<line number="123" hits="1"/>
						<line number="124" hits="1"/>
						<line number="126" hits="1"/>
						<line number="134" hits="0"/>
						<line number="136" hits="1"/>
						<line number="145" hits="0"/>
						<line number="148" hits="1"/>
						<line number="151" hits="1"/>
						<line number="163" hits="0"/>
						<line number="171" hits="1"/>
						<line number="173" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="utils" line-rate="0.3922" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="utils/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
					</lines>
				</class>
				<class name="cookie_manager.py" filename="utils/cookie_manager.py" complexity="0" line-rate="0.3673" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="0"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="30" hits="1"/>
						<line number="41" hits="1"/>
						<line number="44" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="1"/>
						<line number="59" hits="0"/>
						<line number="61" hits="0"/>
						<line number="64" hits="0"/>
						<line number="67" hits="0"/>
						<line number="68" hits="0"/>
						<line number="70" hits="0"/>
						<line number="71" hits="0"/>
						<line number="73" hits="1"/>
						<line number="84" hits="0"/>
						<line number="85" hits="0"/>
						<line number="88" hits="0"/>
						<line number="89" hits="0"/>
						<line number="91" hits="1"/>
						<line number="101" hits="0"/>
						<line number="103" hits="0"/>
						<line number="105" hits="0"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="108" hits="0"/>
						<line number="109" hits="0"/>
						<line number="111" hits="0"/>
						<line number="113" hits="0"/>
						<line number="115" hits="1"/>
						<line number="125" hits="0"/>
						<line number="127" hits="1"/>
						<line number="137" hits="0"/>
						<line number="138" hits="0"/>
						<line number="139" hits="0"/>
						<line number="141" hits="0"/>
						<line number="142" hits="0"/>
						<line number="143" hits="0"/>
						<line number="144" hits="0"/>
						<line number="145" hits="0"/>
						<line number="146" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
