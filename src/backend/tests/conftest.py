"""
pytest配置文件

定义全局fixtures和测试配置
"""

import asyncio
from typing import AsyncGenerator, Generator

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from sqlalchemy import event, text

from app.core.config import get_settings
from app.core.database import get_db
from app.models.base import BaseModel
from app.main import create_application

# 导入所有模型以确保它们被注册到metadata中
from app.models.channel import ChannelInstance
from app.models.channel_connection_log import ChannelConnectionLog

# 测试数据库配置
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """
    创建事件循环用于整个测试会话
    """
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def test_engine():
    """
    创建测试数据库引擎
    """
    engine = create_async_engine(
        TEST_DATABASE_URL,
        echo=False,
        poolclass=StaticPool,
        connect_args={
            "check_same_thread": False,
        },
    )

    # 启用SQLite外键约束
    @event.listens_for(engine.sync_engine, "connect")
    def set_sqlite_pragma(dbapi_connection, connection_record):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.close()

    # 创建所有表
    async with engine.begin() as conn:
        await conn.run_sync(BaseModel.metadata.create_all)

    yield engine

    # 清理
    await engine.dispose()


@pytest.fixture
async def test_db_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    """
    创建测试数据库会话
    """
    async_session = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session() as session:
        yield session
        await session.rollback()

        # 清理所有测试数据
        await session.execute(text("DELETE FROM channel_connection_logs"))
        await session.execute(text("DELETE FROM channel_instances"))
        await session.commit()


@pytest.fixture
async def test_app(test_db_session):
    """
    创建测试应用实例
    """
    app = create_application()

    # 覆盖数据库依赖
    async def override_get_db():
        yield test_db_session

    app.dependency_overrides[get_db] = override_get_db

    yield app

    # 清理依赖覆盖
    app.dependency_overrides.clear()


@pytest.fixture
async def test_client(test_app) -> AsyncGenerator[AsyncClient, None]:
    """
    创建测试HTTP客户端
    """
    async with AsyncClient(app=test_app, base_url="http://test") as client:
        yield client


@pytest.fixture
def test_settings():
    """
    测试环境配置
    """
    settings = get_settings()
    settings.environment = "test"
    settings.debug = True
    settings.database_url = TEST_DATABASE_URL
    return settings


# 测试数据fixtures
@pytest.fixture
def sample_user_data():
    """
    示例用户数据
    """
    return {
        "username": "test_user",
        "email": "<EMAIL>",
        "full_name": "Test User",
        "is_active": True,
    }


@pytest.fixture
def sample_channel_data():
    """
    示例渠道数据
    """
    return {
        "platform": "wechat",
        "channel_id": "wxid_test123",
        "channel_name": "测试微信号",
        "alias": "测试账号",
        "status": "connected",
        "config": {
            "auto_reply": True,
            "ai_enabled": True,
        },
    }


# 测试标记
pytestmark = pytest.mark.asyncio
