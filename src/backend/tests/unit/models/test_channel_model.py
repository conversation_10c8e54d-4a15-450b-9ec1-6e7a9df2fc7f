"""
渠道数据模型单元测试

基于Gherkin剧本中的渠道管理场景，测试渠道数据模型的核心功能：
- 模型字段验证
- 数据类型约束
- 关系映射
- 业务规则验证
- 软删除机制
- 版本控制
- 时间戳管理
- 平台配置验证
"""

import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, Optional
from unittest.mock import MagicMock

import pytest

from app.core.exceptions import (
    BusinessLogicError,
    ValidationError,
)


class MockChannel:
    """
    模拟渠道数据模型实现
    
    基于DDD领域模型设计，包含渠道的核心属性和业务规则
    """
    
    def __init__(
        self,
        id: Optional[str] = None,
        name: str = "",
        platform: str = "",
        platform_config: Optional[Dict[str, Any]] = None,
        is_active: bool = True,
        status: str = "disconnected",
        is_deleted: bool = False,
        deleted_at: Optional[datetime] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
        version: int = 1,
        last_connected_at: Optional[datetime] = None,
        connection_count: int = 0,
        error_count: int = 0,
        last_error: Optional[str] = None,
    ):
        """初始化渠道模型"""
        self.id = id or str(uuid.uuid4())
        self.name = name
        self.platform = platform
        self.platform_config = platform_config or {}
        self.is_active = is_active
        self.status = status
        self.is_deleted = is_deleted
        self.deleted_at = deleted_at
        self.created_at = created_at or datetime.utcnow()
        self.updated_at = updated_at or datetime.utcnow()
        self.version = version
        self.last_connected_at = last_connected_at
        self.connection_count = connection_count
        self.error_count = error_count
        self.last_error = last_error
        
        # 执行验证
        self.validate()
    
    def validate(self) -> None:
        """验证模型数据"""
        # 验证必填字段
        if not self.name or not self.name.strip():
            raise ValidationError("渠道名称不能为空")
        
        if not self.platform or not self.platform.strip():
            raise ValidationError("平台类型不能为空")
        
        # 验证字段长度
        if len(self.name) > 100:
            raise ValidationError("渠道名称长度不能超过100个字符")
        
        if len(self.platform) > 50:
            raise ValidationError("平台类型长度不能超过50个字符")
        
        # 验证状态值
        valid_statuses = ["connected", "disconnected", "connecting", "error"]
        if self.status not in valid_statuses:
            raise ValidationError(f"无效的状态值: {self.status}")
        
        # 验证版本号
        if self.version < 1:
            raise ValidationError("版本号必须大于0")
        
        # 验证计数器
        if self.connection_count < 0:
            raise ValidationError("连接次数不能为负数")
        
        if self.error_count < 0:
            raise ValidationError("错误次数不能为负数")
        
        # 验证软删除逻辑
        if self.is_deleted and self.deleted_at is None:
            raise ValidationError("已删除的渠道必须有删除时间")
        
        if not self.is_deleted and self.deleted_at is not None:
            raise ValidationError("未删除的渠道不能有删除时间")
        
        # 验证平台配置
        self._validate_platform_config()
    
    def _validate_platform_config(self) -> None:
        """验证平台配置"""
        if not self.platform_config:
            return
        
        if self.platform.lower() == "xianyu":
            self._validate_xianyu_config()
    
    def _validate_xianyu_config(self) -> None:
        """验证闲鱼平台配置"""
        if "cookies_str" not in self.platform_config:
            raise ValidationError("闲鱼平台配置必须包含cookies_str字段")
        
        cookies_str = self.platform_config.get("cookies_str", "")
        if not cookies_str or not cookies_str.strip():
            raise ValidationError("闲鱼平台的cookies_str不能为空")
    
    def update_status(self, new_status: str) -> None:
        """更新渠道状态"""
        valid_statuses = ["connected", "disconnected", "connecting", "error"]
        if new_status not in valid_statuses:
            raise ValidationError(f"无效的状态值: {new_status}")
        
        old_status = self.status
        self.status = new_status
        self.updated_at = datetime.utcnow()
        self.version += 1
        
        # 更新连接相关时间戳
        if new_status == "connected" and old_status != "connected":
            self.last_connected_at = datetime.utcnow()
            self.connection_count += 1
    
    def record_error(self, error_message: str) -> None:
        """记录错误信息"""
        if not error_message or not error_message.strip():
            raise ValidationError("错误信息不能为空")
        
        self.last_error = error_message
        self.error_count += 1
        self.status = "error"
        self.updated_at = datetime.utcnow()
        self.version += 1
    
    def soft_delete(self) -> None:
        """软删除渠道"""
        if self.is_deleted:
            raise BusinessLogicError("渠道已被删除")
        
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
        self.is_active = False
        self.status = "disconnected"
        self.updated_at = datetime.utcnow()
        self.version += 1
    
    def restore(self) -> None:
        """恢复已删除的渠道"""
        if not self.is_deleted:
            raise BusinessLogicError("渠道未被删除")
        
        self.is_deleted = False
        self.deleted_at = None
        self.updated_at = datetime.utcnow()
        self.version += 1
    
    def update_config(self, new_config: Dict[str, Any]) -> None:
        """更新平台配置"""
        if not new_config:
            raise ValidationError("配置不能为空")
        
        # 备份原配置用于验证失败时回滚
        old_config = self.platform_config.copy()
        
        try:
            self.platform_config.update(new_config)
            self._validate_platform_config()
            self.updated_at = datetime.utcnow()
            self.version += 1
        except ValidationError:
            # 验证失败时回滚
            self.platform_config = old_config
            raise
    
    def activate(self) -> None:
        """激活渠道"""
        if self.is_deleted:
            raise BusinessLogicError("已删除的渠道不能激活")
        
        self.is_active = True
        self.updated_at = datetime.utcnow()
        self.version += 1
    
    def deactivate(self) -> None:
        """停用渠道"""
        self.is_active = False
        if self.status == "connected":
            self.status = "disconnected"
        self.updated_at = datetime.utcnow()
        self.version += 1
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "platform": self.platform,
            "platform_config": self.platform_config,
            "is_active": self.is_active,
            "status": self.status,
            "is_deleted": self.is_deleted,
            "deleted_at": self.deleted_at.isoformat() if self.deleted_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "version": self.version,
            "last_connected_at": self.last_connected_at.isoformat() if self.last_connected_at else None,
            "connection_count": self.connection_count,
            "error_count": self.error_count,
            "last_error": self.last_error,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MockChannel":
        """从字典创建实例"""
        # 转换时间字段
        for field in ["deleted_at", "created_at", "updated_at", "last_connected_at"]:
            if data.get(field):
                data[field] = datetime.fromisoformat(data[field])
        
        return cls(**data)
    
    def __eq__(self, other) -> bool:
        """相等性比较"""
        if not isinstance(other, MockChannel):
            return False
        return self.id == other.id
    
    def __hash__(self) -> int:
        """哈希值"""
        return hash(self.id)
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"MockChannel(id='{self.id}', name='{self.name}', platform='{self.platform}', status='{self.status}')"


class TestChannelModel:
    """渠道数据模型测试套件"""
    
    @pytest.fixture
    def valid_channel_data(self):
        """有效的渠道数据"""
        return {
            "name": "测试渠道",
            "platform": "xianyu",
            "platform_config": {"cookies_str": "valid_cookie_string"},
            "is_active": True,
            "status": "disconnected",
        }
    
    @pytest.fixture
    def valid_xianyu_config(self):
        """有效的闲鱼配置"""
        return {"cookies_str": "unb=test_user; session_id=test_session"}
    
    def test_channel_model_creation_with_valid_data(self, valid_channel_data):
        """测试使用有效数据创建渠道模型 - 对应Gherkin场景：创建渠道"""
        # Given: 有效的渠道数据
        data = valid_channel_data
        
        # When: 创建渠道模型
        channel = MockChannel(**data)
        
        # Then: 渠道应该成功创建
        assert channel.id is not None
        assert channel.name == "测试渠道"
        assert channel.platform == "xianyu"
        assert channel.is_active is True
        assert channel.status == "disconnected"
        assert channel.is_deleted is False
        assert channel.version == 1
        assert channel.created_at is not None
        assert channel.updated_at is not None
    
    def test_channel_model_creation_with_empty_name(self):
        """测试使用空名称创建渠道模型"""
        # Given: 空名称的渠道数据
        data = {
            "name": "",
            "platform": "xianyu",
            "platform_config": {"cookies_str": "valid_cookie"},
        }
        
        # When & Then: 创建渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            MockChannel(**data)
        
        assert "渠道名称不能为空" in str(exc_info.value)
    
    def test_channel_model_creation_with_empty_platform(self):
        """测试使用空平台创建渠道模型"""
        # Given: 空平台的渠道数据
        data = {
            "name": "测试渠道",
            "platform": "",
            "platform_config": {},
        }
        
        # When & Then: 创建渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            MockChannel(**data)
        
        assert "平台类型不能为空" in str(exc_info.value)

    def test_channel_model_creation_with_long_name(self):
        """测试使用过长名称创建渠道模型"""
        # Given: 过长名称的渠道数据
        long_name = "x" * 101  # 超过100字符限制
        data = {
            "name": long_name,
            "platform": "xianyu",
            "platform_config": {"cookies_str": "valid_cookie"},
        }

        # When & Then: 创建渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            MockChannel(**data)

        assert "渠道名称长度不能超过100个字符" in str(exc_info.value)

    def test_channel_model_creation_with_invalid_status(self):
        """测试使用无效状态创建渠道模型"""
        # Given: 无效状态的渠道数据
        data = {
            "name": "测试渠道",
            "platform": "xianyu",
            "platform_config": {"cookies_str": "valid_cookie"},
            "status": "invalid_status",
        }

        # When & Then: 创建渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            MockChannel(**data)

        assert "无效的状态值: invalid_status" in str(exc_info.value)

    def test_channel_model_creation_with_invalid_version(self):
        """测试使用无效版本号创建渠道模型"""
        # Given: 无效版本号的渠道数据
        data = {
            "name": "测试渠道",
            "platform": "xianyu",
            "platform_config": {"cookies_str": "valid_cookie"},
            "version": 0,
        }

        # When & Then: 创建渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            MockChannel(**data)

        assert "版本号必须大于0" in str(exc_info.value)

    def test_channel_model_creation_with_negative_counters(self):
        """测试使用负数计数器创建渠道模型"""
        # Given: 负数计数器的渠道数据
        test_cases = [
            {"connection_count": -1},
            {"error_count": -1},
        ]

        for invalid_data in test_cases:
            data = {
                "name": "测试渠道",
                "platform": "xianyu",
                "platform_config": {"cookies_str": "valid_cookie"},
                **invalid_data
            }

            # When & Then: 创建渠道应该抛出验证错误
            with pytest.raises(ValidationError):
                MockChannel(**data)

    def test_channel_model_soft_delete_validation(self):
        """测试软删除验证逻辑"""
        # Given: 软删除状态不一致的数据
        invalid_cases = [
            {"is_deleted": True, "deleted_at": None},  # 已删除但无删除时间
            {"is_deleted": False, "deleted_at": datetime.utcnow()},  # 未删除但有删除时间
        ]

        for invalid_data in invalid_cases:
            data = {
                "name": "测试渠道",
                "platform": "xianyu",
                "platform_config": {"cookies_str": "valid_cookie"},
                **invalid_data
            }

            # When & Then: 创建渠道应该抛出验证错误
            with pytest.raises(ValidationError):
                MockChannel(**data)

    def test_channel_model_xianyu_config_validation(self):
        """测试闲鱼平台配置验证"""
        # Given: 无效的闲鱼配置
        invalid_configs = [
            {},  # 缺少cookies_str
            {"cookies_str": ""},  # 空cookies_str
            {"cookies_str": "   "},  # 空白cookies_str
            {"other_field": "value"},  # 缺少cookies_str字段
        ]

        for invalid_config in invalid_configs:
            data = {
                "name": "测试渠道",
                "platform": "xianyu",
                "platform_config": invalid_config,
            }

            # When & Then: 创建渠道应该抛出验证错误
            with pytest.raises(ValidationError):
                MockChannel(**data)

    def test_channel_model_update_status_valid(self, valid_channel_data):
        """测试更新渠道状态为有效值"""
        # Given: 已创建的渠道
        channel = MockChannel(**valid_channel_data)
        original_version = channel.version
        original_updated_at = channel.updated_at

        # When: 更新状态为连接
        channel.update_status("connected")

        # Then: 状态应该更新成功
        assert channel.status == "connected"
        assert channel.version == original_version + 1
        assert channel.updated_at > original_updated_at
        assert channel.last_connected_at is not None
        assert channel.connection_count == 1

    def test_channel_model_update_status_invalid(self, valid_channel_data):
        """测试更新渠道状态为无效值"""
        # Given: 已创建的渠道
        channel = MockChannel(**valid_channel_data)

        # When & Then: 更新为无效状态应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            channel.update_status("invalid_status")

        assert "无效的状态值: invalid_status" in str(exc_info.value)

    def test_channel_model_record_error(self, valid_channel_data):
        """测试记录错误信息"""
        # Given: 已创建的渠道
        channel = MockChannel(**valid_channel_data)
        original_version = channel.version
        original_error_count = channel.error_count

        # When: 记录错误
        error_message = "连接超时"
        channel.record_error(error_message)

        # Then: 错误信息应该被记录
        assert channel.last_error == error_message
        assert channel.error_count == original_error_count + 1
        assert channel.status == "error"
        assert channel.version == original_version + 1

    def test_channel_model_record_empty_error(self, valid_channel_data):
        """测试记录空错误信息"""
        # Given: 已创建的渠道
        channel = MockChannel(**valid_channel_data)

        # When & Then: 记录空错误应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            channel.record_error("")

        assert "错误信息不能为空" in str(exc_info.value)

    def test_channel_model_soft_delete(self, valid_channel_data):
        """测试软删除渠道 - 对应Gherkin场景：删除渠道"""
        # Given: 已创建的渠道
        channel = MockChannel(**valid_channel_data)
        original_version = channel.version

        # When: 软删除渠道
        channel.soft_delete()

        # Then: 渠道应该被软删除
        assert channel.is_deleted is True
        assert channel.deleted_at is not None
        assert channel.is_active is False
        assert channel.status == "disconnected"
        assert channel.version == original_version + 1

    def test_channel_model_soft_delete_already_deleted(self, valid_channel_data):
        """测试软删除已删除的渠道"""
        # Given: 已删除的渠道
        channel = MockChannel(**valid_channel_data)
        channel.soft_delete()

        # When & Then: 再次删除应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            channel.soft_delete()

        assert "渠道已被删除" in str(exc_info.value)

    def test_channel_model_restore(self, valid_channel_data):
        """测试恢复已删除的渠道"""
        # Given: 已删除的渠道
        channel = MockChannel(**valid_channel_data)
        channel.soft_delete()
        original_version = channel.version

        # When: 恢复渠道
        channel.restore()

        # Then: 渠道应该被恢复
        assert channel.is_deleted is False
        assert channel.deleted_at is None
        assert channel.version == original_version + 1

    def test_channel_model_restore_not_deleted(self, valid_channel_data):
        """测试恢复未删除的渠道"""
        # Given: 未删除的渠道
        channel = MockChannel(**valid_channel_data)

        # When & Then: 恢复未删除的渠道应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            channel.restore()

        assert "渠道未被删除" in str(exc_info.value)

    def test_channel_model_update_config_valid(self, valid_channel_data):
        """测试更新有效的平台配置"""
        # Given: 已创建的渠道
        channel = MockChannel(**valid_channel_data)
        original_version = channel.version

        # When: 更新配置
        new_config = {"cookies_str": "updated_cookie_string", "new_field": "value"}
        channel.update_config(new_config)

        # Then: 配置应该更新成功
        assert channel.platform_config["cookies_str"] == "updated_cookie_string"
        assert channel.platform_config["new_field"] == "value"
        assert channel.version == original_version + 1

    def test_channel_model_update_config_invalid(self, valid_channel_data):
        """测试更新无效的平台配置"""
        # Given: 已创建的渠道
        channel = MockChannel(**valid_channel_data)
        original_config = channel.platform_config.copy()
        original_version = channel.version

        # When & Then: 更新为无效配置应该抛出验证错误
        invalid_config = {"cookies_str": ""}  # 空cookies_str
        with pytest.raises(ValidationError):
            channel.update_config(invalid_config)

        # 验证配置回滚
        assert channel.platform_config == original_config
        assert channel.version == original_version

    def test_channel_model_update_config_empty(self, valid_channel_data):
        """测试更新空配置"""
        # Given: 已创建的渠道
        channel = MockChannel(**valid_channel_data)

        # When & Then: 更新空配置应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            channel.update_config({})

        assert "配置不能为空" in str(exc_info.value)

    def test_channel_model_activate(self, valid_channel_data):
        """测试激活渠道"""
        # Given: 已停用的渠道
        valid_channel_data["is_active"] = False
        channel = MockChannel(**valid_channel_data)
        original_version = channel.version

        # When: 激活渠道
        channel.activate()

        # Then: 渠道应该被激活
        assert channel.is_active is True
        assert channel.version == original_version + 1

    def test_channel_model_activate_deleted_channel(self, valid_channel_data):
        """测试激活已删除的渠道"""
        # Given: 已删除的渠道
        channel = MockChannel(**valid_channel_data)
        channel.soft_delete()

        # When & Then: 激活已删除的渠道应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            channel.activate()

        assert "已删除的渠道不能激活" in str(exc_info.value)

    def test_channel_model_deactivate(self, valid_channel_data):
        """测试停用渠道"""
        # Given: 已激活且连接的渠道
        channel = MockChannel(**valid_channel_data)
        channel.update_status("connected")
        original_version = channel.version

        # When: 停用渠道
        channel.deactivate()

        # Then: 渠道应该被停用
        assert channel.is_active is False
        assert channel.status == "disconnected"
        assert channel.version == original_version + 1

    def test_channel_model_to_dict(self, valid_channel_data):
        """测试转换为字典"""
        # Given: 已创建的渠道
        channel = MockChannel(**valid_channel_data)

        # When: 转换为字典
        channel_dict = channel.to_dict()

        # Then: 字典应该包含所有字段
        expected_fields = [
            "id", "name", "platform", "platform_config", "is_active", "status",
            "is_deleted", "deleted_at", "created_at", "updated_at", "version",
            "last_connected_at", "connection_count", "error_count", "last_error"
        ]

        for field in expected_fields:
            assert field in channel_dict

        assert channel_dict["name"] == channel.name
        assert channel_dict["platform"] == channel.platform
        assert channel_dict["is_active"] == channel.is_active

    def test_channel_model_from_dict(self, valid_channel_data):
        """测试从字典创建实例"""
        # Given: 渠道字典数据
        channel = MockChannel(**valid_channel_data)
        channel_dict = channel.to_dict()

        # When: 从字典创建实例
        new_channel = MockChannel.from_dict(channel_dict)

        # Then: 新实例应该与原实例相等
        assert new_channel.id == channel.id
        assert new_channel.name == channel.name
        assert new_channel.platform == channel.platform
        assert new_channel.is_active == channel.is_active
        assert new_channel.status == channel.status

    def test_channel_model_equality(self, valid_channel_data):
        """测试渠道相等性比较"""
        # Given: 两个相同ID的渠道
        channel1 = MockChannel(**valid_channel_data)
        channel2_data = valid_channel_data.copy()
        channel2_data["id"] = channel1.id
        channel2 = MockChannel(**channel2_data)

        # When & Then: 相同ID的渠道应该相等
        assert channel1 == channel2
        assert hash(channel1) == hash(channel2)

    def test_channel_model_inequality(self, valid_channel_data):
        """测试渠道不等性比较"""
        # Given: 两个不同ID的渠道
        channel1 = MockChannel(**valid_channel_data)
        channel2 = MockChannel(**valid_channel_data)  # 不同ID

        # When & Then: 不同ID的渠道应该不相等
        assert channel1 != channel2
        assert hash(channel1) != hash(channel2)

    def test_channel_model_repr(self, valid_channel_data):
        """测试渠道字符串表示"""
        # Given: 已创建的渠道
        channel = MockChannel(**valid_channel_data)

        # When: 获取字符串表示
        repr_str = repr(channel)

        # Then: 字符串表示应该包含关键信息
        assert "MockChannel" in repr_str
        assert channel.id in repr_str
        assert channel.name in repr_str
        assert channel.platform in repr_str
        assert channel.status in repr_str


class TestChannelModelEdgeCases:
    """渠道数据模型边界情况测试"""

    def test_channel_model_with_unicode_name(self):
        """测试包含Unicode字符的渠道名称"""
        # Given: 包含Unicode字符的名称
        unicode_names = [
            "测试渠道",  # 中文
            "チャンネル",  # 日文
            "채널",  # 韩文
            "🚀渠道🎯",  # 表情符号
        ]

        for name in unicode_names:
            # When: 创建渠道
            channel = MockChannel(
                name=name,
                platform="xianyu",
                platform_config={"cookies_str": "valid_cookie"}
            )

            # Then: 渠道应该成功创建
            assert channel.name == name

    def test_channel_model_boundary_name_length(self):
        """测试渠道名称长度边界"""
        # Given: 边界长度的名称
        boundary_cases = [
            ("x", True),  # 单字符
            ("x" * 100, True),  # 恰好100字符
            ("x" * 101, False),  # 超过100字符
        ]

        for name, should_succeed in boundary_cases:
            if should_succeed:
                # When: 创建渠道应该成功
                channel = MockChannel(
                    name=name,
                    platform="xianyu",
                    platform_config={"cookies_str": "valid_cookie"}
                )
                assert channel.name == name
            else:
                # When & Then: 创建渠道应该失败
                with pytest.raises(ValidationError):
                    MockChannel(
                        name=name,
                        platform="xianyu",
                        platform_config={"cookies_str": "valid_cookie"}
                    )

    def test_channel_model_multiple_status_transitions(self):
        """测试多次状态转换"""
        # Given: 已创建的渠道
        channel = MockChannel(
            name="状态测试渠道",
            platform="xianyu",
            platform_config={"cookies_str": "valid_cookie"}
        )

        # When: 进行多次状态转换
        status_sequence = ["connecting", "connected", "error", "disconnected", "connected"]

        for new_status in status_sequence:
            channel.update_status(new_status)
            assert channel.status == new_status

        # Then: 连接次数应该正确计算
        assert channel.connection_count == 2  # 两次转换为connected

    def test_channel_model_concurrent_version_updates(self):
        """测试并发版本更新"""
        # Given: 已创建的渠道
        channel = MockChannel(
            name="并发测试渠道",
            platform="xianyu",
            platform_config={"cookies_str": "valid_cookie"}
        )
        original_version = channel.version

        # When: 进行多个操作
        operations = [
            lambda: channel.update_status("connected"),
            lambda: channel.record_error("测试错误"),
            lambda: channel.activate(),
            lambda: channel.deactivate(),
        ]

        for operation in operations:
            operation()

        # Then: 版本号应该正确递增
        assert channel.version == original_version + len(operations)
