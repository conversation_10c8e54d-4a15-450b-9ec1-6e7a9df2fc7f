"""
连接器基类单元测试

基于Gherkin剧本中的渠道连接管理场景，测试连接器基类的核心功能：
- 连接器初始化和配置
- 连接和断开操作
- 认证和令牌管理
- 状态检查和监控
- 消息监听和解析
- 错误处理和异常情况
"""

import asyncio
import uuid
from abc import ABC
from datetime import datetime
from typing import Any, Dict, Optional
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from app.core.exceptions import (
    AuthenticationError,
    BusinessLogicError,
    NotFoundError,
    ValidationError,
)


class MockStandardMessage:
    """模拟标准消息类"""
    
    def __init__(self, message_id: str, content: str, sender_id: str):
        self.message_id = message_id
        self.content = content
        self.sender_id = sender_id
        self.timestamp = datetime.utcnow()


class MockItemInfo:
    """模拟商品信息类"""
    
    def __init__(self, item_id: str, title: str, price: float):
        self.item_id = item_id
        self.title = title
        self.price = price


class TestableBaseConnector:
    """
    可测试的连接器基类实现
    
    继承自抽象基类，实现所有抽象方法用于测试
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.is_connected = False
        self.token = None
        self.last_error = None
        self.message_callback = None
        self._setup_connector()
    
    def _setup_connector(self):
        """设置连接器"""
        self.session_id = str(uuid.uuid4())
    
    async def connect(self) -> bool:
        """连接到平台"""
        try:
            if not self.config:
                raise ValidationError("连接配置不能为空")
            
            # 模拟认证过程
            if not self.authenticate():
                raise AuthenticationError("平台认证失败")
            
            # 模拟获取令牌
            if not self.get_token():
                raise AuthenticationError("获取访问令牌失败")
            
            self.is_connected = True
            return True
            
        except Exception as e:
            self.last_error = str(e)
            self.is_connected = False
            return False
    
    async def disconnect(self):
        """断开平台连接"""
        self.is_connected = False
        self.token = None
        self.message_callback = None
    
    def authenticate(self) -> bool:
        """平台认证"""
        # 模拟认证逻辑
        required_fields = ["api_key", "secret"]
        for field in required_fields:
            if field not in self.config:
                return False
        return True
    
    def get_token(self) -> Optional[str]:
        """获取访问令牌"""
        if self.authenticate():
            self.token = f"token_{uuid.uuid4()}"
            return self.token
        return None
    
    def refresh_token(self) -> bool:
        """刷新访问令牌"""
        if self.token:
            self.token = f"token_{uuid.uuid4()}"
            return True
        return False
    
    async def listen_messages(self, callback):
        """监听平台消息"""
        self.message_callback = callback
        # 模拟消息监听
        if self.is_connected:
            return True
        return False
    
    def parse_message(self, raw_message: Dict[str, Any]) -> Optional[MockStandardMessage]:
        """解析原始消息为标准格式"""
        try:
            if not raw_message or "content" not in raw_message:
                return None
            
            return MockStandardMessage(
                message_id=raw_message.get("id", str(uuid.uuid4())),
                content=raw_message["content"],
                sender_id=raw_message.get("sender_id", "unknown")
            )
        except Exception:
            return None
    
    def get_item_info(self, item_id: str) -> Optional[MockItemInfo]:
        """获取商品信息"""
        if not self.is_connected:
            return None
        
        # 模拟商品信息获取
        return MockItemInfo(
            item_id=item_id,
            title=f"商品_{item_id}",
            price=99.99
        )
    
    def send_message(self, chat_id: str, content: str) -> bool:
        """发送消息"""
        if not self.is_connected:
            return False
        
        if not chat_id or not content:
            return False
        
        return True
    
    def get_platform_name(self) -> str:
        """获取平台名称"""
        return "test_platform"
    
    def get_connection_status(self) -> Dict[str, Any]:
        """获取连接状态信息"""
        return {
            'platform': self.get_platform_name(),
            'connected': self.is_connected,
            'config_valid': self._validate_config(),
            'token_valid': bool(self.token),
            'last_error': self.last_error
        }
    
    def _validate_config(self) -> bool:
        """验证配置是否有效"""
        return bool(self.config and "api_key" in self.config)


class TestBaseConnector:
    """连接器基类测试套件"""
    
    @pytest.fixture
    def valid_config(self):
        """有效的连接器配置"""
        return {
            "api_key": "test_api_key",
            "secret": "test_secret",
            "platform": "test_platform"
        }
    
    @pytest.fixture
    def invalid_config(self):
        """无效的连接器配置"""
        return {
            "platform": "test_platform"
            # 缺少必要的api_key和secret
        }
    
    @pytest.fixture
    def connector(self, valid_config):
        """创建测试连接器实例"""
        return TestableBaseConnector(valid_config)
    
    @pytest.fixture
    def invalid_connector(self, invalid_config):
        """创建无效配置的连接器实例"""
        return TestableBaseConnector(invalid_config)
    
    def test_connector_initialization_with_valid_config(self, valid_config):
        """测试使用有效配置初始化连接器"""
        # Given: 有效的连接器配置
        # When: 创建连接器实例
        connector = TestableBaseConnector(valid_config)
        
        # Then: 连接器应该正确初始化
        assert connector.config == valid_config
        assert connector.is_connected is False
        assert connector.token is None
        assert connector.session_id is not None
        assert connector._validate_config() is True
    
    def test_connector_initialization_with_invalid_config(self, invalid_config):
        """测试使用无效配置初始化连接器"""
        # Given: 无效的连接器配置
        # When: 创建连接器实例
        connector = TestableBaseConnector(invalid_config)
        
        # Then: 连接器应该标记配置无效
        assert connector.config == invalid_config
        assert connector.is_connected is False
        assert connector._validate_config() is False
    
    @pytest.mark.asyncio
    async def test_successful_connection(self, connector):
        """测试成功连接到平台"""
        # Given: 有效配置的连接器
        assert connector.is_connected is False
        
        # When: 尝试连接到平台
        result = await connector.connect()
        
        # Then: 连接应该成功
        assert result is True
        assert connector.is_connected is True
        assert connector.token is not None
        assert connector.last_error is None
    
    @pytest.mark.asyncio
    async def test_connection_failure_with_invalid_config(self, invalid_connector):
        """测试无效配置导致的连接失败"""
        # Given: 无效配置的连接器
        assert invalid_connector.is_connected is False
        
        # When: 尝试连接到平台
        result = await invalid_connector.connect()
        
        # Then: 连接应该失败
        assert result is False
        assert invalid_connector.is_connected is False
        assert invalid_connector.token is None
        assert invalid_connector.last_error is not None
    
    @pytest.mark.asyncio
    async def test_connection_failure_with_empty_config(self):
        """测试空配置导致的连接失败"""
        # Given: 空配置的连接器
        connector = TestableBaseConnector({})
        
        # When: 尝试连接到平台
        result = await connector.connect()
        
        # Then: 连接应该失败并抛出验证错误
        assert result is False
        assert connector.is_connected is False
        assert "连接配置不能为空" in connector.last_error
    
    @pytest.mark.asyncio
    async def test_disconnect_from_connected_state(self, connector):
        """测试从已连接状态断开连接"""
        # Given: 已连接的连接器
        await connector.connect()
        assert connector.is_connected is True
        assert connector.token is not None
        
        # When: 断开连接
        await connector.disconnect()
        
        # Then: 连接器应该处于断开状态
        assert connector.is_connected is False
        assert connector.token is None
        assert connector.message_callback is None
    
    @pytest.mark.asyncio
    async def test_disconnect_from_disconnected_state(self, connector):
        """测试从已断开状态断开连接"""
        # Given: 未连接的连接器
        assert connector.is_connected is False

        # When: 断开连接
        await connector.disconnect()

        # Then: 连接器应该保持断开状态
        assert connector.is_connected is False
        assert connector.token is None

    def test_authentication_with_valid_credentials(self, connector):
        """测试使用有效凭据进行认证"""
        # Given: 有效配置的连接器
        # When: 进行平台认证
        result = connector.authenticate()

        # Then: 认证应该成功
        assert result is True

    def test_authentication_with_invalid_credentials(self, invalid_connector):
        """测试使用无效凭据进行认证"""
        # Given: 无效配置的连接器
        # When: 进行平台认证
        result = invalid_connector.authenticate()

        # Then: 认证应该失败
        assert result is False

    def test_get_token_after_successful_authentication(self, connector):
        """测试认证成功后获取令牌"""
        # Given: 已认证的连接器
        assert connector.authenticate() is True

        # When: 获取访问令牌
        token = connector.get_token()

        # Then: 应该成功获取令牌
        assert token is not None
        assert token.startswith("token_")
        assert connector.token == token

    def test_get_token_without_authentication(self, invalid_connector):
        """测试未认证时获取令牌"""
        # Given: 未认证的连接器
        assert invalid_connector.authenticate() is False

        # When: 尝试获取访问令牌
        token = invalid_connector.get_token()

        # Then: 应该无法获取令牌
        assert token is None
        assert invalid_connector.token is None

    def test_refresh_token_with_existing_token(self, connector):
        """测试刷新现有令牌"""
        # Given: 已获取令牌的连接器
        original_token = connector.get_token()
        assert original_token is not None

        # When: 刷新令牌
        result = connector.refresh_token()

        # Then: 令牌应该被刷新
        assert result is True
        assert connector.token != original_token
        assert connector.token.startswith("token_")

    def test_refresh_token_without_existing_token(self, connector):
        """测试在没有现有令牌时刷新令牌"""
        # Given: 没有令牌的连接器
        assert connector.token is None

        # When: 尝试刷新令牌
        result = connector.refresh_token()

        # Then: 刷新应该失败
        assert result is False
        assert connector.token is None

    @pytest.mark.asyncio
    async def test_listen_messages_when_connected(self, connector):
        """测试在已连接状态下监听消息"""
        # Given: 已连接的连接器
        await connector.connect()
        assert connector.is_connected is True

        # When: 设置消息监听
        callback = MagicMock()
        result = await connector.listen_messages(callback)

        # Then: 消息监听应该成功设置
        assert result is True
        assert connector.message_callback == callback

    @pytest.mark.asyncio
    async def test_listen_messages_when_disconnected(self, connector):
        """测试在未连接状态下监听消息"""
        # Given: 未连接的连接器
        assert connector.is_connected is False

        # When: 尝试设置消息监听
        callback = MagicMock()
        result = await connector.listen_messages(callback)

        # Then: 消息监听应该失败
        assert result is False
        assert connector.message_callback == callback  # 回调仍会被设置

    def test_parse_message_with_valid_data(self, connector):
        """测试解析有效的消息数据"""
        # Given: 有效的原始消息数据
        raw_message = {
            "id": "msg_123",
            "content": "测试消息内容",
            "sender_id": "user_456"
        }

        # When: 解析消息
        parsed_message = connector.parse_message(raw_message)

        # Then: 消息应该被正确解析
        assert parsed_message is not None
        assert parsed_message.message_id == "msg_123"
        assert parsed_message.content == "测试消息内容"
        assert parsed_message.sender_id == "user_456"
        assert isinstance(parsed_message.timestamp, datetime)

    def test_parse_message_with_minimal_data(self, connector):
        """测试解析最小必要数据的消息"""
        # Given: 只包含必要字段的原始消息
        raw_message = {
            "content": "最小消息内容"
        }

        # When: 解析消息
        parsed_message = connector.parse_message(raw_message)

        # Then: 消息应该被正确解析，缺失字段使用默认值
        assert parsed_message is not None
        assert parsed_message.content == "最小消息内容"
        assert parsed_message.sender_id == "unknown"
        assert parsed_message.message_id is not None  # 自动生成

    def test_parse_message_with_invalid_data(self, connector):
        """测试解析无效的消息数据"""
        # Given: 无效的原始消息数据
        invalid_messages = [
            {},  # 空消息
            {"id": "msg_123"},  # 缺少content
            None,  # None值
            {"content": ""},  # 空内容
        ]

        for raw_message in invalid_messages:
            # When: 尝试解析无效消息
            parsed_message = connector.parse_message(raw_message)

            # Then: 解析应该失败
            assert parsed_message is None

    def test_get_item_info_when_connected(self, connector):
        """测试在已连接状态下获取商品信息"""
        # Given: 已连接的连接器
        connector.is_connected = True
        item_id = "item_123"

        # When: 获取商品信息
        item_info = connector.get_item_info(item_id)

        # Then: 应该成功获取商品信息
        assert item_info is not None
        assert item_info.item_id == item_id
        assert item_info.title == f"商品_{item_id}"
        assert item_info.price == 99.99

    def test_get_item_info_when_disconnected(self, connector):
        """测试在未连接状态下获取商品信息"""
        # Given: 未连接的连接器
        assert connector.is_connected is False

        # When: 尝试获取商品信息
        item_info = connector.get_item_info("item_123")

        # Then: 应该无法获取商品信息
        assert item_info is None

    def test_send_message_when_connected(self, connector):
        """测试在已连接状态下发送消息"""
        # Given: 已连接的连接器
        connector.is_connected = True

        # When: 发送消息
        result = connector.send_message("chat_123", "测试消息")

        # Then: 消息应该发送成功
        assert result is True

    def test_send_message_when_disconnected(self, connector):
        """测试在未连接状态下发送消息"""
        # Given: 未连接的连接器
        assert connector.is_connected is False

        # When: 尝试发送消息
        result = connector.send_message("chat_123", "测试消息")

        # Then: 消息发送应该失败
        assert result is False

    def test_send_message_with_invalid_parameters(self, connector):
        """测试使用无效参数发送消息"""
        # Given: 已连接的连接器
        connector.is_connected = True

        # When & Then: 使用无效参数发送消息应该失败
        assert connector.send_message("", "测试消息") is False  # 空chat_id
        assert connector.send_message("chat_123", "") is False  # 空内容
        assert connector.send_message("", "") is False  # 都为空

    def test_get_platform_name(self, connector):
        """测试获取平台名称"""
        # When: 获取平台名称
        platform_name = connector.get_platform_name()

        # Then: 应该返回正确的平台名称
        assert platform_name == "test_platform"

    def test_get_connection_status_when_connected(self, connector):
        """测试获取已连接状态的连接状态信息"""
        # Given: 已连接且有令牌的连接器
        connector.is_connected = True
        connector.token = "test_token"

        # When: 获取连接状态
        status = connector.get_connection_status()

        # Then: 状态信息应该正确
        assert status['platform'] == "test_platform"
        assert status['connected'] is True
        assert status['config_valid'] is True
        assert status['token_valid'] is True
        assert status['last_error'] is None

    def test_get_connection_status_when_disconnected_with_error(self, connector):
        """测试获取有错误的断开状态的连接状态信息"""
        # Given: 断开连接且有错误的连接器
        connector.is_connected = False
        connector.token = None
        connector.last_error = "连接失败"

        # When: 获取连接状态
        status = connector.get_connection_status()

        # Then: 状态信息应该反映错误状态
        assert status['platform'] == "test_platform"
        assert status['connected'] is False
        assert status['config_valid'] is True
        assert status['token_valid'] is False
        assert status['last_error'] == "连接失败"

    def test_validate_config_with_valid_config(self, connector):
        """测试验证有效配置"""
        # When: 验证配置
        result = connector._validate_config()

        # Then: 配置应该有效
        assert result is True

    def test_validate_config_with_invalid_config(self, invalid_connector):
        """测试验证无效配置"""
        # When: 验证配置
        result = invalid_connector._validate_config()

        # Then: 配置应该无效
        assert result is False

    def test_validate_config_with_empty_config(self):
        """测试验证空配置"""
        # Given: 空配置的连接器
        connector = TestableBaseConnector({})

        # When: 验证配置
        result = connector._validate_config()

        # Then: 配置应该无效
        assert result is False

    def test_is_chat_message_default_implementation(self, connector):
        """测试聊天消息判断的默认实现"""
        # Given: 任意原始消息
        raw_message = {"type": "chat", "content": "测试消息"}

        # When: 判断是否为聊天消息
        result = connector.is_chat_message(raw_message)

        # Then: 默认实现应该返回True
        assert result is True

    def test_is_system_message_default_implementation(self, connector):
        """测试系统消息判断的默认实现"""
        # Given: 任意原始消息
        raw_message = {"type": "system", "content": "系统通知"}

        # When: 判断是否为系统消息
        result = connector.is_system_message(raw_message)

        # Then: 默认实现应该返回False
        assert result is False

    def test_is_typing_status_default_implementation(self, connector):
        """测试输入状态判断的默认实现"""
        # Given: 任意原始消息
        raw_message = {"type": "typing", "status": "typing"}

        # When: 判断是否为输入状态消息
        result = connector.is_typing_status(raw_message)

        # Then: 默认实现应该返回False
        assert result is False

    def test_connector_string_representation(self, connector):
        """测试连接器的字符串表示"""
        # When: 获取连接器的字符串表示
        str_repr = str(connector)

        # Then: 字符串表示应该包含平台名称和连接状态
        assert "test_platform" in str_repr.lower()
        assert "connected=false" in str_repr.lower()

        # Given: 连接后的连接器
        connector.is_connected = True
        str_repr_connected = str(connector)

        # Then: 字符串表示应该反映连接状态
        assert "connected=true" in str_repr_connected.lower()


class TestBaseConnectorEdgeCases:
    """连接器基类边界情况测试"""

    def test_connector_with_none_config(self):
        """测试使用None配置创建连接器"""
        # When: 使用None配置创建连接器
        connector = TestableBaseConnector(None)

        # Then: 连接器应该能够创建但配置无效
        assert connector.config is None
        assert connector._validate_config() is False

    @pytest.mark.asyncio
    async def test_multiple_connect_attempts(self, connector):
        """测试多次连接尝试"""
        # Given: 未连接的连接器
        assert connector.is_connected is False

        # When: 多次尝试连接
        result1 = await connector.connect()
        result2 = await connector.connect()
        result3 = await connector.connect()

        # Then: 所有连接尝试都应该成功
        assert result1 is True
        assert result2 is True
        assert result3 is True
        assert connector.is_connected is True

    @pytest.mark.asyncio
    async def test_multiple_disconnect_attempts(self, connector):
        """测试多次断开连接尝试"""
        # Given: 已连接的连接器
        await connector.connect()
        assert connector.is_connected is True

        # When: 多次断开连接
        await connector.disconnect()
        await connector.disconnect()
        await connector.disconnect()

        # Then: 连接器应该保持断开状态
        assert connector.is_connected is False
        assert connector.token is None

    def test_token_refresh_multiple_times(self, connector):
        """测试多次刷新令牌"""
        # Given: 已获取令牌的连接器
        original_token = connector.get_token()
        assert original_token is not None

        # When: 多次刷新令牌
        tokens = []
        for _ in range(3):
            result = connector.refresh_token()
            assert result is True
            tokens.append(connector.token)

        # Then: 每次刷新都应该生成新的令牌
        assert len(set(tokens)) == 3  # 所有令牌都不同
        assert original_token not in tokens  # 原始令牌不在新令牌中
