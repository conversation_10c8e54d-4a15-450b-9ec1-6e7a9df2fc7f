"""
软删除机制单元测试

基于Gherkin剧本中的渠道管理场景，测试软删除机制的核心功能：
- 软删除操作：标记删除而非物理删除
- 恢复操作：从软删除状态恢复
- 硬删除操作：物理删除数据
- 查询过滤：自动过滤已删除数据
- 业务规则验证：删除状态下的操作限制
- 数据完整性：删除时间戳和状态一致性
- 级联删除：相关数据的删除处理
- 权限控制：删除操作的权限验证
"""

import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from unittest.mock import AsyncMock, MagicMock

import pytest

from app.core.exceptions import (
    BusinessLogicError,
    NotFoundError,
    ValidationError,
    AuthorizationError,
)


class MockSoftDeleteService:
    """
    模拟软删除服务实现
    
    提供完整的软删除机制，包括软删除、恢复、硬删除等功能
    """
    
    def __init__(self):
        """初始化软删除服务"""
        self._entities = {}  # 模拟数据存储
        self._audit_logs = []  # 审计日志
    
    async def soft_delete(self, entity_id: str, deleted_by: str = "system", reason: str = None) -> bool:
        """软删除实体 - 对应Gherkin场景：删除渠道"""
        if not entity_id or not entity_id.strip():
            raise ValidationError("实体ID不能为空")
        
        if not deleted_by or not deleted_by.strip():
            raise ValidationError("删除操作者不能为空")
        
        entity = self._entities.get(entity_id)
        if not entity:
            raise NotFoundError(f"实体 '{entity_id}' 不存在")
        
        if entity["is_deleted"]:
            raise BusinessLogicError(f"实体 '{entity_id}' 已被删除")
        
        # 检查是否可以删除（业务规则）
        if entity.get("status") == "active" and entity.get("has_dependencies", False):
            raise BusinessLogicError(f"实体 '{entity_id}' 存在依赖关系，无法删除")
        
        # 执行软删除
        now = datetime.utcnow()
        entity["is_deleted"] = True
        entity["deleted_at"] = now
        entity["deleted_by"] = deleted_by
        entity["delete_reason"] = reason
        entity["updated_at"] = now
        entity["version"] += 1
        
        # 更新状态（如果是活跃状态）
        if entity.get("is_active"):
            entity["is_active"] = False
            entity["status"] = "deleted"
        
        # 记录审计日志
        self._audit_logs.append({
            "action": "soft_delete",
            "entity_id": entity_id,
            "operator": deleted_by,
            "reason": reason,
            "timestamp": now,
        })
        
        return True
    
    async def restore(self, entity_id: str, restored_by: str = "system", reason: str = None) -> bool:
        """恢复已删除的实体"""
        if not entity_id or not entity_id.strip():
            raise ValidationError("实体ID不能为空")
        
        if not restored_by or not restored_by.strip():
            raise ValidationError("恢复操作者不能为空")
        
        entity = self._entities.get(entity_id)
        if not entity:
            raise NotFoundError(f"实体 '{entity_id}' 不存在")
        
        if not entity["is_deleted"]:
            raise BusinessLogicError(f"实体 '{entity_id}' 未被删除")
        
        # 检查名称冲突（如果有名称字段）
        if "name" in entity:
            for existing_id, existing_entity in self._entities.items():
                if (existing_id != entity_id and 
                    existing_entity.get("name") == entity["name"] and 
                    not existing_entity["is_deleted"]):
                    raise BusinessLogicError(f"名称 '{entity['name']}' 已存在，无法恢复")
        
        # 执行恢复
        now = datetime.utcnow()
        entity["is_deleted"] = False
        entity["deleted_at"] = None
        entity["deleted_by"] = None
        entity["delete_reason"] = None
        entity["restored_at"] = now
        entity["restored_by"] = restored_by
        entity["restore_reason"] = reason
        entity["updated_at"] = now
        entity["version"] += 1
        
        # 恢复默认状态
        entity["status"] = "inactive"  # 恢复为非活跃状态，需要手动激活
        
        # 记录审计日志
        self._audit_logs.append({
            "action": "restore",
            "entity_id": entity_id,
            "operator": restored_by,
            "reason": reason,
            "timestamp": now,
        })
        
        return True
    
    async def hard_delete(self, entity_id: str, deleted_by: str = "system", reason: str = None) -> bool:
        """硬删除实体（物理删除）"""
        if not entity_id or not entity_id.strip():
            raise ValidationError("实体ID不能为空")
        
        if not deleted_by or not deleted_by.strip():
            raise ValidationError("删除操作者不能为空")
        
        entity = self._entities.get(entity_id)
        if not entity:
            raise NotFoundError(f"实体 '{entity_id}' 不存在")
        
        # 检查权限（只有已软删除的实体才能硬删除）
        if not entity["is_deleted"]:
            raise BusinessLogicError(f"实体 '{entity_id}' 必须先软删除才能硬删除")
        
        # 检查硬删除权限
        if entity.get("protect_from_hard_delete", False):
            raise AuthorizationError(f"实体 '{entity_id}' 受保护，无法硬删除")
        
        # 记录审计日志（在删除前）
        self._audit_logs.append({
            "action": "hard_delete",
            "entity_id": entity_id,
            "entity_data": entity.copy(),  # 保存删除前的数据
            "operator": deleted_by,
            "reason": reason,
            "timestamp": datetime.utcnow(),
        })
        
        # 执行硬删除
        del self._entities[entity_id]
        
        return True
    
    async def get_by_id(self, entity_id: str, include_deleted: bool = False) -> Optional[Dict[str, Any]]:
        """根据ID获取实体"""
        if not entity_id or not entity_id.strip():
            raise ValidationError("实体ID不能为空")
        
        entity = self._entities.get(entity_id)
        if not entity:
            return None
        
        # 检查是否包含已删除的实体
        if entity["is_deleted"] and not include_deleted:
            return None
        
        return entity.copy()
    
    async def get_all(self, include_deleted: bool = False) -> List[Dict[str, Any]]:
        """获取所有实体"""
        result = []
        for entity in self._entities.values():
            if entity["is_deleted"] and not include_deleted:
                continue
            result.append(entity.copy())
        
        return result
    
    async def get_deleted_entities(self, deleted_after: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """获取已删除的实体"""
        result = []
        for entity in self._entities.values():
            if not entity["is_deleted"]:
                continue
            
            if deleted_after and entity["deleted_at"] < deleted_after:
                continue
            
            result.append(entity.copy())
        
        return result
    
    async def count(self, include_deleted: bool = False) -> int:
        """统计实体数量"""
        count = 0
        for entity in self._entities.values():
            if entity["is_deleted"] and not include_deleted:
                continue
            count += 1
        return count
    
    async def count_deleted(self) -> int:
        """统计已删除实体数量"""
        count = 0
        for entity in self._entities.values():
            if entity["is_deleted"]:
                count += 1
        return count
    
    async def cleanup_old_deleted_entities(self, days_old: int = 30) -> int:
        """清理旧的已删除实体"""
        if days_old <= 0:
            raise ValidationError("清理天数必须大于0")
        
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        cleaned_count = 0
        
        entities_to_delete = []
        for entity_id, entity in self._entities.items():
            if (entity["is_deleted"] and 
                entity["deleted_at"] and 
                entity["deleted_at"] < cutoff_date):
                entities_to_delete.append(entity_id)
        
        for entity_id in entities_to_delete:
            await self.hard_delete(entity_id, "system", f"自动清理{days_old}天前的删除数据")
            cleaned_count += 1
        
        return cleaned_count
    
    async def get_audit_logs(self, entity_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取审计日志"""
        if entity_id:
            return [log for log in self._audit_logs if log["entity_id"] == entity_id]
        return self._audit_logs.copy()
    
    def create_entity(self, entity_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建实体（用于测试）"""
        entity_id = str(uuid.uuid4())
        now = datetime.utcnow()
        
        entity = {
            "id": entity_id,
            "is_deleted": False,
            "deleted_at": None,
            "deleted_by": None,
            "delete_reason": None,
            "restored_at": None,
            "restored_by": None,
            "restore_reason": None,
            "created_at": now,
            "updated_at": now,
            "version": 1,
            **entity_data
        }
        
        self._entities[entity_id] = entity
        return entity.copy()
    
    def clear_all(self) -> None:
        """清空所有数据（仅用于测试）"""
        self._entities.clear()
        self._audit_logs.clear()


class TestSoftDeleteService:
    """软删除服务测试套件"""
    
    @pytest.fixture
    def soft_delete_service(self):
        """创建软删除服务实例"""
        return MockSoftDeleteService()
    
    @pytest.fixture
    def sample_entity_data(self):
        """示例实体数据"""
        return {
            "name": "测试实体",
            "status": "inactive",
            "is_active": True,
            "has_dependencies": False,
        }
    
    @pytest.fixture
    def sample_entity_with_dependencies(self):
        """有依赖关系的示例实体数据"""
        return {
            "name": "有依赖的实体",
            "status": "active",
            "is_active": True,
            "has_dependencies": True,
        }
    
    @pytest.mark.asyncio
    async def test_soft_delete_valid_entity(self, soft_delete_service, sample_entity_data):
        """测试软删除有效实体 - 对应Gherkin场景：删除渠道"""
        # Given: 已创建的实体
        entity = soft_delete_service.create_entity(sample_entity_data)
        entity_id = entity["id"]
        
        # When: 软删除实体
        result = await soft_delete_service.soft_delete(entity_id, "test_user", "测试删除")
        
        # Then: 删除应该成功
        assert result is True
        
        # 验证实体状态
        deleted_entity = await soft_delete_service.get_by_id(entity_id, include_deleted=True)
        assert deleted_entity["is_deleted"] is True
        assert deleted_entity["deleted_at"] is not None
        assert deleted_entity["deleted_by"] == "test_user"
        assert deleted_entity["delete_reason"] == "测试删除"
        assert deleted_entity["is_active"] is False
        assert deleted_entity["status"] == "deleted"
        assert deleted_entity["version"] == 2
    
    @pytest.mark.asyncio
    async def test_soft_delete_nonexistent_entity(self, soft_delete_service):
        """测试软删除不存在的实体"""
        # Given: 不存在的实体ID
        nonexistent_id = str(uuid.uuid4())
        
        # When & Then: 软删除应该抛出未找到错误
        with pytest.raises(NotFoundError) as exc_info:
            await soft_delete_service.soft_delete(nonexistent_id, "test_user")
        
        assert f"实体 '{nonexistent_id}' 不存在" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_soft_delete_empty_entity_id(self, soft_delete_service):
        """测试使用空实体ID软删除"""
        # Given: 空实体ID
        empty_id = ""
        
        # When & Then: 软删除应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await soft_delete_service.soft_delete(empty_id, "test_user")
        
        assert "实体ID不能为空" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_soft_delete_empty_deleted_by(self, soft_delete_service, sample_entity_data):
        """测试使用空删除操作者软删除"""
        # Given: 已创建的实体
        entity = soft_delete_service.create_entity(sample_entity_data)
        entity_id = entity["id"]
        
        # When & Then: 软删除应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await soft_delete_service.soft_delete(entity_id, "")
        
        assert "删除操作者不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_soft_delete_already_deleted_entity(self, soft_delete_service, sample_entity_data):
        """测试软删除已删除的实体"""
        # Given: 已删除的实体
        entity = soft_delete_service.create_entity(sample_entity_data)
        entity_id = entity["id"]
        await soft_delete_service.soft_delete(entity_id, "test_user")

        # When & Then: 再次软删除应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await soft_delete_service.soft_delete(entity_id, "test_user")

        assert f"实体 '{entity_id}' 已被删除" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_soft_delete_entity_with_dependencies(self, soft_delete_service, sample_entity_with_dependencies):
        """测试软删除有依赖关系的实体"""
        # Given: 有依赖关系的活跃实体
        entity = soft_delete_service.create_entity(sample_entity_with_dependencies)
        entity_id = entity["id"]

        # When & Then: 软删除应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await soft_delete_service.soft_delete(entity_id, "test_user")

        assert f"实体 '{entity_id}' 存在依赖关系，无法删除" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_restore_deleted_entity(self, soft_delete_service, sample_entity_data):
        """测试恢复已删除的实体"""
        # Given: 已删除的实体
        entity = soft_delete_service.create_entity(sample_entity_data)
        entity_id = entity["id"]
        await soft_delete_service.soft_delete(entity_id, "test_user", "测试删除")

        # When: 恢复实体
        result = await soft_delete_service.restore(entity_id, "test_user", "测试恢复")

        # Then: 恢复应该成功
        assert result is True

        # 验证实体状态
        restored_entity = await soft_delete_service.get_by_id(entity_id)
        assert restored_entity is not None
        assert restored_entity["is_deleted"] is False
        assert restored_entity["deleted_at"] is None
        assert restored_entity["deleted_by"] is None
        assert restored_entity["delete_reason"] is None
        assert restored_entity["restored_at"] is not None
        assert restored_entity["restored_by"] == "test_user"
        assert restored_entity["restore_reason"] == "测试恢复"
        assert restored_entity["status"] == "inactive"  # 恢复为非活跃状态
        assert restored_entity["version"] == 3  # 删除+恢复=版本3

    @pytest.mark.asyncio
    async def test_restore_nonexistent_entity(self, soft_delete_service):
        """测试恢复不存在的实体"""
        # Given: 不存在的实体ID
        nonexistent_id = str(uuid.uuid4())

        # When & Then: 恢复应该抛出未找到错误
        with pytest.raises(NotFoundError) as exc_info:
            await soft_delete_service.restore(nonexistent_id, "test_user")

        assert f"实体 '{nonexistent_id}' 不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_restore_not_deleted_entity(self, soft_delete_service, sample_entity_data):
        """测试恢复未删除的实体"""
        # Given: 未删除的实体
        entity = soft_delete_service.create_entity(sample_entity_data)
        entity_id = entity["id"]

        # When & Then: 恢复应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await soft_delete_service.restore(entity_id, "test_user")

        assert f"实体 '{entity_id}' 未被删除" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_restore_with_name_conflict(self, soft_delete_service):
        """测试恢复时名称冲突"""
        # Given: 已删除的实体和同名的新实体
        original_data = {"name": "冲突名称", "status": "inactive", "is_active": False}
        new_data = {"name": "冲突名称", "status": "inactive", "is_active": False}

        # 创建并删除原实体
        original_entity = soft_delete_service.create_entity(original_data)
        await soft_delete_service.soft_delete(original_entity["id"], "test_user")

        # 创建同名新实体
        soft_delete_service.create_entity(new_data)

        # When & Then: 恢复原实体应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await soft_delete_service.restore(original_entity["id"], "test_user")

        assert "名称 '冲突名称' 已存在，无法恢复" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_restore_empty_entity_id(self, soft_delete_service):
        """测试使用空实体ID恢复"""
        # Given: 空实体ID
        empty_id = ""

        # When & Then: 恢复应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await soft_delete_service.restore(empty_id, "test_user")

        assert "实体ID不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_restore_empty_restored_by(self, soft_delete_service, sample_entity_data):
        """测试使用空恢复操作者恢复"""
        # Given: 已删除的实体
        entity = soft_delete_service.create_entity(sample_entity_data)
        entity_id = entity["id"]
        await soft_delete_service.soft_delete(entity_id, "test_user")

        # When & Then: 恢复应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await soft_delete_service.restore(entity_id, "")

        assert "恢复操作者不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_hard_delete_soft_deleted_entity(self, soft_delete_service, sample_entity_data):
        """测试硬删除已软删除的实体"""
        # Given: 已软删除的实体
        entity = soft_delete_service.create_entity(sample_entity_data)
        entity_id = entity["id"]
        await soft_delete_service.soft_delete(entity_id, "test_user", "测试删除")

        # When: 硬删除实体
        result = await soft_delete_service.hard_delete(entity_id, "admin_user", "彻底清理")

        # Then: 硬删除应该成功
        assert result is True

        # 验证实体已被物理删除
        deleted_entity = await soft_delete_service.get_by_id(entity_id, include_deleted=True)
        assert deleted_entity is None

    @pytest.mark.asyncio
    async def test_hard_delete_not_soft_deleted_entity(self, soft_delete_service, sample_entity_data):
        """测试硬删除未软删除的实体"""
        # Given: 未软删除的实体
        entity = soft_delete_service.create_entity(sample_entity_data)
        entity_id = entity["id"]

        # When & Then: 硬删除应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await soft_delete_service.hard_delete(entity_id, "admin_user")

        assert f"实体 '{entity_id}' 必须先软删除才能硬删除" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_hard_delete_protected_entity(self, soft_delete_service):
        """测试硬删除受保护的实体"""
        # Given: 受保护的已软删除实体
        protected_data = {
            "name": "受保护实体",
            "status": "inactive",
            "is_active": False,
            "protect_from_hard_delete": True,
        }
        entity = soft_delete_service.create_entity(protected_data)
        entity_id = entity["id"]
        await soft_delete_service.soft_delete(entity_id, "test_user")

        # When & Then: 硬删除应该抛出权限拒绝错误
        with pytest.raises(AuthorizationError) as exc_info:
            await soft_delete_service.hard_delete(entity_id, "admin_user")

        assert f"实体 '{entity_id}' 受保护，无法硬删除" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_hard_delete_nonexistent_entity(self, soft_delete_service):
        """测试硬删除不存在的实体"""
        # Given: 不存在的实体ID
        nonexistent_id = str(uuid.uuid4())

        # When & Then: 硬删除应该抛出未找到错误
        with pytest.raises(NotFoundError) as exc_info:
            await soft_delete_service.hard_delete(nonexistent_id, "admin_user")

        assert f"实体 '{nonexistent_id}' 不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_by_id_exclude_deleted(self, soft_delete_service, sample_entity_data):
        """测试获取实体（排除已删除）"""
        # Given: 已删除的实体
        entity = soft_delete_service.create_entity(sample_entity_data)
        entity_id = entity["id"]
        await soft_delete_service.soft_delete(entity_id, "test_user")

        # When: 获取实体（不包括已删除）
        retrieved_entity = await soft_delete_service.get_by_id(entity_id, include_deleted=False)

        # Then: 应该返回None
        assert retrieved_entity is None

    @pytest.mark.asyncio
    async def test_get_by_id_include_deleted(self, soft_delete_service, sample_entity_data):
        """测试获取实体（包括已删除）"""
        # Given: 已删除的实体
        entity = soft_delete_service.create_entity(sample_entity_data)
        entity_id = entity["id"]
        await soft_delete_service.soft_delete(entity_id, "test_user")

        # When: 获取实体（包括已删除）
        retrieved_entity = await soft_delete_service.get_by_id(entity_id, include_deleted=True)

        # Then: 应该成功获取实体
        assert retrieved_entity is not None
        assert retrieved_entity["id"] == entity_id
        assert retrieved_entity["is_deleted"] is True

    @pytest.mark.asyncio
    async def test_get_all_exclude_deleted(self, soft_delete_service):
        """测试获取所有实体（排除已删除）"""
        # Given: 包含已删除实体的多个实体
        entity1_data = {"name": "实体1", "status": "inactive", "is_active": False}
        entity2_data = {"name": "实体2", "status": "inactive", "is_active": False}

        entity1 = soft_delete_service.create_entity(entity1_data)
        entity2 = soft_delete_service.create_entity(entity2_data)

        # 删除一个实体
        await soft_delete_service.soft_delete(entity1["id"], "test_user")

        # When: 获取所有实体（不包括已删除）
        all_entities = await soft_delete_service.get_all(include_deleted=False)

        # Then: 应该只返回未删除的实体
        assert len(all_entities) == 1
        assert all_entities[0]["name"] == "实体2"

    @pytest.mark.asyncio
    async def test_get_all_include_deleted(self, soft_delete_service):
        """测试获取所有实体（包括已删除）"""
        # Given: 包含已删除实体的多个实体
        entity1_data = {"name": "实体1", "status": "inactive", "is_active": False}
        entity2_data = {"name": "实体2", "status": "inactive", "is_active": False}

        entity1 = soft_delete_service.create_entity(entity1_data)
        entity2 = soft_delete_service.create_entity(entity2_data)

        # 删除一个实体
        await soft_delete_service.soft_delete(entity1["id"], "test_user")

        # When: 获取所有实体（包括已删除）
        all_entities = await soft_delete_service.get_all(include_deleted=True)

        # Then: 应该返回所有实体
        assert len(all_entities) == 2
        entity_names = [e["name"] for e in all_entities]
        assert "实体1" in entity_names
        assert "实体2" in entity_names

    @pytest.mark.asyncio
    async def test_get_deleted_entities(self, soft_delete_service):
        """测试获取已删除的实体"""
        # Given: 包含已删除实体的多个实体
        entity1_data = {"name": "实体1", "status": "inactive", "is_active": False}
        entity2_data = {"name": "实体2", "status": "inactive", "is_active": False}
        entity3_data = {"name": "实体3", "status": "inactive", "is_active": False}

        entity1 = soft_delete_service.create_entity(entity1_data)
        entity2 = soft_delete_service.create_entity(entity2_data)
        entity3 = soft_delete_service.create_entity(entity3_data)

        # 删除两个实体
        await soft_delete_service.soft_delete(entity1["id"], "test_user")
        await soft_delete_service.soft_delete(entity2["id"], "test_user")

        # When: 获取已删除的实体
        deleted_entities = await soft_delete_service.get_deleted_entities()

        # Then: 应该返回已删除的实体
        assert len(deleted_entities) == 2
        deleted_names = [e["name"] for e in deleted_entities]
        assert "实体1" in deleted_names
        assert "实体2" in deleted_names
        assert "实体3" not in deleted_names

    @pytest.mark.asyncio
    async def test_get_deleted_entities_with_date_filter(self, soft_delete_service):
        """测试获取已删除的实体（带日期过滤）"""
        # Given: 在不同时间删除的实体
        entity1_data = {"name": "实体1", "status": "inactive", "is_active": False}
        entity2_data = {"name": "实体2", "status": "inactive", "is_active": False}

        entity1 = soft_delete_service.create_entity(entity1_data)
        entity2 = soft_delete_service.create_entity(entity2_data)

        # 删除第一个实体
        await soft_delete_service.soft_delete(entity1["id"], "test_user")

        # 记录时间点
        filter_time = datetime.utcnow()

        # 稍后删除第二个实体
        await soft_delete_service.soft_delete(entity2["id"], "test_user")

        # When: 获取指定时间后删除的实体
        recent_deleted = await soft_delete_service.get_deleted_entities(deleted_after=filter_time)

        # Then: 应该只返回最近删除的实体
        assert len(recent_deleted) == 1
        assert recent_deleted[0]["name"] == "实体2"

    @pytest.mark.asyncio
    async def test_count_entities(self, soft_delete_service):
        """测试统计实体数量"""
        # Given: 包含已删除实体的多个实体
        entity1_data = {"name": "实体1", "status": "inactive", "is_active": False}
        entity2_data = {"name": "实体2", "status": "inactive", "is_active": False}
        entity3_data = {"name": "实体3", "status": "inactive", "is_active": False}

        entity1 = soft_delete_service.create_entity(entity1_data)
        entity2 = soft_delete_service.create_entity(entity2_data)
        entity3 = soft_delete_service.create_entity(entity3_data)

        # 删除一个实体
        await soft_delete_service.soft_delete(entity1["id"], "test_user")

        # When: 统计实体数量
        total_count = await soft_delete_service.count(include_deleted=True)
        active_count = await soft_delete_service.count(include_deleted=False)
        deleted_count = await soft_delete_service.count_deleted()

        # Then: 数量应该正确
        assert total_count == 3
        assert active_count == 2
        assert deleted_count == 1

    @pytest.mark.asyncio
    async def test_cleanup_old_deleted_entities(self, soft_delete_service):
        """测试清理旧的已删除实体"""
        # Given: 不同时间删除的实体
        old_entity_data = {"name": "旧实体", "status": "inactive", "is_active": False}
        recent_entity_data = {"name": "新实体", "status": "inactive", "is_active": False}

        old_entity = soft_delete_service.create_entity(old_entity_data)
        recent_entity = soft_delete_service.create_entity(recent_entity_data)

        # 删除实体并手动设置删除时间
        await soft_delete_service.soft_delete(old_entity["id"], "test_user")
        await soft_delete_service.soft_delete(recent_entity["id"], "test_user")

        # 手动设置旧实体的删除时间为35天前
        old_deleted_time = datetime.utcnow() - timedelta(days=35)
        soft_delete_service._entities[old_entity["id"]]["deleted_at"] = old_deleted_time

        # When: 清理30天前的已删除实体
        cleaned_count = await soft_delete_service.cleanup_old_deleted_entities(days_old=30)

        # Then: 应该清理旧实体
        assert cleaned_count == 1

        # 验证旧实体已被硬删除
        old_entity_check = await soft_delete_service.get_by_id(old_entity["id"], include_deleted=True)
        assert old_entity_check is None

        # 验证新实体仍然存在
        recent_entity_check = await soft_delete_service.get_by_id(recent_entity["id"], include_deleted=True)
        assert recent_entity_check is not None

    @pytest.mark.asyncio
    async def test_cleanup_invalid_days(self, soft_delete_service):
        """测试使用无效天数清理"""
        # Given: 无效的天数
        invalid_days = [0, -1, -10]

        for days in invalid_days:
            # When & Then: 清理应该抛出验证错误
            with pytest.raises(ValidationError) as exc_info:
                await soft_delete_service.cleanup_old_deleted_entities(days_old=days)

            assert "清理天数必须大于0" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_audit_logs(self, soft_delete_service, sample_entity_data):
        """测试审计日志功能"""
        # Given: 已创建的实体
        entity = soft_delete_service.create_entity(sample_entity_data)
        entity_id = entity["id"]

        # When: 进行一系列操作
        await soft_delete_service.soft_delete(entity_id, "user1", "测试删除")
        await soft_delete_service.restore(entity_id, "user2", "测试恢复")
        await soft_delete_service.soft_delete(entity_id, "user3", "再次删除")
        await soft_delete_service.hard_delete(entity_id, "admin", "彻底清理")

        # Then: 审计日志应该记录所有操作
        all_logs = await soft_delete_service.get_audit_logs()
        entity_logs = await soft_delete_service.get_audit_logs(entity_id)

        assert len(entity_logs) == 4

        # 验证操作顺序和内容
        assert entity_logs[0]["action"] == "soft_delete"
        assert entity_logs[0]["operator"] == "user1"
        assert entity_logs[0]["reason"] == "测试删除"

        assert entity_logs[1]["action"] == "restore"
        assert entity_logs[1]["operator"] == "user2"
        assert entity_logs[1]["reason"] == "测试恢复"

        assert entity_logs[2]["action"] == "soft_delete"
        assert entity_logs[2]["operator"] == "user3"
        assert entity_logs[2]["reason"] == "再次删除"

        assert entity_logs[3]["action"] == "hard_delete"
        assert entity_logs[3]["operator"] == "admin"
        assert entity_logs[3]["reason"] == "彻底清理"
        assert "entity_data" in entity_logs[3]  # 硬删除前保存数据


class TestSoftDeleteEdgeCases:
    """软删除机制边界情况测试"""

    @pytest.fixture
    def soft_delete_service_edge_cases(self):
        """边界情况测试的软删除服务实例"""
        return MockSoftDeleteService()

    @pytest.mark.asyncio
    async def test_concurrent_soft_delete_operations(self, soft_delete_service_edge_cases):
        """测试并发软删除操作"""
        import asyncio

        # Given: 多个实体
        entities = []
        for i in range(5):
            entity_data = {"name": f"并发实体_{i}", "status": "inactive", "is_active": False}
            entity = soft_delete_service_edge_cases.create_entity(entity_data)
            entities.append(entity)

        # When: 并发删除所有实体
        async def delete_entity(entity):
            return await soft_delete_service_edge_cases.soft_delete(entity["id"], "concurrent_user")

        tasks = [delete_entity(entity) for entity in entities]
        results = await asyncio.gather(*tasks)

        # Then: 所有删除操作都应该成功
        assert all(results)

        # 验证所有实体都被删除
        deleted_count = await soft_delete_service_edge_cases.count_deleted()
        assert deleted_count == 5

    @pytest.mark.asyncio
    async def test_soft_delete_restore_cycle(self, soft_delete_service_edge_cases):
        """测试软删除-恢复循环"""
        # Given: 已创建的实体
        entity_data = {"name": "循环测试实体", "status": "inactive", "is_active": False}
        entity = soft_delete_service_edge_cases.create_entity(entity_data)
        entity_id = entity["id"]

        # When: 进行多次删除-恢复循环
        for i in range(3):
            await soft_delete_service_edge_cases.soft_delete(entity_id, f"user_{i}", f"删除_{i}")
            await soft_delete_service_edge_cases.restore(entity_id, f"user_{i}", f"恢复_{i}")

        # Then: 实体应该处于恢复状态
        final_entity = await soft_delete_service_edge_cases.get_by_id(entity_id)
        assert final_entity is not None
        assert final_entity["is_deleted"] is False
        assert final_entity["version"] == 7  # 1 + 3*2 = 7 (创建 + 3次删除恢复)

        # 验证审计日志
        logs = await soft_delete_service_edge_cases.get_audit_logs(entity_id)
        assert len(logs) == 6  # 3次删除 + 3次恢复

    @pytest.mark.asyncio
    async def test_soft_delete_with_unicode_data(self, soft_delete_service_edge_cases):
        """测试软删除处理Unicode数据"""
        # Given: 包含Unicode字符的实体
        unicode_data = {
            "name": "测试实体🚀",
            "description": "包含中文、日文チャンネル、韩文채널的描述",
            "status": "inactive",
            "is_active": False,
        }
        entity = soft_delete_service_edge_cases.create_entity(unicode_data)
        entity_id = entity["id"]

        # When: 软删除实体
        result = await soft_delete_service_edge_cases.soft_delete(
            entity_id,
            "测试用户🎯",
            "Unicode删除原因：测试中文字符"
        )

        # Then: 删除应该成功
        assert result is True

        # 验证Unicode数据保持完整
        deleted_entity = await soft_delete_service_edge_cases.get_by_id(entity_id, include_deleted=True)
        assert deleted_entity["name"] == "测试实体🚀"
        assert deleted_entity["deleted_by"] == "测试用户🎯"
        assert deleted_entity["delete_reason"] == "Unicode删除原因：测试中文字符"

    @pytest.mark.asyncio
    async def test_soft_delete_performance_with_large_dataset(self, soft_delete_service_edge_cases):
        """测试软删除处理大量数据的性能"""
        # Given: 大量实体
        entities_count = 100
        entities = []

        for i in range(entities_count):
            entity_data = {"name": f"性能测试实体_{i}", "status": "inactive", "is_active": False}
            entity = soft_delete_service_edge_cases.create_entity(entity_data)
            entities.append(entity)

        # When: 删除所有实体
        start_time = datetime.utcnow()
        for entity in entities:
            await soft_delete_service_edge_cases.soft_delete(entity["id"], "performance_user")
        end_time = datetime.utcnow()

        # Then: 操作应该在合理时间内完成
        duration = (end_time - start_time).total_seconds()
        assert duration < 5.0  # 应该在5秒内完成

        # 验证所有实体都被删除
        deleted_count = await soft_delete_service_edge_cases.count_deleted()
        assert deleted_count == entities_count
