"""
渠道服务单元测试

基于Gherkin剧本中的渠道管理场景，测试渠道服务的核心功能：
- 渠道创建和配置
- 渠道状态管理
- 渠道连接器集成
- 业务逻辑验证
- 错误处理和异常情况
- 渠道查询和过滤
"""

import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from app.core.exceptions import (
    AuthenticationError,
    BusinessLogicError,
    NotFoundError,
    ValidationError,
)


class MockChannel:
    """模拟渠道数据模型"""
    
    def __init__(self, **kwargs):
        self.id = kwargs.get('id', str(uuid.uuid4()))
        self.name = kwargs.get('name', 'test_channel')
        self.platform = kwargs.get('platform', 'xianyu')
        self.config = kwargs.get('config', {})
        self.status = kwargs.get('status', 'inactive')
        self.is_active = kwargs.get('is_active', True)
        self.is_deleted = kwargs.get('is_deleted', False)
        self.created_at = kwargs.get('created_at', datetime.utcnow())
        self.updated_at = kwargs.get('updated_at', datetime.utcnow())
        self.deleted_at = kwargs.get('deleted_at', None)
        self.last_connected_at = kwargs.get('last_connected_at', None)
        self.connection_error = kwargs.get('connection_error', None)
        self.version = kwargs.get('version', 1)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'platform': self.platform,
            'config': self.config,
            'status': self.status,
            'is_active': self.is_active,
            'is_deleted': self.is_deleted,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'deleted_at': self.deleted_at.isoformat() if self.deleted_at else None,
            'last_connected_at': self.last_connected_at.isoformat() if self.last_connected_at else None,
            'connection_error': self.connection_error,
            'version': self.version
        }


class MockChannelRepository:
    """模拟渠道仓储"""
    
    def __init__(self):
        self.channels = {}
        self.next_id = 1
    
    async def create(self, channel_data: Dict[str, Any]) -> MockChannel:
        """创建渠道"""
        channel = MockChannel(**channel_data)
        self.channels[channel.id] = channel
        return channel
    
    async def get_by_id(self, channel_id: str) -> Optional[MockChannel]:
        """根据ID获取渠道"""
        return self.channels.get(channel_id)
    
    async def get_by_name(self, name: str) -> Optional[MockChannel]:
        """根据名称获取渠道"""
        for channel in self.channels.values():
            if channel.name == name and not channel.is_deleted:
                return channel
        return None
    
    async def get_all(self, include_deleted: bool = False) -> List[MockChannel]:
        """获取所有渠道"""
        if include_deleted:
            return list(self.channels.values())
        return [ch for ch in self.channels.values() if not ch.is_deleted]
    
    async def get_by_platform(self, platform: str) -> List[MockChannel]:
        """根据平台获取渠道"""
        return [ch for ch in self.channels.values() 
                if ch.platform == platform and not ch.is_deleted]
    
    async def get_active_channels(self) -> List[MockChannel]:
        """获取活跃渠道"""
        return [ch for ch in self.channels.values() 
                if ch.is_active and not ch.is_deleted]
    
    async def update(self, channel_id: str, update_data: Dict[str, Any]) -> Optional[MockChannel]:
        """更新渠道"""
        channel = self.channels.get(channel_id)
        if not channel:
            return None
        
        for key, value in update_data.items():
            if hasattr(channel, key):
                setattr(channel, key, value)
        
        channel.updated_at = datetime.utcnow()
        channel.version += 1
        return channel
    
    async def soft_delete(self, channel_id: str) -> bool:
        """软删除渠道"""
        channel = self.channels.get(channel_id)
        if not channel:
            return False
        
        channel.is_deleted = True
        channel.deleted_at = datetime.utcnow()
        channel.updated_at = datetime.utcnow()
        return True
    
    async def restore(self, channel_id: str) -> bool:
        """恢复已删除的渠道"""
        channel = self.channels.get(channel_id)
        if not channel:
            return False
        
        channel.is_deleted = False
        channel.deleted_at = None
        channel.updated_at = datetime.utcnow()
        return True
    
    async def hard_delete(self, channel_id: str) -> bool:
        """硬删除渠道"""
        if channel_id in self.channels:
            del self.channels[channel_id]
            return True
        return False


class MockConnectorFactory:
    """模拟连接器工厂"""
    
    def __init__(self):
        self.connectors = {}
    
    def create_connector(self, platform: str, config: Dict[str, Any]):
        """创建连接器"""
        connector = MagicMock()
        connector.platform = platform
        connector.config = config
        connector.is_connected = False
        connector.connect = AsyncMock(return_value=True)
        connector.disconnect = AsyncMock()
        connector.authenticate = MagicMock(return_value=True)
        connector.get_connection_status = MagicMock(return_value={
            'platform': platform,
            'connected': False,
            'config_valid': True,
            'token_valid': False,
            'last_error': None
        })
        
        self.connectors[platform] = connector
        return connector
    
    def get_connector(self, platform: str):
        """获取连接器"""
        return self.connectors.get(platform)


class MockChannelService:
    """模拟渠道服务实现"""
    
    def __init__(self, repository: MockChannelRepository, connector_factory: MockConnectorFactory):
        self.repository = repository
        self.connector_factory = connector_factory
        self._active_connectors = {}
    
    async def create_channel(self, name: str, platform: str, config: Dict[str, Any]) -> MockChannel:
        """创建新渠道 - 对应Gherkin场景：创建新渠道"""
        # 验证输入参数
        if not name or not name.strip():
            raise ValidationError("渠道名称不能为空")
        
        if not platform or not platform.strip():
            raise ValidationError("平台类型不能为空")
        
        # 检查渠道名称是否已存在
        existing_channel = await self.repository.get_by_name(name.strip())
        if existing_channel:
            raise BusinessLogicError(f"渠道名称 '{name}' 已存在")
        
        # 验证平台配置
        if not self._validate_platform_config(platform, config):
            raise ValidationError(f"平台 '{platform}' 的配置无效")
        
        # 创建渠道数据
        channel_data = {
            'name': name.strip(),
            'platform': platform.lower(),
            'config': config,
            'status': 'inactive',
            'is_active': True,
            'created_at': datetime.utcnow()
        }
        
        # 保存到数据库
        channel = await self.repository.create(channel_data)
        
        return channel
    
    async def get_channel_by_id(self, channel_id: str) -> Optional[MockChannel]:
        """根据ID获取渠道"""
        if not channel_id:
            raise ValidationError("渠道ID不能为空")
        
        channel = await self.repository.get_by_id(channel_id)
        if not channel or channel.is_deleted:
            return None
        
        return channel
    
    async def get_channel_by_name(self, name: str) -> Optional[MockChannel]:
        """根据名称获取渠道"""
        if not name:
            raise ValidationError("渠道名称不能为空")
        
        return await self.repository.get_by_name(name.strip())
    
    async def get_all_channels(self, include_deleted: bool = False) -> List[MockChannel]:
        """获取所有渠道"""
        return await self.repository.get_all(include_deleted)
    
    async def get_channels_by_platform(self, platform: str) -> List[MockChannel]:
        """根据平台获取渠道"""
        if not platform:
            raise ValidationError("平台类型不能为空")
        
        return await self.repository.get_by_platform(platform.lower())
    
    async def get_active_channels(self) -> List[MockChannel]:
        """获取活跃渠道"""
        return await self.repository.get_active_channels()
    
    async def update_channel(self, channel_id: str, update_data: Dict[str, Any]) -> Optional[MockChannel]:
        """更新渠道信息 - 对应Gherkin场景：修改渠道信息"""
        if not channel_id:
            raise ValidationError("渠道ID不能为空")
        
        # 检查渠道是否存在
        channel = await self.repository.get_by_id(channel_id)
        if not channel or channel.is_deleted:
            raise NotFoundError(f"渠道 '{channel_id}' 不存在")
        
        # 验证更新数据
        validated_data = self._validate_update_data(update_data)
        
        # 如果更新名称，检查是否重复
        if 'name' in validated_data:
            existing_channel = await self.repository.get_by_name(validated_data['name'])
            if existing_channel and existing_channel.id != channel_id:
                raise BusinessLogicError(f"渠道名称 '{validated_data['name']}' 已存在")
        
        # 执行更新
        updated_channel = await self.repository.update(channel_id, validated_data)
        
        return updated_channel
    
    async def delete_channel(self, channel_id: str) -> bool:
        """删除渠道 - 对应Gherkin场景：删除渠道"""
        if not channel_id:
            raise ValidationError("渠道ID不能为空")
        
        # 检查渠道是否存在
        channel = await self.repository.get_by_id(channel_id)
        if not channel:
            raise NotFoundError(f"渠道 '{channel_id}' 不存在")
        
        if channel.is_deleted:
            raise BusinessLogicError(f"渠道 '{channel_id}' 已被删除")
        
        # 如果渠道正在连接，先断开连接
        if channel.status == 'connected':
            await self.disconnect_channel(channel_id)
        
        # 执行软删除
        return await self.repository.soft_delete(channel_id)
    
    async def restore_channel(self, channel_id: str) -> bool:
        """恢复已删除的渠道"""
        if not channel_id:
            raise ValidationError("渠道ID不能为空")
        
        channel = await self.repository.get_by_id(channel_id)
        if not channel:
            raise NotFoundError(f"渠道 '{channel_id}' 不存在")
        
        if not channel.is_deleted:
            raise BusinessLogicError(f"渠道 '{channel_id}' 未被删除")
        
        return await self.repository.restore(channel_id)
    
    def _validate_platform_config(self, platform: str, config: Dict[str, Any]) -> bool:
        """验证平台配置"""
        if platform.lower() == 'xianyu':
            return 'cookies_str' in config and config['cookies_str']
        
        # 其他平台的验证逻辑
        return True
    
    def _validate_update_data(self, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证更新数据"""
        allowed_fields = {'name', 'config', 'is_active', 'status'}
        validated_data = {}
        
        for key, value in update_data.items():
            if key in allowed_fields:
                if key == 'name' and (not value or not value.strip()):
                    raise ValidationError("渠道名称不能为空")
                validated_data[key] = value
        
        return validated_data


class TestChannelService:
    """渠道服务测试套件"""

    @pytest.fixture
    def mock_repository(self):
        """模拟渠道仓储"""
        return MockChannelRepository()

    @pytest.fixture
    def mock_connector_factory(self):
        """模拟连接器工厂"""
        return MockConnectorFactory()

    @pytest.fixture
    def channel_service(self, mock_repository, mock_connector_factory):
        """渠道服务实例"""
        return MockChannelService(mock_repository, mock_connector_factory)

    @pytest.fixture
    def valid_xianyu_config(self):
        """有效的闲鱼渠道配置"""
        return {
            "cookies_str": "unb=test_user_123; session_id=test_session_456; _tb_token_=abc123"
        }

    @pytest.fixture
    def invalid_xianyu_config(self):
        """无效的闲鱼渠道配置"""
        return {
            "cookies_str": ""
        }

    @pytest.mark.asyncio
    async def test_create_channel_with_valid_data(self, channel_service, valid_xianyu_config):
        """测试使用有效数据创建渠道 - 对应Gherkin场景：创建新渠道"""
        # Given: 有效的渠道创建数据
        name = "测试闲鱼渠道"
        platform = "xianyu"
        config = valid_xianyu_config

        # When: 创建渠道
        channel = await channel_service.create_channel(name, platform, config)

        # Then: 渠道应该成功创建
        assert channel is not None
        assert channel.name == name
        assert channel.platform == platform.lower()
        assert channel.config == config
        assert channel.status == 'inactive'
        assert channel.is_active is True
        assert channel.is_deleted is False
        assert channel.created_at is not None

    @pytest.mark.asyncio
    async def test_create_channel_with_empty_name(self, channel_service, valid_xianyu_config):
        """测试使用空名称创建渠道"""
        # Given: 空名称
        name = ""
        platform = "xianyu"
        config = valid_xianyu_config

        # When & Then: 创建渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_service.create_channel(name, platform, config)

        assert "渠道名称不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_channel_with_whitespace_name(self, channel_service, valid_xianyu_config):
        """测试使用只有空格的名称创建渠道"""
        # Given: 只有空格的名称
        name = "   "
        platform = "xianyu"
        config = valid_xianyu_config

        # When & Then: 创建渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_service.create_channel(name, platform, config)

        assert "渠道名称不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_channel_with_empty_platform(self, channel_service, valid_xianyu_config):
        """测试使用空平台类型创建渠道"""
        # Given: 空平台类型
        name = "测试渠道"
        platform = ""
        config = valid_xianyu_config

        # When & Then: 创建渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_service.create_channel(name, platform, config)

        assert "平台类型不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_channel_with_invalid_config(self, channel_service, invalid_xianyu_config):
        """测试使用无效配置创建渠道"""
        # Given: 无效配置
        name = "测试渠道"
        platform = "xianyu"
        config = invalid_xianyu_config

        # When & Then: 创建渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_service.create_channel(name, platform, config)

        assert "配置无效" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_channel_with_duplicate_name(self, channel_service, valid_xianyu_config):
        """测试创建重名渠道 - 对应Gherkin场景：渠道名称重复检查"""
        # Given: 已存在的渠道名称
        name = "重复渠道名称"
        platform = "xianyu"
        config = valid_xianyu_config

        # 先创建一个渠道
        await channel_service.create_channel(name, platform, config)

        # When & Then: 创建同名渠道应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await channel_service.create_channel(name, platform, config)

        assert f"渠道名称 '{name}' 已存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_channel_by_id_existing_channel(self, channel_service, valid_xianyu_config):
        """测试根据ID获取存在的渠道"""
        # Given: 已创建的渠道
        name = "测试渠道"
        platform = "xianyu"
        config = valid_xianyu_config
        created_channel = await channel_service.create_channel(name, platform, config)

        # When: 根据ID获取渠道
        retrieved_channel = await channel_service.get_channel_by_id(created_channel.id)

        # Then: 应该成功获取渠道
        assert retrieved_channel is not None
        assert retrieved_channel.id == created_channel.id
        assert retrieved_channel.name == name
        assert retrieved_channel.platform == platform.lower()

    @pytest.mark.asyncio
    async def test_get_channel_by_id_nonexistent_channel(self, channel_service):
        """测试根据ID获取不存在的渠道"""
        # Given: 不存在的渠道ID
        nonexistent_id = str(uuid.uuid4())

        # When: 根据ID获取渠道
        retrieved_channel = await channel_service.get_channel_by_id(nonexistent_id)

        # Then: 应该返回None
        assert retrieved_channel is None

    @pytest.mark.asyncio
    async def test_get_channel_by_id_empty_id(self, channel_service):
        """测试使用空ID获取渠道"""
        # Given: 空ID
        empty_id = ""

        # When & Then: 获取渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_service.get_channel_by_id(empty_id)

        assert "渠道ID不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_channel_by_name_existing_channel(self, channel_service, valid_xianyu_config):
        """测试根据名称获取存在的渠道"""
        # Given: 已创建的渠道
        name = "测试渠道名称"
        platform = "xianyu"
        config = valid_xianyu_config
        await channel_service.create_channel(name, platform, config)

        # When: 根据名称获取渠道
        retrieved_channel = await channel_service.get_channel_by_name(name)

        # Then: 应该成功获取渠道
        assert retrieved_channel is not None
        assert retrieved_channel.name == name
        assert retrieved_channel.platform == platform.lower()

    @pytest.mark.asyncio
    async def test_get_channel_by_name_nonexistent_channel(self, channel_service):
        """测试根据名称获取不存在的渠道"""
        # Given: 不存在的渠道名称
        nonexistent_name = "不存在的渠道"

        # When: 根据名称获取渠道
        retrieved_channel = await channel_service.get_channel_by_name(nonexistent_name)

        # Then: 应该返回None
        assert retrieved_channel is None

    @pytest.mark.asyncio
    async def test_get_channel_by_name_empty_name(self, channel_service):
        """测试使用空名称获取渠道"""
        # Given: 空名称
        empty_name = ""

        # When & Then: 获取渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_service.get_channel_by_name(empty_name)

        assert "渠道名称不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_all_channels_with_multiple_channels(self, channel_service, valid_xianyu_config):
        """测试获取所有渠道（多个渠道）"""
        # Given: 多个已创建的渠道
        channels_data = [
            ("渠道1", "xianyu", valid_xianyu_config),
            ("渠道2", "xianyu", valid_xianyu_config),
            ("渠道3", "xianyu", valid_xianyu_config),
        ]

        for name, platform, config in channels_data:
            await channel_service.create_channel(name, platform, config)

        # When: 获取所有渠道
        all_channels = await channel_service.get_all_channels()

        # Then: 应该返回所有渠道
        assert len(all_channels) == 3
        channel_names = [ch.name for ch in all_channels]
        assert "渠道1" in channel_names
        assert "渠道2" in channel_names
        assert "渠道3" in channel_names

    @pytest.mark.asyncio
    async def test_get_all_channels_exclude_deleted(self, channel_service, valid_xianyu_config):
        """测试获取所有渠道（排除已删除）"""
        # Given: 包含已删除渠道的多个渠道
        channel1 = await channel_service.create_channel("渠道1", "xianyu", valid_xianyu_config)
        channel2 = await channel_service.create_channel("渠道2", "xianyu", valid_xianyu_config)

        # 删除一个渠道
        await channel_service.delete_channel(channel1.id)

        # When: 获取所有渠道（不包括已删除）
        all_channels = await channel_service.get_all_channels(include_deleted=False)

        # Then: 应该只返回未删除的渠道
        assert len(all_channels) == 1
        assert all_channels[0].name == "渠道2"

    @pytest.mark.asyncio
    async def test_get_all_channels_include_deleted(self, channel_service, valid_xianyu_config):
        """测试获取所有渠道（包括已删除）"""
        # Given: 包含已删除渠道的多个渠道
        channel1 = await channel_service.create_channel("渠道1", "xianyu", valid_xianyu_config)
        channel2 = await channel_service.create_channel("渠道2", "xianyu", valid_xianyu_config)

        # 删除一个渠道
        await channel_service.delete_channel(channel1.id)

        # When: 获取所有渠道（包括已删除）
        all_channels = await channel_service.get_all_channels(include_deleted=True)

        # Then: 应该返回所有渠道
        assert len(all_channels) == 2
        channel_names = [ch.name for ch in all_channels]
        assert "渠道1" in channel_names
        assert "渠道2" in channel_names

    @pytest.mark.asyncio
    async def test_get_channels_by_platform_existing_platform(self, channel_service, valid_xianyu_config):
        """测试根据平台获取渠道（存在的平台）"""
        # Given: 不同平台的多个渠道
        await channel_service.create_channel("闲鱼渠道1", "xianyu", valid_xianyu_config)
        await channel_service.create_channel("闲鱼渠道2", "xianyu", valid_xianyu_config)
        await channel_service.create_channel("其他平台渠道", "other_platform", {})

        # When: 根据平台获取渠道
        xianyu_channels = await channel_service.get_channels_by_platform("xianyu")

        # Then: 应该只返回指定平台的渠道
        assert len(xianyu_channels) == 2
        for channel in xianyu_channels:
            assert channel.platform == "xianyu"

    @pytest.mark.asyncio
    async def test_get_channels_by_platform_nonexistent_platform(self, channel_service):
        """测试根据平台获取渠道（不存在的平台）"""
        # Given: 不存在的平台
        nonexistent_platform = "nonexistent_platform"

        # When: 根据平台获取渠道
        channels = await channel_service.get_channels_by_platform(nonexistent_platform)

        # Then: 应该返回空列表
        assert len(channels) == 0

    @pytest.mark.asyncio
    async def test_get_channels_by_platform_empty_platform(self, channel_service):
        """测试使用空平台类型获取渠道"""
        # Given: 空平台类型
        empty_platform = ""

        # When & Then: 获取渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_service.get_channels_by_platform(empty_platform)

        assert "平台类型不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_active_channels(self, channel_service, valid_xianyu_config):
        """测试获取活跃渠道"""
        # Given: 包含活跃和非活跃渠道
        active_channel = await channel_service.create_channel("活跃渠道", "xianyu", valid_xianyu_config)
        inactive_channel = await channel_service.create_channel("非活跃渠道", "xianyu", valid_xianyu_config)

        # 设置一个渠道为非活跃
        await channel_service.update_channel(inactive_channel.id, {"is_active": False})

        # When: 获取活跃渠道
        active_channels = await channel_service.get_active_channels()

        # Then: 应该只返回活跃渠道
        assert len(active_channels) == 1
        assert active_channels[0].name == "活跃渠道"
        assert active_channels[0].is_active is True

    @pytest.mark.asyncio
    async def test_update_channel_with_valid_data(self, channel_service, valid_xianyu_config):
        """测试使用有效数据更新渠道 - 对应Gherkin场景：修改渠道信息"""
        # Given: 已创建的渠道
        channel = await channel_service.create_channel("原始渠道", "xianyu", valid_xianyu_config)

        # When: 更新渠道信息
        update_data = {
            "name": "更新后的渠道",
            "is_active": False,
            "status": "connected"
        }
        updated_channel = await channel_service.update_channel(channel.id, update_data)

        # Then: 渠道信息应该被正确更新
        assert updated_channel is not None
        assert updated_channel.name == "更新后的渠道"
        assert updated_channel.is_active is False
        assert updated_channel.status == "connected"
        assert updated_channel.version == 2  # 版本号应该增加

    @pytest.mark.asyncio
    async def test_update_channel_nonexistent_channel(self, channel_service):
        """测试更新不存在的渠道"""
        # Given: 不存在的渠道ID
        nonexistent_id = str(uuid.uuid4())
        update_data = {"name": "新名称"}

        # When & Then: 更新渠道应该抛出未找到错误
        with pytest.raises(NotFoundError) as exc_info:
            await channel_service.update_channel(nonexistent_id, update_data)

        assert f"渠道 '{nonexistent_id}' 不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_channel_empty_id(self, channel_service):
        """测试使用空ID更新渠道"""
        # Given: 空ID
        empty_id = ""
        update_data = {"name": "新名称"}

        # When & Then: 更新渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_service.update_channel(empty_id, update_data)

        assert "渠道ID不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_channel_with_duplicate_name(self, channel_service, valid_xianyu_config):
        """测试更新渠道为重复名称"""
        # Given: 两个已创建的渠道
        channel1 = await channel_service.create_channel("渠道1", "xianyu", valid_xianyu_config)
        channel2 = await channel_service.create_channel("渠道2", "xianyu", valid_xianyu_config)

        # When & Then: 将渠道2的名称更新为渠道1的名称应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await channel_service.update_channel(channel2.id, {"name": "渠道1"})

        assert "渠道名称 '渠道1' 已存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_channel_with_empty_name(self, channel_service, valid_xianyu_config):
        """测试更新渠道为空名称"""
        # Given: 已创建的渠道
        channel = await channel_service.create_channel("原始渠道", "xianyu", valid_xianyu_config)

        # When & Then: 更新为空名称应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_service.update_channel(channel.id, {"name": ""})

        assert "渠道名称不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_delete_channel_existing_channel(self, channel_service, valid_xianyu_config):
        """测试删除存在的渠道 - 对应Gherkin场景：删除渠道"""
        # Given: 已创建的渠道
        channel = await channel_service.create_channel("待删除渠道", "xianyu", valid_xianyu_config)

        # When: 删除渠道
        result = await channel_service.delete_channel(channel.id)

        # Then: 删除应该成功
        assert result is True

        # 验证渠道已被软删除
        deleted_channel = await channel_service.repository.get_by_id(channel.id)
        assert deleted_channel.is_deleted is True
        assert deleted_channel.deleted_at is not None

    @pytest.mark.asyncio
    async def test_delete_channel_nonexistent_channel(self, channel_service):
        """测试删除不存在的渠道"""
        # Given: 不存在的渠道ID
        nonexistent_id = str(uuid.uuid4())

        # When & Then: 删除渠道应该抛出未找到错误
        with pytest.raises(NotFoundError) as exc_info:
            await channel_service.delete_channel(nonexistent_id)

        assert f"渠道 '{nonexistent_id}' 不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_delete_channel_empty_id(self, channel_service):
        """测试使用空ID删除渠道"""
        # Given: 空ID
        empty_id = ""

        # When & Then: 删除渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_service.delete_channel(empty_id)

        assert "渠道ID不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_delete_already_deleted_channel(self, channel_service, valid_xianyu_config):
        """测试删除已删除的渠道"""
        # Given: 已删除的渠道
        channel = await channel_service.create_channel("已删除渠道", "xianyu", valid_xianyu_config)
        await channel_service.delete_channel(channel.id)

        # When & Then: 再次删除应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await channel_service.delete_channel(channel.id)

        assert f"渠道 '{channel.id}' 已被删除" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_restore_channel_existing_deleted_channel(self, channel_service, valid_xianyu_config):
        """测试恢复已删除的渠道"""
        # Given: 已删除的渠道
        channel = await channel_service.create_channel("待恢复渠道", "xianyu", valid_xianyu_config)
        await channel_service.delete_channel(channel.id)

        # When: 恢复渠道
        result = await channel_service.restore_channel(channel.id)

        # Then: 恢复应该成功
        assert result is True

        # 验证渠道已被恢复
        restored_channel = await channel_service.repository.get_by_id(channel.id)
        assert restored_channel.is_deleted is False
        assert restored_channel.deleted_at is None

    @pytest.mark.asyncio
    async def test_restore_channel_nonexistent_channel(self, channel_service):
        """测试恢复不存在的渠道"""
        # Given: 不存在的渠道ID
        nonexistent_id = str(uuid.uuid4())

        # When & Then: 恢复渠道应该抛出未找到错误
        with pytest.raises(NotFoundError) as exc_info:
            await channel_service.restore_channel(nonexistent_id)

        assert f"渠道 '{nonexistent_id}' 不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_restore_channel_not_deleted_channel(self, channel_service, valid_xianyu_config):
        """测试恢复未删除的渠道"""
        # Given: 未删除的渠道
        channel = await channel_service.create_channel("未删除渠道", "xianyu", valid_xianyu_config)

        # When & Then: 恢复未删除的渠道应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await channel_service.restore_channel(channel.id)

        assert f"渠道 '{channel.id}' 未被删除" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_restore_channel_empty_id(self, channel_service):
        """测试使用空ID恢复渠道"""
        # Given: 空ID
        empty_id = ""

        # When & Then: 恢复渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_service.restore_channel(empty_id)

        assert "渠道ID不能为空" in str(exc_info.value)


class TestChannelServiceEdgeCases:
    """渠道服务边界情况测试"""

    @pytest.fixture
    def channel_service_with_data(self):
        """预填充数据的渠道服务"""
        repository = MockChannelRepository()
        connector_factory = MockConnectorFactory()
        service = MockChannelService(repository, connector_factory)
        return service, repository

    @pytest.mark.asyncio
    async def test_channel_service_with_large_dataset(self, valid_xianyu_config):
        """测试渠道服务处理大量数据"""
        # Given: 大量渠道数据
        service, _ = self.channel_service_with_data()

        # When: 创建大量渠道
        channels = []
        for i in range(100):
            channel = await service.create_channel(f"渠道_{i}", "xianyu", valid_xianyu_config)
            channels.append(channel)

        # Then: 所有渠道都应该成功创建
        assert len(channels) == 100

        # 验证获取所有渠道
        all_channels = await service.get_all_channels()
        assert len(all_channels) == 100

    @pytest.mark.asyncio
    async def test_channel_name_with_special_characters(self, channel_service, valid_xianyu_config):
        """测试包含特殊字符的渠道名称"""
        # Given: 包含特殊字符的渠道名称
        special_names = [
            "渠道@#$%",
            "渠道 with spaces",
            "渠道_with_underscores",
            "渠道-with-dashes",
            "渠道.with.dots",
            "渠道(with)parentheses",
            "渠道[with]brackets",
            "渠道{with}braces",
        ]

        # When: 创建包含特殊字符的渠道
        created_channels = []
        for name in special_names:
            channel = await channel_service.create_channel(name, "xianyu", valid_xianyu_config)
            created_channels.append(channel)

        # Then: 所有渠道都应该成功创建
        assert len(created_channels) == len(special_names)
        for i, channel in enumerate(created_channels):
            assert channel.name == special_names[i]

    @pytest.mark.asyncio
    async def test_channel_name_with_unicode_characters(self, channel_service, valid_xianyu_config):
        """测试包含Unicode字符的渠道名称"""
        # Given: 包含Unicode字符的渠道名称
        unicode_names = [
            "渠道测试",  # 中文
            "チャンネル",  # 日文
            "채널",  # 韩文
            "канал",  # 俄文
            "قناة",  # 阿拉伯文
            "🚀渠道🎯",  # 表情符号
        ]

        # When: 创建包含Unicode字符的渠道
        created_channels = []
        for name in unicode_names:
            channel = await channel_service.create_channel(name, "xianyu", valid_xianyu_config)
            created_channels.append(channel)

        # Then: 所有渠道都应该成功创建
        assert len(created_channels) == len(unicode_names)
        for i, channel in enumerate(created_channels):
            assert channel.name == unicode_names[i]

    @pytest.mark.asyncio
    async def test_concurrent_channel_operations(self, channel_service, valid_xianyu_config):
        """测试并发渠道操作"""
        # Given: 并发操作场景
        import asyncio

        # When: 并发创建多个渠道
        tasks = []
        for i in range(10):
            task = channel_service.create_channel(f"并发渠道_{i}", "xianyu", valid_xianyu_config)
            tasks.append(task)

        # 等待所有任务完成
        channels = await asyncio.gather(*tasks)

        # Then: 所有渠道都应该成功创建
        assert len(channels) == 10
        for i, channel in enumerate(channels):
            assert channel.name == f"并发渠道_{i}"

    @pytest.mark.asyncio
    async def test_platform_config_validation_edge_cases(self, channel_service):
        """测试平台配置验证的边界情况"""
        # Given: 各种边界情况的配置
        edge_case_configs = [
            ("xianyu", {"cookies_str": "valid_cookie"}),  # 有效配置
            ("xianyu", {"cookies_str": ""}),  # 空cookie
            ("xianyu", {}),  # 缺少cookie字段
            ("unknown_platform", {"some_config": "value"}),  # 未知平台
        ]

        for platform, config in edge_case_configs:
            try:
                # When: 尝试创建渠道
                await channel_service.create_channel(f"测试_{platform}", platform, config)

                # Then: 只有有效配置和未知平台应该成功
                assert platform in ["unknown_platform"] or (platform == "xianyu" and config.get("cookies_str"))

            except ValidationError:
                # Then: 无效配置应该抛出验证错误
                assert platform == "xianyu" and not config.get("cookies_str")
