"""
渠道CRUD操作单元测试

基于Gherkin剧本中的渠道管理场景，测试渠道CRUD操作的核心功能：
- Create: 创建渠道操作
- Read: 读取渠道操作（单个和批量）
- Update: 更新渠道操作
- Delete: 删除渠道操作（软删除）
- 业务规则验证
- 数据完整性检查
- 并发操作处理
- 错误处理和异常情况
"""

import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from unittest.mock import AsyncMock, MagicMock

import pytest

from app.core.exceptions import (
    BusinessLogicError,
    NotFoundError,
    ValidationError,
)


class MockChannelCRUD:
    """
    模拟渠道CRUD操作实现
    
    基于Repository模式设计，提供渠道的基本CRUD操作
    """
    
    def __init__(self):
        """初始化CRUD操作类"""
        self._channels = {}  # 模拟数据存储
        self._next_id = 1
    
    async def create(self, channel_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建渠道 - 对应Gherkin场景：创建渠道"""
        # 验证必填字段
        if not channel_data.get("name") or not channel_data["name"].strip():
            raise ValidationError("渠道名称不能为空")
        
        if not channel_data.get("platform") or not channel_data["platform"].strip():
            raise ValidationError("平台类型不能为空")
        
        # 检查名称唯一性
        for existing_channel in self._channels.values():
            if (existing_channel["name"] == channel_data["name"] and 
                not existing_channel["is_deleted"]):
                raise BusinessLogicError(f"渠道名称 '{channel_data['name']}' 已存在")
        
        # 验证平台配置
        self._validate_platform_config(channel_data.get("platform"), 
                                     channel_data.get("platform_config", {}))
        
        # 生成新渠道
        channel_id = str(uuid.uuid4())
        now = datetime.utcnow()
        
        new_channel = {
            "id": channel_id,
            "name": channel_data["name"],
            "platform": channel_data["platform"],
            "platform_config": channel_data.get("platform_config", {}),
            "is_active": channel_data.get("is_active", True),
            "status": channel_data.get("status", "disconnected"),
            "is_deleted": False,
            "deleted_at": None,
            "created_at": now,
            "updated_at": now,
            "version": 1,
            "last_connected_at": None,
            "connection_count": 0,
            "error_count": 0,
            "last_error": None,
        }
        
        self._channels[channel_id] = new_channel
        return new_channel.copy()
    
    async def get_by_id(self, channel_id: str, include_deleted: bool = False) -> Optional[Dict[str, Any]]:
        """根据ID获取渠道"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        channel = self._channels.get(channel_id)
        if not channel:
            return None
        
        # 检查是否包含已删除的渠道
        if channel["is_deleted"] and not include_deleted:
            return None
        
        return channel.copy()
    
    async def get_by_name(self, name: str, include_deleted: bool = False) -> Optional[Dict[str, Any]]:
        """根据名称获取渠道"""
        if not name or not name.strip():
            raise ValidationError("渠道名称不能为空")
        
        for channel in self._channels.values():
            if channel["name"] == name:
                if channel["is_deleted"] and not include_deleted:
                    continue
                return channel.copy()
        
        return None
    
    async def get_all(self, include_deleted: bool = False) -> List[Dict[str, Any]]:
        """获取所有渠道"""
        result = []
        for channel in self._channels.values():
            if channel["is_deleted"] and not include_deleted:
                continue
            result.append(channel.copy())
        
        return result
    
    async def get_by_platform(self, platform: str, include_deleted: bool = False) -> List[Dict[str, Any]]:
        """根据平台获取渠道"""
        if not platform or not platform.strip():
            raise ValidationError("平台类型不能为空")
        
        result = []
        for channel in self._channels.values():
            if channel["platform"] == platform:
                if channel["is_deleted"] and not include_deleted:
                    continue
                result.append(channel.copy())
        
        return result
    
    async def get_active_channels(self) -> List[Dict[str, Any]]:
        """获取活跃渠道"""
        result = []
        for channel in self._channels.values():
            if channel["is_active"] and not channel["is_deleted"]:
                result.append(channel.copy())
        
        return result
    
    async def update(self, channel_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新渠道 - 对应Gherkin场景：修改渠道信息"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        channel = self._channels.get(channel_id)
        if not channel:
            raise NotFoundError(f"渠道 '{channel_id}' 不存在")
        
        if channel["is_deleted"]:
            raise BusinessLogicError(f"渠道 '{channel_id}' 已被删除，无法更新")
        
        # 验证名称唯一性（如果更新名称）
        if "name" in update_data:
            new_name = update_data["name"]
            if not new_name or not new_name.strip():
                raise ValidationError("渠道名称不能为空")
            
            for existing_id, existing_channel in self._channels.items():
                if (existing_id != channel_id and 
                    existing_channel["name"] == new_name and 
                    not existing_channel["is_deleted"]):
                    raise BusinessLogicError(f"渠道名称 '{new_name}' 已存在")
        
        # 验证平台配置（如果更新配置）
        if "platform_config" in update_data:
            self._validate_platform_config(channel["platform"], update_data["platform_config"])
        
        # 验证状态值（如果更新状态）
        if "status" in update_data:
            valid_statuses = ["connected", "disconnected", "connecting", "error"]
            if update_data["status"] not in valid_statuses:
                raise ValidationError(f"无效的状态值: {update_data['status']}")
        
        # 更新渠道数据
        for key, value in update_data.items():
            if key in ["id", "created_at", "is_deleted", "deleted_at"]:
                continue  # 这些字段不允许直接更新
            channel[key] = value
        
        # 更新元数据
        channel["updated_at"] = datetime.utcnow()
        channel["version"] += 1
        
        return channel.copy()
    
    async def delete(self, channel_id: str) -> bool:
        """软删除渠道 - 对应Gherkin场景：删除渠道"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        channel = self._channels.get(channel_id)
        if not channel:
            raise NotFoundError(f"渠道 '{channel_id}' 不存在")
        
        if channel["is_deleted"]:
            raise BusinessLogicError(f"渠道 '{channel_id}' 已被删除")
        
        # 执行软删除
        channel["is_deleted"] = True
        channel["deleted_at"] = datetime.utcnow()
        channel["is_active"] = False
        channel["status"] = "disconnected"
        channel["updated_at"] = datetime.utcnow()
        channel["version"] += 1
        
        return True
    
    async def restore(self, channel_id: str) -> bool:
        """恢复已删除的渠道"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        channel = self._channels.get(channel_id)
        if not channel:
            raise NotFoundError(f"渠道 '{channel_id}' 不存在")
        
        if not channel["is_deleted"]:
            raise BusinessLogicError(f"渠道 '{channel_id}' 未被删除")
        
        # 检查名称冲突
        for existing_channel in self._channels.values():
            if (existing_channel["name"] == channel["name"] and 
                not existing_channel["is_deleted"] and 
                existing_channel["id"] != channel_id):
                raise BusinessLogicError(f"渠道名称 '{channel['name']}' 已存在，无法恢复")
        
        # 执行恢复
        channel["is_deleted"] = False
        channel["deleted_at"] = None
        channel["updated_at"] = datetime.utcnow()
        channel["version"] += 1
        
        return True
    
    async def hard_delete(self, channel_id: str) -> bool:
        """硬删除渠道（物理删除）"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        if channel_id not in self._channels:
            raise NotFoundError(f"渠道 '{channel_id}' 不存在")
        
        del self._channels[channel_id]
        return True
    
    async def count(self, include_deleted: bool = False) -> int:
        """统计渠道数量"""
        count = 0
        for channel in self._channels.values():
            if channel["is_deleted"] and not include_deleted:
                continue
            count += 1
        return count
    
    async def exists_by_name(self, name: str, exclude_id: Optional[str] = None) -> bool:
        """检查名称是否存在"""
        if not name or not name.strip():
            return False
        
        for channel_id, channel in self._channels.items():
            if (channel["name"] == name and 
                not channel["is_deleted"] and 
                channel_id != exclude_id):
                return True
        
        return False
    
    def _validate_platform_config(self, platform: str, config: Dict[str, Any]) -> None:
        """验证平台配置"""
        if platform.lower() == "xianyu":
            if not config:
                raise ValidationError("闲鱼平台配置不能为空")

            if "cookies_str" not in config:
                raise ValidationError("闲鱼平台配置必须包含cookies_str字段")

            cookies_str = config.get("cookies_str", "")
            if not cookies_str or not cookies_str.strip():
                raise ValidationError("闲鱼平台的cookies_str不能为空")
    
    def clear_all(self) -> None:
        """清空所有数据（仅用于测试）"""
        self._channels.clear()


class TestChannelCRUD:
    """渠道CRUD操作测试套件"""
    
    @pytest.fixture
    def channel_crud(self):
        """创建渠道CRUD实例"""
        return MockChannelCRUD()
    
    @pytest.fixture
    def valid_channel_data(self):
        """有效的渠道数据"""
        return {
            "name": "测试渠道",
            "platform": "xianyu",
            "platform_config": {"cookies_str": "valid_cookie_string"},
            "is_active": True,
            "status": "disconnected",
        }
    
    @pytest.fixture
    def valid_xianyu_config(self):
        """有效的闲鱼配置"""
        return {"cookies_str": "unb=test_user; session_id=test_session"}
    
    @pytest.mark.asyncio
    async def test_create_channel_with_valid_data(self, channel_crud, valid_channel_data):
        """测试使用有效数据创建渠道 - 对应Gherkin场景：创建渠道"""
        # Given: 有效的渠道数据
        data = valid_channel_data
        
        # When: 创建渠道
        created_channel = await channel_crud.create(data)
        
        # Then: 渠道应该成功创建
        assert created_channel is not None
        assert created_channel["id"] is not None
        assert created_channel["name"] == "测试渠道"
        assert created_channel["platform"] == "xianyu"
        assert created_channel["is_active"] is True
        assert created_channel["status"] == "disconnected"
        assert created_channel["is_deleted"] is False
        assert created_channel["version"] == 1
        assert created_channel["created_at"] is not None
        assert created_channel["updated_at"] is not None

    @pytest.mark.asyncio
    async def test_create_channel_with_empty_name(self, channel_crud):
        """测试使用空名称创建渠道"""
        # Given: 空名称的渠道数据
        data = {
            "name": "",
            "platform": "xianyu",
            "platform_config": {"cookies_str": "valid_cookie"},
        }

        # When & Then: 创建渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_crud.create(data)

        assert "渠道名称不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_channel_with_empty_platform(self, channel_crud):
        """测试使用空平台创建渠道"""
        # Given: 空平台的渠道数据
        data = {
            "name": "测试渠道",
            "platform": "",
            "platform_config": {},
        }

        # When & Then: 创建渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_crud.create(data)

        assert "平台类型不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_channel_with_duplicate_name(self, channel_crud, valid_channel_data):
        """测试创建重复名称的渠道"""
        # Given: 已存在的渠道
        await channel_crud.create(valid_channel_data)

        # When & Then: 创建同名渠道应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await channel_crud.create(valid_channel_data)

        assert "渠道名称 '测试渠道' 已存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_channel_with_invalid_xianyu_config(self, channel_crud):
        """测试使用无效闲鱼配置创建渠道"""
        # Given: 无效的闲鱼配置
        invalid_configs = [
            {},  # 缺少cookies_str
            {"cookies_str": ""},  # 空cookies_str
            {"other_field": "value"},  # 缺少cookies_str字段
        ]

        for invalid_config in invalid_configs:
            data = {
                "name": f"测试渠道_{len(invalid_config)}",
                "platform": "xianyu",
                "platform_config": invalid_config,
            }

            # When & Then: 创建渠道应该抛出验证错误
            with pytest.raises(ValidationError):
                await channel_crud.create(data)

    @pytest.mark.asyncio
    async def test_get_by_id_existing_channel(self, channel_crud, valid_channel_data):
        """测试根据ID获取存在的渠道"""
        # Given: 已创建的渠道
        created_channel = await channel_crud.create(valid_channel_data)
        channel_id = created_channel["id"]

        # When: 根据ID获取渠道
        retrieved_channel = await channel_crud.get_by_id(channel_id)

        # Then: 应该成功获取渠道
        assert retrieved_channel is not None
        assert retrieved_channel["id"] == channel_id
        assert retrieved_channel["name"] == "测试渠道"

    @pytest.mark.asyncio
    async def test_get_by_id_nonexistent_channel(self, channel_crud):
        """测试根据ID获取不存在的渠道"""
        # Given: 不存在的渠道ID
        nonexistent_id = str(uuid.uuid4())

        # When: 根据ID获取渠道
        retrieved_channel = await channel_crud.get_by_id(nonexistent_id)

        # Then: 应该返回None
        assert retrieved_channel is None

    @pytest.mark.asyncio
    async def test_get_by_id_empty_id(self, channel_crud):
        """测试使用空ID获取渠道"""
        # Given: 空ID
        empty_id = ""

        # When & Then: 获取渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_crud.get_by_id(empty_id)

        assert "渠道ID不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_by_id_deleted_channel_exclude(self, channel_crud, valid_channel_data):
        """测试获取已删除的渠道（排除已删除）"""
        # Given: 已删除的渠道
        created_channel = await channel_crud.create(valid_channel_data)
        channel_id = created_channel["id"]
        await channel_crud.delete(channel_id)

        # When: 获取渠道（不包括已删除）
        retrieved_channel = await channel_crud.get_by_id(channel_id, include_deleted=False)

        # Then: 应该返回None
        assert retrieved_channel is None

    @pytest.mark.asyncio
    async def test_get_by_id_deleted_channel_include(self, channel_crud, valid_channel_data):
        """测试获取已删除的渠道（包括已删除）"""
        # Given: 已删除的渠道
        created_channel = await channel_crud.create(valid_channel_data)
        channel_id = created_channel["id"]
        await channel_crud.delete(channel_id)

        # When: 获取渠道（包括已删除）
        retrieved_channel = await channel_crud.get_by_id(channel_id, include_deleted=True)

        # Then: 应该成功获取渠道
        assert retrieved_channel is not None
        assert retrieved_channel["id"] == channel_id
        assert retrieved_channel["is_deleted"] is True

    @pytest.mark.asyncio
    async def test_get_by_name_existing_channel(self, channel_crud, valid_channel_data):
        """测试根据名称获取存在的渠道"""
        # Given: 已创建的渠道
        await channel_crud.create(valid_channel_data)

        # When: 根据名称获取渠道
        retrieved_channel = await channel_crud.get_by_name("测试渠道")

        # Then: 应该成功获取渠道
        assert retrieved_channel is not None
        assert retrieved_channel["name"] == "测试渠道"

    @pytest.mark.asyncio
    async def test_get_by_name_nonexistent_channel(self, channel_crud):
        """测试根据名称获取不存在的渠道"""
        # Given: 不存在的渠道名称
        nonexistent_name = "不存在的渠道"

        # When: 根据名称获取渠道
        retrieved_channel = await channel_crud.get_by_name(nonexistent_name)

        # Then: 应该返回None
        assert retrieved_channel is None

    @pytest.mark.asyncio
    async def test_get_by_name_empty_name(self, channel_crud):
        """测试使用空名称获取渠道"""
        # Given: 空名称
        empty_name = ""

        # When & Then: 获取渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_crud.get_by_name(empty_name)

        assert "渠道名称不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_all_channels_multiple_channels(self, channel_crud, valid_xianyu_config):
        """测试获取所有渠道（多个渠道）"""
        # Given: 多个已创建的渠道
        channels_data = [
            {"name": "渠道1", "platform": "xianyu", "platform_config": valid_xianyu_config},
            {"name": "渠道2", "platform": "xianyu", "platform_config": valid_xianyu_config},
            {"name": "渠道3", "platform": "xianyu", "platform_config": valid_xianyu_config},
        ]

        for data in channels_data:
            await channel_crud.create(data)

        # When: 获取所有渠道
        all_channels = await channel_crud.get_all()

        # Then: 应该返回所有渠道
        assert len(all_channels) == 3
        channel_names = [ch["name"] for ch in all_channels]
        assert "渠道1" in channel_names
        assert "渠道2" in channel_names
        assert "渠道3" in channel_names

    @pytest.mark.asyncio
    async def test_get_all_channels_exclude_deleted(self, channel_crud, valid_xianyu_config):
        """测试获取所有渠道（排除已删除）"""
        # Given: 包含已删除渠道的多个渠道
        channel1_data = {"name": "渠道1", "platform": "xianyu", "platform_config": valid_xianyu_config}
        channel2_data = {"name": "渠道2", "platform": "xianyu", "platform_config": valid_xianyu_config}

        channel1 = await channel_crud.create(channel1_data)
        await channel_crud.create(channel2_data)

        # 删除一个渠道
        await channel_crud.delete(channel1["id"])

        # When: 获取所有渠道（不包括已删除）
        all_channels = await channel_crud.get_all(include_deleted=False)

        # Then: 应该只返回未删除的渠道
        assert len(all_channels) == 1
        assert all_channels[0]["name"] == "渠道2"

    @pytest.mark.asyncio
    async def test_get_all_channels_include_deleted(self, channel_crud, valid_xianyu_config):
        """测试获取所有渠道（包括已删除）"""
        # Given: 包含已删除渠道的多个渠道
        channel1_data = {"name": "渠道1", "platform": "xianyu", "platform_config": valid_xianyu_config}
        channel2_data = {"name": "渠道2", "platform": "xianyu", "platform_config": valid_xianyu_config}

        channel1 = await channel_crud.create(channel1_data)
        await channel_crud.create(channel2_data)

        # 删除一个渠道
        await channel_crud.delete(channel1["id"])

        # When: 获取所有渠道（包括已删除）
        all_channels = await channel_crud.get_all(include_deleted=True)

        # Then: 应该返回所有渠道
        assert len(all_channels) == 2
        channel_names = [ch["name"] for ch in all_channels]
        assert "渠道1" in channel_names
        assert "渠道2" in channel_names

    @pytest.mark.asyncio
    async def test_get_by_platform_existing_platform(self, channel_crud, valid_xianyu_config):
        """测试根据平台获取渠道（存在的平台）"""
        # Given: 不同平台的多个渠道
        channels_data = [
            {"name": "闲鱼渠道1", "platform": "xianyu", "platform_config": valid_xianyu_config},
            {"name": "闲鱼渠道2", "platform": "xianyu", "platform_config": valid_xianyu_config},
            {"name": "其他平台渠道", "platform": "other_platform", "platform_config": {}},
        ]

        for data in channels_data:
            await channel_crud.create(data)

        # When: 根据平台获取渠道
        xianyu_channels = await channel_crud.get_by_platform("xianyu")

        # Then: 应该只返回指定平台的渠道
        assert len(xianyu_channels) == 2
        for channel in xianyu_channels:
            assert channel["platform"] == "xianyu"

    @pytest.mark.asyncio
    async def test_get_by_platform_nonexistent_platform(self, channel_crud):
        """测试根据平台获取渠道（不存在的平台）"""
        # Given: 不存在的平台
        nonexistent_platform = "nonexistent_platform"

        # When: 根据平台获取渠道
        channels = await channel_crud.get_by_platform(nonexistent_platform)

        # Then: 应该返回空列表
        assert len(channels) == 0

    @pytest.mark.asyncio
    async def test_get_by_platform_empty_platform(self, channel_crud):
        """测试使用空平台类型获取渠道"""
        # Given: 空平台类型
        empty_platform = ""

        # When & Then: 获取渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_crud.get_by_platform(empty_platform)

        assert "平台类型不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_active_channels(self, channel_crud, valid_xianyu_config):
        """测试获取活跃渠道"""
        # Given: 包含活跃和非活跃渠道
        active_data = {"name": "活跃渠道", "platform": "xianyu", "platform_config": valid_xianyu_config, "is_active": True}
        inactive_data = {"name": "非活跃渠道", "platform": "xianyu", "platform_config": valid_xianyu_config, "is_active": False}

        await channel_crud.create(active_data)
        await channel_crud.create(inactive_data)

        # When: 获取活跃渠道
        active_channels = await channel_crud.get_active_channels()

        # Then: 应该只返回活跃渠道
        assert len(active_channels) == 1
        assert active_channels[0]["name"] == "活跃渠道"
        assert active_channels[0]["is_active"] is True

    @pytest.mark.asyncio
    async def test_update_channel_with_valid_data(self, channel_crud, valid_channel_data):
        """测试使用有效数据更新渠道 - 对应Gherkin场景：修改渠道信息"""
        # Given: 已创建的渠道
        created_channel = await channel_crud.create(valid_channel_data)
        channel_id = created_channel["id"]
        original_version = created_channel["version"]

        # When: 更新渠道信息
        update_data = {
            "name": "更新后的渠道",
            "is_active": False,
            "status": "connected"
        }
        updated_channel = await channel_crud.update(channel_id, update_data)

        # Then: 渠道信息应该被正确更新
        assert updated_channel is not None
        assert updated_channel["name"] == "更新后的渠道"
        assert updated_channel["is_active"] is False
        assert updated_channel["status"] == "connected"
        assert updated_channel["version"] == original_version + 1

    @pytest.mark.asyncio
    async def test_update_channel_nonexistent_channel(self, channel_crud):
        """测试更新不存在的渠道"""
        # Given: 不存在的渠道ID
        nonexistent_id = str(uuid.uuid4())
        update_data = {"name": "新名称"}

        # When & Then: 更新渠道应该抛出未找到错误
        with pytest.raises(NotFoundError) as exc_info:
            await channel_crud.update(nonexistent_id, update_data)

        assert f"渠道 '{nonexistent_id}' 不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_channel_empty_id(self, channel_crud):
        """测试使用空ID更新渠道"""
        # Given: 空ID
        empty_id = ""
        update_data = {"name": "新名称"}

        # When & Then: 更新渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_crud.update(empty_id, update_data)

        assert "渠道ID不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_channel_deleted_channel(self, channel_crud, valid_channel_data):
        """测试更新已删除的渠道"""
        # Given: 已删除的渠道
        created_channel = await channel_crud.create(valid_channel_data)
        channel_id = created_channel["id"]
        await channel_crud.delete(channel_id)

        # When & Then: 更新已删除的渠道应该抛出业务逻辑错误
        update_data = {"name": "新名称"}
        with pytest.raises(BusinessLogicError) as exc_info:
            await channel_crud.update(channel_id, update_data)

        assert f"渠道 '{channel_id}' 已被删除，无法更新" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_channel_with_duplicate_name(self, channel_crud, valid_xianyu_config):
        """测试更新渠道为重复名称"""
        # Given: 两个已创建的渠道
        channel1_data = {"name": "渠道1", "platform": "xianyu", "platform_config": valid_xianyu_config}
        channel2_data = {"name": "渠道2", "platform": "xianyu", "platform_config": valid_xianyu_config}

        await channel_crud.create(channel1_data)
        channel2 = await channel_crud.create(channel2_data)

        # When & Then: 将渠道2的名称更新为渠道1的名称应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await channel_crud.update(channel2["id"], {"name": "渠道1"})

        assert "渠道名称 '渠道1' 已存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_channel_with_empty_name(self, channel_crud, valid_channel_data):
        """测试更新渠道为空名称"""
        # Given: 已创建的渠道
        created_channel = await channel_crud.create(valid_channel_data)
        channel_id = created_channel["id"]

        # When & Then: 更新为空名称应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_crud.update(channel_id, {"name": ""})

        assert "渠道名称不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_channel_with_invalid_status(self, channel_crud, valid_channel_data):
        """测试更新渠道为无效状态"""
        # Given: 已创建的渠道
        created_channel = await channel_crud.create(valid_channel_data)
        channel_id = created_channel["id"]

        # When & Then: 更新为无效状态应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_crud.update(channel_id, {"status": "invalid_status"})

        assert "无效的状态值: invalid_status" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_update_channel_with_invalid_config(self, channel_crud, valid_channel_data):
        """测试更新渠道为无效配置"""
        # Given: 已创建的渠道
        created_channel = await channel_crud.create(valid_channel_data)
        channel_id = created_channel["id"]

        # When & Then: 更新为无效配置应该抛出验证错误
        invalid_config = {"cookies_str": ""}  # 空cookies_str
        with pytest.raises(ValidationError):
            await channel_crud.update(channel_id, {"platform_config": invalid_config})

    @pytest.mark.asyncio
    async def test_delete_channel_existing_channel(self, channel_crud, valid_channel_data):
        """测试删除存在的渠道 - 对应Gherkin场景：删除渠道"""
        # Given: 已创建的渠道
        created_channel = await channel_crud.create(valid_channel_data)
        channel_id = created_channel["id"]

        # When: 删除渠道
        result = await channel_crud.delete(channel_id)

        # Then: 删除应该成功
        assert result is True

        # 验证渠道已被软删除
        deleted_channel = await channel_crud.get_by_id(channel_id, include_deleted=True)
        assert deleted_channel["is_deleted"] is True
        assert deleted_channel["deleted_at"] is not None
        assert deleted_channel["is_active"] is False
        assert deleted_channel["status"] == "disconnected"

    @pytest.mark.asyncio
    async def test_delete_channel_nonexistent_channel(self, channel_crud):
        """测试删除不存在的渠道"""
        # Given: 不存在的渠道ID
        nonexistent_id = str(uuid.uuid4())

        # When & Then: 删除渠道应该抛出未找到错误
        with pytest.raises(NotFoundError) as exc_info:
            await channel_crud.delete(nonexistent_id)

        assert f"渠道 '{nonexistent_id}' 不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_delete_channel_empty_id(self, channel_crud):
        """测试使用空ID删除渠道"""
        # Given: 空ID
        empty_id = ""

        # When & Then: 删除渠道应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await channel_crud.delete(empty_id)

        assert "渠道ID不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_delete_already_deleted_channel(self, channel_crud, valid_channel_data):
        """测试删除已删除的渠道"""
        # Given: 已删除的渠道
        created_channel = await channel_crud.create(valid_channel_data)
        channel_id = created_channel["id"]
        await channel_crud.delete(channel_id)

        # When & Then: 再次删除应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await channel_crud.delete(channel_id)

        assert f"渠道 '{channel_id}' 已被删除" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_restore_channel_existing_deleted_channel(self, channel_crud, valid_channel_data):
        """测试恢复已删除的渠道"""
        # Given: 已删除的渠道
        created_channel = await channel_crud.create(valid_channel_data)
        channel_id = created_channel["id"]
        await channel_crud.delete(channel_id)

        # When: 恢复渠道
        result = await channel_crud.restore(channel_id)

        # Then: 恢复应该成功
        assert result is True

        # 验证渠道已被恢复
        restored_channel = await channel_crud.get_by_id(channel_id)
        assert restored_channel is not None
        assert restored_channel["is_deleted"] is False
        assert restored_channel["deleted_at"] is None

    @pytest.mark.asyncio
    async def test_restore_channel_nonexistent_channel(self, channel_crud):
        """测试恢复不存在的渠道"""
        # Given: 不存在的渠道ID
        nonexistent_id = str(uuid.uuid4())

        # When & Then: 恢复渠道应该抛出未找到错误
        with pytest.raises(NotFoundError) as exc_info:
            await channel_crud.restore(nonexistent_id)

        assert f"渠道 '{nonexistent_id}' 不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_restore_channel_not_deleted_channel(self, channel_crud, valid_channel_data):
        """测试恢复未删除的渠道"""
        # Given: 未删除的渠道
        created_channel = await channel_crud.create(valid_channel_data)
        channel_id = created_channel["id"]

        # When & Then: 恢复未删除的渠道应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await channel_crud.restore(channel_id)

        assert f"渠道 '{channel_id}' 未被删除" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_restore_channel_with_name_conflict(self, channel_crud, valid_xianyu_config):
        """测试恢复渠道时名称冲突"""
        # Given: 已删除的渠道和同名的新渠道
        original_data = {"name": "冲突渠道", "platform": "xianyu", "platform_config": valid_xianyu_config}
        new_data = {"name": "冲突渠道", "platform": "xianyu", "platform_config": valid_xianyu_config}

        # 创建并删除原渠道
        original_channel = await channel_crud.create(original_data)
        await channel_crud.delete(original_channel["id"])

        # 创建同名新渠道
        await channel_crud.create(new_data)

        # When & Then: 恢复原渠道应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await channel_crud.restore(original_channel["id"])

        assert "渠道名称 '冲突渠道' 已存在，无法恢复" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_hard_delete_channel(self, channel_crud, valid_channel_data):
        """测试硬删除渠道"""
        # Given: 已创建的渠道
        created_channel = await channel_crud.create(valid_channel_data)
        channel_id = created_channel["id"]

        # When: 硬删除渠道
        result = await channel_crud.hard_delete(channel_id)

        # Then: 删除应该成功
        assert result is True

        # 验证渠道已被物理删除
        deleted_channel = await channel_crud.get_by_id(channel_id, include_deleted=True)
        assert deleted_channel is None

    @pytest.mark.asyncio
    async def test_hard_delete_nonexistent_channel(self, channel_crud):
        """测试硬删除不存在的渠道"""
        # Given: 不存在的渠道ID
        nonexistent_id = str(uuid.uuid4())

        # When & Then: 硬删除渠道应该抛出未找到错误
        with pytest.raises(NotFoundError) as exc_info:
            await channel_crud.hard_delete(nonexistent_id)

        assert f"渠道 '{nonexistent_id}' 不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_count_channels(self, channel_crud, valid_xianyu_config):
        """测试统计渠道数量"""
        # Given: 多个渠道（包含已删除）
        channels_data = [
            {"name": "渠道1", "platform": "xianyu", "platform_config": valid_xianyu_config},
            {"name": "渠道2", "platform": "xianyu", "platform_config": valid_xianyu_config},
            {"name": "渠道3", "platform": "xianyu", "platform_config": valid_xianyu_config},
        ]

        created_channels = []
        for data in channels_data:
            channel = await channel_crud.create(data)
            created_channels.append(channel)

        # 删除一个渠道
        await channel_crud.delete(created_channels[0]["id"])

        # When: 统计渠道数量
        total_count = await channel_crud.count(include_deleted=True)
        active_count = await channel_crud.count(include_deleted=False)

        # Then: 数量应该正确
        assert total_count == 3
        assert active_count == 2

    @pytest.mark.asyncio
    async def test_exists_by_name(self, channel_crud, valid_channel_data):
        """测试检查名称是否存在"""
        # Given: 已创建的渠道
        await channel_crud.create(valid_channel_data)

        # When: 检查名称是否存在
        exists = await channel_crud.exists_by_name("测试渠道")
        not_exists = await channel_crud.exists_by_name("不存在的渠道")

        # Then: 结果应该正确
        assert exists is True
        assert not_exists is False

    @pytest.mark.asyncio
    async def test_exists_by_name_exclude_id(self, channel_crud, valid_xianyu_config):
        """测试检查名称是否存在（排除指定ID）"""
        # Given: 两个渠道
        channel1_data = {"name": "渠道1", "platform": "xianyu", "platform_config": valid_xianyu_config}
        channel2_data = {"name": "渠道2", "platform": "xianyu", "platform_config": valid_xianyu_config}

        channel1 = await channel_crud.create(channel1_data)
        await channel_crud.create(channel2_data)

        # When: 检查名称是否存在（排除自身）
        exists_exclude_self = await channel_crud.exists_by_name("渠道1", exclude_id=channel1["id"])
        exists_exclude_other = await channel_crud.exists_by_name("渠道1", exclude_id="other_id")

        # Then: 结果应该正确
        assert exists_exclude_self is False  # 排除自身，不存在
        assert exists_exclude_other is True  # 不排除自身，存在


class TestChannelCRUDEdgeCases:
    """渠道CRUD操作边界情况测试"""

    @pytest.fixture
    def channel_crud_edge_cases(self):
        """边界情况测试的CRUD实例"""
        return MockChannelCRUD()

    @pytest.fixture
    def valid_xianyu_config(self):
        """有效的闲鱼配置"""
        return {"cookies_str": "unb=test_user; session_id=test_session"}

    @pytest.mark.asyncio
    async def test_crud_operations_with_large_dataset(self, channel_crud_edge_cases, valid_xianyu_config):
        """测试CRUD操作处理大量数据"""
        # Given: 大量渠道数据
        channels_count = 100

        # When: 创建大量渠道
        created_channels = []
        for i in range(channels_count):
            data = {
                "name": f"渠道_{i}",
                "platform": "xianyu",
                "platform_config": valid_xianyu_config
            }
            channel = await channel_crud_edge_cases.create(data)
            created_channels.append(channel)

        # Then: 所有渠道都应该成功创建
        assert len(created_channels) == channels_count

        # 验证获取所有渠道
        all_channels = await channel_crud_edge_cases.get_all()
        assert len(all_channels) == channels_count

        # 验证统计功能
        count = await channel_crud_edge_cases.count()
        assert count == channels_count

    @pytest.mark.asyncio
    async def test_concurrent_crud_operations(self, channel_crud_edge_cases, valid_xianyu_config):
        """测试并发CRUD操作"""
        import asyncio

        # Given: 并发操作场景
        async def create_channel(index):
            data = {
                "name": f"并发渠道_{index}",
                "platform": "xianyu",
                "platform_config": valid_xianyu_config
            }
            return await channel_crud_edge_cases.create(data)

        # When: 并发创建多个渠道
        tasks = [create_channel(i) for i in range(10)]
        channels = await asyncio.gather(*tasks)

        # Then: 所有渠道都应该成功创建
        assert len(channels) == 10
        for i, channel in enumerate(channels):
            assert channel["name"] == f"并发渠道_{i}"

    @pytest.mark.asyncio
    async def test_crud_operations_with_unicode_data(self, channel_crud_edge_cases, valid_xianyu_config):
        """测试CRUD操作处理Unicode数据"""
        # Given: 包含Unicode字符的数据
        unicode_names = [
            "测试渠道",  # 中文
            "チャンネル",  # 日文
            "채널",  # 韩文
            "🚀渠道🎯",  # 表情符号
        ]

        # When: 创建包含Unicode字符的渠道
        created_channels = []
        for name in unicode_names:
            data = {
                "name": name,
                "platform": "xianyu",
                "platform_config": valid_xianyu_config
            }
            channel = await channel_crud_edge_cases.create(data)
            created_channels.append(channel)

        # Then: 所有渠道都应该成功创建
        assert len(created_channels) == len(unicode_names)

        # 验证可以正确检索
        for i, name in enumerate(unicode_names):
            retrieved = await channel_crud_edge_cases.get_by_name(name)
            assert retrieved is not None
            assert retrieved["name"] == name

    @pytest.mark.asyncio
    async def test_crud_operations_boundary_conditions(self, channel_crud_edge_cases):
        """测试CRUD操作的边界条件"""
        # Given: 边界条件数据
        boundary_cases = [
            {"name": "x", "platform": "p"},  # 最短名称
            {"name": "x" * 100, "platform": "p" * 50},  # 最长名称
        ]

        for data in boundary_cases:
            # When: 创建边界条件渠道
            data["platform_config"] = {}
            channel = await channel_crud_edge_cases.create(data)

            # Then: 应该成功创建
            assert channel["name"] == data["name"]
            assert channel["platform"] == data["platform"]

    @pytest.mark.asyncio
    async def test_crud_operations_state_consistency(self, channel_crud_edge_cases, valid_xianyu_config):
        """测试CRUD操作的状态一致性"""
        # Given: 已创建的渠道
        data = {
            "name": "状态测试渠道",
            "platform": "xianyu",
            "platform_config": valid_xianyu_config
        }
        channel = await channel_crud_edge_cases.create(data)
        channel_id = channel["id"]

        # When: 进行一系列操作
        # 1. 更新渠道
        await channel_crud_edge_cases.update(channel_id, {"status": "connected"})

        # 2. 软删除
        await channel_crud_edge_cases.delete(channel_id)

        # 3. 恢复
        await channel_crud_edge_cases.restore(channel_id)

        # Then: 状态应该保持一致
        final_channel = await channel_crud_edge_cases.get_by_id(channel_id)
        assert final_channel is not None
        assert final_channel["is_deleted"] is False
        assert final_channel["deleted_at"] is None
        assert final_channel["version"] > 1  # 版本号应该递增
