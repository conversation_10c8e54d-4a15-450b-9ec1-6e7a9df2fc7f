"""
Cookie管理工具单元测试

基于Gherkin剧本中的Cookie安全管理场景，测试Cookie管理工具的核心功能：
- Cookie加密存储和解密
- Cookie脱敏显示
- Cookie格式验证
- Cookie安全管理
- AES加密/解密功能
- Cookie过期检查
- 错误处理和异常情况
"""

import base64
import json
import os
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from unittest.mock import MagicMock, patch

import pytest
from cryptography.fernet import Fernet

from app.core.exceptions import (
    BusinessLogicError,
    ValidationError,
)


class MockCookieManager:
    """
    模拟Cookie管理工具实现
    
    基于安全要求实现Cookie的加密存储、脱敏显示和格式验证功能
    """
    
    def __init__(self, encryption_key: Optional[str] = None):
        """初始化Cookie管理器"""
        if encryption_key:
            self.encryption_key = encryption_key.encode()
        else:
            self.encryption_key = Fernet.generate_key()
        
        self.fernet = Fernet(self.encryption_key)
        self._cookie_cache = {}
    
    def encrypt_cookie(self, cookie_str: str) -> str:
        """加密Cookie字符串"""
        if not cookie_str:
            raise ValidationError("Cookie字符串不能为空")
        
        try:
            # 将Cookie字符串编码为字节
            cookie_bytes = cookie_str.encode('utf-8')
            
            # 使用Fernet加密
            encrypted_bytes = self.fernet.encrypt(cookie_bytes)
            
            # 转换为base64字符串便于存储
            encrypted_str = base64.b64encode(encrypted_bytes).decode('utf-8')
            
            return encrypted_str
            
        except Exception as e:
            raise BusinessLogicError(f"Cookie加密失败: {e}")
    
    def decrypt_cookie(self, encrypted_cookie: str) -> str:
        """解密Cookie字符串"""
        if not encrypted_cookie:
            raise ValidationError("加密Cookie字符串不能为空")
        
        try:
            # 从base64字符串解码
            encrypted_bytes = base64.b64decode(encrypted_cookie.encode('utf-8'))
            
            # 使用Fernet解密
            decrypted_bytes = self.fernet.decrypt(encrypted_bytes)
            
            # 转换为字符串
            cookie_str = decrypted_bytes.decode('utf-8')
            
            return cookie_str
            
        except Exception as e:
            raise BusinessLogicError(f"Cookie解密失败: {e}")
    
    def mask_cookie_for_display(self, cookie_str: str, mask_char: str = "*") -> str:
        """脱敏显示Cookie - 对应Gherkin场景：Cookie安全管理"""
        if not cookie_str:
            return ""
        
        # 对于短字符串，全部用星号替换
        if len(cookie_str) <= 20:
            return mask_char * len(cookie_str)
        
        # 对于长字符串，显示前10个和后10个字符，中间用星号替换
        prefix = cookie_str[:10]
        suffix = cookie_str[-10:]
        middle_length = len(cookie_str) - 20
        middle = mask_char * middle_length
        
        return f"{prefix}{middle}{suffix}"
    
    def validate_cookie_format(self, cookie_str: str, platform: str = "xianyu") -> bool:
        """验证Cookie格式"""
        if not cookie_str or not cookie_str.strip():
            return False
        
        # 基本格式检查
        if "=" not in cookie_str:
            return False
        
        # 平台特定的验证
        if platform.lower() == "xianyu":
            return self._validate_xianyu_cookie_format(cookie_str)
        
        # 默认验证：检查是否包含键值对
        return True
    
    def _validate_xianyu_cookie_format(self, cookie_str: str) -> bool:
        """验证闲鱼Cookie格式"""
        required_fields = ['unb', 'session_id']
        
        for field in required_fields:
            if field not in cookie_str:
                return False
        
        # 检查是否有有效的键值对格式
        try:
            pairs = cookie_str.split(';')
            for pair in pairs:
                if '=' not in pair.strip():
                    continue
                key, value = pair.strip().split('=', 1)
                if not key.strip() or not value.strip():
                    return False
            return True
        except Exception:
            return False
    
    def parse_cookie_string(self, cookie_str: str) -> Dict[str, str]:
        """解析Cookie字符串为字典"""
        if not cookie_str:
            return {}
        
        cookie_dict = {}
        try:
            pairs = cookie_str.split(';')
            for pair in pairs:
                if '=' not in pair:
                    continue
                key, value = pair.strip().split('=', 1)
                cookie_dict[key.strip()] = value.strip()
        except Exception:
            raise ValidationError("Cookie字符串格式无效")
        
        return cookie_dict
    
    def build_cookie_string(self, cookie_dict: Dict[str, str]) -> str:
        """从字典构建Cookie字符串"""
        if not cookie_dict:
            return ""
        
        pairs = []
        for key, value in cookie_dict.items():
            if key and value:
                pairs.append(f"{key}={value}")
        
        return "; ".join(pairs)
    
    def extract_user_info(self, cookie_str: str, platform: str = "xianyu") -> Dict[str, Any]:
        """从Cookie中提取用户信息"""
        if not cookie_str:
            return {}
        
        cookie_dict = self.parse_cookie_string(cookie_str)
        
        if platform.lower() == "xianyu":
            return self._extract_xianyu_user_info(cookie_dict)
        
        return {}
    
    def _extract_xianyu_user_info(self, cookie_dict: Dict[str, str]) -> Dict[str, Any]:
        """从闲鱼Cookie中提取用户信息"""
        user_info = {}
        
        # 提取用户ID
        if 'unb' in cookie_dict:
            user_info['user_id'] = cookie_dict['unb']
        
        # 提取会话ID
        if 'session_id' in cookie_dict:
            user_info['session_id'] = cookie_dict['session_id']
        
        # 提取token
        if '_tb_token_' in cookie_dict:
            user_info['token'] = cookie_dict['_tb_token_']
        
        return user_info
    
    def is_cookie_expired(self, cookie_str: str, platform: str = "xianyu") -> bool:
        """检查Cookie是否过期"""
        if not cookie_str:
            return True
        
        # 简单的过期检查逻辑
        # 在实际实现中，这里会检查Cookie中的过期时间戳或进行网络验证
        cookie_dict = self.parse_cookie_string(cookie_str)
        
        # 检查是否包含过期标识
        if "expired" in cookie_str.lower():
            return True
        
        # 检查必要字段是否存在
        if platform.lower() == "xianyu":
            required_fields = ['unb', 'session_id']
            for field in required_fields:
                if field not in cookie_dict or not cookie_dict[field]:
                    return True
        
        return False
    
    def store_cookie_securely(self, cookie_str: str, identifier: str) -> str:
        """安全存储Cookie"""
        if not cookie_str or not identifier:
            raise ValidationError("Cookie字符串和标识符不能为空")
        
        # 加密Cookie
        encrypted_cookie = self.encrypt_cookie(cookie_str)
        
        # 存储到缓存（在实际实现中会存储到数据库）
        self._cookie_cache[identifier] = {
            'encrypted_cookie': encrypted_cookie,
            'stored_at': datetime.utcnow(),
            'masked_display': self.mask_cookie_for_display(cookie_str)
        }
        
        return encrypted_cookie
    
    def retrieve_cookie_securely(self, identifier: str) -> Optional[str]:
        """安全检索Cookie"""
        if not identifier:
            raise ValidationError("标识符不能为空")
        
        if identifier not in self._cookie_cache:
            return None
        
        stored_data = self._cookie_cache[identifier]
        encrypted_cookie = stored_data['encrypted_cookie']
        
        # 解密Cookie
        return self.decrypt_cookie(encrypted_cookie)
    
    def get_cookie_display_info(self, identifier: str) -> Optional[Dict[str, Any]]:
        """获取Cookie显示信息（脱敏）"""
        if not identifier or identifier not in self._cookie_cache:
            return None
        
        stored_data = self._cookie_cache[identifier]
        return {
            'identifier': identifier,
            'masked_cookie': stored_data['masked_display'],
            'stored_at': stored_data['stored_at'].isoformat(),
            'is_encrypted': True
        }
    
    def delete_cookie(self, identifier: str) -> bool:
        """删除存储的Cookie"""
        if identifier in self._cookie_cache:
            del self._cookie_cache[identifier]
            return True
        return False
    
    def list_stored_cookies(self) -> List[Dict[str, Any]]:
        """列出所有存储的Cookie（脱敏显示）"""
        result = []
        for identifier, data in self._cookie_cache.items():
            result.append({
                'identifier': identifier,
                'masked_cookie': data['masked_display'],
                'stored_at': data['stored_at'].isoformat()
            })
        return result
    
    def validate_encryption_key(self, key: str) -> bool:
        """验证加密密钥格式"""
        try:
            # 检查是否是有效的Fernet密钥
            if isinstance(key, str):
                key_bytes = key.encode()
            else:
                key_bytes = key
            
            # 尝试创建Fernet实例
            Fernet(key_bytes)
            return True
        except Exception:
            return False
    
    def generate_new_encryption_key(self) -> str:
        """生成新的加密密钥"""
        return Fernet.generate_key().decode('utf-8')


class TestCookieManager:
    """Cookie管理工具测试套件"""
    
    @pytest.fixture
    def cookie_manager(self):
        """创建Cookie管理器实例"""
        return MockCookieManager()
    
    @pytest.fixture
    def cookie_manager_with_key(self):
        """创建带指定密钥的Cookie管理器实例"""
        key = Fernet.generate_key().decode('utf-8')
        return MockCookieManager(key)
    
    @pytest.fixture
    def valid_xianyu_cookie(self):
        """有效的闲鱼Cookie字符串"""
        return "unb=test_user_123; session_id=test_session_456; _tb_token_=abc123def456; cookie_valid=true"
    
    @pytest.fixture
    def invalid_xianyu_cookie(self):
        """无效的闲鱼Cookie字符串"""
        return "invalid_cookie_format"
    
    @pytest.fixture
    def expired_xianyu_cookie(self):
        """过期的闲鱼Cookie字符串"""
        return "unb=test_user_123; session_id=expired_session; _tb_token_=expired123"

    def test_cookie_manager_initialization_default_key(self):
        """测试使用默认密钥初始化Cookie管理器"""
        # When: 创建Cookie管理器实例
        manager = MockCookieManager()

        # Then: 管理器应该正确初始化
        assert manager.encryption_key is not None
        assert manager.fernet is not None
        assert isinstance(manager._cookie_cache, dict)
        assert len(manager._cookie_cache) == 0

    def test_cookie_manager_initialization_custom_key(self):
        """测试使用自定义密钥初始化Cookie管理器"""
        # Given: 自定义加密密钥
        custom_key = Fernet.generate_key().decode('utf-8')

        # When: 创建Cookie管理器实例
        manager = MockCookieManager(custom_key)

        # Then: 管理器应该使用自定义密钥
        assert manager.encryption_key == custom_key.encode()
        assert manager.fernet is not None

    def test_encrypt_cookie_with_valid_string(self, cookie_manager, valid_xianyu_cookie):
        """测试加密有效的Cookie字符串 - 对应Gherkin场景：Cookie安全管理"""
        # Given: 有效的Cookie字符串
        cookie_str = valid_xianyu_cookie

        # When: 加密Cookie
        encrypted_cookie = cookie_manager.encrypt_cookie(cookie_str)

        # Then: 加密应该成功
        assert encrypted_cookie is not None
        assert encrypted_cookie != cookie_str
        assert len(encrypted_cookie) > 0
        # 验证是有效的base64字符串
        try:
            base64.b64decode(encrypted_cookie)
        except Exception:
            pytest.fail("加密结果不是有效的base64字符串")

    def test_encrypt_cookie_with_empty_string(self, cookie_manager):
        """测试加密空Cookie字符串"""
        # Given: 空Cookie字符串
        empty_cookie = ""

        # When & Then: 加密空字符串应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            cookie_manager.encrypt_cookie(empty_cookie)

        assert "Cookie字符串不能为空" in str(exc_info.value)

    def test_decrypt_cookie_with_valid_encrypted_string(self, cookie_manager, valid_xianyu_cookie):
        """测试解密有效的加密Cookie字符串"""
        # Given: 加密的Cookie字符串
        original_cookie = valid_xianyu_cookie
        encrypted_cookie = cookie_manager.encrypt_cookie(original_cookie)

        # When: 解密Cookie
        decrypted_cookie = cookie_manager.decrypt_cookie(encrypted_cookie)

        # Then: 解密应该成功，结果应该与原始字符串相同
        assert decrypted_cookie == original_cookie

    def test_decrypt_cookie_with_empty_string(self, cookie_manager):
        """测试解密空加密字符串"""
        # Given: 空加密字符串
        empty_encrypted = ""

        # When & Then: 解密空字符串应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            cookie_manager.decrypt_cookie(empty_encrypted)

        assert "加密Cookie字符串不能为空" in str(exc_info.value)

    def test_decrypt_cookie_with_invalid_encrypted_string(self, cookie_manager):
        """测试解密无效的加密字符串"""
        # Given: 无效的加密字符串
        invalid_encrypted = "invalid_encrypted_string"

        # When & Then: 解密无效字符串应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            cookie_manager.decrypt_cookie(invalid_encrypted)

        assert "Cookie解密失败" in str(exc_info.value)

    def test_mask_cookie_for_display_long_string(self, cookie_manager, valid_xianyu_cookie):
        """测试脱敏显示长Cookie字符串 - 对应Gherkin场景：Cookie安全管理"""
        # Given: 长Cookie字符串
        long_cookie = valid_xianyu_cookie

        # When: 脱敏显示Cookie
        masked_cookie = cookie_manager.mask_cookie_for_display(long_cookie)

        # Then: Cookie应该被正确脱敏
        assert masked_cookie.startswith(long_cookie[:10])
        assert masked_cookie.endswith(long_cookie[-10:])
        assert "*" in masked_cookie
        assert len(masked_cookie) == len(long_cookie)
        # 确保中间部分被星号替换
        middle_part = masked_cookie[10:-10]
        assert all(char == "*" for char in middle_part)

    def test_mask_cookie_for_display_short_string(self, cookie_manager):
        """测试脱敏显示短Cookie字符串"""
        # Given: 短Cookie字符串
        short_cookie = "short_cookie"

        # When: 脱敏显示Cookie
        masked_cookie = cookie_manager.mask_cookie_for_display(short_cookie)

        # Then: 整个字符串应该被星号替换
        assert masked_cookie == "*" * len(short_cookie)
        assert len(masked_cookie) == len(short_cookie)

    def test_mask_cookie_for_display_empty_string(self, cookie_manager):
        """测试脱敏显示空Cookie字符串"""
        # Given: 空Cookie字符串
        empty_cookie = ""

        # When: 脱敏显示Cookie
        masked_cookie = cookie_manager.mask_cookie_for_display(empty_cookie)

        # Then: 应该返回空字符串
        assert masked_cookie == ""

    def test_mask_cookie_for_display_custom_mask_char(self, cookie_manager, valid_xianyu_cookie):
        """测试使用自定义脱敏字符"""
        # Given: Cookie字符串和自定义脱敏字符
        cookie_str = valid_xianyu_cookie
        custom_mask_char = "#"

        # When: 使用自定义字符脱敏显示Cookie
        masked_cookie = cookie_manager.mask_cookie_for_display(cookie_str, custom_mask_char)

        # Then: 应该使用自定义字符进行脱敏
        assert custom_mask_char in masked_cookie
        assert "*" not in masked_cookie  # 不应该包含默认的星号

    def test_validate_cookie_format_valid_xianyu_cookie(self, cookie_manager, valid_xianyu_cookie):
        """测试验证有效的闲鱼Cookie格式"""
        # Given: 有效的闲鱼Cookie
        cookie_str = valid_xianyu_cookie

        # When: 验证Cookie格式
        is_valid = cookie_manager.validate_cookie_format(cookie_str, "xianyu")

        # Then: 验证应该成功
        assert is_valid is True

    def test_validate_cookie_format_invalid_xianyu_cookie(self, cookie_manager, invalid_xianyu_cookie):
        """测试验证无效的闲鱼Cookie格式"""
        # Given: 无效的闲鱼Cookie
        cookie_str = invalid_xianyu_cookie

        # When: 验证Cookie格式
        is_valid = cookie_manager.validate_cookie_format(cookie_str, "xianyu")

        # Then: 验证应该失败
        assert is_valid is False

    def test_validate_cookie_format_empty_cookie(self, cookie_manager):
        """测试验证空Cookie格式"""
        # Given: 空Cookie字符串
        empty_cookie = ""

        # When: 验证Cookie格式
        is_valid = cookie_manager.validate_cookie_format(empty_cookie)

        # Then: 验证应该失败
        assert is_valid is False

    def test_validate_cookie_format_missing_required_fields(self, cookie_manager):
        """测试验证缺少必要字段的闲鱼Cookie"""
        # Given: 缺少必要字段的Cookie
        incomplete_cookies = [
            "session_id=test_session",  # 缺少unb
            "unb=test_user",  # 缺少session_id
            "other_field=value",  # 缺少所有必要字段
        ]

        for cookie_str in incomplete_cookies:
            # When: 验证Cookie格式
            is_valid = cookie_manager.validate_cookie_format(cookie_str, "xianyu")

            # Then: 验证应该失败
            assert is_valid is False

    def test_validate_cookie_format_unknown_platform(self, cookie_manager):
        """测试验证未知平台的Cookie格式"""
        # Given: 未知平台和任意Cookie
        cookie_str = "any_key=any_value"
        unknown_platform = "unknown_platform"

        # When: 验证Cookie格式
        is_valid = cookie_manager.validate_cookie_format(cookie_str, unknown_platform)

        # Then: 验证应该成功（默认验证）
        assert is_valid is True

    def test_parse_cookie_string_valid_format(self, cookie_manager, valid_xianyu_cookie):
        """测试解析有效格式的Cookie字符串"""
        # Given: 有效格式的Cookie字符串
        cookie_str = valid_xianyu_cookie

        # When: 解析Cookie字符串
        cookie_dict = cookie_manager.parse_cookie_string(cookie_str)

        # Then: 解析应该成功
        assert isinstance(cookie_dict, dict)
        assert "unb" in cookie_dict
        assert "session_id" in cookie_dict
        assert "_tb_token_" in cookie_dict
        assert cookie_dict["unb"] == "test_user_123"
        assert cookie_dict["session_id"] == "test_session_456"

    def test_parse_cookie_string_empty_string(self, cookie_manager):
        """测试解析空Cookie字符串"""
        # Given: 空Cookie字符串
        empty_cookie = ""

        # When: 解析Cookie字符串
        cookie_dict = cookie_manager.parse_cookie_string(empty_cookie)

        # Then: 应该返回空字典
        assert cookie_dict == {}

    def test_parse_cookie_string_invalid_format(self, cookie_manager):
        """测试解析无效格式的Cookie字符串"""
        # Given: 无效格式的Cookie字符串
        invalid_cookie = "invalid format without equals"

        # When & Then: 解析无效格式应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            cookie_manager.parse_cookie_string(invalid_cookie)

        assert "Cookie字符串格式无效" in str(exc_info.value)

    def test_build_cookie_string_valid_dict(self, cookie_manager):
        """测试从有效字典构建Cookie字符串"""
        # Given: 有效的Cookie字典
        cookie_dict = {
            "unb": "test_user_123",
            "session_id": "test_session_456",
            "_tb_token_": "abc123def456"
        }

        # When: 构建Cookie字符串
        cookie_str = cookie_manager.build_cookie_string(cookie_dict)

        # Then: 构建应该成功
        assert "unb=test_user_123" in cookie_str
        assert "session_id=test_session_456" in cookie_str
        assert "_tb_token_=abc123def456" in cookie_str
        assert "; " in cookie_str  # 检查分隔符

    def test_build_cookie_string_empty_dict(self, cookie_manager):
        """测试从空字典构建Cookie字符串"""
        # Given: 空字典
        empty_dict = {}

        # When: 构建Cookie字符串
        cookie_str = cookie_manager.build_cookie_string(empty_dict)

        # Then: 应该返回空字符串
        assert cookie_str == ""

    def test_build_cookie_string_with_empty_values(self, cookie_manager):
        """测试从包含空值的字典构建Cookie字符串"""
        # Given: 包含空值的字典
        cookie_dict = {
            "valid_key": "valid_value",
            "empty_key": "",
            "none_key": None,
            "": "empty_key_name"
        }

        # When: 构建Cookie字符串
        cookie_str = cookie_manager.build_cookie_string(cookie_dict)

        # Then: 只有有效的键值对应该被包含
        assert "valid_key=valid_value" in cookie_str
        assert "empty_key=" not in cookie_str
        assert "none_key=" not in cookie_str
        assert "=empty_key_name" not in cookie_str

    def test_extract_user_info_xianyu_cookie(self, cookie_manager, valid_xianyu_cookie):
        """测试从闲鱼Cookie中提取用户信息"""
        # Given: 有效的闲鱼Cookie
        cookie_str = valid_xianyu_cookie

        # When: 提取用户信息
        user_info = cookie_manager.extract_user_info(cookie_str, "xianyu")

        # Then: 用户信息应该被正确提取
        assert isinstance(user_info, dict)
        assert user_info["user_id"] == "test_user_123"
        assert user_info["session_id"] == "test_session_456"
        assert user_info["token"] == "abc123def456"

    def test_extract_user_info_empty_cookie(self, cookie_manager):
        """测试从空Cookie中提取用户信息"""
        # Given: 空Cookie字符串
        empty_cookie = ""

        # When: 提取用户信息
        user_info = cookie_manager.extract_user_info(empty_cookie)

        # Then: 应该返回空字典
        assert user_info == {}

    def test_extract_user_info_unknown_platform(self, cookie_manager, valid_xianyu_cookie):
        """测试从未知平台Cookie中提取用户信息"""
        # Given: 未知平台
        cookie_str = valid_xianyu_cookie
        unknown_platform = "unknown_platform"

        # When: 提取用户信息
        user_info = cookie_manager.extract_user_info(cookie_str, unknown_platform)

        # Then: 应该返回空字典
        assert user_info == {}

    def test_is_cookie_expired_valid_cookie(self, cookie_manager, valid_xianyu_cookie):
        """测试检查有效Cookie是否过期"""
        # Given: 有效的Cookie
        cookie_str = valid_xianyu_cookie

        # When: 检查是否过期
        is_expired = cookie_manager.is_cookie_expired(cookie_str, "xianyu")

        # Then: Cookie应该未过期
        assert is_expired is False

    def test_is_cookie_expired_empty_cookie(self, cookie_manager):
        """测试检查空Cookie是否过期"""
        # Given: 空Cookie
        empty_cookie = ""

        # When: 检查是否过期
        is_expired = cookie_manager.is_cookie_expired(empty_cookie)

        # Then: 空Cookie应该被认为已过期
        assert is_expired is True

    def test_is_cookie_expired_with_expired_marker(self, cookie_manager):
        """测试检查包含过期标识的Cookie"""
        # Given: 包含过期标识的Cookie
        expired_cookie = "unb=test_user; session_id=expired_session; status=expired"

        # When: 检查是否过期
        is_expired = cookie_manager.is_cookie_expired(expired_cookie)

        # Then: Cookie应该被认为已过期
        assert is_expired is True

    def test_is_cookie_expired_missing_required_fields(self, cookie_manager):
        """测试检查缺少必要字段的Cookie是否过期"""
        # Given: 缺少必要字段的Cookie
        incomplete_cookie = "other_field=value"

        # When: 检查是否过期
        is_expired = cookie_manager.is_cookie_expired(incomplete_cookie, "xianyu")

        # Then: 缺少必要字段的Cookie应该被认为已过期
        assert is_expired is True

    def test_store_cookie_securely_valid_data(self, cookie_manager, valid_xianyu_cookie):
        """测试安全存储有效Cookie数据"""
        # Given: 有效的Cookie和标识符
        cookie_str = valid_xianyu_cookie
        identifier = "test_channel_001"

        # When: 安全存储Cookie
        encrypted_result = cookie_manager.store_cookie_securely(cookie_str, identifier)

        # Then: 存储应该成功
        assert encrypted_result is not None
        assert encrypted_result != cookie_str
        assert identifier in cookie_manager._cookie_cache

        # 验证存储的数据结构
        stored_data = cookie_manager._cookie_cache[identifier]
        assert "encrypted_cookie" in stored_data
        assert "stored_at" in stored_data
        assert "masked_display" in stored_data

    def test_store_cookie_securely_empty_data(self, cookie_manager):
        """测试安全存储空数据"""
        # Given: 空Cookie或标识符
        test_cases = [
            ("", "valid_identifier"),
            ("valid_cookie", ""),
            ("", ""),
        ]

        for cookie_str, identifier in test_cases:
            # When & Then: 存储空数据应该抛出验证错误
            with pytest.raises(ValidationError) as exc_info:
                cookie_manager.store_cookie_securely(cookie_str, identifier)

            assert "Cookie字符串和标识符不能为空" in str(exc_info.value)

    def test_retrieve_cookie_securely_existing_identifier(self, cookie_manager, valid_xianyu_cookie):
        """测试安全检索存在的Cookie"""
        # Given: 已存储的Cookie
        cookie_str = valid_xianyu_cookie
        identifier = "test_channel_002"
        cookie_manager.store_cookie_securely(cookie_str, identifier)

        # When: 安全检索Cookie
        retrieved_cookie = cookie_manager.retrieve_cookie_securely(identifier)

        # Then: 检索应该成功，结果应该与原始Cookie相同
        assert retrieved_cookie == cookie_str

    def test_retrieve_cookie_securely_nonexistent_identifier(self, cookie_manager):
        """测试安全检索不存在的Cookie"""
        # Given: 不存在的标识符
        nonexistent_identifier = "nonexistent_channel"

        # When: 安全检索Cookie
        retrieved_cookie = cookie_manager.retrieve_cookie_securely(nonexistent_identifier)

        # Then: 应该返回None
        assert retrieved_cookie is None

    def test_retrieve_cookie_securely_empty_identifier(self, cookie_manager):
        """测试使用空标识符安全检索Cookie"""
        # Given: 空标识符
        empty_identifier = ""

        # When & Then: 检索应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            cookie_manager.retrieve_cookie_securely(empty_identifier)

        assert "标识符不能为空" in str(exc_info.value)

    def test_get_cookie_display_info_existing_cookie(self, cookie_manager, valid_xianyu_cookie):
        """测试获取存在Cookie的显示信息"""
        # Given: 已存储的Cookie
        cookie_str = valid_xianyu_cookie
        identifier = "test_channel_003"
        cookie_manager.store_cookie_securely(cookie_str, identifier)

        # When: 获取显示信息
        display_info = cookie_manager.get_cookie_display_info(identifier)

        # Then: 显示信息应该正确
        assert display_info is not None
        assert display_info["identifier"] == identifier
        assert "masked_cookie" in display_info
        assert "stored_at" in display_info
        assert display_info["is_encrypted"] is True
        # 验证脱敏显示
        assert "*" in display_info["masked_cookie"]

    def test_get_cookie_display_info_nonexistent_cookie(self, cookie_manager):
        """测试获取不存在Cookie的显示信息"""
        # Given: 不存在的标识符
        nonexistent_identifier = "nonexistent_channel"

        # When: 获取显示信息
        display_info = cookie_manager.get_cookie_display_info(nonexistent_identifier)

        # Then: 应该返回None
        assert display_info is None

    def test_delete_cookie_existing_cookie(self, cookie_manager, valid_xianyu_cookie):
        """测试删除存在的Cookie"""
        # Given: 已存储的Cookie
        cookie_str = valid_xianyu_cookie
        identifier = "test_channel_004"
        cookie_manager.store_cookie_securely(cookie_str, identifier)

        # When: 删除Cookie
        result = cookie_manager.delete_cookie(identifier)

        # Then: 删除应该成功
        assert result is True
        assert identifier not in cookie_manager._cookie_cache

    def test_delete_cookie_nonexistent_cookie(self, cookie_manager):
        """测试删除不存在的Cookie"""
        # Given: 不存在的标识符
        nonexistent_identifier = "nonexistent_channel"

        # When: 删除Cookie
        result = cookie_manager.delete_cookie(nonexistent_identifier)

        # Then: 删除应该失败
        assert result is False

    def test_list_stored_cookies_multiple_cookies(self, cookie_manager, valid_xianyu_cookie):
        """测试列出多个存储的Cookie"""
        # Given: 多个已存储的Cookie
        cookies_data = [
            ("channel_001", valid_xianyu_cookie),
            ("channel_002", "unb=user2; session_id=session2"),
            ("channel_003", "unb=user3; session_id=session3"),
        ]

        for identifier, cookie_str in cookies_data:
            cookie_manager.store_cookie_securely(cookie_str, identifier)

        # When: 列出存储的Cookie
        cookie_list = cookie_manager.list_stored_cookies()

        # Then: 列表应该包含所有Cookie
        assert len(cookie_list) == 3
        identifiers = [item["identifier"] for item in cookie_list]
        assert "channel_001" in identifiers
        assert "channel_002" in identifiers
        assert "channel_003" in identifiers

        # 验证脱敏显示
        for item in cookie_list:
            assert "masked_cookie" in item
            assert "stored_at" in item
            assert "*" in item["masked_cookie"]

    def test_list_stored_cookies_empty_cache(self, cookie_manager):
        """测试列出空缓存的Cookie"""
        # Given: 空的Cookie缓存
        # When: 列出存储的Cookie
        cookie_list = cookie_manager.list_stored_cookies()

        # Then: 应该返回空列表
        assert cookie_list == []

    def test_validate_encryption_key_valid_key(self, cookie_manager):
        """测试验证有效的加密密钥"""
        # Given: 有效的Fernet密钥
        valid_key = Fernet.generate_key().decode('utf-8')

        # When: 验证密钥
        is_valid = cookie_manager.validate_encryption_key(valid_key)

        # Then: 验证应该成功
        assert is_valid is True

    def test_validate_encryption_key_invalid_key(self, cookie_manager):
        """测试验证无效的加密密钥"""
        # Given: 无效的密钥
        invalid_keys = [
            "invalid_key",
            "too_short",
            "",
            "not_base64_encoded_key",
        ]

        for invalid_key in invalid_keys:
            # When: 验证密钥
            is_valid = cookie_manager.validate_encryption_key(invalid_key)

            # Then: 验证应该失败
            assert is_valid is False

    def test_generate_new_encryption_key(self, cookie_manager):
        """测试生成新的加密密钥"""
        # When: 生成新密钥
        new_key = cookie_manager.generate_new_encryption_key()

        # Then: 新密钥应该有效
        assert new_key is not None
        assert isinstance(new_key, str)
        assert len(new_key) > 0

        # 验证生成的密钥是有效的Fernet密钥
        assert cookie_manager.validate_encryption_key(new_key) is True


class TestCookieManagerEdgeCases:
    """Cookie管理工具边界情况测试"""

    @pytest.fixture
    def cookie_manager_edge_cases(self):
        """边界情况测试的Cookie管理器"""
        return MockCookieManager()

    def test_cookie_encryption_decryption_round_trip(self, cookie_manager_edge_cases):
        """测试Cookie加密解密往返过程"""
        # Given: 各种类型的Cookie字符串
        test_cookies = [
            "simple=value",
            "unb=user123; session_id=session456; token=abc123",
            "complex=value with spaces and special chars @#$%",
            "unicode=测试中文内容🚀",
            "long=" + "x" * 1000,  # 长字符串
        ]

        for original_cookie in test_cookies:
            # When: 加密然后解密
            encrypted = cookie_manager_edge_cases.encrypt_cookie(original_cookie)
            decrypted = cookie_manager_edge_cases.decrypt_cookie(encrypted)

            # Then: 解密结果应该与原始字符串相同
            assert decrypted == original_cookie

    def test_cookie_masking_various_lengths(self, cookie_manager_edge_cases):
        """测试各种长度Cookie的脱敏处理"""
        # Given: 不同长度的Cookie字符串
        test_cases = [
            ("", ""),  # 空字符串
            ("a", "*"),  # 单字符
            ("short", "*****"),  # 短字符串
            ("exactly_twenty_char", "********************"),  # 恰好20字符
            ("this_is_longer_than_twenty_characters", "this_is_lo**********characters"),  # 长字符串
        ]

        for original, expected in test_cases:
            # When: 脱敏处理
            masked = cookie_manager_edge_cases.mask_cookie_for_display(original)

            # Then: 结果应该符合预期
            assert masked == expected

    def test_cookie_parsing_edge_cases(self, cookie_manager_edge_cases):
        """测试Cookie解析的边界情况"""
        # Given: 各种边界情况的Cookie字符串
        edge_cases = [
            ("key=value", {"key": "value"}),  # 单个键值对
            ("key1=value1; key2=value2", {"key1": "value1", "key2": "value2"}),  # 多个键值对
            ("key=", {"key": ""}),  # 空值
            ("key=value;", {"key": "value"}),  # 末尾分号
            (" key = value ", {"key": "value"}),  # 前后空格
            ("key1=value1;key2=value2", {"key1": "value1", "key2": "value2"}),  # 无空格分隔
        ]

        for cookie_str, expected in edge_cases:
            # When: 解析Cookie
            result = cookie_manager_edge_cases.parse_cookie_string(cookie_str)

            # Then: 结果应该符合预期
            assert result == expected

    def test_concurrent_cookie_operations(self, cookie_manager_edge_cases, valid_xianyu_cookie):
        """测试并发Cookie操作"""
        import threading
        import time

        # Given: 并发操作场景
        results = []
        errors = []

        def store_and_retrieve(identifier_suffix):
            try:
                identifier = f"concurrent_test_{identifier_suffix}"
                # 存储Cookie
                cookie_manager_edge_cases.store_cookie_securely(valid_xianyu_cookie, identifier)
                time.sleep(0.01)  # 模拟一些处理时间
                # 检索Cookie
                retrieved = cookie_manager_edge_cases.retrieve_cookie_securely(identifier)
                results.append((identifier, retrieved == valid_xianyu_cookie))
            except Exception as e:
                errors.append(str(e))

        # When: 并发执行多个操作
        threads = []
        for i in range(10):
            thread = threading.Thread(target=store_and_retrieve, args=(i,))
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # Then: 所有操作都应该成功
        assert len(errors) == 0
        assert len(results) == 10
        for identifier, success in results:
            assert success is True
