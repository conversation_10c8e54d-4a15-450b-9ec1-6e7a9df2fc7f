"""
状态监控服务单元测试

基于Gherkin剧本中的渠道监控场景，测试状态监控服务的核心功能：
- 状态检测：实时监控渠道连接状态
- 异常处理：检测和处理连接异常
- 通知机制：状态变更时的通知功能
- 健康检查：定期健康状态检查
- 性能监控：响应时间和性能指标监控
- 告警机制：异常状态的告警处理
- 状态历史：状态变更历史记录
- 批量监控：多渠道并发监控
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Callable
from unittest.mock import AsyncMock, MagicMock

import pytest

from app.core.exceptions import (
    BusinessLogicError,
    NotFoundError,
    ValidationError,
)


class ChannelStatus(Enum):
    """渠道状态枚举"""
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    ERROR = "error"
    UNKNOWN = "unknown"


class MonitoringEvent(Enum):
    """监控事件类型"""
    STATUS_CHANGED = "status_changed"
    CONNECTION_LOST = "connection_lost"
    CONNECTION_RESTORED = "connection_restored"
    HEALTH_CHECK_FAILED = "health_check_failed"
    PERFORMANCE_DEGRADED = "performance_degraded"


class MockStatusMonitor:
    """
    模拟状态监控服务实现
    
    提供完整的状态监控功能，包括状态检测、异常处理、通知机制等
    """
    
    def __init__(self):
        """初始化状态监控服务"""
        self._channels = {}  # 渠道状态存储
        self._status_history = {}  # 状态历史记录
        self._event_listeners = []  # 事件监听器
        self._health_check_results = {}  # 健康检查结果
        self._performance_metrics = {}  # 性能指标
        self._monitoring_active = False  # 监控是否激活
        self._check_interval = 30  # 检查间隔（秒）
        self._alert_thresholds = {
            "response_time_ms": 5000,  # 响应时间阈值
            "error_rate_percent": 10,  # 错误率阈值
            "consecutive_failures": 3,  # 连续失败次数阈值
        }
    
    async def start_monitoring(self) -> bool:
        """启动监控服务"""
        if self._monitoring_active:
            raise BusinessLogicError("监控服务已经在运行")
        
        self._monitoring_active = True
        return True
    
    async def stop_monitoring(self) -> bool:
        """停止监控服务"""
        if not self._monitoring_active:
            raise BusinessLogicError("监控服务未运行")
        
        self._monitoring_active = False
        return True
    
    async def add_channel(self, channel_id: str, channel_config: Dict[str, Any]) -> bool:
        """添加渠道到监控列表"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        if not channel_config:
            raise ValidationError("渠道配置不能为空")
        
        if channel_id in self._channels:
            raise BusinessLogicError(f"渠道 '{channel_id}' 已在监控列表中")
        
        # 初始化渠道状态
        now = datetime.utcnow()
        self._channels[channel_id] = {
            "id": channel_id,
            "config": channel_config,
            "status": ChannelStatus.UNKNOWN,
            "last_check": None,
            "last_status_change": now,
            "consecutive_failures": 0,
            "total_checks": 0,
            "successful_checks": 0,
            "created_at": now,
            "updated_at": now,
        }
        
        # 初始化状态历史
        self._status_history[channel_id] = []
        
        # 初始化性能指标
        self._performance_metrics[channel_id] = {
            "avg_response_time_ms": 0,
            "min_response_time_ms": float('inf'),
            "max_response_time_ms": 0,
            "error_rate_percent": 0,
            "uptime_percent": 100,
            "last_error": None,
        }
        
        return True
    
    async def remove_channel(self, channel_id: str) -> bool:
        """从监控列表中移除渠道"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        if channel_id not in self._channels:
            raise NotFoundError(f"渠道 '{channel_id}' 不在监控列表中")
        
        # 清理相关数据
        del self._channels[channel_id]
        del self._status_history[channel_id]
        del self._performance_metrics[channel_id]
        if channel_id in self._health_check_results:
            del self._health_check_results[channel_id]
        
        return True
    
    async def check_channel_status(self, channel_id: str) -> ChannelStatus:
        """检查单个渠道状态 - 对应Gherkin场景：监控渠道状态"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        if channel_id not in self._channels:
            raise NotFoundError(f"渠道 '{channel_id}' 不在监控列表中")
        
        channel = self._channels[channel_id]
        
        # 模拟状态检查逻辑
        try:
            # 模拟网络检查
            check_start = datetime.utcnow()
            
            # 根据配置模拟不同的检查结果
            config = channel["config"]
            if config.get("simulate_failure", False):
                new_status = ChannelStatus.ERROR
                response_time_ms = 0
            elif config.get("simulate_slow_response", False):
                new_status = ChannelStatus.CONNECTED
                response_time_ms = 8000  # 慢响应
            else:
                new_status = ChannelStatus.CONNECTED
                response_time_ms = 150  # 正常响应
            
            check_end = datetime.utcnow()
            actual_response_time = (check_end - check_start).total_seconds() * 1000
            
            # 更新渠道状态
            old_status = channel["status"]
            channel["status"] = new_status
            channel["last_check"] = check_end
            channel["total_checks"] += 1
            channel["updated_at"] = check_end
            
            if new_status == ChannelStatus.CONNECTED:
                channel["successful_checks"] += 1
                channel["consecutive_failures"] = 0
            else:
                channel["consecutive_failures"] += 1
            
            # 记录状态历史
            if old_status != new_status:
                channel["last_status_change"] = check_end
                self._record_status_change(channel_id, old_status, new_status, check_end)
            
            # 更新性能指标
            self._update_performance_metrics(channel_id, response_time_ms, new_status == ChannelStatus.CONNECTED)
            
            # 触发事件
            if old_status != new_status:
                await self._trigger_event(MonitoringEvent.STATUS_CHANGED, {
                    "channel_id": channel_id,
                    "old_status": old_status.value if old_status else None,
                    "new_status": new_status.value,
                    "timestamp": check_end,
                })
            
            return new_status
            
        except Exception as e:
            # 处理检查异常
            error_time = datetime.utcnow()
            channel["status"] = ChannelStatus.ERROR
            channel["last_check"] = error_time
            channel["total_checks"] += 1
            channel["consecutive_failures"] += 1
            channel["updated_at"] = error_time
            
            # 记录错误
            self._performance_metrics[channel_id]["last_error"] = str(e)
            
            # 触发错误事件
            await self._trigger_event(MonitoringEvent.HEALTH_CHECK_FAILED, {
                "channel_id": channel_id,
                "error": str(e),
                "timestamp": error_time,
            })
            
            return ChannelStatus.ERROR
    
    async def check_all_channels(self) -> Dict[str, ChannelStatus]:
        """检查所有渠道状态"""
        if not self._monitoring_active:
            raise BusinessLogicError("监控服务未运行")
        
        results = {}
        for channel_id in self._channels.keys():
            try:
                status = await self.check_channel_status(channel_id)
                results[channel_id] = status
            except Exception as e:
                results[channel_id] = ChannelStatus.ERROR
        
        return results
    
    async def get_channel_status(self, channel_id: str) -> Dict[str, Any]:
        """获取渠道状态信息"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        if channel_id not in self._channels:
            raise NotFoundError(f"渠道 '{channel_id}' 不在监控列表中")
        
        channel = self._channels[channel_id].copy()
        channel["status"] = channel["status"].value
        return channel
    
    async def get_all_channels_status(self) -> List[Dict[str, Any]]:
        """获取所有渠道状态信息"""
        results = []
        for channel in self._channels.values():
            channel_copy = channel.copy()
            channel_copy["status"] = channel_copy["status"].value
            results.append(channel_copy)
        
        return results
    
    async def get_status_history(self, channel_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """获取渠道状态历史"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        if channel_id not in self._channels:
            raise NotFoundError(f"渠道 '{channel_id}' 不在监控列表中")
        
        if limit <= 0:
            raise ValidationError("限制数量必须大于0")
        
        history = self._status_history.get(channel_id, [])
        return history[-limit:] if limit < len(history) else history
    
    async def get_performance_metrics(self, channel_id: str) -> Dict[str, Any]:
        """获取渠道性能指标"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        if channel_id not in self._channels:
            raise NotFoundError(f"渠道 '{channel_id}' 不在监控列表中")
        
        return self._performance_metrics.get(channel_id, {}).copy()
    
    async def set_alert_thresholds(self, thresholds: Dict[str, Any]) -> bool:
        """设置告警阈值"""
        if not thresholds:
            raise ValidationError("阈值配置不能为空")
        
        # 验证阈值配置
        valid_keys = {"response_time_ms", "error_rate_percent", "consecutive_failures"}
        for key in thresholds:
            if key not in valid_keys:
                raise ValidationError(f"无效的阈值配置项: {key}")
            
            if not isinstance(thresholds[key], (int, float)) or thresholds[key] < 0:
                raise ValidationError(f"阈值 '{key}' 必须是非负数")
        
        self._alert_thresholds.update(thresholds)
        return True
    
    async def get_alert_thresholds(self) -> Dict[str, Any]:
        """获取告警阈值"""
        return self._alert_thresholds.copy()
    
    def add_event_listener(self, listener: Callable) -> bool:
        """添加事件监听器"""
        if not callable(listener):
            raise ValidationError("监听器必须是可调用对象")
        
        if listener not in self._event_listeners:
            self._event_listeners.append(listener)
        
        return True
    
    def remove_event_listener(self, listener: Callable) -> bool:
        """移除事件监听器"""
        if listener in self._event_listeners:
            self._event_listeners.remove(listener)
            return True
        return False
    
    async def _trigger_event(self, event_type: MonitoringEvent, event_data: Dict[str, Any]) -> None:
        """触发监控事件"""
        event = {
            "type": event_type.value,
            "data": event_data,
            "timestamp": datetime.utcnow(),
        }
        
        # 通知所有监听器
        for listener in self._event_listeners:
            try:
                if asyncio.iscoroutinefunction(listener):
                    await listener(event)
                else:
                    listener(event)
            except Exception:
                # 忽略监听器异常，避免影响主流程
                pass
    
    def _record_status_change(self, channel_id: str, old_status: ChannelStatus, new_status: ChannelStatus, timestamp: datetime) -> None:
        """记录状态变更历史"""
        if channel_id not in self._status_history:
            self._status_history[channel_id] = []
        
        self._status_history[channel_id].append({
            "old_status": old_status.value if old_status else None,
            "new_status": new_status.value,
            "timestamp": timestamp,
        })
    
    def _update_performance_metrics(self, channel_id: str, response_time_ms: float, success: bool) -> None:
        """更新性能指标"""
        metrics = self._performance_metrics[channel_id]
        channel = self._channels[channel_id]
        
        # 更新响应时间指标
        if success:
            if metrics["avg_response_time_ms"] == 0:
                metrics["avg_response_time_ms"] = response_time_ms
            else:
                # 简单移动平均
                metrics["avg_response_time_ms"] = (metrics["avg_response_time_ms"] + response_time_ms) / 2
            
            metrics["min_response_time_ms"] = min(metrics["min_response_time_ms"], response_time_ms)
            metrics["max_response_time_ms"] = max(metrics["max_response_time_ms"], response_time_ms)
        
        # 更新错误率
        if channel["total_checks"] > 0:
            metrics["error_rate_percent"] = ((channel["total_checks"] - channel["successful_checks"]) / channel["total_checks"]) * 100
        
        # 更新可用性
        if channel["total_checks"] > 0:
            metrics["uptime_percent"] = (channel["successful_checks"] / channel["total_checks"]) * 100
    
    def clear_all_data(self) -> None:
        """清空所有数据（仅用于测试）"""
        self._channels.clear()
        self._status_history.clear()
        self._event_listeners.clear()
        self._health_check_results.clear()
        self._performance_metrics.clear()
        self._monitoring_active = False


class TestStatusMonitor:
    """状态监控服务测试套件"""
    
    @pytest.fixture
    def status_monitor(self):
        """创建状态监控服务实例"""
        return MockStatusMonitor()
    
    @pytest.fixture
    def sample_channel_config(self):
        """示例渠道配置"""
        return {
            "name": "测试渠道",
            "platform": "xianyu",
            "endpoint": "https://api.example.com",
            "timeout": 30,
        }
    
    @pytest.fixture
    def failing_channel_config(self):
        """失败渠道配置"""
        return {
            "name": "失败渠道",
            "platform": "xianyu",
            "endpoint": "https://api.example.com",
            "timeout": 30,
            "simulate_failure": True,
        }
    
    @pytest.mark.asyncio
    async def test_start_monitoring(self, status_monitor):
        """测试启动监控服务"""
        # Given: 未启动的监控服务
        assert status_monitor._monitoring_active is False
        
        # When: 启动监控服务
        result = await status_monitor.start_monitoring()
        
        # Then: 启动应该成功
        assert result is True
        assert status_monitor._monitoring_active is True
    
    @pytest.mark.asyncio
    async def test_start_monitoring_already_running(self, status_monitor):
        """测试启动已运行的监控服务"""
        # Given: 已启动的监控服务
        await status_monitor.start_monitoring()
        
        # When & Then: 再次启动应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await status_monitor.start_monitoring()
        
        assert "监控服务已经在运行" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_stop_monitoring(self, status_monitor):
        """测试停止监控服务"""
        # Given: 已启动的监控服务
        await status_monitor.start_monitoring()

        # When: 停止监控服务
        result = await status_monitor.stop_monitoring()

        # Then: 停止应该成功
        assert result is True
        assert status_monitor._monitoring_active is False

    @pytest.mark.asyncio
    async def test_stop_monitoring_not_running(self, status_monitor):
        """测试停止未运行的监控服务"""
        # Given: 未启动的监控服务
        assert status_monitor._monitoring_active is False

        # When & Then: 停止应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await status_monitor.stop_monitoring()

        assert "监控服务未运行" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_add_channel(self, status_monitor, sample_channel_config):
        """测试添加渠道到监控列表"""
        # Given: 渠道ID和配置
        channel_id = str(uuid.uuid4())

        # When: 添加渠道
        result = await status_monitor.add_channel(channel_id, sample_channel_config)

        # Then: 添加应该成功
        assert result is True
        assert channel_id in status_monitor._channels

        # 验证渠道初始状态
        channel = status_monitor._channels[channel_id]
        assert channel["id"] == channel_id
        assert channel["config"] == sample_channel_config
        assert channel["status"] == ChannelStatus.UNKNOWN
        assert channel["consecutive_failures"] == 0
        assert channel["total_checks"] == 0
        assert channel["successful_checks"] == 0

    @pytest.mark.asyncio
    async def test_add_channel_empty_id(self, status_monitor, sample_channel_config):
        """测试使用空ID添加渠道"""
        # Given: 空渠道ID
        empty_id = ""

        # When & Then: 添加应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await status_monitor.add_channel(empty_id, sample_channel_config)

        assert "渠道ID不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_add_channel_empty_config(self, status_monitor):
        """测试使用空配置添加渠道"""
        # Given: 渠道ID和空配置
        channel_id = str(uuid.uuid4())
        empty_config = {}

        # When & Then: 添加应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await status_monitor.add_channel(channel_id, empty_config)

        assert "渠道配置不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_add_duplicate_channel(self, status_monitor, sample_channel_config):
        """测试添加重复渠道"""
        # Given: 已添加的渠道
        channel_id = str(uuid.uuid4())
        await status_monitor.add_channel(channel_id, sample_channel_config)

        # When & Then: 再次添加应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await status_monitor.add_channel(channel_id, sample_channel_config)

        assert f"渠道 '{channel_id}' 已在监控列表中" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_remove_channel(self, status_monitor, sample_channel_config):
        """测试从监控列表中移除渠道"""
        # Given: 已添加的渠道
        channel_id = str(uuid.uuid4())
        await status_monitor.add_channel(channel_id, sample_channel_config)

        # When: 移除渠道
        result = await status_monitor.remove_channel(channel_id)

        # Then: 移除应该成功
        assert result is True
        assert channel_id not in status_monitor._channels
        assert channel_id not in status_monitor._status_history
        assert channel_id not in status_monitor._performance_metrics

    @pytest.mark.asyncio
    async def test_remove_nonexistent_channel(self, status_monitor):
        """测试移除不存在的渠道"""
        # Given: 不存在的渠道ID
        nonexistent_id = str(uuid.uuid4())

        # When & Then: 移除应该抛出未找到错误
        with pytest.raises(NotFoundError) as exc_info:
            await status_monitor.remove_channel(nonexistent_id)

        assert f"渠道 '{nonexistent_id}' 不在监控列表中" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_check_channel_status_success(self, status_monitor, sample_channel_config):
        """测试检查渠道状态（成功）- 对应Gherkin场景：监控渠道状态"""
        # Given: 已添加的正常渠道
        channel_id = str(uuid.uuid4())
        await status_monitor.add_channel(channel_id, sample_channel_config)

        # When: 检查渠道状态
        status = await status_monitor.check_channel_status(channel_id)

        # Then: 状态应该是连接成功
        assert status == ChannelStatus.CONNECTED

        # 验证渠道状态更新
        channel = status_monitor._channels[channel_id]
        assert channel["status"] == ChannelStatus.CONNECTED
        assert channel["last_check"] is not None
        assert channel["total_checks"] == 1
        assert channel["successful_checks"] == 1
        assert channel["consecutive_failures"] == 0

    @pytest.mark.asyncio
    async def test_check_channel_status_failure(self, status_monitor, failing_channel_config):
        """测试检查渠道状态（失败）"""
        # Given: 已添加的失败渠道
        channel_id = str(uuid.uuid4())
        await status_monitor.add_channel(channel_id, failing_channel_config)

        # When: 检查渠道状态
        status = await status_monitor.check_channel_status(channel_id)

        # Then: 状态应该是错误
        assert status == ChannelStatus.ERROR

        # 验证渠道状态更新
        channel = status_monitor._channels[channel_id]
        assert channel["status"] == ChannelStatus.ERROR
        assert channel["total_checks"] == 1
        assert channel["successful_checks"] == 0
        assert channel["consecutive_failures"] == 1

    @pytest.mark.asyncio
    async def test_check_channel_status_nonexistent(self, status_monitor):
        """测试检查不存在渠道的状态"""
        # Given: 不存在的渠道ID
        nonexistent_id = str(uuid.uuid4())

        # When & Then: 检查应该抛出未找到错误
        with pytest.raises(NotFoundError) as exc_info:
            await status_monitor.check_channel_status(nonexistent_id)

        assert f"渠道 '{nonexistent_id}' 不在监控列表中" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_check_all_channels(self, status_monitor, sample_channel_config, failing_channel_config):
        """测试检查所有渠道状态"""
        # Given: 启动监控服务并添加多个渠道
        await status_monitor.start_monitoring()

        channel1_id = str(uuid.uuid4())
        channel2_id = str(uuid.uuid4())

        await status_monitor.add_channel(channel1_id, sample_channel_config)
        await status_monitor.add_channel(channel2_id, failing_channel_config)

        # When: 检查所有渠道状态
        results = await status_monitor.check_all_channels()

        # Then: 应该返回所有渠道的状态
        assert len(results) == 2
        assert channel1_id in results
        assert channel2_id in results
        assert results[channel1_id] == ChannelStatus.CONNECTED
        assert results[channel2_id] == ChannelStatus.ERROR

    @pytest.mark.asyncio
    async def test_check_all_channels_not_running(self, status_monitor, sample_channel_config):
        """测试在监控服务未运行时检查所有渠道"""
        # Given: 未启动的监控服务
        channel_id = str(uuid.uuid4())
        await status_monitor.add_channel(channel_id, sample_channel_config)

        # When & Then: 检查应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await status_monitor.check_all_channels()

        assert "监控服务未运行" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_channel_status(self, status_monitor, sample_channel_config):
        """测试获取渠道状态信息"""
        # Given: 已添加并检查过的渠道
        channel_id = str(uuid.uuid4())
        await status_monitor.add_channel(channel_id, sample_channel_config)
        await status_monitor.check_channel_status(channel_id)

        # When: 获取渠道状态信息
        status_info = await status_monitor.get_channel_status(channel_id)

        # Then: 应该返回完整的状态信息
        assert status_info["id"] == channel_id
        assert status_info["config"] == sample_channel_config
        assert status_info["status"] == ChannelStatus.CONNECTED.value
        assert status_info["last_check"] is not None
        assert status_info["total_checks"] == 1
        assert status_info["successful_checks"] == 1
        assert status_info["consecutive_failures"] == 0

    @pytest.mark.asyncio
    async def test_get_all_channels_status(self, status_monitor, sample_channel_config):
        """测试获取所有渠道状态信息"""
        # Given: 多个已添加的渠道
        channel1_id = str(uuid.uuid4())
        channel2_id = str(uuid.uuid4())

        await status_monitor.add_channel(channel1_id, sample_channel_config)
        await status_monitor.add_channel(channel2_id, sample_channel_config)

        # When: 获取所有渠道状态信息
        all_status = await status_monitor.get_all_channels_status()

        # Then: 应该返回所有渠道的状态信息
        assert len(all_status) == 2
        channel_ids = [status["id"] for status in all_status]
        assert channel1_id in channel_ids
        assert channel2_id in channel_ids

    @pytest.mark.asyncio
    async def test_get_status_history(self, status_monitor, sample_channel_config, failing_channel_config):
        """测试获取渠道状态历史"""
        # Given: 已添加的渠道，进行多次状态检查
        channel_id = str(uuid.uuid4())
        await status_monitor.add_channel(channel_id, sample_channel_config)

        # 第一次检查（成功）
        await status_monitor.check_channel_status(channel_id)

        # 修改配置为失败模式
        status_monitor._channels[channel_id]["config"]["simulate_failure"] = True

        # 第二次检查（失败）
        await status_monitor.check_channel_status(channel_id)

        # When: 获取状态历史
        history = await status_monitor.get_status_history(channel_id)

        # Then: 应该包含状态变更记录
        assert len(history) == 2  # 两次状态变更（UNKNOWN -> CONNECTED -> ERROR）

        # 验证第一次状态变更（UNKNOWN -> CONNECTED）
        assert history[0]["old_status"] == ChannelStatus.UNKNOWN.value
        assert history[0]["new_status"] == ChannelStatus.CONNECTED.value
        assert history[0]["timestamp"] is not None

        # 验证第二次状态变更（CONNECTED -> ERROR）
        assert history[1]["old_status"] == ChannelStatus.CONNECTED.value
        assert history[1]["new_status"] == ChannelStatus.ERROR.value
        assert history[1]["timestamp"] is not None

    @pytest.mark.asyncio
    async def test_get_status_history_with_limit(self, status_monitor, sample_channel_config):
        """测试获取状态历史（带限制）"""
        # Given: 已添加的渠道
        channel_id = str(uuid.uuid4())
        await status_monitor.add_channel(channel_id, sample_channel_config)

        # 手动添加多条历史记录
        for i in range(10):
            status_monitor._record_status_change(
                channel_id,
                ChannelStatus.CONNECTED,
                ChannelStatus.ERROR,
                datetime.utcnow()
            )

        # When: 获取限制数量的历史记录
        history = await status_monitor.get_status_history(channel_id, limit=5)

        # Then: 应该返回最新的5条记录
        assert len(history) == 5

    @pytest.mark.asyncio
    async def test_get_status_history_invalid_limit(self, status_monitor, sample_channel_config):
        """测试使用无效限制获取状态历史"""
        # Given: 已添加的渠道
        channel_id = str(uuid.uuid4())
        await status_monitor.add_channel(channel_id, sample_channel_config)

        # When & Then: 使用无效限制应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await status_monitor.get_status_history(channel_id, limit=0)

        assert "限制数量必须大于0" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_performance_metrics(self, status_monitor, sample_channel_config):
        """测试获取渠道性能指标"""
        # Given: 已添加并检查过的渠道
        channel_id = str(uuid.uuid4())
        await status_monitor.add_channel(channel_id, sample_channel_config)
        await status_monitor.check_channel_status(channel_id)

        # When: 获取性能指标
        metrics = await status_monitor.get_performance_metrics(channel_id)

        # Then: 应该包含性能指标
        assert "avg_response_time_ms" in metrics
        assert "min_response_time_ms" in metrics
        assert "max_response_time_ms" in metrics
        assert "error_rate_percent" in metrics
        assert "uptime_percent" in metrics
        assert metrics["uptime_percent"] == 100  # 一次成功检查
        assert metrics["error_rate_percent"] == 0  # 无错误

    @pytest.mark.asyncio
    async def test_set_alert_thresholds(self, status_monitor):
        """测试设置告警阈值"""
        # Given: 新的阈值配置
        new_thresholds = {
            "response_time_ms": 3000,
            "error_rate_percent": 5,
            "consecutive_failures": 5,
        }

        # When: 设置告警阈值
        result = await status_monitor.set_alert_thresholds(new_thresholds)

        # Then: 设置应该成功
        assert result is True

        # 验证阈值已更新
        current_thresholds = await status_monitor.get_alert_thresholds()
        assert current_thresholds["response_time_ms"] == 3000
        assert current_thresholds["error_rate_percent"] == 5
        assert current_thresholds["consecutive_failures"] == 5

    @pytest.mark.asyncio
    async def test_set_alert_thresholds_empty(self, status_monitor):
        """测试设置空告警阈值"""
        # Given: 空阈值配置
        empty_thresholds = {}

        # When & Then: 设置应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await status_monitor.set_alert_thresholds(empty_thresholds)

        assert "阈值配置不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_set_alert_thresholds_invalid_key(self, status_monitor):
        """测试设置无效的告警阈值"""
        # Given: 包含无效键的阈值配置
        invalid_thresholds = {
            "invalid_key": 100,
        }

        # When & Then: 设置应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await status_monitor.set_alert_thresholds(invalid_thresholds)

        assert "无效的阈值配置项: invalid_key" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_set_alert_thresholds_negative_value(self, status_monitor):
        """测试设置负数告警阈值"""
        # Given: 包含负数的阈值配置
        negative_thresholds = {
            "response_time_ms": -100,
        }

        # When & Then: 设置应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await status_monitor.set_alert_thresholds(negative_thresholds)

        assert "阈值 'response_time_ms' 必须是非负数" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_event_listener_functionality(self, status_monitor, sample_channel_config):
        """测试事件监听器功能"""
        # Given: 事件监听器
        events_received = []

        def event_listener(event):
            events_received.append(event)

        # 添加事件监听器
        status_monitor.add_event_listener(event_listener)

        # 添加渠道并检查状态（触发状态变更事件）
        channel_id = str(uuid.uuid4())
        await status_monitor.add_channel(channel_id, sample_channel_config)
        await status_monitor.check_channel_status(channel_id)

        # When: 修改配置并再次检查（触发状态变更）
        status_monitor._channels[channel_id]["config"]["simulate_failure"] = True
        await status_monitor.check_channel_status(channel_id)

        # Then: 应该收到状态变更事件
        assert len(events_received) == 2  # UNKNOWN->CONNECTED, CONNECTED->ERROR

        # 验证事件内容
        status_change_events = [e for e in events_received if e["type"] == MonitoringEvent.STATUS_CHANGED.value]
        assert len(status_change_events) == 2

        # 验证第一个事件（UNKNOWN -> CONNECTED）
        first_event = status_change_events[0]
        assert first_event["data"]["channel_id"] == channel_id
        assert first_event["data"]["old_status"] == ChannelStatus.UNKNOWN.value
        assert first_event["data"]["new_status"] == ChannelStatus.CONNECTED.value

        # 验证第二个事件（CONNECTED -> ERROR）
        second_event = status_change_events[1]
        assert second_event["data"]["channel_id"] == channel_id
        assert second_event["data"]["old_status"] == ChannelStatus.CONNECTED.value
        assert second_event["data"]["new_status"] == ChannelStatus.ERROR.value

    @pytest.mark.asyncio
    async def test_add_invalid_event_listener(self, status_monitor):
        """测试添加无效的事件监听器"""
        # Given: 非可调用对象
        invalid_listener = "not_callable"

        # When & Then: 添加应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            status_monitor.add_event_listener(invalid_listener)

        assert "监听器必须是可调用对象" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_remove_event_listener(self, status_monitor):
        """测试移除事件监听器"""
        # Given: 已添加的事件监听器
        def event_listener(event):
            pass

        status_monitor.add_event_listener(event_listener)
        assert event_listener in status_monitor._event_listeners

        # When: 移除事件监听器
        result = status_monitor.remove_event_listener(event_listener)

        # Then: 移除应该成功
        assert result is True
        assert event_listener not in status_monitor._event_listeners

    @pytest.mark.asyncio
    async def test_remove_nonexistent_event_listener(self, status_monitor):
        """测试移除不存在的事件监听器"""
        # Given: 不存在的事件监听器
        def nonexistent_listener(event):
            pass

        # When: 移除不存在的监听器
        result = status_monitor.remove_event_listener(nonexistent_listener)

        # Then: 应该返回False
        assert result is False


class TestStatusMonitorEdgeCases:
    """状态监控服务边界情况测试"""

    @pytest.fixture
    def status_monitor_edge_cases(self):
        """边界情况测试的状态监控服务实例"""
        return MockStatusMonitor()

    @pytest.mark.asyncio
    async def test_concurrent_channel_operations(self, status_monitor_edge_cases):
        """测试并发渠道操作"""
        import asyncio

        # Given: 多个渠道配置
        configs = []
        for i in range(5):
            configs.append({
                "name": f"并发渠道_{i}",
                "platform": "xianyu",
                "endpoint": f"https://api{i}.example.com",
                "timeout": 30,
            })

        # When: 并发添加渠道
        async def add_channel(index, config):
            channel_id = f"concurrent_channel_{index}"
            return await status_monitor_edge_cases.add_channel(channel_id, config)

        tasks = [add_channel(i, config) for i, config in enumerate(configs)]
        results = await asyncio.gather(*tasks)

        # Then: 所有操作都应该成功
        assert all(results)
        assert len(status_monitor_edge_cases._channels) == 5

    @pytest.mark.asyncio
    async def test_status_monitoring_with_large_dataset(self, status_monitor_edge_cases):
        """测试大量渠道的状态监控"""
        # Given: 大量渠道
        channels_count = 50

        for i in range(channels_count):
            channel_id = f"large_dataset_channel_{i}"
            config = {
                "name": f"大量数据渠道_{i}",
                "platform": "xianyu",
                "endpoint": f"https://api{i}.example.com",
                "timeout": 30,
            }
            await status_monitor_edge_cases.add_channel(channel_id, config)

        # When: 启动监控并检查所有渠道
        await status_monitor_edge_cases.start_monitoring()
        start_time = datetime.utcnow()
        results = await status_monitor_edge_cases.check_all_channels()
        end_time = datetime.utcnow()

        # Then: 操作应该在合理时间内完成
        duration = (end_time - start_time).total_seconds()
        assert duration < 5.0  # 应该在5秒内完成
        assert len(results) == channels_count

        # 验证所有渠道都有状态
        for channel_id in results:
            assert results[channel_id] in [ChannelStatus.CONNECTED, ChannelStatus.ERROR]

    @pytest.mark.asyncio
    async def test_status_history_memory_management(self, status_monitor_edge_cases):
        """测试状态历史的内存管理"""
        # Given: 已添加的渠道
        channel_id = "memory_test_channel"
        config = {"name": "内存测试渠道", "platform": "xianyu", "endpoint": "https://api.example.com", "timeout": 30}
        await status_monitor_edge_cases.add_channel(channel_id, config)

        # When: 生成大量状态历史记录
        for i in range(1000):
            status_monitor_edge_cases._record_status_change(
                channel_id,
                ChannelStatus.CONNECTED if i % 2 == 0 else ChannelStatus.ERROR,
                ChannelStatus.ERROR if i % 2 == 0 else ChannelStatus.CONNECTED,
                datetime.utcnow()
            )

        # Then: 获取历史记录应该正常工作
        history = await status_monitor_edge_cases.get_status_history(channel_id, limit=100)
        assert len(history) == 100

        # 验证返回的是最新的记录
        all_history = await status_monitor_edge_cases.get_status_history(channel_id, limit=1000)
        assert len(all_history) == 1000

    @pytest.mark.asyncio
    async def test_performance_metrics_accuracy(self, status_monitor_edge_cases):
        """测试性能指标的准确性"""
        # Given: 已添加的渠道
        channel_id = "metrics_test_channel"
        config = {"name": "指标测试渠道", "platform": "xianyu", "endpoint": "https://api.example.com", "timeout": 30}
        await status_monitor_edge_cases.add_channel(channel_id, config)

        # When: 进行多次状态检查（成功和失败混合）
        success_count = 0
        total_count = 10

        for i in range(total_count):
            if i < 7:  # 前7次成功
                config["simulate_failure"] = False
                success_count += 1
            else:  # 后3次失败
                config["simulate_failure"] = True

            await status_monitor_edge_cases.check_channel_status(channel_id)

        # Then: 性能指标应该准确
        metrics = await status_monitor_edge_cases.get_performance_metrics(channel_id)

        # 验证可用性计算
        expected_uptime = (success_count / total_count) * 100
        assert abs(metrics["uptime_percent"] - expected_uptime) < 0.1

        # 验证错误率计算
        expected_error_rate = ((total_count - success_count) / total_count) * 100
        assert abs(metrics["error_rate_percent"] - expected_error_rate) < 0.1
