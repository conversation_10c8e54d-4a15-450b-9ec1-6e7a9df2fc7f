"""
重连机制单元测试

基于Gherkin剧本中的渠道监控场景，测试重连机制的核心功能：
- 自动重连：连接断开时的自动重连
- 重连策略：指数退避、最大重试次数等策略
- 失败处理：重连失败时的处理机制
- 连接恢复：成功重连后的状态恢复
- 重连配置：可配置的重连参数
- 重连历史：重连尝试的历史记录
- 并发重连：多渠道并发重连处理
- 重连通知：重连状态变更的通知机制
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Callable
from unittest.mock import AsyncMock, MagicMock

import pytest

from app.core.exceptions import (
    BusinessLogicError,
    NotFoundError,
    ValidationError,
)


class ReconnectionStatus(Enum):
    """重连状态枚举"""
    IDLE = "idle"
    RECONNECTING = "reconnecting"
    SUCCESS = "success"
    FAILED = "failed"
    EXHAUSTED = "exhausted"  # 重试次数耗尽


class ReconnectionStrategy(Enum):
    """重连策略枚举"""
    IMMEDIATE = "immediate"  # 立即重连
    FIXED_DELAY = "fixed_delay"  # 固定延迟
    EXPONENTIAL_BACKOFF = "exponential_backoff"  # 指数退避
    LINEAR_BACKOFF = "linear_backoff"  # 线性退避


class MockReconnectionManager:
    """
    模拟重连管理器实现
    
    提供完整的重连机制，包括自动重连、重连策略、失败处理等功能
    """
    
    def __init__(self):
        """初始化重连管理器"""
        self._channels = {}  # 渠道重连状态
        self._reconnection_configs = {}  # 重连配置
        self._reconnection_history = {}  # 重连历史
        self._event_listeners = []  # 事件监听器
        self._active_reconnections = set()  # 活跃的重连任务
        self._default_config = {
            "strategy": ReconnectionStrategy.EXPONENTIAL_BACKOFF,
            "max_attempts": 5,
            "initial_delay_seconds": 1,
            "max_delay_seconds": 60,
            "backoff_multiplier": 2.0,
            "jitter": True,  # 添加随机抖动
            "timeout_seconds": 30,
        }
    
    async def register_channel(self, channel_id: str, config: Optional[Dict[str, Any]] = None) -> bool:
        """注册渠道到重连管理器"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        if channel_id in self._channels:
            raise BusinessLogicError(f"渠道 '{channel_id}' 已注册")
        
        # 合并配置
        channel_config = self._default_config.copy()
        if config:
            channel_config.update(config)
        
        # 验证配置
        self._validate_config(channel_config)
        
        # 初始化渠道状态
        now = datetime.utcnow()
        self._channels[channel_id] = {
            "id": channel_id,
            "status": ReconnectionStatus.IDLE,
            "current_attempt": 0,
            "last_attempt": None,
            "next_attempt": None,
            "last_success": None,
            "last_failure": None,
            "total_attempts": 0,
            "successful_reconnections": 0,
            "created_at": now,
            "updated_at": now,
        }
        
        self._reconnection_configs[channel_id] = channel_config
        self._reconnection_history[channel_id] = []
        
        return True
    
    async def unregister_channel(self, channel_id: str) -> bool:
        """从重连管理器中注销渠道"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        if channel_id not in self._channels:
            raise NotFoundError(f"渠道 '{channel_id}' 未注册")
        
        # 停止活跃的重连任务
        if channel_id in self._active_reconnections:
            self._active_reconnections.remove(channel_id)
        
        # 清理数据
        del self._channels[channel_id]
        del self._reconnection_configs[channel_id]
        del self._reconnection_history[channel_id]
        
        return True
    
    async def trigger_reconnection(self, channel_id: str, reason: str = "manual") -> bool:
        """触发渠道重连 - 对应Gherkin场景：连接断开后自动重连"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        if channel_id not in self._channels:
            raise NotFoundError(f"渠道 '{channel_id}' 未注册")
        
        channel = self._channels[channel_id]
        config = self._reconnection_configs[channel_id]
        
        # 检查是否已在重连中
        if channel["status"] == ReconnectionStatus.RECONNECTING:
            raise BusinessLogicError(f"渠道 '{channel_id}' 正在重连中")
        
        # 检查是否已达到最大重试次数
        if channel["current_attempt"] >= config["max_attempts"]:
            channel["status"] = ReconnectionStatus.EXHAUSTED
            await self._trigger_event("reconnection_exhausted", {
                "channel_id": channel_id,
                "reason": "达到最大重试次数",
                "total_attempts": channel["total_attempts"],
            })
            return False
        
        # 开始重连
        channel["status"] = ReconnectionStatus.RECONNECTING
        channel["current_attempt"] += 1
        channel["total_attempts"] += 1
        channel["last_attempt"] = datetime.utcnow()
        channel["updated_at"] = datetime.utcnow()
        
        # 记录重连历史
        self._record_reconnection_attempt(channel_id, reason)
        
        # 添加到活跃重连集合
        self._active_reconnections.add(channel_id)
        
        # 触发重连开始事件
        await self._trigger_event("reconnection_started", {
            "channel_id": channel_id,
            "attempt": channel["current_attempt"],
            "reason": reason,
        })
        
        # 模拟重连过程
        success = await self._perform_reconnection(channel_id)
        
        if success:
            await self._handle_reconnection_success(channel_id)
        else:
            await self._handle_reconnection_failure(channel_id)
        
        # 从活跃重连集合中移除
        self._active_reconnections.discard(channel_id)
        
        return success
    
    async def _perform_reconnection(self, channel_id: str) -> bool:
        """执行实际的重连操作"""
        config = self._reconnection_configs[channel_id]
        
        try:
            # 模拟连接超时
            await asyncio.sleep(0.01)  # 模拟网络延迟
            
            # 根据配置模拟重连结果
            if config.get("simulate_failure", False):
                return False
            elif config.get("simulate_timeout", False):
                await asyncio.sleep(config["timeout_seconds"] + 1)
                return False
            else:
                return True
                
        except asyncio.TimeoutError:
            return False
        except Exception:
            return False
    
    async def _handle_reconnection_success(self, channel_id: str) -> None:
        """处理重连成功"""
        channel = self._channels[channel_id]
        now = datetime.utcnow()
        
        channel["status"] = ReconnectionStatus.SUCCESS
        channel["current_attempt"] = 0  # 重置重试计数
        channel["last_success"] = now
        channel["successful_reconnections"] += 1
        channel["updated_at"] = now
        
        # 记录成功历史
        self._record_reconnection_result(channel_id, True, "重连成功")
        
        # 触发成功事件
        await self._trigger_event("reconnection_success", {
            "channel_id": channel_id,
            "attempt": channel["total_attempts"],
            "duration": (now - channel["last_attempt"]).total_seconds(),
        })
    
    async def _handle_reconnection_failure(self, channel_id: str) -> None:
        """处理重连失败"""
        channel = self._channels[channel_id]
        config = self._reconnection_configs[channel_id]
        now = datetime.utcnow()
        
        channel["status"] = ReconnectionStatus.FAILED
        channel["last_failure"] = now
        channel["updated_at"] = now
        
        # 计算下次重连时间
        if channel["current_attempt"] < config["max_attempts"]:
            delay = self._calculate_next_delay(channel_id)
            channel["next_attempt"] = now + timedelta(seconds=delay)
        else:
            channel["status"] = ReconnectionStatus.EXHAUSTED
        
        # 记录失败历史
        self._record_reconnection_result(channel_id, False, "重连失败")
        
        # 触发失败事件
        await self._trigger_event("reconnection_failed", {
            "channel_id": channel_id,
            "attempt": channel["current_attempt"],
            "next_attempt": channel.get("next_attempt"),
        })
    
    def _calculate_next_delay(self, channel_id: str) -> float:
        """计算下次重连延迟"""
        channel = self._channels[channel_id]
        config = self._reconnection_configs[channel_id]
        strategy = config["strategy"]
        
        if strategy == ReconnectionStrategy.IMMEDIATE:
            return 0
        elif strategy == ReconnectionStrategy.FIXED_DELAY:
            return config["initial_delay_seconds"]
        elif strategy == ReconnectionStrategy.LINEAR_BACKOFF:
            delay = config["initial_delay_seconds"] * channel["current_attempt"]
        elif strategy == ReconnectionStrategy.EXPONENTIAL_BACKOFF:
            delay = config["initial_delay_seconds"] * (config["backoff_multiplier"] ** (channel["current_attempt"] - 1))
        else:
            delay = config["initial_delay_seconds"]
        
        # 应用最大延迟限制
        delay = min(delay, config["max_delay_seconds"])
        
        # 添加随机抖动
        if config.get("jitter", False):
            import random
            jitter = delay * 0.1 * random.random()  # 10%的随机抖动
            delay += jitter
        
        return delay
    
    async def get_channel_status(self, channel_id: str) -> Dict[str, Any]:
        """获取渠道重连状态"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        if channel_id not in self._channels:
            raise NotFoundError(f"渠道 '{channel_id}' 未注册")
        
        channel = self._channels[channel_id].copy()
        channel["status"] = channel["status"].value
        return channel
    
    async def get_all_channels_status(self) -> List[Dict[str, Any]]:
        """获取所有渠道重连状态"""
        results = []
        for channel in self._channels.values():
            channel_copy = channel.copy()
            channel_copy["status"] = channel_copy["status"].value
            results.append(channel_copy)
        
        return results
    
    async def get_reconnection_history(self, channel_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """获取渠道重连历史"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        if channel_id not in self._channels:
            raise NotFoundError(f"渠道 '{channel_id}' 未注册")
        
        if limit <= 0:
            raise ValidationError("限制数量必须大于0")
        
        history = self._reconnection_history.get(channel_id, [])
        return history[-limit:] if limit < len(history) else history
    
    async def update_channel_config(self, channel_id: str, config: Dict[str, Any]) -> bool:
        """更新渠道重连配置"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        if channel_id not in self._channels:
            raise NotFoundError(f"渠道 '{channel_id}' 未注册")
        
        if not config:
            raise ValidationError("配置不能为空")
        
        # 验证新配置
        new_config = self._reconnection_configs[channel_id].copy()
        new_config.update(config)
        self._validate_config(new_config)
        
        # 更新配置
        self._reconnection_configs[channel_id] = new_config
        self._channels[channel_id]["updated_at"] = datetime.utcnow()
        
        return True
    
    async def reset_channel_attempts(self, channel_id: str) -> bool:
        """重置渠道重试计数"""
        if not channel_id or not channel_id.strip():
            raise ValidationError("渠道ID不能为空")
        
        if channel_id not in self._channels:
            raise NotFoundError(f"渠道 '{channel_id}' 未注册")
        
        channel = self._channels[channel_id]
        channel["current_attempt"] = 0
        channel["status"] = ReconnectionStatus.IDLE
        channel["next_attempt"] = None
        channel["updated_at"] = datetime.utcnow()
        
        return True
    
    def add_event_listener(self, listener: Callable) -> bool:
        """添加事件监听器"""
        if not callable(listener):
            raise ValidationError("监听器必须是可调用对象")
        
        if listener not in self._event_listeners:
            self._event_listeners.append(listener)
        
        return True
    
    def remove_event_listener(self, listener: Callable) -> bool:
        """移除事件监听器"""
        if listener in self._event_listeners:
            self._event_listeners.remove(listener)
            return True
        return False
    
    def _validate_config(self, config: Dict[str, Any]) -> None:
        """验证重连配置"""
        required_fields = ["strategy", "max_attempts", "initial_delay_seconds", "max_delay_seconds"]
        for field in required_fields:
            if field not in config:
                raise ValidationError(f"缺少必需的配置项: {field}")
        
        if config["max_attempts"] <= 0:
            raise ValidationError("最大重试次数必须大于0")
        
        if config["initial_delay_seconds"] < 0:
            raise ValidationError("初始延迟不能为负数")
        
        if config["max_delay_seconds"] < config["initial_delay_seconds"]:
            raise ValidationError("最大延迟不能小于初始延迟")
        
        if isinstance(config["strategy"], str):
            try:
                config["strategy"] = ReconnectionStrategy(config["strategy"])
            except ValueError:
                raise ValidationError(f"无效的重连策略: {config['strategy']}")
    
    def _record_reconnection_attempt(self, channel_id: str, reason: str) -> None:
        """记录重连尝试"""
        if channel_id not in self._reconnection_history:
            self._reconnection_history[channel_id] = []
        
        self._reconnection_history[channel_id].append({
            "type": "attempt",
            "attempt": self._channels[channel_id]["current_attempt"],
            "reason": reason,
            "timestamp": datetime.utcnow(),
        })
    
    def _record_reconnection_result(self, channel_id: str, success: bool, message: str) -> None:
        """记录重连结果"""
        if channel_id not in self._reconnection_history:
            self._reconnection_history[channel_id] = []
        
        self._reconnection_history[channel_id].append({
            "type": "result",
            "success": success,
            "message": message,
            "timestamp": datetime.utcnow(),
        })
    
    async def _trigger_event(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """触发重连事件"""
        event = {
            "type": event_type,
            "data": event_data,
            "timestamp": datetime.utcnow(),
        }
        
        # 通知所有监听器
        for listener in self._event_listeners:
            try:
                if asyncio.iscoroutinefunction(listener):
                    await listener(event)
                else:
                    listener(event)
            except Exception:
                # 忽略监听器异常，避免影响主流程
                pass
    
    def clear_all_data(self) -> None:
        """清空所有数据（仅用于测试）"""
        self._channels.clear()
        self._reconnection_configs.clear()
        self._reconnection_history.clear()
        self._event_listeners.clear()
        self._active_reconnections.clear()


class TestReconnectionManager:
    """重连管理器测试套件"""
    
    @pytest.fixture
    def reconnection_manager(self):
        """创建重连管理器实例"""
        return MockReconnectionManager()
    
    @pytest.fixture
    def sample_config(self):
        """示例重连配置"""
        return {
            "strategy": ReconnectionStrategy.EXPONENTIAL_BACKOFF,
            "max_attempts": 3,
            "initial_delay_seconds": 1,
            "max_delay_seconds": 30,
            "backoff_multiplier": 2.0,
            "jitter": False,  # 测试时关闭随机抖动
            "timeout_seconds": 10,
        }
    
    @pytest.fixture
    def failing_config(self):
        """失败重连配置"""
        return {
            "strategy": ReconnectionStrategy.FIXED_DELAY,
            "max_attempts": 2,
            "initial_delay_seconds": 0.1,
            "max_delay_seconds": 1,
            "simulate_failure": True,
        }
    
    @pytest.mark.asyncio
    async def test_register_channel(self, reconnection_manager, sample_config):
        """测试注册渠道到重连管理器"""
        # Given: 渠道ID和配置
        channel_id = str(uuid.uuid4())
        
        # When: 注册渠道
        result = await reconnection_manager.register_channel(channel_id, sample_config)
        
        # Then: 注册应该成功
        assert result is True
        assert channel_id in reconnection_manager._channels
        
        # 验证初始状态
        channel = reconnection_manager._channels[channel_id]
        assert channel["id"] == channel_id
        assert channel["status"] == ReconnectionStatus.IDLE
        assert channel["current_attempt"] == 0
        assert channel["total_attempts"] == 0
        assert channel["successful_reconnections"] == 0

    @pytest.mark.asyncio
    async def test_register_channel_empty_id(self, reconnection_manager, sample_config):
        """测试使用空ID注册渠道"""
        # Given: 空渠道ID
        empty_id = ""

        # When & Then: 注册应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            await reconnection_manager.register_channel(empty_id, sample_config)

        assert "渠道ID不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_register_duplicate_channel(self, reconnection_manager, sample_config):
        """测试注册重复渠道"""
        # Given: 已注册的渠道
        channel_id = str(uuid.uuid4())
        await reconnection_manager.register_channel(channel_id, sample_config)

        # When & Then: 再次注册应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await reconnection_manager.register_channel(channel_id, sample_config)

        assert f"渠道 '{channel_id}' 已注册" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_unregister_channel(self, reconnection_manager, sample_config):
        """测试注销渠道"""
        # Given: 已注册的渠道
        channel_id = str(uuid.uuid4())
        await reconnection_manager.register_channel(channel_id, sample_config)

        # When: 注销渠道
        result = await reconnection_manager.unregister_channel(channel_id)

        # Then: 注销应该成功
        assert result is True
        assert channel_id not in reconnection_manager._channels
        assert channel_id not in reconnection_manager._reconnection_configs
        assert channel_id not in reconnection_manager._reconnection_history

    @pytest.mark.asyncio
    async def test_unregister_nonexistent_channel(self, reconnection_manager):
        """测试注销不存在的渠道"""
        # Given: 不存在的渠道ID
        nonexistent_id = str(uuid.uuid4())

        # When & Then: 注销应该抛出未找到错误
        with pytest.raises(NotFoundError) as exc_info:
            await reconnection_manager.unregister_channel(nonexistent_id)

        assert f"渠道 '{nonexistent_id}' 未注册" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_trigger_reconnection_success(self, reconnection_manager, sample_config):
        """测试触发重连（成功）- 对应Gherkin场景：连接断开后自动重连"""
        # Given: 已注册的渠道
        channel_id = str(uuid.uuid4())
        await reconnection_manager.register_channel(channel_id, sample_config)

        # When: 触发重连
        result = await reconnection_manager.trigger_reconnection(channel_id, "connection_lost")

        # Then: 重连应该成功
        assert result is True

        # 验证渠道状态
        channel = reconnection_manager._channels[channel_id]
        assert channel["status"] == ReconnectionStatus.SUCCESS
        assert channel["current_attempt"] == 0  # 成功后重置
        assert channel["total_attempts"] == 1
        assert channel["successful_reconnections"] == 1
        assert channel["last_success"] is not None

    @pytest.mark.asyncio
    async def test_trigger_reconnection_failure(self, reconnection_manager, failing_config):
        """测试触发重连（失败）"""
        # Given: 已注册的失败渠道
        channel_id = str(uuid.uuid4())
        await reconnection_manager.register_channel(channel_id, failing_config)

        # When: 触发重连
        result = await reconnection_manager.trigger_reconnection(channel_id, "connection_lost")

        # Then: 重连应该失败
        assert result is False

        # 验证渠道状态
        channel = reconnection_manager._channels[channel_id]
        assert channel["status"] == ReconnectionStatus.FAILED
        assert channel["current_attempt"] == 1
        assert channel["total_attempts"] == 1
        assert channel["successful_reconnections"] == 0
        assert channel["last_failure"] is not None

    @pytest.mark.asyncio
    async def test_trigger_reconnection_nonexistent_channel(self, reconnection_manager):
        """测试触发不存在渠道的重连"""
        # Given: 不存在的渠道ID
        nonexistent_id = str(uuid.uuid4())

        # When & Then: 触发重连应该抛出未找到错误
        with pytest.raises(NotFoundError) as exc_info:
            await reconnection_manager.trigger_reconnection(nonexistent_id)

        assert f"渠道 '{nonexistent_id}' 未注册" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_trigger_reconnection_already_reconnecting(self, reconnection_manager, sample_config):
        """测试触发正在重连中的渠道"""
        # Given: 已注册的渠道，手动设置为重连中状态
        channel_id = str(uuid.uuid4())
        await reconnection_manager.register_channel(channel_id, sample_config)
        reconnection_manager._channels[channel_id]["status"] = ReconnectionStatus.RECONNECTING

        # When & Then: 触发重连应该抛出业务逻辑错误
        with pytest.raises(BusinessLogicError) as exc_info:
            await reconnection_manager.trigger_reconnection(channel_id)

        assert f"渠道 '{channel_id}' 正在重连中" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_multiple_reconnection_attempts(self, reconnection_manager, failing_config):
        """测试多次重连尝试直到耗尽"""
        # Given: 已注册的失败渠道（最大重试2次）
        channel_id = str(uuid.uuid4())
        await reconnection_manager.register_channel(channel_id, failing_config)

        # When: 进行多次重连尝试
        # 第一次失败
        result1 = await reconnection_manager.trigger_reconnection(channel_id, "attempt_1")
        assert result1 is False

        channel = reconnection_manager._channels[channel_id]
        assert channel["status"] == ReconnectionStatus.FAILED
        assert channel["current_attempt"] == 1

        # 第二次失败
        result2 = await reconnection_manager.trigger_reconnection(channel_id, "attempt_2")
        assert result2 is False

        channel = reconnection_manager._channels[channel_id]
        assert channel["status"] == ReconnectionStatus.EXHAUSTED
        assert channel["current_attempt"] == 2

        # 第三次应该被拒绝（因为已经达到最大重试次数）
        result3 = await reconnection_manager.trigger_reconnection(channel_id, "attempt_3")
        assert result3 is False

        # 验证状态为耗尽
        channel = reconnection_manager._channels[channel_id]
        assert channel["status"] == ReconnectionStatus.EXHAUSTED

    @pytest.mark.asyncio
    async def test_get_channel_status(self, reconnection_manager, sample_config):
        """测试获取渠道重连状态"""
        # Given: 已注册并触发过重连的渠道
        channel_id = str(uuid.uuid4())
        await reconnection_manager.register_channel(channel_id, sample_config)
        await reconnection_manager.trigger_reconnection(channel_id, "test")

        # When: 获取渠道状态
        status = await reconnection_manager.get_channel_status(channel_id)

        # Then: 应该返回完整的状态信息
        assert status["id"] == channel_id
        assert status["status"] == ReconnectionStatus.SUCCESS.value
        assert status["total_attempts"] == 1
        assert status["successful_reconnections"] == 1
        assert status["last_success"] is not None

    @pytest.mark.asyncio
    async def test_get_all_channels_status(self, reconnection_manager, sample_config):
        """测试获取所有渠道重连状态"""
        # Given: 多个已注册的渠道
        channel1_id = str(uuid.uuid4())
        channel2_id = str(uuid.uuid4())

        await reconnection_manager.register_channel(channel1_id, sample_config)
        await reconnection_manager.register_channel(channel2_id, sample_config)

        # When: 获取所有渠道状态
        all_status = await reconnection_manager.get_all_channels_status()

        # Then: 应该返回所有渠道的状态
        assert len(all_status) == 2
        channel_ids = [status["id"] for status in all_status]
        assert channel1_id in channel_ids
        assert channel2_id in channel_ids

    @pytest.mark.asyncio
    async def test_get_reconnection_history(self, reconnection_manager, failing_config):
        """测试获取重连历史"""
        # Given: 已注册并进行过重连的渠道
        channel_id = str(uuid.uuid4())
        await reconnection_manager.register_channel(channel_id, failing_config)

        # 进行两次重连尝试
        await reconnection_manager.trigger_reconnection(channel_id, "first_attempt")
        await reconnection_manager.trigger_reconnection(channel_id, "second_attempt")

        # When: 获取重连历史
        history = await reconnection_manager.get_reconnection_history(channel_id)

        # Then: 应该包含重连历史记录
        assert len(history) == 4  # 2次尝试 + 2次结果

        # 验证历史记录类型
        attempt_records = [h for h in history if h["type"] == "attempt"]
        result_records = [h for h in history if h["type"] == "result"]

        assert len(attempt_records) == 2
        assert len(result_records) == 2

        # 验证记录内容
        assert attempt_records[0]["reason"] == "first_attempt"
        assert attempt_records[1]["reason"] == "second_attempt"
        assert all(not r["success"] for r in result_records)  # 都是失败的

    @pytest.mark.asyncio
    async def test_get_reconnection_history_with_limit(self, reconnection_manager, sample_config):
        """测试获取限制数量的重连历史"""
        # Given: 已注册的渠道
        channel_id = str(uuid.uuid4())
        await reconnection_manager.register_channel(channel_id, sample_config)

        # 手动添加多条历史记录
        for i in range(10):
            reconnection_manager._record_reconnection_attempt(channel_id, f"attempt_{i}")
            reconnection_manager._record_reconnection_result(channel_id, i % 2 == 0, f"result_{i}")

        # When: 获取限制数量的历史记录
        history = await reconnection_manager.get_reconnection_history(channel_id, limit=5)

        # Then: 应该返回最新的5条记录
        assert len(history) == 5

    @pytest.mark.asyncio
    async def test_update_channel_config(self, reconnection_manager, sample_config):
        """测试更新渠道重连配置"""
        # Given: 已注册的渠道
        channel_id = str(uuid.uuid4())
        await reconnection_manager.register_channel(channel_id, sample_config)

        # When: 更新配置
        new_config = {"max_attempts": 10, "initial_delay_seconds": 2}
        result = await reconnection_manager.update_channel_config(channel_id, new_config)

        # Then: 更新应该成功
        assert result is True

        # 验证配置已更新
        updated_config = reconnection_manager._reconnection_configs[channel_id]
        assert updated_config["max_attempts"] == 10
        assert updated_config["initial_delay_seconds"] == 2
        # 其他配置应该保持不变
        assert updated_config["strategy"] == sample_config["strategy"]

    @pytest.mark.asyncio
    async def test_reset_channel_attempts(self, reconnection_manager, failing_config):
        """测试重置渠道重试计数"""
        # Given: 已进行过重连尝试的渠道
        channel_id = str(uuid.uuid4())
        await reconnection_manager.register_channel(channel_id, failing_config)
        await reconnection_manager.trigger_reconnection(channel_id, "test")

        # 验证初始状态
        channel = reconnection_manager._channels[channel_id]
        assert channel["current_attempt"] == 1
        assert channel["status"] == ReconnectionStatus.FAILED

        # When: 重置重试计数
        result = await reconnection_manager.reset_channel_attempts(channel_id)

        # Then: 重置应该成功
        assert result is True

        # 验证状态已重置
        channel = reconnection_manager._channels[channel_id]
        assert channel["current_attempt"] == 0
        assert channel["status"] == ReconnectionStatus.IDLE
        assert channel["next_attempt"] is None

    @pytest.mark.asyncio
    async def test_event_listener_functionality(self, reconnection_manager, sample_config):
        """测试事件监听器功能"""
        # Given: 事件监听器
        events_received = []

        def event_listener(event):
            events_received.append(event)

        # 添加事件监听器
        reconnection_manager.add_event_listener(event_listener)

        # 注册渠道并触发重连
        channel_id = str(uuid.uuid4())
        await reconnection_manager.register_channel(channel_id, sample_config)

        # When: 触发重连（成功）
        await reconnection_manager.trigger_reconnection(channel_id, "test_event")

        # Then: 应该收到重连事件
        assert len(events_received) == 2  # reconnection_started, reconnection_success

        # 验证事件内容
        start_event = events_received[0]
        assert start_event["type"] == "reconnection_started"
        assert start_event["data"]["channel_id"] == channel_id
        assert start_event["data"]["reason"] == "test_event"

        success_event = events_received[1]
        assert success_event["type"] == "reconnection_success"
        assert success_event["data"]["channel_id"] == channel_id

    @pytest.mark.asyncio
    async def test_event_listener_failure_events(self, reconnection_manager, failing_config):
        """测试重连失败事件"""
        # Given: 事件监听器
        events_received = []

        def event_listener(event):
            events_received.append(event)

        reconnection_manager.add_event_listener(event_listener)

        # 注册失败渠道并触发重连
        channel_id = str(uuid.uuid4())
        await reconnection_manager.register_channel(channel_id, failing_config)

        # When: 触发重连（失败）
        await reconnection_manager.trigger_reconnection(channel_id, "test_failure")

        # Then: 应该收到失败事件
        assert len(events_received) == 2  # reconnection_started, reconnection_failed

        failure_event = events_received[1]
        assert failure_event["type"] == "reconnection_failed"
        assert failure_event["data"]["channel_id"] == channel_id

    @pytest.mark.asyncio
    async def test_add_invalid_event_listener(self, reconnection_manager):
        """测试添加无效的事件监听器"""
        # Given: 非可调用对象
        invalid_listener = "not_callable"

        # When & Then: 添加应该抛出验证错误
        with pytest.raises(ValidationError) as exc_info:
            reconnection_manager.add_event_listener(invalid_listener)

        assert "监听器必须是可调用对象" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_remove_event_listener(self, reconnection_manager):
        """测试移除事件监听器"""
        # Given: 已添加的事件监听器
        def event_listener(event):
            pass

        reconnection_manager.add_event_listener(event_listener)
        assert event_listener in reconnection_manager._event_listeners

        # When: 移除事件监听器
        result = reconnection_manager.remove_event_listener(event_listener)

        # Then: 移除应该成功
        assert result is True
        assert event_listener not in reconnection_manager._event_listeners

    @pytest.mark.asyncio
    async def test_remove_nonexistent_event_listener(self, reconnection_manager):
        """测试移除不存在的事件监听器"""
        # Given: 不存在的事件监听器
        def nonexistent_listener(event):
            pass

        # When: 移除不存在的监听器
        result = reconnection_manager.remove_event_listener(nonexistent_listener)

        # Then: 应该返回False
        assert result is False


class TestReconnectionStrategies:
    """重连策略测试套件"""

    @pytest.fixture
    def reconnection_manager_strategies(self):
        """重连策略测试的管理器实例"""
        return MockReconnectionManager()

    @pytest.mark.asyncio
    async def test_immediate_reconnection_strategy(self, reconnection_manager_strategies):
        """测试立即重连策略"""
        # Given: 立即重连配置
        config = {
            "strategy": ReconnectionStrategy.IMMEDIATE,
            "max_attempts": 3,
            "initial_delay_seconds": 1,
            "max_delay_seconds": 10,
        }

        channel_id = str(uuid.uuid4())
        await reconnection_manager_strategies.register_channel(channel_id, config)

        # When: 计算延迟
        delay = reconnection_manager_strategies._calculate_next_delay(channel_id)

        # Then: 延迟应该为0
        assert delay == 0

    @pytest.mark.asyncio
    async def test_fixed_delay_strategy(self, reconnection_manager_strategies):
        """测试固定延迟策略"""
        # Given: 固定延迟配置
        config = {
            "strategy": ReconnectionStrategy.FIXED_DELAY,
            "max_attempts": 3,
            "initial_delay_seconds": 5,
            "max_delay_seconds": 10,
        }

        channel_id = str(uuid.uuid4())
        await reconnection_manager_strategies.register_channel(channel_id, config)

        # When: 计算延迟（多次尝试）
        for attempt in range(1, 4):
            reconnection_manager_strategies._channels[channel_id]["current_attempt"] = attempt
            delay = reconnection_manager_strategies._calculate_next_delay(channel_id)

            # Then: 延迟应该始终为初始延迟
            assert delay == 5

    @pytest.mark.asyncio
    async def test_linear_backoff_strategy(self, reconnection_manager_strategies):
        """测试线性退避策略"""
        # Given: 线性退避配置
        config = {
            "strategy": ReconnectionStrategy.LINEAR_BACKOFF,
            "max_attempts": 4,
            "initial_delay_seconds": 2,
            "max_delay_seconds": 10,
            "jitter": False,  # 关闭随机抖动
        }

        channel_id = str(uuid.uuid4())
        await reconnection_manager_strategies.register_channel(channel_id, config)

        # When & Then: 验证线性增长
        expected_delays = [2, 4, 6, 8]  # 2 * attempt

        for attempt, expected_delay in enumerate(expected_delays, 1):
            reconnection_manager_strategies._channels[channel_id]["current_attempt"] = attempt
            delay = reconnection_manager_strategies._calculate_next_delay(channel_id)
            assert delay == expected_delay

    @pytest.mark.asyncio
    async def test_exponential_backoff_strategy(self, reconnection_manager_strategies):
        """测试指数退避策略"""
        # Given: 指数退避配置
        config = {
            "strategy": ReconnectionStrategy.EXPONENTIAL_BACKOFF,
            "max_attempts": 4,
            "initial_delay_seconds": 1,
            "max_delay_seconds": 20,
            "backoff_multiplier": 2.0,
            "jitter": False,  # 关闭随机抖动
        }

        channel_id = str(uuid.uuid4())
        await reconnection_manager_strategies.register_channel(channel_id, config)

        # When & Then: 验证指数增长
        expected_delays = [1, 2, 4, 8]  # 1 * (2 ^ (attempt - 1))

        for attempt, expected_delay in enumerate(expected_delays, 1):
            reconnection_manager_strategies._channels[channel_id]["current_attempt"] = attempt
            delay = reconnection_manager_strategies._calculate_next_delay(channel_id)
            assert delay == expected_delay

    @pytest.mark.asyncio
    async def test_max_delay_limit(self, reconnection_manager_strategies):
        """测试最大延迟限制"""
        # Given: 指数退避配置，但有较小的最大延迟
        config = {
            "strategy": ReconnectionStrategy.EXPONENTIAL_BACKOFF,
            "max_attempts": 10,
            "initial_delay_seconds": 1,
            "max_delay_seconds": 5,  # 较小的最大延迟
            "backoff_multiplier": 2.0,
            "jitter": False,  # 关闭随机抖动
        }

        channel_id = str(uuid.uuid4())
        await reconnection_manager_strategies.register_channel(channel_id, config)

        # When: 计算大尝试次数的延迟
        reconnection_manager_strategies._channels[channel_id]["current_attempt"] = 10
        delay = reconnection_manager_strategies._calculate_next_delay(channel_id)

        # Then: 延迟不应超过最大值
        assert delay <= 5

    @pytest.mark.asyncio
    async def test_jitter_functionality(self, reconnection_manager_strategies):
        """测试随机抖动功能"""
        # Given: 启用抖动的配置
        config = {
            "strategy": ReconnectionStrategy.FIXED_DELAY,
            "max_attempts": 3,
            "initial_delay_seconds": 10,
            "max_delay_seconds": 20,
            "jitter": True,
        }

        channel_id = str(uuid.uuid4())
        await reconnection_manager_strategies.register_channel(channel_id, config)
        reconnection_manager_strategies._channels[channel_id]["current_attempt"] = 1

        # When: 多次计算延迟
        delays = []
        for _ in range(10):
            delay = reconnection_manager_strategies._calculate_next_delay(channel_id)
            delays.append(delay)

        # Then: 延迟应该有变化（由于随机抖动）
        # 所有延迟都应该在基础延迟附近
        base_delay = 10
        for delay in delays:
            assert base_delay <= delay <= base_delay * 1.1  # 10%的抖动


class TestReconnectionEdgeCases:
    """重连机制边界情况测试"""

    @pytest.fixture
    def reconnection_manager_edge_cases(self):
        """边界情况测试的重连管理器实例"""
        return MockReconnectionManager()

    @pytest.mark.asyncio
    async def test_concurrent_reconnections(self, reconnection_manager_edge_cases):
        """测试并发重连"""
        import asyncio

        # Given: 多个渠道配置
        configs = []
        for i in range(5):
            configs.append({
                "strategy": ReconnectionStrategy.IMMEDIATE,
                "max_attempts": 1,
                "initial_delay_seconds": 0,
                "max_delay_seconds": 1,
            })

        # 注册所有渠道
        channel_ids = []
        for i, config in enumerate(configs):
            channel_id = f"concurrent_channel_{i}"
            channel_ids.append(channel_id)
            await reconnection_manager_edge_cases.register_channel(channel_id, config)

        # When: 并发触发重连
        async def trigger_reconnection(channel_id):
            return await reconnection_manager_edge_cases.trigger_reconnection(channel_id, "concurrent_test")

        tasks = [trigger_reconnection(channel_id) for channel_id in channel_ids]
        results = await asyncio.gather(*tasks)

        # Then: 所有重连都应该成功
        assert all(results)

        # 验证所有渠道状态
        for channel_id in channel_ids:
            channel = reconnection_manager_edge_cases._channels[channel_id]
            assert channel["status"] == ReconnectionStatus.SUCCESS

    @pytest.mark.asyncio
    async def test_config_validation_edge_cases(self, reconnection_manager_edge_cases):
        """测试配置验证边界情况"""
        channel_id = str(uuid.uuid4())

        # 测试负数最大重试次数
        with pytest.raises(ValidationError) as exc_info:
            await reconnection_manager_edge_cases.register_channel(channel_id, {
                "strategy": ReconnectionStrategy.FIXED_DELAY,
                "max_attempts": -1,
                "initial_delay_seconds": 1,
                "max_delay_seconds": 10,
            })
        assert "最大重试次数必须大于0" in str(exc_info.value)

        # 测试负数初始延迟
        with pytest.raises(ValidationError) as exc_info:
            await reconnection_manager_edge_cases.register_channel(channel_id, {
                "strategy": ReconnectionStrategy.FIXED_DELAY,
                "max_attempts": 3,
                "initial_delay_seconds": -1,
                "max_delay_seconds": 10,
            })
        assert "初始延迟不能为负数" in str(exc_info.value)

        # 测试最大延迟小于初始延迟
        with pytest.raises(ValidationError) as exc_info:
            await reconnection_manager_edge_cases.register_channel(channel_id, {
                "strategy": ReconnectionStrategy.FIXED_DELAY,
                "max_attempts": 3,
                "initial_delay_seconds": 10,
                "max_delay_seconds": 5,
            })
        assert "最大延迟不能小于初始延迟" in str(exc_info.value)

        # 测试无效策略
        with pytest.raises(ValidationError) as exc_info:
            await reconnection_manager_edge_cases.register_channel(channel_id, {
                "strategy": "invalid_strategy",
                "max_attempts": 3,
                "initial_delay_seconds": 1,
                "max_delay_seconds": 10,
            })
        assert "无效的重连策略" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_memory_management_with_large_history(self, reconnection_manager_edge_cases):
        """测试大量历史记录的内存管理"""
        # Given: 已注册的渠道
        channel_id = "memory_test_channel"
        config = {
            "strategy": ReconnectionStrategy.FIXED_DELAY,
            "max_attempts": 1000,  # 允许大量重试
            "initial_delay_seconds": 0,
            "max_delay_seconds": 1,
        }
        await reconnection_manager_edge_cases.register_channel(channel_id, config)

        # When: 生成大量历史记录
        for i in range(500):
            reconnection_manager_edge_cases._record_reconnection_attempt(channel_id, f"attempt_{i}")
            reconnection_manager_edge_cases._record_reconnection_result(channel_id, i % 2 == 0, f"result_{i}")

        # Then: 获取历史记录应该正常工作
        history = await reconnection_manager_edge_cases.get_reconnection_history(channel_id, limit=100)
        assert len(history) == 100

        # 验证返回的是最新的记录
        all_history = await reconnection_manager_edge_cases.get_reconnection_history(channel_id, limit=1000)
        assert len(all_history) == 1000

    @pytest.mark.asyncio
    async def test_event_listener_exception_handling(self, reconnection_manager_edge_cases):
        """测试事件监听器异常处理"""
        # Given: 会抛出异常的事件监听器
        def failing_listener(event):
            raise Exception("监听器异常")

        def normal_listener(event):
            normal_listener.events_received = getattr(normal_listener, 'events_received', [])
            normal_listener.events_received.append(event)

        reconnection_manager_edge_cases.add_event_listener(failing_listener)
        reconnection_manager_edge_cases.add_event_listener(normal_listener)

        # 注册渠道并触发重连
        channel_id = str(uuid.uuid4())
        config = {
            "strategy": ReconnectionStrategy.IMMEDIATE,
            "max_attempts": 1,
            "initial_delay_seconds": 0,
            "max_delay_seconds": 1,
        }
        await reconnection_manager_edge_cases.register_channel(channel_id, config)

        # When: 触发重连（应该不会因为监听器异常而失败）
        result = await reconnection_manager_edge_cases.trigger_reconnection(channel_id, "exception_test")

        # Then: 重连应该成功，正常监听器应该收到事件
        assert result is True
        assert hasattr(normal_listener, 'events_received')
        assert len(normal_listener.events_received) == 2  # start + success events
