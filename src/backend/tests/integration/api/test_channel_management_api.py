"""
渠道管理API集成测试

基于BDD剧本 features/core_channel_management/channel_management.feature
测试渠道CRUD操作的API端点完整流程
"""

import pytest
import json
from httpx import AsyncClient
from unittest.mock import AsyncMock, patch
from datetime import datetime, timezone

from app.main import app
from app.core.database import get_db
from app.models.channel import ChannelInstance


class TestChannelManagementAPI:
    """渠道管理API集成测试类"""

    @pytest.fixture
    async def client(self):
        """创建测试客户端"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            yield ac

    @pytest.fixture
    async def db_session(self):
        """创建测试数据库会话"""
        async with get_db() as session:
            yield session

    @pytest.fixture
    async def sample_channels(self, client: AsyncClient):
        """创建示例渠道数据"""
        channels_data = [
            {
                "platform": "xianyu",
                "platform_account_id": "xianyu_shop_123",
                "display_name": "主店铺",
                "status": "connected",
                "cookie_config": {"session_id": "test123"}
            },
            {
                "platform": "xianyu", 
                "platform_account_id": "xianyu_shop_456",
                "display_name": "客服店铺",
                "status": "connected",
                "cookie_config": {"session_id": "test456"}
            },
            {
                "platform": "douyin",
                "platform_account_id": "dy_789",
                "display_name": "官方号",
                "status": "connected",
                "cookie_config": {"access_token": "dy_token"}
            }
        ]

        created_channels = []
        for channel_data in channels_data:
            with patch('app.connectors.base_connector.BaseConnector.validate_cookie') as mock_validate:
                mock_validate.return_value = True
                
                response = await client.post("/api/v1/channels/connect", json=channel_data)
                assert response.status_code == 201
                created_channels.append(response.json()["data"])

        return created_channels

    async def test_set_channel_alias(self, client: AsyncClient, sample_channels):
        """
        测试设置渠道别名
        对应BDD场景: 设置渠道别名
        """
        # Given: 获取已存在的渠道
        channel = sample_channels[0]
        channel_id = channel["id"]
        
        # When: 更新渠道别名
        update_data = {
            "display_name": "业务咨询店铺"
        }
        
        response = await client.patch(
            f"/api/v1/channels/{channel_id}",
            json=update_data
        )

        # Then: 验证响应
        assert response.status_code == 200
        response_data = response.json()
        
        assert response_data["message"] == "保存成功"
        assert response_data["data"]["display_name"] == "业务咨询店铺"
        assert response_data["data"]["id"] == channel_id

        # 验证别名在列表中更新
        list_response = await client.get("/api/v1/channels")
        assert list_response.status_code == 200
        channels = list_response.json()["data"]
        
        updated_channel = next(c for c in channels if c["id"] == channel_id)
        assert updated_channel["display_name"] == "业务咨询店铺"

        # 验证操作日志被记录
        logs_response = await client.get(f"/api/v1/channels/{channel_id}/logs")
        assert logs_response.status_code == 200
        logs = logs_response.json()["data"]
        
        alias_update_log = next(
            (log for log in logs if log["operation_type"] == "alias_update"), 
            None
        )
        assert alias_update_log is not None
        assert "业务咨询店铺" in alias_update_log["metadata"]["new_alias"]

    async def test_alias_validation(self, client: AsyncClient, sample_channels):
        """
        测试别名输入验证
        对应BDD场景: 别名输入验证
        """
        # Given: 获取已存在的渠道
        channel = sample_channels[0]
        channel_id = channel["id"]
        original_alias = channel["display_name"]
        
        # When: 尝试设置空白别名
        update_data = {
            "display_name": ""
        }
        
        response = await client.patch(
            f"/api/v1/channels/{channel_id}",
            json=update_data
        )

        # Then: 验证响应
        assert response.status_code == 400
        response_data = response.json()
        
        assert "别名不能为空" in response_data["message"]
        assert response_data["error_code"] == "INVALID_ALIAS"

        # 验证别名保持原有值
        detail_response = await client.get(f"/api/v1/channels/{channel_id}")
        assert detail_response.status_code == 200
        detail_data = detail_response.json()["data"]
        assert detail_data["display_name"] == original_alias

    async def test_soft_delete_channel(self, client: AsyncClient, sample_channels):
        """
        测试软删除渠道连接
        对应BDD场景: 软删除渠道连接
        """
        # Given: 获取要删除的渠道
        channel = sample_channels[1]  # 客服店铺
        channel_id = channel["id"]
        
        # When: 执行软删除
        response = await client.delete(f"/api/v1/channels/{channel_id}")

        # Then: 验证响应
        assert response.status_code == 200
        response_data = response.json()
        
        assert response_data["message"] == "删除成功"
        assert response_data["data"]["is_deleted"] is True
        assert "deleted_at" in response_data["data"]

        # 验证渠道从主列表中移除
        list_response = await client.get("/api/v1/channels")
        assert list_response.status_code == 200
        active_channels = list_response.json()["data"]
        
        active_channel_ids = [c["id"] for c in active_channels]
        assert channel_id not in active_channel_ids

        # 验证渠道在历史列表中存在
        history_response = await client.get("/api/v1/channels/history")
        assert history_response.status_code == 200
        deleted_channels = history_response.json()["data"]
        
        deleted_channel = next(c for c in deleted_channels if c["id"] == channel_id)
        assert deleted_channel["is_deleted"] is True
        assert deleted_channel["deleted_at"] is not None

        # 验证监控服务被停止
        with patch('app.services.monitoring_service.ChannelMonitoringService.stop_monitoring') as mock_stop:
            # 软删除操作应该已经调用了停止监控
            mock_stop.assert_called_with(channel_id)

        # 验证操作日志被记录
        logs_response = await client.get(f"/api/v1/channels/{channel_id}/logs")
        assert logs_response.status_code == 200
        logs = logs_response.json()["data"]
        
        delete_log = next(
            (log for log in logs if log["operation_type"] == "soft_delete"), 
            None
        )
        assert delete_log is not None

    async def test_view_history_channels(self, client: AsyncClient, sample_channels):
        """
        测试查看历史渠道
        对应BDD场景: 查看历史渠道
        """
        # Given: 先软删除一个渠道
        channel = sample_channels[0]
        channel_id = channel["id"]
        
        delete_response = await client.delete(f"/api/v1/channels/{channel_id}")
        assert delete_response.status_code == 200

        # When: 查看历史渠道
        response = await client.get("/api/v1/channels/history")

        # Then: 验证响应
        assert response.status_code == 200
        response_data = response.json()
        
        assert "data" in response_data
        deleted_channels = response_data["data"]
        assert len(deleted_channels) >= 1

        # 验证删除的渠道在历史列表中
        deleted_channel = next(c for c in deleted_channels if c["id"] == channel_id)
        assert deleted_channel["platform"] == "xianyu"
        assert deleted_channel["platform_account_id"] == "xianyu_shop_123"
        assert deleted_channel["is_deleted"] is True
        assert "deleted_at" in deleted_channel
        assert "delete_reason" in deleted_channel

        # 验证页面提示信息
        assert "message" in response_data
        assert "这些渠道已被删除，不会出现在数据统计中" in response_data["message"]

    async def test_restore_deleted_channel(self, client: AsyncClient, sample_channels):
        """
        测试恢复已删除渠道
        对应BDD场景: 恢复已删除渠道
        """
        # Given: 先软删除一个渠道
        channel = sample_channels[0]
        channel_id = channel["id"]
        
        delete_response = await client.delete(f"/api/v1/channels/{channel_id}")
        assert delete_response.status_code == 200

        # When: 恢复渠道
        restore_response = await client.post(f"/api/v1/channels/{channel_id}/restore")

        # Then: 验证响应
        assert restore_response.status_code == 200
        response_data = restore_response.json()
        
        assert response_data["message"] == "恢复成功"
        assert response_data["data"]["is_deleted"] is False
        assert response_data["data"]["deleted_at"] is None
        assert response_data["data"]["status"] == "connected"

        # 验证渠道重新出现在主列表中
        list_response = await client.get("/api/v1/channels")
        assert list_response.status_code == 200
        active_channels = list_response.json()["data"]
        
        restored_channel = next(c for c in active_channels if c["id"] == channel_id)
        assert restored_channel["is_deleted"] is False

        # 验证渠道从历史列表中移除
        history_response = await client.get("/api/v1/channels/history")
        assert history_response.status_code == 200
        deleted_channels = history_response.json()["data"]
        
        deleted_channel_ids = [c["id"] for c in deleted_channels]
        assert channel_id not in deleted_channel_ids

        # 验证监控服务被重新启动
        with patch('app.services.monitoring_service.ChannelMonitoringService.start_monitoring') as mock_start:
            # 恢复操作应该已经调用了启动监控
            mock_start.assert_called_with(channel_id)

        # 验证操作日志被记录
        logs_response = await client.get(f"/api/v1/channels/{channel_id}/logs")
        assert logs_response.status_code == 200
        logs = logs_response.json()["data"]
        
        restore_log = next(
            (log for log in logs if log["operation_type"] == "restore"), 
            None
        )
        assert restore_log is not None

    async def test_permanent_delete_channel(self, client: AsyncClient, sample_channels):
        """
        测试彻底删除渠道
        对应BDD场景: 彻底删除渠道
        """
        # Given: 先软删除一个渠道
        channel = sample_channels[0]
        channel_id = channel["id"]
        
        delete_response = await client.delete(f"/api/v1/channels/{channel_id}")
        assert delete_response.status_code == 200

        # When: 彻底删除渠道
        permanent_delete_response = await client.delete(
            f"/api/v1/channels/{channel_id}/permanent"
        )

        # Then: 验证响应
        assert permanent_delete_response.status_code == 200
        response_data = permanent_delete_response.json()
        
        assert response_data["message"] == "彻底删除成功"
        assert "warning" in response_data
        assert "此操作不可恢复" in response_data["warning"]

        # 验证渠道从历史列表中移除
        history_response = await client.get("/api/v1/channels/history")
        assert history_response.status_code == 200
        deleted_channels = history_response.json()["data"]
        
        deleted_channel_ids = [c["id"] for c in deleted_channels]
        assert channel_id not in deleted_channel_ids

        # 验证渠道详情无法访问
        detail_response = await client.get(f"/api/v1/channels/{channel_id}")
        assert detail_response.status_code == 404

        # 验证相关数据被清理
        logs_response = await client.get(f"/api/v1/channels/{channel_id}/logs")
        assert logs_response.status_code == 404

    async def test_view_channel_details(self, client: AsyncClient, sample_channels):
        """
        测试查看渠道详细信息
        对应BDD场景: 查看渠道详细信息
        """
        # Given: 获取已存在的渠道
        channel = sample_channels[0]
        channel_id = channel["id"]

        # When: 查看渠道详情
        response = await client.get(f"/api/v1/channels/{channel_id}")

        # Then: 验证响应
        assert response.status_code == 200
        response_data = response.json()["data"]
        
        # 验证基本信息
        assert response_data["platform"] == "xianyu"
        assert response_data["platform_account_id"] == "xianyu_shop_123"
        assert response_data["display_name"] == "主店铺"
        assert "created_at" in response_data
        assert "last_active_at" in response_data

        # 验证统计信息
        assert "message_stats" in response_data
        assert "today_messages" in response_data["message_stats"]
        
        # 验证连接状态信息
        assert "connection_status" in response_data
        assert response_data["connection_status"] == "connected"
        
        # 验证Cookie状态（脱敏显示）
        assert "cookie_status" in response_data
        assert "masked_cookie" in response_data
        assert "*" in response_data["masked_cookie"]

    async def test_channel_sorting_and_filtering(self, client: AsyncClient, sample_channels):
        """
        测试渠道排序和筛选
        对应BDD场景: 渠道排序和筛选
        """
        # When: 按平台排序
        sort_response = await client.get("/api/v1/channels?sort_by=platform&order=asc")
        
        # Then: 验证排序结果
        assert sort_response.status_code == 200
        sorted_channels = sort_response.json()["data"]
        
        platforms = [c["platform"] for c in sorted_channels]
        assert platforms == sorted(platforms)

        # When: 筛选闲鱼平台
        filter_response = await client.get("/api/v1/channels?platform=xianyu")
        
        # Then: 验证筛选结果
        assert filter_response.status_code == 200
        filtered_channels = filter_response.json()["data"]
        
        for channel in filtered_channels:
            assert channel["platform"] == "xianyu"

        # 验证筛选条件保持
        assert "filters" in filter_response.json()
        assert filter_response.json()["filters"]["platform"] == "xianyu"

    async def test_channel_status_toggle(self, client: AsyncClient, sample_channels):
        """
        测试渠道状态切换
        对应BDD场景: 渠道状态切换
        """
        # Given: 获取已连接的渠道
        channel = sample_channels[0]
        channel_id = channel["id"]
        
        # When: 暂停渠道
        pause_response = await client.post(f"/api/v1/channels/{channel_id}/pause")

        # Then: 验证暂停响应
        assert pause_response.status_code == 200
        pause_data = pause_response.json()
        
        assert pause_data["data"]["status"] == "paused"
        assert "状态变更成功" in pause_data["message"]

        # 验证消息接收被停止
        with patch('app.services.monitoring_service.ChannelMonitoringService.pause_monitoring') as mock_pause:
            mock_pause.assert_called_with(channel_id)

        # When: 重新启用渠道
        resume_response = await client.post(f"/api/v1/channels/{channel_id}/resume")

        # Then: 验证启用响应
        assert resume_response.status_code == 200
        resume_data = resume_response.json()
        
        assert resume_data["data"]["status"] == "connected"
        assert "状态变更成功" in resume_data["message"]

        # 验证Cookie有效性被重新验证
        with patch('app.connectors.base_connector.BaseConnector.validate_cookie') as mock_validate:
            mock_validate.return_value = True
            # 启用操作应该已经调用了Cookie验证

        # 验证监控服务被重新启动
        with patch('app.services.monitoring_service.ChannelMonitoringService.resume_monitoring') as mock_resume:
            mock_resume.assert_called_with(channel_id)

    async def test_soft_deleted_channels_hidden_in_main_list(self, client: AsyncClient, sample_channels):
        """
        测试软删除渠道在主列表中隐藏
        对应BDD场景: 验证软删除渠道在主列表中隐藏
        """
        # Given: 软删除一个渠道
        channel_to_delete = sample_channels[1]
        delete_response = await client.delete(f"/api/v1/channels/{channel_to_delete['id']}")
        assert delete_response.status_code == 200

        # When: 获取主渠道列表
        list_response = await client.get("/api/v1/channels")

        # Then: 验证响应
        assert list_response.status_code == 200
        response_data = list_response.json()
        channels = response_data["data"]
        
        # 验证正常渠道显示
        normal_channel_ids = [c["id"] for c in channels]
        assert sample_channels[0]["id"] in normal_channel_ids
        assert sample_channels[2]["id"] in normal_channel_ids
        
        # 验证删除的渠道不显示
        assert channel_to_delete["id"] not in normal_channel_ids
        
        # 验证统计数据不包含已删除渠道
        assert response_data["total_count"] == 2  # 原来3个，删除1个
        
        # 验证搜索不返回已删除渠道
        search_response = await client.get(f"/api/v1/channels?search={channel_to_delete['display_name']}")
        assert search_response.status_code == 200
        search_results = search_response.json()["data"]
        assert len(search_results) == 0
