"""
渠道连接API集成测试

基于BDD剧本 features/core_channel_management/channel_connection.feature
测试渠道连接相关的API端点完整流程
"""

import pytest
import json
from httpx import AsyncClient
from unittest.mock import AsyncMock, patch
from datetime import datetime, timezone

from app.main import app
from app.core.database import get_db
from app.models.channel import ChannelInstance
from app.models.channel_connection_log import ChannelConnectionLog


class TestChannelConnectionAPI:
    """渠道连接API集成测试类"""

    @pytest.fixture
    async def client(self):
        """创建测试客户端"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            yield ac

    @pytest.fixture
    async def db_session(self):
        """创建测试数据库会话"""
        # 这里应该使用测试数据库
        async with get_db() as session:
            yield session

    @pytest.fixture
    def valid_xianyu_cookie(self):
        """有效的闲鱼Cookie数据"""
        return {
            "session_id": "test_session_123",
            "user_id": "xianyu_user_456",
            "auth_token": "valid_auth_token_789",
            "csrf_token": "csrf_token_abc"
        }

    @pytest.fixture
    def invalid_cookie(self):
        """无效的Cookie数据"""
        return {
            "session_id": "",
            "user_id": "invalid_user",
            "auth_token": "expired_token"
        }

    async def test_successful_xianyu_connection(self, client: AsyncClient, valid_xianyu_cookie):
        """
        测试成功连接闲鱼账号
        对应BDD场景: 成功连接闲鱼账号
        """
        # Given: 准备连接数据
        connection_data = {
            "platform": "xianyu",
            "platform_account_id": "xianyu_shop_123",
            "display_name": "测试闲鱼店铺",
            "cookie_config": valid_xianyu_cookie
        }

        # When: 发送连接请求
        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate:
            mock_validate.return_value = True
            
            response = await client.post(
                "/api/v1/channels/connect",
                json=connection_data
            )

        # Then: 验证响应
        assert response.status_code == 201
        response_data = response.json()
        
        assert response_data["message"] == "连接成功"
        assert response_data["data"]["platform"] == "xianyu"
        assert response_data["data"]["platform_account_id"] == "xianyu_shop_123"
        assert response_data["data"]["status"] == "connected"
        assert "id" in response_data["data"]
        assert "created_at" in response_data["data"]
        
        # 验证Cookie被加密存储
        assert "cookie_config" not in response_data["data"]
        
        # 验证连接日志被创建
        channel_id = response_data["data"]["id"]
        log_response = await client.get(f"/api/v1/channels/{channel_id}/logs")
        assert log_response.status_code == 200
        logs = log_response.json()["data"]
        assert len(logs) > 0
        assert logs[0]["operation_type"] == "connect"
        assert logs[0]["status"] == "connected"

    async def test_manual_cookie_input_validation(self, client: AsyncClient):
        """
        测试手动输入Cookie的验证流程
        对应BDD场景: Cookie输入方式 - 手动输入
        """
        # Given: 准备Cookie字符串
        cookie_string = "session_id=test123; user_id=user456; auth_token=token789"
        
        validation_data = {
            "platform": "xianyu",
            "cookie_string": cookie_string,
            "input_method": "manual"
        }

        # When: 发送Cookie验证请求
        with patch('app.utils.cookie_manager.CookieManager.parse_cookie_string') as mock_parse:
            mock_parse.return_value = {
                "session_id": "test123",
                "user_id": "user456", 
                "auth_token": "token789"
            }
            
            response = await client.post(
                "/api/v1/channels/validate-cookie",
                json=validation_data
            )

        # Then: 验证响应
        assert response.status_code == 200
        response_data = response.json()
        
        assert response_data["valid"] is True
        assert response_data["parsed_fields"]["session_id"] == "test123"
        assert response_data["parsed_fields"]["user_id"] == "user456"
        assert response_data["message"] == "Cookie格式验证成功"

    async def test_invalid_cookie_connection(self, client: AsyncClient, invalid_cookie):
        """
        测试无效Cookie连接失败
        对应BDD场景: 连接失败 - Cookie无效
        """
        # Given: 准备无效连接数据
        connection_data = {
            "platform": "xianyu",
            "platform_account_id": "xianyu_shop_invalid",
            "cookie_config": invalid_cookie
        }

        # When: 发送连接请求
        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate:
            mock_validate.return_value = False
            
            response = await client.post(
                "/api/v1/channels/connect",
                json=connection_data
            )

        # Then: 验证响应
        assert response.status_code == 400
        response_data = response.json()
        
        assert "Cookie无效或格式错误" in response_data["message"]
        assert "cookie_guide_url" in response_data
        assert response_data["error_code"] == "INVALID_COOKIE"

    async def test_duplicate_account_connection(self, client: AsyncClient, valid_xianyu_cookie):
        """
        测试重复连接同一账号
        对应BDD场景: 重复连接同一账号
        """
        # Given: 先创建一个已存在的渠道
        existing_connection_data = {
            "platform": "xianyu",
            "platform_account_id": "xianyu_shop_duplicate",
            "display_name": "已存在的店铺",
            "cookie_config": valid_xianyu_cookie
        }

        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate:
            mock_validate.return_value = True
            
            # 第一次连接
            first_response = await client.post(
                "/api/v1/channels/connect",
                json=existing_connection_data
            )
            assert first_response.status_code == 201

            # When: 尝试重复连接
            duplicate_connection_data = {
                "platform": "xianyu",
                "platform_account_id": "xianyu_shop_duplicate",
                "display_name": "重复的店铺",
                "cookie_config": valid_xianyu_cookie
            }
            
            second_response = await client.post(
                "/api/v1/channels/connect",
                json=duplicate_connection_data
            )

        # Then: 验证响应
        assert second_response.status_code == 409
        response_data = second_response.json()
        
        assert "该账号已存在" in response_data["message"]
        assert "existing_channel" in response_data
        assert response_data["existing_channel"]["platform_account_id"] == "xianyu_shop_duplicate"
        assert response_data["error_code"] == "DUPLICATE_ACCOUNT"

    async def test_network_exception_handling(self, client: AsyncClient, valid_xianyu_cookie):
        """
        测试网络异常时的连接处理
        对应BDD场景: 网络异常时的连接处理
        """
        # Given: 准备连接数据
        connection_data = {
            "platform": "xianyu",
            "platform_account_id": "xianyu_shop_network_test",
            "cookie_config": valid_xianyu_cookie
        }

        # When: 模拟网络异常
        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate:
            mock_validate.side_effect = ConnectionError("网络连接异常")
            
            response = await client.post(
                "/api/v1/channels/connect",
                json=connection_data
            )

        # Then: 验证响应
        assert response.status_code == 503
        response_data = response.json()
        
        assert "网络连接异常，请检查网络后重试" in response_data["message"]
        assert response_data["error_code"] == "NETWORK_ERROR"
        assert "retry_after" in response_data

    async def test_cookie_permission_validation(self, client: AsyncClient):
        """
        测试Cookie权限验证
        对应BDD场景: Cookie权限验证
        """
        # Given: 准备权限不足的Cookie
        insufficient_cookie = {
            "session_id": "test_session",
            # 缺少必要的权限字段
        }
        
        connection_data = {
            "platform": "xianyu",
            "platform_account_id": "xianyu_shop_permission_test",
            "cookie_config": insufficient_cookie
        }

        # When: 发送连接请求
        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate:
            mock_validate.side_effect = ValueError("Cookie权限不足")
            
            response = await client.post(
                "/api/v1/channels/connect",
                json=connection_data
            )

        # Then: 验证响应
        assert response.status_code == 400
        response_data = response.json()
        
        assert "Cookie权限不足" in response_data["message"]
        assert "required_fields" in response_data
        assert "cookie_guide_url" in response_data
        assert response_data["error_code"] == "INSUFFICIENT_PERMISSION"

    async def test_connection_history_logging(self, client: AsyncClient, valid_xianyu_cookie):
        """
        测试连接历史记录
        对应BDD场景: 连接历史记录
        """
        # Given: 执行多次连接操作
        operations = [
            {
                "platform": "xianyu",
                "platform_account_id": "xianyu_shop_history_1",
                "cookie_config": valid_xianyu_cookie,
                "expected_status": 201
            },
            {
                "platform": "xianyu", 
                "platform_account_id": "xianyu_shop_history_2",
                "cookie_config": {"invalid": "cookie"},
                "expected_status": 400
            }
        ]

        channel_ids = []
        
        for operation in operations:
            with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate:
                mock_validate.return_value = operation["expected_status"] == 201
                
                response = await client.post(
                    "/api/v1/channels/connect",
                    json=operation
                )
                
                assert response.status_code == operation["expected_status"]
                
                if response.status_code == 201:
                    channel_ids.append(response.json()["data"]["id"])

        # When: 查看连接历史
        history_response = await client.get("/api/v1/channels/connection-history")

        # Then: 验证历史记录
        assert history_response.status_code == 200
        history_data = history_response.json()["data"]
        
        assert len(history_data) >= 2
        
        # 验证记录包含必要字段
        for record in history_data:
            assert "timestamp" in record
            assert "platform" in record
            assert "status" in record
            assert "result" in record
            
        # 验证失败记录包含错误信息
        failed_records = [r for r in history_data if r["status"] == "failed"]
        assert len(failed_records) >= 1
        assert "error_message" in failed_records[0]

    async def test_cookie_security_storage(self, client: AsyncClient, valid_xianyu_cookie):
        """
        测试Cookie安全管理
        对应BDD场景: Cookie安全管理
        """
        # Given: 准备连接数据
        connection_data = {
            "platform": "xianyu",
            "platform_account_id": "xianyu_shop_security_test",
            "cookie_config": valid_xianyu_cookie
        }

        # When: 创建连接
        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate:
            mock_validate.return_value = True
            
            response = await client.post(
                "/api/v1/channels/connect",
                json=connection_data
            )

        assert response.status_code == 201
        channel_id = response.json()["data"]["id"]

        # Then: 验证Cookie安全存储
        # 1. 获取渠道详情，确保Cookie被脱敏
        detail_response = await client.get(f"/api/v1/channels/{channel_id}")
        assert detail_response.status_code == 200
        detail_data = detail_response.json()["data"]
        
        # Cookie应该被脱敏显示
        assert "masked_cookie" in detail_data
        assert "*" in detail_data["masked_cookie"]
        assert "cookie_config" not in detail_data  # 原始Cookie不应暴露

        # 2. 验证Cookie在数据库中被加密存储
        with patch('app.utils.cookie_manager.CookieManager.decrypt_cookie') as mock_decrypt:
            mock_decrypt.return_value = valid_xianyu_cookie
            
            # 管理员接口可以获取解密后的Cookie（用于测试）
            admin_response = await client.get(
                f"/api/v1/admin/channels/{channel_id}/cookie",
                headers={"X-Admin-Token": "test_admin_token"}
            )
            
            assert admin_response.status_code == 200
            admin_data = admin_response.json()["data"]
            assert admin_data["cookie_config"] == valid_xianyu_cookie

    async def test_realtime_connection_status_update(self, client: AsyncClient, valid_xianyu_cookie):
        """
        测试连接状态实时更新
        对应BDD场景: 连接状态实时更新
        """
        # Given: 准备连接数据
        connection_data = {
            "platform": "xianyu",
            "platform_account_id": "xianyu_shop_realtime_test",
            "cookie_config": valid_xianyu_cookie
        }

        # When: 发送连接请求并监控状态变化
        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate:
            # 模拟验证过程中的状态变化
            mock_validate.return_value = True
            
            response = await client.post(
                "/api/v1/channels/connect",
                json=connection_data
            )

        assert response.status_code == 201
        channel_id = response.json()["data"]["id"]

        # Then: 验证状态更新
        # 1. 检查初始状态
        status_response = await client.get(f"/api/v1/channels/{channel_id}/status")
        assert status_response.status_code == 200
        status_data = status_response.json()["data"]
        
        assert status_data["status"] == "connected"
        assert "last_updated" in status_data
        assert "connection_progress" in status_data

        # 2. 模拟状态变化并验证实时更新
        with patch('app.services.monitoring_service.ChannelMonitoringService.update_status') as mock_update:
            mock_update.return_value = True
            
            # 触发状态更新
            update_response = await client.post(
                f"/api/v1/channels/{channel_id}/refresh-status"
            )
            
            assert update_response.status_code == 200
            updated_status = update_response.json()["data"]
            assert "status" in updated_status
            assert "last_updated" in updated_status
