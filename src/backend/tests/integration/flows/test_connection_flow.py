"""
完整连接流程集成测试

基于BDD剧本测试从连接到监控的完整端到端流程
验证各个组件之间的协作和数据流转
"""

import pytest
import asyncio
from datetime import datetime, timezone
from httpx import AsyncClient
from unittest.mock import patch, AsyncMock, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

# from app.main import app
from app.core.database import get_db
from app.models.channel import ChannelInstance
from app.models.channel_connection_log import ChannelConnectionLog


class TestConnectionFlow:
    """完整连接流程集成测试类"""

    @pytest.fixture
    async def client(self, test_client):
        """创建测试客户端"""
        return test_client

    @pytest.fixture
    async def db_session(self, test_db_session):
        """创建测试数据库会话"""
        return test_db_session

    @pytest.fixture
    def valid_xianyu_cookie(self):
        """有效的闲鱼Cookie配置"""
        return {
            "session_id": "valid_session_12345",
            "user_id": "xianyu_user_67890",
            "auth_token": "valid_auth_token_abcdef",
            "csrf_token": "csrf_token_123456"
        }

    async def test_complete_xianyu_connection_flow(self, client: AsyncClient, db_session: AsyncSession, valid_xianyu_cookie):
        """
        测试完整的闲鱼连接流程
        对应BDD场景: 成功连接闲鱼账号的完整流程
        """
        # Given: 准备连接数据
        connection_data = {
            "platform": "xianyu",
            "platform_account_id": "xianyu_shop_flow_test",
            "display_name": "流程测试店铺",
            "cookie_config": valid_xianyu_cookie
        }

        # When: 执行完整连接流程
        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate, \
             patch('app.connectors.xianyu_connector.XianyuConnector.get_account_info') as mock_account_info, \
             patch('app.services.monitoring_service.ChannelMonitoringService.start_monitoring') as mock_start_monitoring:
            
            # 1. 模拟Cookie验证成功
            mock_validate.return_value = True
            
            # 2. 模拟获取账号信息
            mock_account_info.return_value = {
                "account_id": "xianyu_shop_flow_test",
                "display_name": "流程测试店铺",
                "avatar_url": "https://example.com/avatar.jpg",
                "status": "active",
                "shop_info": {
                    "total_products": 25,
                    "total_orders": 150
                }
            }
            
            # 3. 模拟监控服务启动
            mock_start_monitoring.return_value = True

            # Step 1: 发起连接请求
            connect_response = await client.post("/api/v1/channels/connect", json=connection_data)

        # Debug: 打印响应信息
        print(f"Response status: {connect_response.status_code}")
        print(f"Response body: {connect_response.text}")

        # Then: 验证连接响应
        assert connect_response.status_code == 201
        connect_data = connect_response.json()
        
        assert connect_data["message"] == "渠道连接成功"
        assert connect_data["platform"] == "xianyu"
        assert connect_data["alias"] == "流程测试店铺"

        channel_id = connect_data["channel_id"]

        # Step 2: 验证数据库记录创建
        channel_query = select(ChannelInstance).where(ChannelInstance.id == channel_id)
        result = await db_session.execute(channel_query)
        created_channel = result.scalar_one()

        assert created_channel.platform == "xianyu"
        assert created_channel.status == "connected"
        assert created_channel.alias == "流程测试店铺"
        assert created_channel.credentials is not None

        # Step 3: 验证连接日志记录
        log_query = select(ChannelConnectionLog).where(
            ChannelConnectionLog.channel_instance_id == channel_id
        )
        result = await db_session.execute(log_query)
        connection_logs = result.scalars().all()
        
        assert len(connection_logs) >= 1
        connect_log = connection_logs[0]
        assert connect_log.status == "success"
        assert connect_log.operation_type == "connect"

        # Step 4: 验证监控服务启动
        mock_start_monitoring.assert_called_once_with(channel_id)

        # Step 5: 验证渠道状态查询
        status_response = await client.get(f"/api/v1/channels/{channel_id}")
        assert status_response.status_code == 200
        status_data = status_response.json()

        assert status_data["status"] == "connected"
        assert status_data["platform"] == "xianyu"
        assert status_data["alias"] == "流程测试店铺"

        # Step 6: 验证渠道出现在列表中
        list_response = await client.get("/api/v1/channels/")
        assert list_response.status_code == 200
        channels_list = list_response.json()

        channel_ids = [ch["id"] for ch in channels_list]
        assert channel_id in channel_ids

        # Step 7: 验证完整流程成功
        # 所有步骤都已通过，连接流程测试完成
        print(f"✅ 完整连接流程测试成功，渠道ID: {channel_id}")

    async def test_connection_flow_with_cookie_validation_failure(self, client: AsyncClient, db_session: AsyncSession):
        """
        测试Cookie验证失败的连接流程
        对应BDD场景: Cookie验证失败的处理流程
        """
        # Given: 准备无效Cookie数据
        invalid_connection_data = {
            "platform": "xianyu",
            "platform_account_id": "invalid_cookie_shop",
            "display_name": "无效Cookie店铺",
            "cookie_config": {
                "session_id": "invalid_session",
                "user_id": "invalid_user",
                "auth_token": "invalid_token"
            }
        }

        # When: 执行连接流程
        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate:
            # 模拟Cookie验证失败
            mock_validate.side_effect = Exception("Cookie已过期或无效")
            
            connect_response = await client.post("/api/v1/channels/connect", json=invalid_connection_data)

        # Then: 验证失败响应
        assert connect_response.status_code == 400
        error_data = connect_response.json()
        
        assert "Cookie验证失败" in error_data["message"]
        assert "error_details" in error_data
        assert "Cookie已过期或无效" in error_data["error_details"]

        # 验证没有创建渠道记录
        channel_query = select(Channel).where(
            Channel.platform_account_id == "invalid_cookie_shop"
        )
        result = await db_session.execute(channel_query)
        created_channel = result.scalar_one_or_none()
        
        assert created_channel is None

        # 验证没有创建连接日志
        log_query = select(ChannelConnectionLog).where(
            ChannelConnectionLog.metadata_info.contains({"platform_account_id": "invalid_cookie_shop"})
        )
        result = await db_session.execute(log_query)
        connection_logs = result.scalars().all()
        
        assert len(connection_logs) == 0

    async def test_connection_flow_with_duplicate_account(self, client: AsyncClient, db_session: AsyncSession, valid_xianyu_cookie):
        """
        测试重复账号连接的处理流程
        对应BDD场景: 重复账号连接的处理流程
        """
        # Given: 先创建一个已存在的渠道
        existing_connection_data = {
            "platform": "xianyu",
            "platform_account_id": "duplicate_test_shop",
            "display_name": "原始店铺",
            "cookie_config": valid_xianyu_cookie
        }

        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate, \
             patch('app.connectors.xianyu_connector.XianyuConnector.get_account_info') as mock_account_info:
            
            mock_validate.return_value = True
            mock_account_info.return_value = {
                "account_id": "duplicate_test_shop",
                "display_name": "原始店铺"
            }
            
            # 创建第一个渠道
            first_response = await client.post("/api/v1/channels/connect", json=existing_connection_data)
            assert first_response.status_code == 201
            first_channel_id = first_response.json()["data"]["id"]

        # When: 尝试连接相同账号
        duplicate_connection_data = {
            "platform": "xianyu",
            "platform_account_id": "duplicate_test_shop",  # 相同的账号ID
            "display_name": "重复店铺",
            "cookie_config": {
                "session_id": "different_session",
                "user_id": "different_user",
                "auth_token": "different_token"
            }
        }

        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate:
            mock_validate.return_value = True
            
            duplicate_response = await client.post("/api/v1/channels/connect", json=duplicate_connection_data)

        # Then: 验证重复处理
        assert duplicate_response.status_code == 409  # Conflict
        duplicate_data = duplicate_response.json()
        
        assert "账号已存在" in duplicate_data["message"]
        assert "existing_channel_id" in duplicate_data["data"]
        assert duplicate_data["data"]["existing_channel_id"] == first_channel_id

        # 验证原渠道信息未被覆盖
        original_channel_response = await client.get(f"/api/v1/channels/{first_channel_id}")
        assert original_channel_response.status_code == 200
        original_data = original_channel_response.json()["data"]
        
        assert original_data["display_name"] == "原始店铺"  # 保持原有名称
        assert original_data["status"] == "connected"

        # 验证只有一个渠道记录
        channel_query = select(Channel).where(
            Channel.platform_account_id == "duplicate_test_shop"
        )
        result = await db_session.execute(channel_query)
        channels = result.scalars().all()
        
        assert len(channels) == 1

    async def test_connection_to_monitoring_flow(self, client: AsyncClient, db_session: AsyncSession, valid_xianyu_cookie):
        """
        测试从连接到监控的完整流程
        对应BDD场景: 连接成功后自动启动监控的流程
        """
        # Given: 准备连接数据
        connection_data = {
            "platform": "xianyu",
            "platform_account_id": "monitoring_flow_shop",
            "display_name": "监控流程店铺",
            "cookie_config": valid_xianyu_cookie
        }

        # When: 执行连接到监控的完整流程
        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate, \
             patch('app.connectors.xianyu_connector.XianyuConnector.get_account_info') as mock_account_info, \
             patch('app.services.monitoring_service.ChannelMonitoringService.start_monitoring') as mock_start_monitoring, \
             patch('app.services.monitoring_service.ChannelMonitoringService.get_status') as mock_get_status:
            
            # 1. 模拟连接成功
            mock_validate.return_value = True
            mock_account_info.return_value = {
                "account_id": "monitoring_flow_shop",
                "display_name": "监控流程店铺"
            }
            
            # 2. 模拟监控服务
            mock_start_monitoring.return_value = True
            mock_get_status.return_value = {
                "status": "monitoring",
                "last_check": datetime.now(timezone.utc).isoformat(),
                "check_interval": 300,  # 5分钟
                "health": "good"
            }

            # Step 1: 连接渠道
            connect_response = await client.post("/api/v1/channels/connect", json=connection_data)
            assert connect_response.status_code == 201
            channel_id = connect_response.json()["data"]["id"]

            # Step 2: 验证监控自动启动
            await asyncio.sleep(0.1)  # 模拟异步启动延迟
            mock_start_monitoring.assert_called_once_with(channel_id)

            # Step 3: 检查监控状态
            monitoring_status_response = await client.get(f"/api/v1/monitoring/channels/{channel_id}/status")
            assert monitoring_status_response.status_code == 200
            monitoring_data = monitoring_status_response.json()["data"]
            
            assert monitoring_data["status"] == "monitoring"
            assert monitoring_data["monitoring_active"] is True
            assert "last_check" in monitoring_data

            # Step 4: 验证实时状态更新
            with patch('app.services.websocket_service.WebSocketManager.broadcast') as mock_broadcast:
                # 模拟状态变化
                status_update_response = await client.post(
                    f"/api/v1/monitoring/channels/{channel_id}/trigger-status-update"
                )
                assert status_update_response.status_code == 200
                
                # 验证WebSocket广播
                mock_broadcast.assert_called()

            # Step 5: 验证监控统计
            stats_response = await client.get("/api/v1/monitoring/statistics")
            assert stats_response.status_code == 200
            stats_data = stats_response.json()["data"]
            
            assert stats_data["total_channels"] >= 1
            assert stats_data["monitoring_channels"] >= 1

    async def test_connection_flow_with_network_interruption(self, client: AsyncClient, db_session: AsyncSession, valid_xianyu_cookie):
        """
        测试网络中断情况下的连接流程
        对应BDD场景: 网络中断时的连接重试机制
        """
        # Given: 准备连接数据
        connection_data = {
            "platform": "xianyu",
            "platform_account_id": "network_test_shop",
            "display_name": "网络测试店铺",
            "cookie_config": valid_xianyu_cookie
        }

        # When: 模拟网络中断和重试
        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate, \
             patch('app.connectors.xianyu_connector.XianyuConnector.get_account_info') as mock_account_info:
            
            # 第一次尝试：网络超时
            mock_validate.side_effect = Exception("网络连接超时")
            
            first_response = await client.post("/api/v1/channels/connect", json=connection_data)
            assert first_response.status_code == 500
            assert "网络连接超时" in first_response.json()["message"]

            # 第二次尝试：连接成功
            mock_validate.side_effect = None
            mock_validate.return_value = True
            mock_account_info.return_value = {
                "account_id": "network_test_shop",
                "display_name": "网络测试店铺"
            }
            
            retry_response = await client.post("/api/v1/channels/connect", json=connection_data)

        # Then: 验证重试成功
        assert retry_response.status_code == 201
        retry_data = retry_response.json()
        
        assert retry_data["message"] == "渠道连接成功"
        channel_id = retry_data["data"]["id"]

        # 验证连接日志记录了重试过程
        log_query = select(ChannelConnectionLog).where(
            ChannelConnectionLog.channel_id == channel_id
        )
        result = await db_session.execute(log_query)
        connection_logs = result.scalars().all()
        
        # 应该有成功连接的日志
        success_logs = [log for log in connection_logs if log.status == "connected"]
        assert len(success_logs) >= 1

    async def test_end_to_end_channel_lifecycle(self, client: AsyncClient, db_session: AsyncSession, valid_xianyu_cookie):
        """
        测试渠道的完整生命周期
        对应BDD场景: 从连接到删除的完整生命周期
        """
        # Given: 准备连接数据
        connection_data = {
            "platform": "xianyu",
            "platform_account_id": "lifecycle_test_shop",
            "display_name": "生命周期测试店铺",
            "cookie_config": valid_xianyu_cookie
        }

        # When: 执行完整生命周期流程
        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate, \
             patch('app.connectors.xianyu_connector.XianyuConnector.get_account_info') as mock_account_info, \
             patch('app.services.monitoring_service.ChannelMonitoringService.start_monitoring') as mock_start_monitoring, \
             patch('app.services.monitoring_service.ChannelMonitoringService.stop_monitoring') as mock_stop_monitoring:
            
            mock_validate.return_value = True
            mock_account_info.return_value = {
                "account_id": "lifecycle_test_shop",
                "display_name": "生命周期测试店铺"
            }
            mock_start_monitoring.return_value = True
            mock_stop_monitoring.return_value = True

            # Phase 1: 连接渠道
            connect_response = await client.post("/api/v1/channels/connect", json=connection_data)
            assert connect_response.status_code == 201
            channel_id = connect_response.json()["data"]["id"]

            # Phase 2: 更新渠道信息
            update_data = {"display_name": "更新后的店铺名称"}
            update_response = await client.patch(f"/api/v1/channels/{channel_id}", json=update_data)
            assert update_response.status_code == 200
            assert update_response.json()["data"]["display_name"] == "更新后的店铺名称"

            # Phase 3: 暂停渠道
            pause_response = await client.post(f"/api/v1/channels/{channel_id}/pause")
            assert pause_response.status_code == 200
            assert pause_response.json()["data"]["status"] == "paused"

            # Phase 4: 恢复渠道
            resume_response = await client.post(f"/api/v1/channels/{channel_id}/resume")
            assert resume_response.status_code == 200
            assert resume_response.json()["data"]["status"] == "connected"

            # Phase 5: 软删除渠道
            soft_delete_response = await client.delete(f"/api/v1/channels/{channel_id}")
            assert soft_delete_response.status_code == 200
            assert "软删除成功" in soft_delete_response.json()["message"]

            # Phase 6: 验证软删除状态
            deleted_status_response = await client.get(f"/api/v1/channels/{channel_id}/status")
            assert deleted_status_response.status_code == 200
            deleted_data = deleted_status_response.json()["data"]
            assert deleted_data["is_deleted"] is True

            # Phase 7: 恢复已删除渠道
            restore_response = await client.post(f"/api/v1/channels/{channel_id}/restore")
            assert restore_response.status_code == 200
            assert "恢复成功" in restore_response.json()["message"]

            # Phase 8: 彻底删除渠道
            permanent_delete_response = await client.delete(f"/api/v1/channels/{channel_id}/permanent")
            assert permanent_delete_response.status_code == 200
            assert "彻底删除成功" in permanent_delete_response.json()["message"]

        # Then: 验证完整生命周期
        # 验证渠道已被彻底删除
        final_check_response = await client.get(f"/api/v1/channels/{channel_id}")
        assert final_check_response.status_code == 404

        # 验证数据库中没有记录
        channel_query = select(Channel).where(Channel.id == channel_id)
        result = await db_session.execute(channel_query)
        final_channel = result.scalar_one_or_none()
        assert final_channel is None

    async def test_concurrent_connection_flows(self, client: AsyncClient, db_session: AsyncSession):
        """
        测试并发连接流程
        验证系统在高并发情况下的稳定性
        """
        # Given: 准备多个连接数据
        connection_tasks = []
        
        async def create_connection(account_suffix: str):
            """创建单个连接的异步任务"""
            connection_data = {
                "platform": "xianyu",
                "platform_account_id": f"concurrent_shop_{account_suffix}",
                "display_name": f"并发测试店铺{account_suffix}",
                "cookie_config": {
                    "session_id": f"session_{account_suffix}",
                    "user_id": f"user_{account_suffix}",
                    "auth_token": f"token_{account_suffix}"
                }
            }
            
            with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate, \
                 patch('app.connectors.xianyu_connector.XianyuConnector.get_account_info') as mock_account_info:
                
                mock_validate.return_value = True
                mock_account_info.return_value = {
                    "account_id": f"concurrent_shop_{account_suffix}",
                    "display_name": f"并发测试店铺{account_suffix}"
                }
                
                response = await client.post("/api/v1/channels/connect", json=connection_data)
                return response

        # When: 执行并发连接
        for i in range(5):
            task = create_connection(f"{i:03d}")
            connection_tasks.append(task)

        responses = await asyncio.gather(*connection_tasks, return_exceptions=True)

        # Then: 验证并发结果
        successful_connections = 0
        created_channel_ids = []
        
        for response in responses:
            if hasattr(response, 'status_code') and response.status_code == 201:
                successful_connections += 1
                channel_data = response.json()["data"]
                created_channel_ids.append(channel_data["id"])

        assert successful_connections == 5  # 所有连接都应该成功
        assert len(created_channel_ids) == 5
        assert len(set(created_channel_ids)) == 5  # 所有ID都应该是唯一的

        # 验证数据库中的记录
        concurrent_channels_query = select(Channel).where(
            Channel.platform_account_id.like("concurrent_shop_%")
        )
        result = await db_session.execute(concurrent_channels_query)
        concurrent_channels = result.scalars().all()
        
        assert len(concurrent_channels) == 5

        # 清理测试数据
        for channel in concurrent_channels:
            await db_session.delete(channel)
        await db_session.commit()

    async def test_connection_flow_error_recovery(self, client: AsyncClient, db_session: AsyncSession, valid_xianyu_cookie):
        """
        测试连接流程的错误恢复机制
        对应BDD场景: 连接过程中的错误恢复
        """
        # Given: 准备连接数据
        connection_data = {
            "platform": "xianyu",
            "platform_account_id": "error_recovery_shop",
            "display_name": "错误恢复测试店铺",
            "cookie_config": valid_xianyu_cookie
        }

        # When: 模拟连接过程中的各种错误
        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate, \
             patch('app.connectors.xianyu_connector.XianyuConnector.get_account_info') as mock_account_info, \
             patch('app.services.monitoring_service.ChannelMonitoringService.start_monitoring') as mock_start_monitoring:

            # 1. Cookie验证成功
            mock_validate.return_value = True

            # 2. 获取账号信息失败
            mock_account_info.side_effect = Exception("获取账号信息失败")

            # 3. 监控服务启动失败
            mock_start_monitoring.side_effect = Exception("监控服务启动失败")

            # 尝试连接
            error_response = await client.post("/api/v1/channels/connect", json=connection_data)

        # Then: 验证错误处理
        assert error_response.status_code == 500
        error_data = error_response.json()
        assert "连接过程中发生错误" in error_data["message"]

        # 验证没有创建不完整的渠道记录
        channel_query = select(Channel).where(
            Channel.platform_account_id == "error_recovery_shop"
        )
        result = await db_session.execute(channel_query)
        error_channel = result.scalar_one_or_none()
        assert error_channel is None

        # When: 修复错误后重新连接
        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate, \
             patch('app.connectors.xianyu_connector.XianyuConnector.get_account_info') as mock_account_info, \
             patch('app.services.monitoring_service.ChannelMonitoringService.start_monitoring') as mock_start_monitoring:

            # 所有服务正常
            mock_validate.return_value = True
            mock_account_info.return_value = {
                "account_id": "error_recovery_shop",
                "display_name": "错误恢复测试店铺"
            }
            mock_start_monitoring.return_value = True

            recovery_response = await client.post("/api/v1/channels/connect", json=connection_data)

        # Then: 验证恢复成功
        assert recovery_response.status_code == 201
        recovery_data = recovery_response.json()
        assert recovery_data["message"] == "渠道连接成功"

        # 验证渠道记录正确创建
        channel_query = select(Channel).where(
            Channel.platform_account_id == "error_recovery_shop"
        )
        result = await db_session.execute(channel_query)
        recovery_channel = result.scalar_one()
        assert recovery_channel.status == "connected"
