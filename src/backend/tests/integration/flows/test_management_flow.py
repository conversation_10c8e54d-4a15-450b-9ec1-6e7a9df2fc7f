"""
渠道管理流程集成测试

基于BDD剧本测试渠道管理的完整端到端流程
验证CRUD操作、状态管理和业务规则的协作
"""

import pytest
import asyncio
from datetime import datetime, timezone, timedelta
from httpx import AsyncClient
from unittest.mock import patch, AsyncMock, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

# from app.main import app
from app.core.database import get_db
from app.models.channel import ChannelInstance
from app.models.channel_connection_log import ChannelConnectionLog


class TestManagementFlow:
    """渠道管理流程集成测试类"""

    @pytest.fixture
    async def client(self, test_client):
        """创建测试客户端"""
        return test_client

    @pytest.fixture
    async def db_session(self, test_db_session):
        """创建测试数据库会话"""
        return test_db_session

    @pytest.fixture
    async def sample_connected_channels(self, client: AsyncClient):
        """创建已连接的示例渠道"""
        import time
        import uuid

        # 使用时间戳和随机数确保唯一性
        timestamp = int(time.time() * 1000)
        unique_suffix = str(uuid.uuid4())[:8]

        channels_data = [
            {
                "platform": "xianyu",
                "alias": f"管理测试店铺A_{timestamp}",
                "cookie_config": {
                    "session_id": f"session_001_{timestamp}_{unique_suffix}",
                    "user_id": f"user_001_{timestamp}",
                    "auth_token": f"token_001_{timestamp}_{unique_suffix}"
                }
            },
            {
                "platform": "xianyu",
                "alias": f"管理测试店铺B_{timestamp}",
                "cookie_config": {
                    "session_id": f"session_002_{timestamp}_{unique_suffix}",
                    "user_id": f"user_002_{timestamp}",
                    "auth_token": f"token_002_{timestamp}_{unique_suffix}"
                }
            },
            {
                "platform": "douyin",
                "platform_account_id": "mgmt_dy_001",
                "display_name": "管理测试抖音号",
                "cookie_config": {"session_id": "dy_session_001"}
            }
        ]

        created_channels = []
        for channel_data in channels_data:
            with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate, \
                 patch('app.connectors.douyin_connector.DouyinConnector.validate_cookie') as mock_dy_validate:
                
                mock_validate.return_value = True
                mock_dy_validate.return_value = True
                
                response = await client.post("/api/v1/channels/connect", json=channel_data)
                print(f"DEBUG: Connect response status: {response.status_code}")
                print(f"DEBUG: Connect response body: {response.text}")
                assert response.status_code == 201
                created_channels.append(response.json()["data"])

        return created_channels

    async def test_complete_channel_management_workflow(self, client: AsyncClient, db_session: AsyncSession, sample_connected_channels):
        """
        测试完整的渠道管理工作流
        对应BDD场景: 从创建到删除的完整管理流程
        """
        # Given: 获取已创建的渠道
        channel = sample_connected_channels[0]
        channel_id = channel["id"]
        original_name = channel["alias"]

        print(f"DEBUG: Created channel: {channel}")
        print(f"DEBUG: Channel ID: {channel_id}")

        # Phase 1: 查看渠道详情
        detail_response = await client.get(f"/api/v1/channels/{channel_id}")
        print(f"DEBUG: Detail response status: {detail_response.status_code}")
        print(f"DEBUG: Detail response body: {detail_response.text}")
        assert detail_response.status_code == 200
        detail_data = detail_response.json()["data"]

        assert detail_data["platform"] == "xianyu"
        assert detail_data["alias"] == original_name
        assert detail_data["status"] == "connected"

        # Phase 2: 更新渠道别名
        new_alias = "更新后的店铺别名"
        update_data = {"alias": new_alias}

        update_response = await client.put(f"/api/v1/channels/{channel_id}", json=update_data)
        assert update_response.status_code == 200
        updated_data = update_response.json()["data"]

        assert updated_data["alias"] == new_alias
        assert updated_data["updated_at"] != detail_data["updated_at"]

        # Phase 3: 暂停渠道
        with patch('app.services.monitoring_service.ChannelMonitoringService.pause_monitoring') as mock_pause:
            mock_pause.return_value = True
            
            pause_response = await client.post(f"/api/v1/channels/{channel_id}/pause")
            assert pause_response.status_code == 200
            paused_data = pause_response.json()["data"]
            
            assert paused_data["status"] == "paused"
            mock_pause.assert_called_once_with(channel_id)

        # Phase 4: 恢复渠道
        with patch('app.services.monitoring_service.ChannelMonitoringService.resume_monitoring') as mock_resume:
            mock_resume.return_value = True
            
            resume_response = await client.post(f"/api/v1/channels/{channel_id}/resume")
            assert resume_response.status_code == 200
            resumed_data = resume_response.json()["data"]
            
            assert resumed_data["status"] == "connected"
            mock_resume.assert_called_once_with(channel_id)

        # Phase 5: 查看操作历史
        history_response = await client.get(f"/api/v1/channels/{channel_id}/logs")
        assert history_response.status_code == 200
        history_data = history_response.json()["data"]
        
        # 验证操作历史包含所有操作
        operation_types = [log["operation_type"] for log in history_data]
        expected_operations = ["connect", "update", "pause", "resume"]
        
        for operation in expected_operations:
            assert operation in operation_types

        # Phase 6: 软删除渠道
        with patch('app.services.monitoring_service.ChannelMonitoringService.stop_monitoring') as mock_stop:
            mock_stop.return_value = True
            
            delete_response = await client.delete(f"/api/v1/channels/{channel_id}")
            assert delete_response.status_code == 200
            delete_data = delete_response.json()
            
            assert "软删除成功" in delete_data["message"]
            mock_stop.assert_called_once_with(channel_id)

        # Phase 7: 验证软删除状态
        deleted_status_response = await client.get(f"/api/v1/channels/{channel_id}/status")
        assert deleted_status_response.status_code == 200
        deleted_status = deleted_status_response.json()["data"]
        
        assert deleted_status["is_deleted"] is True
        assert deleted_status["deleted_at"] is not None

        # Phase 8: 验证渠道从主列表中隐藏
        list_response = await client.get("/api/v1/channels/")
        assert list_response.status_code == 200
        active_channels = list_response.json()["data"]
        
        active_channel_ids = [ch["id"] for ch in active_channels]
        assert channel_id not in active_channel_ids

        # Phase 9: 查看已删除渠道列表
        deleted_list_response = await client.get("/api/v1/channels/?include_deleted=true")
        assert deleted_list_response.status_code == 200
        all_channels = deleted_list_response.json()["data"]
        
        deleted_channel = next((ch for ch in all_channels if ch["id"] == channel_id), None)
        assert deleted_channel is not None
        assert deleted_channel["is_deleted"] is True

    async def test_batch_channel_management_operations(self, client: AsyncClient, db_session: AsyncSession, sample_connected_channels):
        """
        测试批量渠道管理操作
        对应BDD场景: 批量操作渠道的管理流程
        """
        # Given: 获取多个渠道
        channel_ids = [ch["id"] for ch in sample_connected_channels]
        
        # Phase 1: 批量暂停渠道
        batch_pause_data = {
            "channel_ids": channel_ids[:2],  # 暂停前两个
            "operation": "pause",
            "reason": "批量维护"
        }
        
        with patch('app.services.monitoring_service.ChannelMonitoringService.pause_monitoring') as mock_pause:
            mock_pause.return_value = True
            
            batch_pause_response = await client.post("/api/v1/channels/batch-operation", json=batch_pause_data)
            assert batch_pause_response.status_code == 200
            pause_result = batch_pause_response.json()["data"]
            
            assert pause_result["successful_count"] == 2
            assert pause_result["failed_count"] == 0
            assert len(pause_result["results"]) == 2

        # Phase 2: 验证批量暂停结果
        for channel_id in channel_ids[:2]:
            status_response = await client.get(f"/api/v1/channels/{channel_id}/status")
            status_data = status_response.json()["data"]
            assert status_data["status"] == "paused"

        # Phase 3: 批量恢复渠道
        batch_resume_data = {
            "channel_ids": channel_ids[:2],
            "operation": "resume",
            "reason": "维护完成"
        }
        
        with patch('app.services.monitoring_service.ChannelMonitoringService.resume_monitoring') as mock_resume:
            mock_resume.return_value = True
            
            batch_resume_response = await client.post("/api/v1/channels/batch-operation", json=batch_resume_data)
            assert batch_resume_response.status_code == 200
            resume_result = batch_resume_response.json()["data"]
            
            assert resume_result["successful_count"] == 2

        # Phase 4: 批量更新别名
        batch_update_data = {
            "updates": [
                {"channel_id": channel_ids[0], "display_name": "批量更新店铺A"},
                {"channel_id": channel_ids[1], "display_name": "批量更新店铺B"}
            ]
        }
        
        batch_update_response = await client.patch("/api/v1/channels/batch-update", json=batch_update_data)
        assert batch_update_response.status_code == 200
        update_result = batch_update_response.json()["data"]
        
        assert update_result["successful_count"] == 2

        # Phase 5: 验证批量更新结果
        for i, channel_id in enumerate(channel_ids[:2]):
            detail_response = await client.get(f"/api/v1/channels/{channel_id}")
            detail_data = detail_response.json()["data"]
            expected_name = f"批量更新店铺{'A' if i == 0 else 'B'}"
            assert detail_data["display_name"] == expected_name

        # Phase 6: 批量软删除
        batch_delete_data = {
            "channel_ids": channel_ids,
            "operation": "soft_delete",
            "reason": "批量清理"
        }
        
        with patch('app.services.monitoring_service.ChannelMonitoringService.stop_monitoring') as mock_stop:
            mock_stop.return_value = True
            
            batch_delete_response = await client.post("/api/v1/channels/batch-operation", json=batch_delete_data)
            assert batch_delete_response.status_code == 200
            delete_result = batch_delete_response.json()["data"]
            
            assert delete_result["successful_count"] == 3

        # Phase 7: 验证批量删除结果
        for channel_id in channel_ids:
            status_response = await client.get(f"/api/v1/channels/{channel_id}/status")
            status_data = status_response.json()["data"]
            assert status_data["is_deleted"] is True

    async def test_channel_status_transition_flow(self, client: AsyncClient, db_session: AsyncSession, sample_connected_channels):
        """
        测试渠道状态转换流程
        对应BDD场景: 渠道状态的完整转换流程
        """
        # Given: 获取测试渠道
        channel = sample_connected_channels[0]
        channel_id = channel["id"]

        # 定义状态转换序列
        status_transitions = [
            ("connected", "pause", "paused"),
            ("paused", "resume", "connected"),
            ("connected", "disconnect", "disconnected"),
            ("disconnected", "reconnect", "connected"),
            ("connected", "pause", "paused"),
            ("paused", "disconnect", "disconnected")
        ]

        # When & Then: 执行状态转换序列
        for current_status, action, expected_status in status_transitions:
            # 验证当前状态
            status_response = await client.get(f"/api/v1/channels/{channel_id}/status")
            current_data = status_response.json()["data"]
            assert current_data["status"] == current_status

            # 执行状态转换
            with patch('app.services.monitoring_service.ChannelMonitoringService.pause_monitoring') as mock_pause, \
                 patch('app.services.monitoring_service.ChannelMonitoringService.resume_monitoring') as mock_resume, \
                 patch('app.services.monitoring_service.ChannelMonitoringService.stop_monitoring') as mock_stop, \
                 patch('app.services.monitoring_service.ChannelMonitoringService.start_monitoring') as mock_start:
                
                mock_pause.return_value = True
                mock_resume.return_value = True
                mock_stop.return_value = True
                mock_start.return_value = True

                if action == "pause":
                    action_response = await client.post(f"/api/v1/channels/{channel_id}/pause")
                elif action == "resume":
                    action_response = await client.post(f"/api/v1/channels/{channel_id}/resume")
                elif action == "disconnect":
                    action_response = await client.post(f"/api/v1/channels/{channel_id}/disconnect")
                elif action == "reconnect":
                    with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate:
                        mock_validate.return_value = True
                        action_response = await client.post(f"/api/v1/channels/{channel_id}/reconnect")

            # 验证状态转换结果
            assert action_response.status_code == 200
            result_data = action_response.json()["data"]
            assert result_data["status"] == expected_status

            # 验证状态转换日志
            logs_response = await client.get(f"/api/v1/channels/{channel_id}/logs")
            logs_data = logs_response.json()["data"]
            
            latest_log = logs_data[0]  # 最新的日志
            assert latest_log["operation_type"] == action
            assert latest_log["status"] == expected_status

    async def test_channel_configuration_management_flow(self, client: AsyncClient, db_session: AsyncSession, sample_connected_channels):
        """
        测试渠道配置管理流程
        对应BDD场景: 渠道配置的完整管理流程
        """
        # Given: 获取测试渠道
        channel = sample_connected_channels[0]
        channel_id = channel["id"]

        # Phase 1: 查看当前配置
        config_response = await client.get(f"/api/v1/channels/{channel_id}/config")
        assert config_response.status_code == 200
        current_config = config_response.json()["data"]
        
        assert "cookie_config" in current_config
        assert "monitoring_config" in current_config
        assert "automation_config" in current_config

        # Phase 2: 更新Cookie配置
        new_cookie_config = {
            "session_id": "updated_session_123",
            "user_id": "updated_user_456",
            "auth_token": "updated_token_789"
        }
        
        cookie_update_data = {
            "cookie_config": new_cookie_config,
            "validate_immediately": True
        }
        
        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate:
            mock_validate.return_value = True
            
            cookie_update_response = await client.patch(
                f"/api/v1/channels/{channel_id}/cookie-config",
                json=cookie_update_data
            )
            
        assert cookie_update_response.status_code == 200
        cookie_result = cookie_update_response.json()
        assert "Cookie配置更新成功" in cookie_result["message"]

        # Phase 3: 更新监控配置
        monitoring_config_data = {
            "monitoring_config": {
                "check_interval": 300,  # 5分钟
                "retry_attempts": 3,
                "timeout": 30,
                "alert_on_failure": True
            }
        }
        
        monitoring_update_response = await client.patch(
            f"/api/v1/channels/{channel_id}/monitoring-config",
            json=monitoring_config_data
        )
        
        assert monitoring_update_response.status_code == 200
        monitoring_result = monitoring_update_response.json()
        assert "监控配置更新成功" in monitoring_result["message"]

        # Phase 4: 更新自动化配置
        automation_config_data = {
            "automation_config": {
                "auto_reply_enabled": True,
                "auto_reply_delay": 60,
                "working_hours": {
                    "start": "09:00",
                    "end": "18:00"
                },
                "keywords_filter": ["关键词1", "关键词2"]
            }
        }
        
        automation_update_response = await client.patch(
            f"/api/v1/channels/{channel_id}/automation-config",
            json=automation_config_data
        )
        
        assert automation_update_response.status_code == 200
        automation_result = automation_update_response.json()
        assert "自动化配置更新成功" in automation_result["message"]

        # Phase 5: 验证配置更新
        updated_config_response = await client.get(f"/api/v1/channels/{channel_id}/config")
        updated_config = updated_config_response.json()["data"]
        
        # 验证监控配置
        assert updated_config["monitoring_config"]["check_interval"] == 300
        assert updated_config["monitoring_config"]["retry_attempts"] == 3
        
        # 验证自动化配置
        assert updated_config["automation_config"]["auto_reply_enabled"] is True
        assert updated_config["automation_config"]["auto_reply_delay"] == 60

        # Phase 6: 重置配置为默认值
        reset_response = await client.post(f"/api/v1/channels/{channel_id}/reset-config")
        assert reset_response.status_code == 200
        reset_result = reset_response.json()
        assert "配置重置成功" in reset_result["message"]

        # Phase 7: 验证配置重置
        reset_config_response = await client.get(f"/api/v1/channels/{channel_id}/config")
        reset_config = reset_config_response.json()["data"]
        
        # 验证配置已重置为默认值
        assert reset_config["monitoring_config"]["check_interval"] == 600  # 默认10分钟
        assert reset_config["automation_config"]["auto_reply_enabled"] is False  # 默认关闭

    async def test_channel_data_export_and_backup_flow(self, client: AsyncClient, db_session: AsyncSession, sample_connected_channels):
        """
        测试渠道数据导出和备份流程
        对应BDD场景: 渠道数据的导出和备份管理流程
        """
        # Given: 获取测试渠道
        channel_ids = [ch["id"] for ch in sample_connected_channels]

        # Phase 1: 导出单个渠道数据
        single_export_response = await client.get(f"/api/v1/channels/{channel_ids[0]}/export")
        assert single_export_response.status_code == 200
        single_export_data = single_export_response.json()["data"]

        assert "channel_info" in single_export_data
        assert "connection_logs" in single_export_data
        assert "configuration" in single_export_data
        assert single_export_data["channel_info"]["id"] == channel_ids[0]

        # Phase 2: 批量导出渠道数据
        batch_export_data = {
            "channel_ids": channel_ids,
            "include_logs": True,
            "include_config": True,
            "date_range": {
                "start": (datetime.now(timezone.utc) - timedelta(days=30)).isoformat(),
                "end": datetime.now(timezone.utc).isoformat()
            }
        }

        batch_export_response = await client.post("/api/v1/channels/batch-export", json=batch_export_data)
        assert batch_export_response.status_code == 200
        batch_export_result = batch_export_response.json()["data"]

        assert "export_id" in batch_export_result
        assert "download_url" in batch_export_result
        assert batch_export_result["total_channels"] == 3

        # Phase 3: 检查导出状态
        export_id = batch_export_result["export_id"]
        status_response = await client.get(f"/api/v1/exports/{export_id}/status")
        assert status_response.status_code == 200
        status_data = status_response.json()["data"]

        assert status_data["status"] in ["processing", "completed"]
        assert "progress" in status_data

        # Phase 4: 创建渠道备份
        backup_data = {
            "backup_name": "测试备份_" + datetime.now().strftime("%Y%m%d_%H%M%S"),
            "channel_ids": channel_ids,
            "include_full_config": True,
            "include_logs": True
        }

        backup_response = await client.post("/api/v1/channels/create-backup", json=backup_data)
        assert backup_response.status_code == 201
        backup_result = backup_response.json()["data"]

        assert "backup_id" in backup_result
        assert "backup_name" in backup_result
        backup_id = backup_result["backup_id"]

        # Phase 5: 查看备份列表
        backups_response = await client.get("/api/v1/backups")
        assert backups_response.status_code == 200
        backups_data = backups_response.json()["data"]

        backup_ids = [backup["backup_id"] for backup in backups_data]
        assert backup_id in backup_ids

        # Phase 6: 恢复渠道备份（模拟场景）
        restore_data = {
            "backup_id": backup_id,
            "restore_options": {
                "restore_config": True,
                "restore_logs": False,
                "overwrite_existing": False
            }
        }

        restore_response = await client.post("/api/v1/channels/restore-backup", json=restore_data)
        assert restore_response.status_code == 200
        restore_result = restore_response.json()
        assert "恢复操作已启动" in restore_result["message"]

    async def test_channel_analytics_and_reporting_flow(self, client: AsyncClient, db_session: AsyncSession, sample_connected_channels):
        """
        测试渠道分析和报告流程
        对应BDD场景: 渠道数据分析和报告生成流程
        """
        # Given: 获取测试渠道
        channel_ids = [ch["id"] for ch in sample_connected_channels]

        # Phase 1: 获取渠道概览统计
        overview_response = await client.get("/api/v1/analytics/channels/overview")
        assert overview_response.status_code == 200
        overview_data = overview_response.json()["data"]

        assert "total_channels" in overview_data
        assert "active_channels" in overview_data
        assert "platform_distribution" in overview_data
        assert overview_data["total_channels"] >= 3

        # Phase 2: 获取单个渠道详细分析
        channel_analytics_response = await client.get(f"/api/v1/analytics/channels/{channel_ids[0]}")
        assert channel_analytics_response.status_code == 200
        analytics_data = channel_analytics_response.json()["data"]

        assert "connection_stats" in analytics_data
        assert "performance_metrics" in analytics_data
        assert "activity_timeline" in analytics_data

        # Phase 3: 生成渠道性能报告
        report_data = {
            "report_type": "performance",
            "channel_ids": channel_ids,
            "date_range": {
                "start": (datetime.now(timezone.utc) - timedelta(days=7)).isoformat(),
                "end": datetime.now(timezone.utc).isoformat()
            },
            "metrics": ["uptime", "response_time", "error_rate", "message_count"]
        }

        report_response = await client.post("/api/v1/analytics/generate-report", json=report_data)
        assert report_response.status_code == 201
        report_result = report_response.json()["data"]

        assert "report_id" in report_result
        assert "estimated_completion" in report_result
        report_id = report_result["report_id"]

        # Phase 4: 检查报告生成状态
        report_status_response = await client.get(f"/api/v1/analytics/reports/{report_id}/status")
        assert report_status_response.status_code == 200
        report_status = report_status_response.json()["data"]

        assert report_status["status"] in ["generating", "completed", "failed"]

        # Phase 5: 获取平台对比分析
        comparison_response = await client.get("/api/v1/analytics/platform-comparison")
        assert comparison_response.status_code == 200
        comparison_data = comparison_response.json()["data"]

        assert "platforms" in comparison_data
        assert len(comparison_data["platforms"]) >= 2  # 至少有xianyu和douyin

        # Phase 6: 获取趋势分析
        trend_data = {
            "metric": "connection_stability",
            "period": "7d",
            "channel_ids": channel_ids
        }

        trend_response = await client.post("/api/v1/analytics/trend-analysis", json=trend_data)
        assert trend_response.status_code == 200
        trend_result = trend_response.json()["data"]

        assert "time_series" in trend_result
        assert "trend_direction" in trend_result
        assert "insights" in trend_result

    async def test_channel_maintenance_and_health_check_flow(self, client: AsyncClient, db_session: AsyncSession, sample_connected_channels):
        """
        测试渠道维护和健康检查流程
        对应BDD场景: 渠道维护和健康监控的完整流程
        """
        # Given: 获取测试渠道
        channel = sample_connected_channels[0]
        channel_id = channel["id"]

        # Phase 1: 执行渠道健康检查
        health_check_response = await client.post(f"/api/v1/channels/{channel_id}/health-check")
        assert health_check_response.status_code == 200
        health_data = health_check_response.json()["data"]

        assert "overall_health" in health_data
        assert "component_status" in health_data
        assert "recommendations" in health_data

        # Phase 2: 执行渠道诊断
        diagnostic_response = await client.post(f"/api/v1/channels/{channel_id}/diagnose")
        assert diagnostic_response.status_code == 200
        diagnostic_data = diagnostic_response.json()["data"]

        assert "diagnostic_results" in diagnostic_data
        assert "issues_found" in diagnostic_data
        assert "suggested_actions" in diagnostic_data

        # Phase 3: 执行渠道维护
        maintenance_data = {
            "maintenance_type": "routine",
            "actions": [
                "clear_cache",
                "refresh_connection",
                "update_monitoring"
            ],
            "schedule_downtime": False
        }

        with patch('app.services.maintenance_service.ChannelMaintenanceService.perform_maintenance') as mock_maintenance:
            mock_maintenance.return_value = {
                "status": "completed",
                "actions_performed": maintenance_data["actions"],
                "duration": 30  # 秒
            }

            maintenance_response = await client.post(
                f"/api/v1/channels/{channel_id}/maintenance",
                json=maintenance_data
            )

        assert maintenance_response.status_code == 200
        maintenance_result = maintenance_response.json()["data"]
        assert maintenance_result["status"] == "completed"

        # Phase 4: 批量健康检查
        batch_health_data = {
            "channel_ids": [ch["id"] for ch in sample_connected_channels],
            "check_type": "comprehensive"
        }

        batch_health_response = await client.post("/api/v1/channels/batch-health-check", json=batch_health_data)
        assert batch_health_response.status_code == 200
        batch_health_result = batch_health_response.json()["data"]

        assert "total_checked" in batch_health_result
        assert "healthy_count" in batch_health_result
        assert "issues_count" in batch_health_result
        assert batch_health_result["total_checked"] == 3

        # Phase 5: 查看维护历史
        maintenance_history_response = await client.get(f"/api/v1/channels/{channel_id}/maintenance-history")
        assert maintenance_history_response.status_code == 200
        history_data = maintenance_history_response.json()["data"]

        assert len(history_data) >= 1
        latest_maintenance = history_data[0]
        assert latest_maintenance["maintenance_type"] == "routine"

        # Phase 6: 设置维护计划
        schedule_data = {
            "maintenance_type": "scheduled",
            "schedule": {
                "frequency": "weekly",
                "day_of_week": "sunday",
                "time": "02:00"
            },
            "actions": ["health_check", "performance_optimization"],
            "auto_execute": True
        }

        schedule_response = await client.post(
            f"/api/v1/channels/{channel_id}/schedule-maintenance",
            json=schedule_data
        )
        assert schedule_response.status_code == 201
        schedule_result = schedule_response.json()
        assert "维护计划创建成功" in schedule_result["message"]

    async def test_error_handling_and_recovery_in_management_flow(self, client: AsyncClient, db_session: AsyncSession, sample_connected_channels):
        """
        测试管理流程中的错误处理和恢复
        对应BDD场景: 管理操作中的错误处理和恢复机制
        """
        # Given: 获取测试渠道
        channel = sample_connected_channels[0]
        channel_id = channel["id"]

        # Phase 1: 模拟更新操作失败
        invalid_update_data = {
            "display_name": "",  # 空名称应该失败
            "invalid_field": "invalid_value"
        }

        invalid_update_response = await client.patch(f"/api/v1/channels/{channel_id}", json=invalid_update_data)
        assert invalid_update_response.status_code == 400
        error_data = invalid_update_response.json()
        assert "validation_errors" in error_data

        # Phase 2: 模拟状态转换失败
        with patch('app.services.monitoring_service.ChannelMonitoringService.pause_monitoring') as mock_pause:
            mock_pause.side_effect = Exception("监控服务不可用")

            pause_error_response = await client.post(f"/api/v1/channels/{channel_id}/pause")
            assert pause_error_response.status_code == 500
            pause_error_data = pause_error_response.json()
            assert "监控服务不可用" in pause_error_data["message"]

        # Phase 3: 验证渠道状态未改变
        status_check_response = await client.get(f"/api/v1/channels/{channel_id}/status")
        status_data = status_check_response.json()["data"]
        assert status_data["status"] == "connected"  # 状态应该保持不变

        # Phase 4: 模拟配置更新失败后的恢复
        invalid_config_data = {
            "cookie_config": {
                "invalid_session": "test"  # 无效的Cookie格式
            }
        }

        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate:
            mock_validate.side_effect = Exception("Cookie格式无效")

            config_error_response = await client.patch(
                f"/api/v1/channels/{channel_id}/cookie-config",
                json=invalid_config_data
            )

        assert config_error_response.status_code == 400
        config_error_data = config_error_response.json()
        assert "Cookie格式无效" in config_error_data["message"]

        # Phase 5: 执行正确的配置更新
        valid_config_data = {
            "cookie_config": {
                "session_id": "valid_session_recovery",
                "user_id": "valid_user_recovery",
                "auth_token": "valid_token_recovery"
            }
        }

        with patch('app.connectors.xianyu_connector.XianyuConnector.validate_cookie') as mock_validate:
            mock_validate.return_value = True

            recovery_response = await client.patch(
                f"/api/v1/channels/{channel_id}/cookie-config",
                json=valid_config_data
            )

        assert recovery_response.status_code == 200
        recovery_data = recovery_response.json()
        assert "Cookie配置更新成功" in recovery_data["message"]

        # Phase 6: 验证错误日志记录
        error_logs_response = await client.get(f"/api/v1/channels/{channel_id}/error-logs")
        assert error_logs_response.status_code == 200
        error_logs = error_logs_response.json()["data"]

        # 应该有错误操作的日志记录
        error_operations = [log for log in error_logs if log["status"] == "error"]
        assert len(error_operations) >= 2  # 至少有暂停失败和配置更新失败的日志
