"""
渠道数据库操作集成测试

基于设计文档中的channel_instances表结构
测试渠道数据库的CRUD操作，验证数据一致性、约束条件和事务处理
"""

import pytest
import asyncio
import uuid
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from unittest.mock import patch

from app.core.database import get_db, database_manager
from app.models.channel import ChannelInstance
from app.models.channel_connection_log import ChannelConnectionLog


class TestChannelDatabaseOperations:
    """渠道数据库操作集成测试类 - 符合设计文档规范"""

    @pytest.fixture
    def sample_channel_data(self):
        """示例渠道数据 - 符合设计文档的channel_instances表结构"""
        return {
            "id": "ci_xianyu_test_123",
            "platform": "xianyu",
            "alias": "测试店铺",
            "status": "connected",
            "credentials": {
                "session_id": "test_session_123",
                "user_id": "test_user_456",
                "auth_token": "test_token_789"
            }
        }

    async def test_create_channel_instance(self, test_db_session: AsyncSession, sample_channel_data):
        """
        测试创建渠道实例
        验证符合设计文档channel_instances表结构的数据创建
        """
        # Given: 准备渠道数据
        channel_data = sample_channel_data.copy()
        
        # When: 创建渠道实例记录
        channel = ChannelInstance(
            id=channel_data["id"],
            platform=channel_data["platform"],
            alias=channel_data["alias"],
            status=channel_data["status"],
            credentials=channel_data["credentials"]
        )
        
        test_db_session.add(channel)
        await test_db_session.commit()
        await test_db_session.refresh(channel)

        # Then: 验证数据库记录符合设计文档规范
        assert channel.id == "ci_xianyu_test_123"  # VARCHAR(50)
        assert channel.platform == "xianyu"  # VARCHAR(50)
        assert channel.alias == "测试店铺"  # VARCHAR(100)
        assert channel.status == "connected"  # VARCHAR(50)
        assert channel.credentials == channel_data["credentials"]  # JSONB
        assert channel.created_at is not None  # TIMESTAMPTZ
        assert channel.updated_at is not None  # TIMESTAMPTZ
        assert channel.team_id is None  # UUID, nullable

    async def test_channel_instance_with_team_id(self, test_db_session: AsyncSession, sample_channel_data):
        """
        测试创建带团队ID的渠道实例
        验证team_id外键字段
        """
        # Given: 准备带团队ID的渠道数据
        team_id = uuid.uuid4()
        channel_data = sample_channel_data.copy()
        channel_data["id"] = "ci_xianyu_team_456"

        # When: 创建渠道实例记录
        channel = ChannelInstance(
            id=channel_data["id"],
            team_id=team_id,
            platform=channel_data["platform"],
            alias=channel_data["alias"],
            status=channel_data["status"],
            credentials=channel_data["credentials"]
        )

        test_db_session.add(channel)
        await test_db_session.commit()
        await test_db_session.refresh(channel)

        # Then: 验证团队ID字段
        assert channel.team_id == team_id
        assert isinstance(channel.team_id, uuid.UUID)

    async def test_channel_instance_credentials_jsonb(self, test_db_session: AsyncSession, sample_channel_data):
        """
        测试渠道实例的JSONB凭证字段
        验证复杂JSON数据的存储和检索
        """
        # Given: 准备复杂的凭证数据
        complex_credentials = {
            "session_id": "complex_session_123",
            "cookies": {
                "auth_token": "token_value",
                "csrf_token": "csrf_value",
                "session_data": {
                    "user_id": "12345",
                    "permissions": ["read", "write"],
                    "metadata": {
                        "last_login": "2025-08-03T10:00:00Z",
                        "device_info": "Chrome/91.0"
                    }
                }
            },
            "api_keys": ["key1", "key2", "key3"]
        }
        
        channel_data = sample_channel_data.copy()
        channel_data["id"] = "ci_xianyu_complex_789"
        channel_data["credentials"] = complex_credentials
        
        # When: 创建渠道实例
        channel = ChannelInstance(
            id=channel_data["id"],
            platform=channel_data["platform"],
            alias=channel_data["alias"],
            status=channel_data["status"],
            credentials=channel_data["credentials"]
        )
        
        test_db_session.add(channel)
        await test_db_session.commit()
        await test_db_session.refresh(channel)

        # Then: 验证JSONB数据完整性
        assert channel.credentials == complex_credentials
        assert channel.credentials["cookies"]["session_data"]["user_id"] == "12345"
        assert len(channel.credentials["api_keys"]) == 3

    async def test_channel_instance_query_operations(self, test_db_session: AsyncSession, sample_channel_data):
        """
        测试渠道实例的查询操作
        验证按平台、状态等字段的查询功能
        """
        # Given: 创建多个渠道实例
        channels_data = [
            {
                "id": "ci_xianyu_query_1",
                "platform": "xianyu",
                "alias": "闲鱼店铺1",
                "status": "connected",
                "credentials": {"session": "session1"}
            },
            {
                "id": "ci_douyin_query_2", 
                "platform": "douyin",
                "alias": "抖音店铺1",
                "status": "disconnected",
                "credentials": {"session": "session2"}
            },
            {
                "id": "ci_xianyu_query_3",
                "platform": "xianyu", 
                "alias": "闲鱼店铺2",
                "status": "connected",
                "credentials": {"session": "session3"}
            }
        ]
        
        for data in channels_data:
            channel = ChannelInstance(**data)
            test_db_session.add(channel)
        
        await test_db_session.commit()

        # When & Then: 测试各种查询操作
        
        # 查询所有闲鱼平台的渠道
        xianyu_query = select(ChannelInstance).where(ChannelInstance.platform == "xianyu")
        result = await test_db_session.execute(xianyu_query)
        xianyu_channels = result.scalars().all()
        assert len(xianyu_channels) == 2
        
        # 查询已连接状态的渠道
        connected_query = select(ChannelInstance).where(ChannelInstance.status == "connected")
        result = await test_db_session.execute(connected_query)
        connected_channels = result.scalars().all()
        assert len(connected_channels) == 2
        
        # 组合查询：闲鱼平台且已连接
        combined_query = select(ChannelInstance).where(
            and_(
                ChannelInstance.platform == "xianyu",
                ChannelInstance.status == "connected"
            )
        )
        result = await test_db_session.execute(combined_query)
        combined_channels = result.scalars().all()
        assert len(combined_channels) == 2

    async def test_channel_instance_update_operations(self, test_db_session: AsyncSession, sample_channel_data):
        """
        测试渠道实例的更新操作
        验证字段更新和时间戳自动更新
        """
        # Given: 创建渠道实例
        channel_data = sample_channel_data.copy()
        channel_data["id"] = "ci_xianyu_update_test"
        
        channel = ChannelInstance(**channel_data)
        test_db_session.add(channel)
        await test_db_session.commit()
        await test_db_session.refresh(channel)
        
        original_updated_at = channel.updated_at

        # When: 更新渠道信息
        await asyncio.sleep(0.1)  # 确保时间戳不同
        channel.alias = "更新后的店铺名称"
        channel.status = "disconnected"
        channel.credentials = {"new_session": "updated_session"}
        
        await test_db_session.commit()
        await test_db_session.refresh(channel)

        # Then: 验证更新结果
        assert channel.alias == "更新后的店铺名称"
        assert channel.status == "disconnected"
        assert channel.credentials == {"new_session": "updated_session"}
        assert channel.updated_at > original_updated_at

    async def test_channel_instance_delete_operations(self, test_db_session: AsyncSession, sample_channel_data):
        """
        测试渠道实例的删除操作
        验证物理删除功能（设计文档中没有软删除字段）
        """
        # Given: 创建渠道实例
        channel_data = sample_channel_data.copy()
        channel_data["id"] = "ci_xianyu_delete_test"
        
        channel = ChannelInstance(**channel_data)
        test_db_session.add(channel)
        await test_db_session.commit()
        
        # 验证渠道存在
        query = select(ChannelInstance).where(ChannelInstance.id == channel_data["id"])
        result = await test_db_session.execute(query)
        found_channel = result.scalar_one_or_none()
        assert found_channel is not None

        # When: 删除渠道实例
        await test_db_session.delete(channel)
        await test_db_session.commit()

        # Then: 验证渠道已被删除
        result = await test_db_session.execute(query)
        deleted_channel = result.scalar_one_or_none()
        assert deleted_channel is None
