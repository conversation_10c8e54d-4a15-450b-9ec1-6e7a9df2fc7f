"""
渠道删除流程集成测试

基于设计文档channel_instances表结构测试渠道删除流程
验证数据一致性、业务规则和关联数据处理
注意：设计文档中没有软删除字段，采用物理删除
"""

import pytest
import asyncio
from datetime import datetime, timezone, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from unittest.mock import patch, AsyncMock

from app.core.database import get_db, database_manager
from app.models.channel import ChannelInstance
from app.models.channel_connection_log import ChannelConnectionLog


class TestChannelDeleteFlow:
    """渠道删除流程集成测试类 - 符合设计文档规范"""

    @pytest.fixture
    async def sample_channels_with_logs(self, test_db_session: AsyncSession):
        """创建包含连接日志的示例渠道"""
        import uuid
        import time

        # 使用时间戳和随机数确保ID唯一性
        timestamp = int(time.time() * 1000)
        unique_suffix = str(uuid.uuid4())[:8]

        channels_data = [
            {
                "id": f"ci_xianyu_delete_{timestamp}_001_{unique_suffix}",
                "platform": "xianyu",
                "alias": f"待删除店铺A_{timestamp}",
                "status": "connected",
                "credentials": {"session": f"session_001_{timestamp}"}
            },
            {
                "id": f"ci_xianyu_delete_{timestamp}_002_{unique_suffix}",
                "platform": "xianyu",
                "alias": f"待删除店铺B_{timestamp}",
                "status": "disconnected",
                "credentials": {"session": f"session_002_{timestamp}"}
            },
            {
                "id": f"ci_douyin_delete_{timestamp}_003_{unique_suffix}",
                "platform": "douyin",
                "alias": f"抖音店铺C_{timestamp}",
                "status": "connected",
                "credentials": {"session": f"session_003_{timestamp}"}
            }
        ]
        
        channels = []
        for data in channels_data:
            channel = ChannelInstance(**data)
            test_db_session.add(channel)
            channels.append(channel)
        
        await test_db_session.commit()
        
        # 为每个渠道创建连接日志
        for i, channel in enumerate(channels):
            for j in range(3):  # 每个渠道3条日志
                log = ChannelConnectionLog(
                    channel_instance_id=channel.id,
                    status="connected" if j % 2 == 0 else "disconnected",
                    operation_type="connect" if j % 2 == 0 else "disconnect",
                    metadata_info={"test_log": f"log_{i}_{j}"}
                )
                test_db_session.add(log)
        
        await test_db_session.commit()
        return channels

    async def test_delete_channel_instance_basic(self, test_db_session: AsyncSession, sample_channels_with_logs):
        """
        测试基本的渠道实例删除
        验证物理删除功能
        """
        # Given: 选择要删除的渠道
        channel_to_delete = sample_channels_with_logs[0]
        channel_id = channel_to_delete.id
        
        # 验证渠道存在
        query = select(ChannelInstance).where(ChannelInstance.id == channel_id)
        result = await test_db_session.execute(query)
        found_channel = result.scalar_one_or_none()
        assert found_channel is not None
        assert "待删除店铺A" in found_channel.alias

        # When: 删除渠道实例
        await test_db_session.delete(channel_to_delete)
        await test_db_session.commit()

        # Then: 验证渠道已被物理删除
        result = await test_db_session.execute(query)
        deleted_channel = result.scalar_one_or_none()
        assert deleted_channel is None

    async def test_delete_channel_with_cascade_logs(self, test_db_session: AsyncSession):
        """
        测试删除渠道时关联日志的处理
        验证外键约束和级联删除
        """
        import uuid
        import time

        # Given: 创建独立的测试数据
        timestamp = int(time.time() * 1000)
        unique_suffix = str(uuid.uuid4())[:8]

        channel_id = f"ci_cascade_test_{timestamp}_{unique_suffix}"
        channel = ChannelInstance(
            id=channel_id,
            platform="xianyu",
            alias=f"级联测试店铺_{timestamp}",
            status="connected",
            credentials={"session": f"session_cascade_{timestamp}"}
        )
        test_db_session.add(channel)
        await test_db_session.commit()

        # 为渠道创建连接日志
        for i in range(3):
            log = ChannelConnectionLog(
                channel_instance_id=channel_id,
                status="connected" if i % 2 == 0 else "disconnected",
                operation_type="connect" if i % 2 == 0 else "disconnect",
                metadata_info={"test_log": f"cascade_log_{i}_{timestamp}"}
            )
            test_db_session.add(log)
        await test_db_session.commit()

        # 验证渠道和关联日志存在
        logs_query = select(ChannelConnectionLog).where(
            ChannelConnectionLog.channel_instance_id == channel_id
        )
        result = await test_db_session.execute(logs_query)
        logs_before = result.scalars().all()
        assert len(logs_before) == 3  # 每个渠道有3条日志

        # When: 删除渠道实例（使用SQL DELETE触发CASCADE）
        from sqlalchemy import delete
        delete_stmt = delete(ChannelInstance).where(ChannelInstance.id == channel_id)
        await test_db_session.execute(delete_stmt)
        await test_db_session.commit()

        # Then: 验证关联日志的处理
        # 根据外键约束设置，日志可能被级联删除或设置为NULL
        result = await test_db_session.execute(logs_query)
        logs_after = result.scalars().all()
        
        # 如果设置了CASCADE，日志应该被删除
        # 如果设置了SET NULL，日志的channel_instance_id应该为NULL
        # 这里假设设置了CASCADE删除
        assert len(logs_after) == 0

    async def test_delete_multiple_channels(self, test_db_session: AsyncSession):
        """
        测试批量删除渠道实例
        验证批量操作的数据一致性
        """
        import uuid
        import time

        # Given: 创建独立的测试数据
        timestamp = int(time.time() * 1000)
        unique_suffix = str(uuid.uuid4())[:8]

        channels_to_delete = []
        for i in range(2):
            channel = ChannelInstance(
                id=f"ci_batch_delete_{timestamp}_{i}_{unique_suffix}",
                platform="xianyu",
                alias=f"批量删除店铺{i}_{timestamp}",
                status="connected",
                credentials={"session": f"session_batch_{i}_{timestamp}"}
            )
            test_db_session.add(channel)
            channels_to_delete.append(channel)

        # 创建一个不删除的渠道
        remaining_channel = ChannelInstance(
            id=f"ci_batch_remain_{timestamp}_{unique_suffix}",
            platform="douyin",
            alias=f"保留店铺_{timestamp}",
            status="connected",
            credentials={"session": f"session_remain_{timestamp}"}
        )
        test_db_session.add(remaining_channel)
        await test_db_session.commit()

        channel_ids = [ch.id for ch in channels_to_delete]

        # 验证渠道存在
        query = select(ChannelInstance).where(ChannelInstance.id.in_(channel_ids))
        result = await test_db_session.execute(query)
        channels_before = result.scalars().all()
        assert len(channels_before) == 2

        # When: 批量删除渠道
        for channel in channels_to_delete:
            await test_db_session.delete(channel)
        await test_db_session.commit()

        # Then: 验证批量删除结果
        result = await test_db_session.execute(query)
        channels_after = result.scalars().all()
        assert len(channels_after) == 0
        
        # 验证未删除的渠道仍然存在
        remaining_query = select(ChannelInstance).where(
            ChannelInstance.id == remaining_channel.id
        )
        result = await test_db_session.execute(remaining_query)
        found_remaining_channel = result.scalar_one_or_none()
        assert found_remaining_channel is not None
        assert "保留店铺" in found_remaining_channel.alias

    async def test_delete_channel_by_platform(self, test_db_session: AsyncSession, sample_channels_with_logs):
        """
        测试按平台删除渠道实例
        验证条件删除功能
        """
        # Given: 统计删除前的渠道数量
        all_query = select(func.count(ChannelInstance.id))
        result = await test_db_session.execute(all_query)
        total_before = result.scalar()
        assert total_before == 3
        
        xianyu_query = select(ChannelInstance).where(ChannelInstance.platform == "xianyu")
        result = await test_db_session.execute(xianyu_query)
        xianyu_channels = result.scalars().all()
        assert len(xianyu_channels) == 2

        # When: 删除所有闲鱼平台的渠道
        for channel in xianyu_channels:
            await test_db_session.delete(channel)
        await test_db_session.commit()

        # Then: 验证删除结果
        # 验证闲鱼渠道已被删除
        result = await test_db_session.execute(xianyu_query)
        remaining_xianyu = result.scalars().all()
        assert len(remaining_xianyu) == 0
        
        # 验证其他平台渠道仍然存在
        douyin_query = select(ChannelInstance).where(ChannelInstance.platform == "douyin")
        result = await test_db_session.execute(douyin_query)
        douyin_channels = result.scalars().all()
        assert len(douyin_channels) == 1
        assert "抖音店铺C" in douyin_channels[0].alias

    async def test_delete_channel_with_transaction_rollback(self, test_db_session: AsyncSession, sample_channels_with_logs):
        """
        测试删除操作的事务回滚
        验证数据一致性和错误处理
        """
        # Given: 选择要删除的渠道
        channel_to_delete = sample_channels_with_logs[0]
        channel_id = channel_to_delete.id
        
        # 验证渠道存在
        query = select(ChannelInstance).where(ChannelInstance.id == channel_id)
        result = await test_db_session.execute(query)
        found_channel = result.scalar_one_or_none()
        assert found_channel is not None

        # When: 模拟删除过程中的错误
        try:
            await test_db_session.delete(channel_to_delete)
            # 模拟在提交前发生错误
            raise Exception("模拟删除过程中的错误")
        except Exception:
            # 回滚事务
            await test_db_session.rollback()

        # Then: 验证回滚后渠道仍然存在
        # 重新查询（因为session已回滚，需要重新查询）
        result = await test_db_session.execute(query)
        channel_after_rollback = result.scalar_one_or_none()
        assert channel_after_rollback is not None
        assert "待删除店铺A" in channel_after_rollback.alias

    async def test_delete_nonexistent_channel(self, test_db_session: AsyncSession):
        """
        测试删除不存在的渠道
        验证错误处理和边界情况
        """
        # Given: 尝试查询不存在的渠道
        nonexistent_id = "ci_nonexistent_999"
        query = select(ChannelInstance).where(ChannelInstance.id == nonexistent_id)
        result = await test_db_session.execute(query)
        channel = result.scalar_one_or_none()
        
        # Then: 验证渠道不存在
        assert channel is None
        
        # When & Then: 尝试删除不存在的渠道不应该引发错误
        # 这是正常的业务场景，应该优雅处理
        # 在实际业务逻辑中，应该先检查渠道是否存在再删除

    async def test_channel_deletion_statistics(self, test_db_session: AsyncSession, sample_channels_with_logs):
        """
        测试删除操作对统计数据的影响
        验证删除后的数据统计正确性
        """
        # Given: 统计删除前的数据
        total_query = select(func.count(ChannelInstance.id))
        result = await test_db_session.execute(total_query)
        total_before = result.scalar()
        
        platform_stats_query = select(
            ChannelInstance.platform,
            func.count(ChannelInstance.id).label('count')
        ).group_by(ChannelInstance.platform)
        result = await test_db_session.execute(platform_stats_query)
        platform_stats_before = {row.platform: row.count for row in result}
        
        assert total_before == 3
        assert platform_stats_before['xianyu'] == 2
        assert platform_stats_before['douyin'] == 1

        # When: 删除一个闲鱼渠道
        channel_to_delete = sample_channels_with_logs[0]  # 闲鱼渠道
        await test_db_session.delete(channel_to_delete)
        await test_db_session.commit()

        # Then: 验证删除后的统计数据
        result = await test_db_session.execute(total_query)
        total_after = result.scalar()
        assert total_after == 2
        
        result = await test_db_session.execute(platform_stats_query)
        platform_stats_after = {row.platform: row.count for row in result}
        
        assert platform_stats_after['xianyu'] == 1  # 减少了1个
        assert platform_stats_after['douyin'] == 1  # 保持不变
