["tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_connection_history_logging", "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_cookie_permission_validation", "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_cookie_security_storage", "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_duplicate_account_connection", "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_invalid_cookie_connection", "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_manual_cookie_input_validation", "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_network_exception_handling", "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_realtime_connection_status_update", "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_successful_xianyu_connection", "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_alias_validation", "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_channel_sorting_and_filtering", "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_channel_status_toggle", "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_permanent_delete_channel", "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_restore_deleted_channel", "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_set_channel_alias", "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_soft_delete_channel", "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_soft_deleted_channels_hidden_in_main_list", "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_view_channel_details", "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_view_history_channels", "tests/integration/api/test_channel_monitoring_api.py::TestChannelMonitoringAPI::test_auto_reconnection_network_error", "tests/integration/api/test_channel_monitoring_api.py::TestChannelMonitoringAPI::test_cookie_expiry_detection", "tests/integration/api/test_channel_monitoring_api.py::TestChannelMonitoringAPI::test_manual_cookie_update", "tests/integration/api/test_channel_monitoring_api.py::TestChannelMonitoringAPI::test_monitoring_service_health_check", "tests/integration/api/test_channel_monitoring_api.py::TestChannelMonitoringAPI::test_monitoring_statistics", "tests/integration/api/test_channel_monitoring_api.py::TestChannelMonitoringAPI::test_normal_status_monitoring", "tests/integration/api/test_channel_monitoring_api.py::TestChannelMonitoringAPI::test_realtime_monitoring_updates", "tests/integration/api/test_channel_monitoring_api.py::TestChannelMonitoringAPI::test_reconnection_failure_cookie_issue", "tests/integration/db/test_channel_operations.py::TestChannelDatabaseOperations::test_channel_instance_credentials_jsonb", "tests/integration/db/test_channel_operations.py::TestChannelDatabaseOperations::test_channel_instance_delete_operations", "tests/integration/db/test_channel_operations.py::TestChannelDatabaseOperations::test_channel_instance_query_operations", "tests/integration/db/test_channel_operations.py::TestChannelDatabaseOperations::test_channel_instance_update_operations", "tests/integration/db/test_channel_operations.py::TestChannelDatabaseOperations::test_channel_instance_with_team_id", "tests/integration/db/test_channel_operations.py::TestChannelDatabaseOperations::test_create_channel_instance", "tests/integration/db/test_soft_delete_flow.py::TestChannelDeleteFlow::test_channel_deletion_statistics", "tests/integration/db/test_soft_delete_flow.py::TestChannelDeleteFlow::test_delete_channel_by_platform", "tests/integration/db/test_soft_delete_flow.py::TestChannelDeleteFlow::test_delete_channel_instance_basic", "tests/integration/db/test_soft_delete_flow.py::TestChannelDeleteFlow::test_delete_channel_with_cascade_logs", "tests/integration/db/test_soft_delete_flow.py::TestChannelDeleteFlow::test_delete_channel_with_transaction_rollback", "tests/integration/db/test_soft_delete_flow.py::TestChannelDeleteFlow::test_delete_multiple_channels", "tests/integration/db/test_soft_delete_flow.py::TestChannelDeleteFlow::test_delete_nonexistent_channel", "tests/integration/flows/test_connection_flow.py::TestConnectionFlow::test_complete_xianyu_connection_flow", "tests/integration/flows/test_connection_flow.py::TestConnectionFlow::test_concurrent_connection_flows", "tests/integration/flows/test_connection_flow.py::TestConnectionFlow::test_connection_flow_error_recovery", "tests/integration/flows/test_connection_flow.py::TestConnectionFlow::test_connection_flow_with_cookie_validation_failure", "tests/integration/flows/test_connection_flow.py::TestConnectionFlow::test_connection_flow_with_duplicate_account", "tests/integration/flows/test_connection_flow.py::TestConnectionFlow::test_connection_flow_with_network_interruption", "tests/integration/flows/test_connection_flow.py::TestConnectionFlow::test_connection_to_monitoring_flow", "tests/integration/flows/test_connection_flow.py::TestConnectionFlow::test_end_to_end_channel_lifecycle", "tests/integration/flows/test_management_flow.py::TestManagementFlow::test_batch_channel_management_operations", "tests/integration/flows/test_management_flow.py::TestManagementFlow::test_channel_analytics_and_reporting_flow", "tests/integration/flows/test_management_flow.py::TestManagementFlow::test_channel_configuration_management_flow", "tests/integration/flows/test_management_flow.py::TestManagementFlow::test_channel_data_export_and_backup_flow", "tests/integration/flows/test_management_flow.py::TestManagementFlow::test_channel_maintenance_and_health_check_flow", "tests/integration/flows/test_management_flow.py::TestManagementFlow::test_channel_status_transition_flow", "tests/integration/flows/test_management_flow.py::TestManagementFlow::test_complete_channel_management_workflow", "tests/integration/flows/test_management_flow.py::TestManagementFlow::test_error_handling_and_recovery_in_management_flow", "tests/integration/test_database.py::TestDatabaseIntegration::test_database_connection", "tests/integration/test_database.py::TestDatabaseIntegration::test_database_transaction", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_authentication_with_invalid_credentials", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_authentication_with_valid_credentials", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_connection_failure_with_empty_config", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_connection_failure_with_invalid_config", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_connector_initialization_with_invalid_config", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_connector_initialization_with_valid_config", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_connector_string_representation", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_disconnect_from_connected_state", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_disconnect_from_disconnected_state", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_get_connection_status_when_connected", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_get_connection_status_when_disconnected_with_error", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_get_item_info_when_connected", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_get_item_info_when_disconnected", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_get_platform_name", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_get_token_after_successful_authentication", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_get_token_without_authentication", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_is_chat_message_default_implementation", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_is_system_message_default_implementation", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_is_typing_status_default_implementation", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_listen_messages_when_connected", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_listen_messages_when_disconnected", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_parse_message_with_invalid_data", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_parse_message_with_minimal_data", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_parse_message_with_valid_data", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_refresh_token_with_existing_token", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_refresh_token_without_existing_token", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_send_message_when_connected", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_send_message_when_disconnected", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_send_message_with_invalid_parameters", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_successful_connection", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_validate_config_with_empty_config", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_validate_config_with_invalid_config", "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_validate_config_with_valid_config", "tests/unit/connectors/test_base_connector.py::TestBaseConnectorEdgeCases::test_connector_with_none_config", "tests/unit/connectors/test_base_connector.py::TestBaseConnectorEdgeCases::test_multiple_connect_attempts", "tests/unit/connectors/test_base_connector.py::TestBaseConnectorEdgeCases::test_multiple_disconnect_attempts", "tests/unit/connectors/test_base_connector.py::TestBaseConnectorEdgeCases::test_token_refresh_multiple_times", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_cookie_format_validation_with_invalid_cookies", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_cookie_format_validation_with_valid_cookies", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_cookie_masking_for_display", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_cookie_masking_for_empty_string", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_cookie_masking_for_short_string", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_successful_xianyu_connection", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_authentication_with_invalid_cookies", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_authentication_with_valid_cookies", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_connection_failure_with_empty_cookies", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_connection_failure_with_expired_cookies", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_connector_initialization_with_empty_cookies", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_connector_initialization_with_invalid_cookies", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_connector_initialization_with_valid_config", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_disconnect_from_connected_state", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_get_connection_status_when_connected", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_get_connection_status_when_disconnected_with_error", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_get_item_info_when_connected", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_get_item_info_when_disconnected", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_get_platform_name", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_get_token_after_successful_authentication", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_get_token_without_authentication", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_listen_messages_when_connected", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_listen_messages_when_disconnected", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_login_status_check_with_expired_session", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_login_status_check_with_retry_limit", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_login_status_check_with_valid_session", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_parse_message_with_invalid_data", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_parse_message_with_minimal_data", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_parse_message_with_valid_data", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_refresh_token_with_existing_token", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_refresh_token_without_existing_token", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_send_message_when_connected", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_send_message_when_disconnected", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_send_message_with_invalid_parameters", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnectorEdgeCases::test_xianyu_connection_with_network_simulation", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnectorEdgeCases::test_xianyu_connector_with_none_config", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnectorEdgeCases::test_xianyu_cookie_validation_edge_cases", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnectorEdgeCases::test_xianyu_message_parsing_edge_cases", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnectorEdgeCases::test_xianyu_multiple_connect_attempts", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnectorEdgeCases::test_xianyu_multiple_disconnect_attempts", "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnectorEdgeCases::test_xianyu_token_refresh_multiple_times", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_activate", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_activate_deleted_channel", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_creation_with_empty_name", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_creation_with_empty_platform", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_creation_with_invalid_status", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_creation_with_invalid_version", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_creation_with_long_name", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_creation_with_negative_counters", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_creation_with_valid_data", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_deactivate", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_equality", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_from_dict", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_inequality", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_record_empty_error", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_record_error", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_repr", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_restore", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_restore_not_deleted", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_soft_delete", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_soft_delete_already_deleted", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_soft_delete_validation", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_to_dict", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_update_config_empty", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_update_config_invalid", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_update_config_valid", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_update_status_invalid", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_update_status_valid", "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_xianyu_config_validation", "tests/unit/models/test_channel_model.py::TestChannelModelEdgeCases::test_channel_model_boundary_name_length", "tests/unit/models/test_channel_model.py::TestChannelModelEdgeCases::test_channel_model_concurrent_version_updates", "tests/unit/models/test_channel_model.py::TestChannelModelEdgeCases::test_channel_model_multiple_status_transitions", "tests/unit/models/test_channel_model.py::TestChannelModelEdgeCases::test_channel_model_with_unicode_name", "tests/unit/monitoring/test_reconnection.py::TestReconnectionEdgeCases::test_concurrent_reconnections", "tests/unit/monitoring/test_reconnection.py::TestReconnectionEdgeCases::test_config_validation_edge_cases", "tests/unit/monitoring/test_reconnection.py::TestReconnectionEdgeCases::test_event_listener_exception_handling", "tests/unit/monitoring/test_reconnection.py::TestReconnectionEdgeCases::test_memory_management_with_large_history", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_add_invalid_event_listener", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_event_listener_failure_events", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_event_listener_functionality", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_get_all_channels_status", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_get_channel_status", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_get_reconnection_history", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_get_reconnection_history_with_limit", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_multiple_reconnection_attempts", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_register_channel", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_register_channel_empty_id", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_register_duplicate_channel", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_remove_event_listener", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_remove_nonexistent_event_listener", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_reset_channel_attempts", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_trigger_reconnection_already_reconnecting", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_trigger_reconnection_failure", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_trigger_reconnection_nonexistent_channel", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_trigger_reconnection_success", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_unregister_channel", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_unregister_nonexistent_channel", "tests/unit/monitoring/test_reconnection.py::TestReconnectionManager::test_update_channel_config", "tests/unit/monitoring/test_reconnection.py::TestReconnectionStrategies::test_exponential_backoff_strategy", "tests/unit/monitoring/test_reconnection.py::TestReconnectionStrategies::test_fixed_delay_strategy", "tests/unit/monitoring/test_reconnection.py::TestReconnectionStrategies::test_immediate_reconnection_strategy", "tests/unit/monitoring/test_reconnection.py::TestReconnectionStrategies::test_jitter_functionality", "tests/unit/monitoring/test_reconnection.py::TestReconnectionStrategies::test_linear_backoff_strategy", "tests/unit/monitoring/test_reconnection.py::TestReconnectionStrategies::test_max_delay_limit", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_add_channel", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_add_channel_empty_config", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_add_channel_empty_id", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_add_duplicate_channel", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_add_invalid_event_listener", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_check_all_channels", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_check_all_channels_not_running", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_check_channel_status_failure", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_check_channel_status_nonexistent", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_check_channel_status_success", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_event_listener_functionality", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_get_all_channels_status", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_get_channel_status", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_get_performance_metrics", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_get_status_history", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_get_status_history_invalid_limit", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_get_status_history_with_limit", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_remove_channel", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_remove_event_listener", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_remove_nonexistent_channel", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_remove_nonexistent_event_listener", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_set_alert_thresholds", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_set_alert_thresholds_empty", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_set_alert_thresholds_invalid_key", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_set_alert_thresholds_negative_value", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_start_monitoring", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_start_monitoring_already_running", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_stop_monitoring", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitor::test_stop_monitoring_not_running", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitorEdgeCases::test_concurrent_channel_operations", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitorEdgeCases::test_performance_metrics_accuracy", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitorEdgeCases::test_status_history_memory_management", "tests/unit/monitoring/test_status_monitor.py::TestStatusMonitorEdgeCases::test_status_monitoring_with_large_dataset", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_count_channels", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_create_channel_with_duplicate_name", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_create_channel_with_empty_name", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_create_channel_with_empty_platform", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_create_channel_with_invalid_xianyu_config", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_create_channel_with_valid_data", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_delete_already_deleted_channel", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_delete_channel_empty_id", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_delete_channel_existing_channel", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_delete_channel_nonexistent_channel", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_exists_by_name", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_exists_by_name_exclude_id", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_get_active_channels", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_get_all_channels_exclude_deleted", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_get_all_channels_include_deleted", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_get_all_channels_multiple_channels", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_get_by_id_deleted_channel_exclude", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_get_by_id_deleted_channel_include", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_get_by_id_empty_id", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_get_by_id_existing_channel", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_get_by_id_nonexistent_channel", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_get_by_name_empty_name", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_get_by_name_existing_channel", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_get_by_name_nonexistent_channel", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_get_by_platform_empty_platform", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_get_by_platform_existing_platform", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_get_by_platform_nonexistent_platform", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_hard_delete_channel", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_hard_delete_nonexistent_channel", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_restore_channel_existing_deleted_channel", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_restore_channel_nonexistent_channel", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_restore_channel_not_deleted_channel", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_restore_channel_with_name_conflict", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_update_channel_deleted_channel", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_update_channel_empty_id", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_update_channel_nonexistent_channel", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_update_channel_with_duplicate_name", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_update_channel_with_empty_name", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_update_channel_with_invalid_config", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_update_channel_with_invalid_status", "tests/unit/services/test_channel_crud.py::TestChannelCRUD::test_update_channel_with_valid_data", "tests/unit/services/test_channel_crud.py::TestChannelCRUDEdgeCases::test_concurrent_crud_operations", "tests/unit/services/test_channel_crud.py::TestChannelCRUDEdgeCases::test_crud_operations_boundary_conditions", "tests/unit/services/test_channel_crud.py::TestChannelCRUDEdgeCases::test_crud_operations_state_consistency", "tests/unit/services/test_channel_crud.py::TestChannelCRUDEdgeCases::test_crud_operations_with_large_dataset", "tests/unit/services/test_channel_crud.py::TestChannelCRUDEdgeCases::test_crud_operations_with_unicode_data", "tests/unit/services/test_channel_service.py::TestChannelService::test_create_channel_with_duplicate_name", "tests/unit/services/test_channel_service.py::TestChannelService::test_create_channel_with_empty_name", "tests/unit/services/test_channel_service.py::TestChannelService::test_create_channel_with_empty_platform", "tests/unit/services/test_channel_service.py::TestChannelService::test_create_channel_with_invalid_config", "tests/unit/services/test_channel_service.py::TestChannelService::test_create_channel_with_valid_data", "tests/unit/services/test_channel_service.py::TestChannelService::test_create_channel_with_whitespace_name", "tests/unit/services/test_channel_service.py::TestChannelService::test_delete_already_deleted_channel", "tests/unit/services/test_channel_service.py::TestChannelService::test_delete_channel_empty_id", "tests/unit/services/test_channel_service.py::TestChannelService::test_delete_channel_existing_channel", "tests/unit/services/test_channel_service.py::TestChannelService::test_delete_channel_nonexistent_channel", "tests/unit/services/test_channel_service.py::TestChannelService::test_get_active_channels", "tests/unit/services/test_channel_service.py::TestChannelService::test_get_all_channels_exclude_deleted", "tests/unit/services/test_channel_service.py::TestChannelService::test_get_all_channels_include_deleted", "tests/unit/services/test_channel_service.py::TestChannelService::test_get_all_channels_with_multiple_channels", "tests/unit/services/test_channel_service.py::TestChannelService::test_get_channel_by_id_empty_id", "tests/unit/services/test_channel_service.py::TestChannelService::test_get_channel_by_id_existing_channel", "tests/unit/services/test_channel_service.py::TestChannelService::test_get_channel_by_id_nonexistent_channel", "tests/unit/services/test_channel_service.py::TestChannelService::test_get_channel_by_name_empty_name", "tests/unit/services/test_channel_service.py::TestChannelService::test_get_channel_by_name_existing_channel", "tests/unit/services/test_channel_service.py::TestChannelService::test_get_channel_by_name_nonexistent_channel", "tests/unit/services/test_channel_service.py::TestChannelService::test_get_channels_by_platform_empty_platform", "tests/unit/services/test_channel_service.py::TestChannelService::test_get_channels_by_platform_existing_platform", "tests/unit/services/test_channel_service.py::TestChannelService::test_get_channels_by_platform_nonexistent_platform", "tests/unit/services/test_channel_service.py::TestChannelService::test_restore_channel_empty_id", "tests/unit/services/test_channel_service.py::TestChannelService::test_restore_channel_existing_deleted_channel", "tests/unit/services/test_channel_service.py::TestChannelService::test_restore_channel_nonexistent_channel", "tests/unit/services/test_channel_service.py::TestChannelService::test_restore_channel_not_deleted_channel", "tests/unit/services/test_channel_service.py::TestChannelService::test_update_channel_empty_id", "tests/unit/services/test_channel_service.py::TestChannelService::test_update_channel_nonexistent_channel", "tests/unit/services/test_channel_service.py::TestChannelService::test_update_channel_with_duplicate_name", "tests/unit/services/test_channel_service.py::TestChannelService::test_update_channel_with_empty_name", "tests/unit/services/test_channel_service.py::TestChannelService::test_update_channel_with_valid_data", "tests/unit/services/test_channel_service.py::TestChannelServiceEdgeCases::test_channel_name_with_special_characters", "tests/unit/services/test_channel_service.py::TestChannelServiceEdgeCases::test_channel_name_with_unicode_characters", "tests/unit/services/test_channel_service.py::TestChannelServiceEdgeCases::test_channel_service_with_large_dataset", "tests/unit/services/test_channel_service.py::TestChannelServiceEdgeCases::test_concurrent_channel_operations", "tests/unit/services/test_channel_service.py::TestChannelServiceEdgeCases::test_platform_config_validation_edge_cases", "tests/unit/services/test_soft_delete.py::TestSoftDeleteEdgeCases::test_concurrent_soft_delete_operations", "tests/unit/services/test_soft_delete.py::TestSoftDeleteEdgeCases::test_soft_delete_performance_with_large_dataset", "tests/unit/services/test_soft_delete.py::TestSoftDeleteEdgeCases::test_soft_delete_restore_cycle", "tests/unit/services/test_soft_delete.py::TestSoftDeleteEdgeCases::test_soft_delete_with_unicode_data", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_audit_logs", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_cleanup_invalid_days", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_cleanup_old_deleted_entities", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_count_entities", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_get_all_exclude_deleted", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_get_all_include_deleted", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_get_by_id_exclude_deleted", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_get_by_id_include_deleted", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_get_deleted_entities", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_get_deleted_entities_with_date_filter", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_hard_delete_nonexistent_entity", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_hard_delete_not_soft_deleted_entity", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_hard_delete_protected_entity", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_hard_delete_soft_deleted_entity", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_restore_deleted_entity", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_restore_empty_entity_id", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_restore_empty_restored_by", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_restore_nonexistent_entity", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_restore_not_deleted_entity", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_restore_with_name_conflict", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_soft_delete_already_deleted_entity", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_soft_delete_empty_deleted_by", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_soft_delete_empty_entity_id", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_soft_delete_entity_with_dependencies", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_soft_delete_nonexistent_entity", "tests/unit/services/test_soft_delete.py::TestSoftDeleteService::test_soft_delete_valid_entity", "tests/unit/test_config.py::TestConfiguration::test_get_settings_returns_settings_instance", "tests/unit/test_config.py::TestConfiguration::test_settings_default_values", "tests/unit/test_config.py::TestConfiguration::test_settings_environment_specific", "tests/unit/test_config.py::TestConfiguration::test_settings_has_required_attributes", "tests/unit/test_health.py::TestApplicationConfig::test_settings_configuration", "tests/unit/test_health.py::TestHealthCheck::test_health_check_success", "tests/unit/test_health.py::TestHealthCheck::test_root_endpoint", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_build_cookie_string_empty_dict", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_build_cookie_string_valid_dict", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_build_cookie_string_with_empty_values", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_cookie_manager_initialization_custom_key", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_cookie_manager_initialization_default_key", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_decrypt_cookie_with_empty_string", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_decrypt_cookie_with_invalid_encrypted_string", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_decrypt_cookie_with_valid_encrypted_string", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_delete_cookie_existing_cookie", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_delete_cookie_nonexistent_cookie", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_encrypt_cookie_with_empty_string", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_encrypt_cookie_with_valid_string", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_extract_user_info_empty_cookie", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_extract_user_info_unknown_platform", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_extract_user_info_xianyu_cookie", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_generate_new_encryption_key", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_get_cookie_display_info_existing_cookie", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_get_cookie_display_info_nonexistent_cookie", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_is_cookie_expired_empty_cookie", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_is_cookie_expired_missing_required_fields", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_is_cookie_expired_valid_cookie", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_is_cookie_expired_with_expired_marker", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_list_stored_cookies_empty_cache", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_list_stored_cookies_multiple_cookies", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_mask_cookie_for_display_custom_mask_char", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_mask_cookie_for_display_empty_string", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_mask_cookie_for_display_long_string", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_mask_cookie_for_display_short_string", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_parse_cookie_string_empty_string", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_parse_cookie_string_invalid_format", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_parse_cookie_string_valid_format", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_retrieve_cookie_securely_empty_identifier", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_retrieve_cookie_securely_existing_identifier", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_retrieve_cookie_securely_nonexistent_identifier", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_store_cookie_securely_empty_data", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_store_cookie_securely_valid_data", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_validate_cookie_format_empty_cookie", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_validate_cookie_format_invalid_xianyu_cookie", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_validate_cookie_format_missing_required_fields", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_validate_cookie_format_unknown_platform", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_validate_cookie_format_valid_xianyu_cookie", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_validate_encryption_key_invalid_key", "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_validate_encryption_key_valid_key", "tests/unit/utils/test_cookie_manager.py::TestCookieManagerEdgeCases::test_concurrent_cookie_operations", "tests/unit/utils/test_cookie_manager.py::TestCookieManagerEdgeCases::test_cookie_encryption_decryption_round_trip", "tests/unit/utils/test_cookie_manager.py::TestCookieManagerEdgeCases::test_cookie_masking_various_lengths", "tests/unit/utils/test_cookie_manager.py::TestCookieManagerEdgeCases::test_cookie_parsing_edge_cases"]