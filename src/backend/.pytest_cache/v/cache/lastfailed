{"tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnectorEdgeCases": true, "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_parse_message_with_invalid_data": true, "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_is_chat_message_default_implementation": true, "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_is_system_message_default_implementation": true, "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_is_typing_status_default_implementation": true, "tests/unit/connectors/test_base_connector.py::TestBaseConnector::test_connector_string_representation": true, "tests/unit/connectors/test_base_connector.py::TestBaseConnectorEdgeCases::test_multiple_connect_attempts": true, "tests/unit/connectors/test_base_connector.py::TestBaseConnectorEdgeCases::test_multiple_disconnect_attempts": true, "tests/unit/connectors/test_base_connector.py::TestBaseConnectorEdgeCases::test_token_refresh_multiple_times": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_connector_initialization_with_valid_config": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_connector_initialization_with_invalid_cookies": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_connector_initialization_with_empty_cookies": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_cookie_format_validation_with_valid_cookies": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_cookie_format_validation_with_invalid_cookies": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_cookie_masking_for_display": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_cookie_masking_for_short_string": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_cookie_masking_for_empty_string": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_successful_xianyu_connection": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_connection_failure_with_empty_cookies": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_connection_failure_with_expired_cookies": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_disconnect_from_connected_state": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_authentication_with_valid_cookies": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_authentication_with_invalid_cookies": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_login_status_check_with_valid_session": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_login_status_check_with_expired_session": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_login_status_check_with_retry_limit": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_get_token_after_successful_authentication": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_get_token_without_authentication": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_refresh_token_with_existing_token": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_refresh_token_without_existing_token": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_listen_messages_when_connected": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_listen_messages_when_disconnected": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_parse_message_with_valid_data": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_parse_message_with_minimal_data": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_parse_message_with_invalid_data": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_get_item_info_when_connected": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_get_item_info_when_disconnected": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_send_message_when_connected": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_send_message_when_disconnected": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_send_message_with_invalid_parameters": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_get_connection_status_when_connected": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnector::test_xianyu_get_connection_status_when_disconnected_with_error": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnectorEdgeCases::test_xianyu_multiple_connect_attempts": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnectorEdgeCases::test_xianyu_multiple_disconnect_attempts": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnectorEdgeCases::test_xianyu_token_refresh_multiple_times": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnectorEdgeCases::test_xianyu_cookie_validation_edge_cases": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnectorEdgeCases::test_xianyu_message_parsing_edge_cases": true, "tests/unit/connectors/test_xianyu_connector.py::TestXianyuConnectorEdgeCases::test_xianyu_connection_with_network_simulation": true, "tests/unit/models/test_channel_model.py::TestChannelModel::test_channel_model_xianyu_config_validation": true, "tests/unit/services/test_channel_service.py::TestChannelServiceEdgeCases::test_channel_service_with_large_dataset": true, "tests/unit/services/test_channel_service.py::TestChannelServiceEdgeCases::test_channel_name_with_special_characters": true, "tests/unit/services/test_channel_service.py::TestChannelServiceEdgeCases::test_channel_name_with_unicode_characters": true, "tests/unit/services/test_channel_service.py::TestChannelServiceEdgeCases::test_concurrent_channel_operations": true, "tests/unit/services/test_channel_service.py::TestChannelServiceEdgeCases::test_platform_config_validation_edge_cases": true, "tests/unit/utils/test_cookie_manager.py::TestCookieManager::test_parse_cookie_string_invalid_format": true, "tests/unit/utils/test_cookie_manager.py::TestCookieManagerEdgeCases::test_cookie_masking_various_lengths": true, "tests/unit/utils/test_cookie_manager.py::TestCookieManagerEdgeCases::test_concurrent_cookie_operations": true, "tests/integration/db/test_channel_operations.py::TestChannelDatabaseOperations::test_channel_instance_update_operations": true, "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_successful_xianyu_connection": true, "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_manual_cookie_input_validation": true, "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_invalid_cookie_connection": true, "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_duplicate_account_connection": true, "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_network_exception_handling": true, "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_cookie_permission_validation": true, "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_connection_history_logging": true, "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_cookie_security_storage": true, "tests/integration/api/test_channel_connection_api.py::TestChannelConnectionAPI::test_realtime_connection_status_update": true, "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_set_channel_alias": true, "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_alias_validation": true, "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_soft_delete_channel": true, "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_view_history_channels": true, "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_restore_deleted_channel": true, "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_permanent_delete_channel": true, "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_view_channel_details": true, "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_channel_sorting_and_filtering": true, "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_channel_status_toggle": true, "tests/integration/api/test_channel_management_api.py::TestChannelManagementAPI::test_soft_deleted_channels_hidden_in_main_list": true, "tests/integration/api/test_channel_monitoring_api.py::TestChannelMonitoringAPI::test_auto_reconnection_network_error": true, "tests/integration/api/test_channel_monitoring_api.py::TestChannelMonitoringAPI::test_reconnection_failure_cookie_issue": true, "tests/integration/api/test_channel_monitoring_api.py::TestChannelMonitoringAPI::test_manual_cookie_update": true, "tests/integration/api/test_channel_monitoring_api.py::TestChannelMonitoringAPI::test_realtime_monitoring_updates": true, "tests/integration/db/test_channel_operations.py::TestChannelDatabaseOperations::test_channel_instance_query_operations": true, "tests/integration/flows/test_connection_flow.py::TestConnectionFlow::test_connection_flow_with_cookie_validation_failure": true, "tests/integration/flows/test_connection_flow.py::TestConnectionFlow::test_connection_flow_with_duplicate_account": true, "tests/integration/flows/test_connection_flow.py::TestConnectionFlow::test_connection_to_monitoring_flow": true, "tests/integration/flows/test_connection_flow.py::TestConnectionFlow::test_connection_flow_with_network_interruption": true, "tests/integration/flows/test_connection_flow.py::TestConnectionFlow::test_end_to_end_channel_lifecycle": true, "tests/integration/flows/test_connection_flow.py::TestConnectionFlow::test_concurrent_connection_flows": true, "tests/integration/flows/test_connection_flow.py::TestConnectionFlow::test_connection_flow_error_recovery": true, "tests/integration/flows/test_management_flow.py::TestManagementFlow::test_batch_channel_management_operations": true, "tests/integration/flows/test_management_flow.py::TestManagementFlow::test_channel_status_transition_flow": true, "tests/integration/flows/test_management_flow.py::TestManagementFlow::test_channel_configuration_management_flow": true, "tests/integration/flows/test_management_flow.py::TestManagementFlow::test_channel_data_export_and_backup_flow": true, "tests/integration/flows/test_management_flow.py::TestManagementFlow::test_channel_analytics_and_reporting_flow": true, "tests/integration/flows/test_management_flow.py::TestManagementFlow::test_channel_maintenance_and_health_check_flow": true, "tests/integration/flows/test_management_flow.py::TestManagementFlow::test_error_handling_and_recovery_in_management_flow": true}