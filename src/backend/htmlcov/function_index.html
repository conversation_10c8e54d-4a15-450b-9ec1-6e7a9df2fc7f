<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">49%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-04 02:14 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app/__init__.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html">app/api/__init__.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_257b53c25398f6ee___init___py.html">app/api/v1/__init__.py</a></td>
                <td class="name left"><a href="z_257b53c25398f6ee___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_257b53c25398f6ee_api_py.html">app/api/v1/api.py</a></td>
                <td class="name left"><a href="z_257b53c25398f6ee_api_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d___init___py.html">app/api/v1/endpoints/__init__.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t58">app/api/v1/endpoints/channels.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t58"><data value='connect_channel'>connect_channel</data></a></td>
                <td>33</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="10 33">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t177">app/api/v1/endpoints/channels.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t177"><data value='get_channel_status'>get_channel_status</data></a></td>
                <td>8</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="2 8">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t220">app/api/v1/endpoints/channels.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t220"><data value='get_channel'>get_channel</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t265">app/api/v1/endpoints/channels.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t265"><data value='list_channels'>list_channels</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t318">app/api/v1/endpoints/channels.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t318"><data value='update_channel'>update_channel</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t408">app/api/v1/endpoints/channels.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t408"><data value='pause_channel'>pause_channel</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t481">app/api/v1/endpoints/channels.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t481"><data value='resume_channel'>resume_channel</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t554">app/api/v1/endpoints/channels.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t554"><data value='get_channel_logs'>get_channel_logs</data></a></td>
                <td>13</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="2 13">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t611">app/api/v1/endpoints/channels.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html#t611"><data value='delete_channel'>delete_channel</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html">app/api/v1/endpoints/channels.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>51</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="51 51">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html#t23">app/api/v1/endpoints/health.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html#t23"><data value='basic_health_check'>basic_health_check</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html#t44">app/api/v1/endpoints/health.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html#t44"><data value='detailed_health_check'>detailed_health_check</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html#t139">app/api/v1/endpoints/health.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html#t139"><data value='readiness_check'>readiness_check</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html#t179">app/api/v1/endpoints/health.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html#t179"><data value='liveness_check'>liveness_check</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html">app/api/v1/endpoints/health.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html#t27">app/api/v1/endpoints/monitoring.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html#t27"><data value='get_monitoring_service_health'>get_monitoring_service_health</data></a></td>
                <td>10</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="2 10">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html#t86">app/api/v1/endpoints/monitoring.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html#t86"><data value='get_channels_monitoring_status'>get_channels_monitoring_status</data></a></td>
                <td>20</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="4 20">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html#t152">app/api/v1/endpoints/monitoring.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html#t152"><data value='get_monitoring_statistics'>get_monitoring_statistics</data></a></td>
                <td>30</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="9 30">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html#t252">app/api/v1/endpoints/monitoring.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html#t252"><data value='simulate_network_error'>simulate_network_error</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html#t290">app/api/v1/endpoints/monitoring.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html#t290"><data value='complete_reconnection'>complete_reconnection</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html#t328">app/api/v1/endpoints/monitoring.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html#t328"><data value='trigger_status_change'>trigger_status_change</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html#t381">app/api/v1/endpoints/monitoring.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html#t381"><data value='check_channel_status'>check_channel_status</data></a></td>
                <td>42</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="2 42">5%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html">app/api/v1/endpoints/monitoring.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_notifications_py.html#t18">app/api/v1/endpoints/notifications.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_notifications_py.html#t18"><data value='get_notifications'>get_notifications</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_notifications_py.html#t103">app/api/v1/endpoints/notifications.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_notifications_py.html#t103"><data value='mark_notification_read'>mark_notification_read</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_notifications_py.html#t128">app/api/v1/endpoints/notifications.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_notifications_py.html#t128"><data value='delete_notification'>delete_notification</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_notifications_py.html#t153">app/api/v1/endpoints/notifications.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_notifications_py.html#t153"><data value='create_notification'>create_notification</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_notifications_py.html">app/api/v1/endpoints/notifications.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_notifications_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t25">app/api/v1/endpoints/system.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t25"><data value='get_system_info'>get_system_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t56">app/api/v1/endpoints/system.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t56"><data value='get_system_metrics'>get_system_metrics</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t121">app/api/v1/endpoints/system.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t121"><data value='get_config_info'>get_config_info</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t186">app/api/v1/endpoints/system.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t186"><data value='get_environment_info'>get_environment_info</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t231">app/api/v1/endpoints/system.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t231"><data value='get_dependencies_info'>get_dependencies_info</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html">app/api/v1/endpoints/system.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060___init___py.html">app/connectors/__init__.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_douyin_connector_py.html#t13">app/connectors/douyin_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_douyin_connector_py.html#t13"><data value='init__'>DouyinConnector.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_douyin_connector_py.html#t16">app/connectors/douyin_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_douyin_connector_py.html#t16"><data value='validate_cookie'>DouyinConnector.validate_cookie</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_douyin_connector_py.html#t30">app/connectors/douyin_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_douyin_connector_py.html#t30"><data value='get_account_info'>DouyinConnector.get_account_info</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_douyin_connector_py.html#t54">app/connectors/douyin_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_douyin_connector_py.html#t54"><data value='test_connection'>DouyinConnector.test_connection</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_douyin_connector_py.html#t66">app/connectors/douyin_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_douyin_connector_py.html#t66"><data value='get_connection_status'>DouyinConnector.get_connection_status</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_douyin_connector_py.html">app/connectors/douyin_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_douyin_connector_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_xianyu_connector_py.html#t13">app/connectors/xianyu_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_xianyu_connector_py.html#t13"><data value='init__'>XianyuConnector.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_xianyu_connector_py.html#t16">app/connectors/xianyu_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_xianyu_connector_py.html#t16"><data value='validate_cookie'>XianyuConnector.validate_cookie</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_xianyu_connector_py.html#t30">app/connectors/xianyu_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_xianyu_connector_py.html#t30"><data value='get_account_info'>XianyuConnector.get_account_info</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_xianyu_connector_py.html#t56">app/connectors/xianyu_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_xianyu_connector_py.html#t56"><data value='test_connection'>XianyuConnector.test_connection</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_xianyu_connector_py.html#t68">app/connectors/xianyu_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_xianyu_connector_py.html#t68"><data value='get_connection_status'>XianyuConnector.get_connection_status</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_xianyu_connector_py.html">app/connectors/xianyu_connector.py</a></td>
                <td class="name left"><a href="z_7589b33177cad060_xianyu_connector_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html">app/core/__init__.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t127">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t127"><data value='validate_environment'>Settings.validate_environment</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t135">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t135"><data value='validate_log_level'>Settings.validate_log_level</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t143">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t143"><data value='parse_cors_origins'>Settings.parse_cors_origins</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t150">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t150"><data value='parse_allowed_hosts'>Settings.parse_allowed_hosts</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t157">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t157"><data value='is_development'>Settings.is_development</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t162">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t162"><data value='is_production'>Settings.is_production</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t167">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t167"><data value='is_testing'>Settings.is_testing</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t179">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t179"><data value='get_settings'>get_settings</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>68</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="68 68">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t43">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t43"><data value='init__'>DatabaseManager.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t48">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t48"><data value='initialize'>DatabaseManager.initialize</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t88">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t88"><data value='close'>DatabaseManager.close</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t112">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t112"><data value='get_session'>DatabaseManager.get_session</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t134">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t134"><data value='health_check'>DatabaseManager.health_check</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t161">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t161"><data value='execute_raw_sql'>DatabaseManager.execute_raw_sql</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t185">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t185"><data value='engine'>DatabaseManager.engine</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t190">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t190"><data value='is_initialized'>DatabaseManager.is_initialized</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t199">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t199"><data value='get_db'>get_db</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t212">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t212"><data value='create_tables'>create_tables</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t234">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t234"><data value='drop_tables'>drop_tables</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t257">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t257"><data value='get_test_db'>get_test_db</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t23">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t23"><data value='init__'>BaseCustomException.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t42">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t42"><data value='init__'>ValidationError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t60">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t60"><data value='init__'>BusinessLogicError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t73">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t73"><data value='init__'>DatabaseError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t91">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t91"><data value='init__'>AuthenticationError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t102">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t102"><data value='init__'>AuthorizationError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t120">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t120"><data value='init__'>NotFoundError.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t143">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t143"><data value='init__'>ConflictError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t161">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t161"><data value='init__'>ExternalServiceError.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t184">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t184"><data value='init__'>RateLimitError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t202">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t202"><data value='init__'>ConfigurationError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t213">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t213"><data value='global_exception_handler'>global_exception_handler</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t264">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t264"><data value='create_error_response'>create_error_response</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t23">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t23"><data value='configure_logging'>configure_logging</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t37">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t37"><data value='configure_standard_logging'>configure_standard_logging</data></a></td>
                <td>21</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="15 21">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t85">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t85"><data value='configure_structlog'>configure_structlog</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t127">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t127"><data value='add_app_context'>add_app_context</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t153">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t153"><data value='get_logger'>get_logger</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t175">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t175"><data value='logger'>LoggingMixin.logger</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t180">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t180"><data value='log_function_call'>log_function_call</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t193">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t193"><data value='log_api_request'>log_api_request</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t207">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t207"><data value='log_api_response'>log_api_response</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t230">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t230"><data value='log_database_operation'>log_database_operation</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t244">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t244"><data value='log_external_service_call'>log_external_service_call</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t258">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t258"><data value='log_business_event'>log_business_event</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t271">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t271"><data value='log_security_event'>log_security_event</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t284">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t284"><data value='log_performance_metric'>log_performance_metric</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t29">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t29"><data value='dispatch'>ProcessTimeMiddleware.dispatch</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t62">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t62"><data value='dispatch'>LoggingMiddleware.dispatch</data></a></td>
                <td>15</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="11 15">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t137">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t137"><data value='get_client_ip'>LoggingMiddleware._get_client_ip</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t168">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t168"><data value='init__'>RateLimitMiddleware.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t176">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t176"><data value='dispatch'>RateLimitMiddleware.dispatch</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t238">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t238"><data value='get_client_ip'>RateLimitMiddleware._get_client_ip</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t250">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t250"><data value='cleanup_expired_records'>RateLimitMiddleware._cleanup_expired_records</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t279">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t279"><data value='dispatch'>SecurityHeadersMiddleware.dispatch</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t315">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t315"><data value='dispatch'>CacheControlMiddleware.dispatch</data></a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t50">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t50"><data value='lifespan'>lifespan</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t82">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t82"><data value='create_application'>create_application</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t114">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t114"><data value='configure_middleware'>configure_middleware</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t147">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t147"><data value='configure_exception_handlers'>configure_exception_handlers</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t157">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t157"><data value='business_logic_exception_handler'>configure_exception_handlers.business_logic_exception_handler</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t172">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t172"><data value='database_exception_handler'>configure_exception_handlers.database_exception_handler</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t185">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t185"><data value='validation_exception_handler'>configure_exception_handlers.validation_exception_handler</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t200">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t200"><data value='configure_routes'>configure_routes</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t210">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t210"><data value='health_check'>configure_routes.health_check</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t242">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t242"><data value='root'>configure_routes.root</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="26 28">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html">app/models/__init__.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t24">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t24"><data value='load_dialect_impl'>GUID.load_dialect_impl</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t30">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t30"><data value='process_bind_param'>GUID.process_bind_param</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t42">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t42"><data value='process_result_value'>GUID.process_result_value</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t64">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t64"><data value='to_dict'>BaseModel.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t75">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t75"><data value='update_from_dict'>BaseModel.update_from_dict</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t86">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t86"><data value='repr__'>BaseModel.__repr__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t149">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t149"><data value='is_deleted'>SoftDeleteMixin.is_deleted</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t158">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t158"><data value='soft_delete'>SoftDeleteMixin.soft_delete</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t164">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t164"><data value='restore'>SoftDeleteMixin.restore</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t180">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t180"><data value='increment_version'>VersionMixin.increment_version</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_channel_py.html#t87">app/models/channel.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_channel_py.html#t87"><data value='repr__'>ChannelInstance.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_channel_py.html#t90">app/models/channel.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_channel_py.html#t90"><data value='is_connected'>ChannelInstance.is_connected</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_channel_py.html#t95">app/models/channel.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_channel_py.html#t95"><data value='generate_id'>ChannelInstance.generate_id</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_channel_py.html">app/models/channel.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_channel_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html#t152">app/models/channel_connection_log.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html#t152"><data value='repr__'>ChannelConnectionLog.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html#t155">app/models/channel_connection_log.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html#t155"><data value='is_successful'>ChannelConnectionLog.is_successful</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html#t165">app/models/channel_connection_log.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html#t165"><data value='is_failed'>ChannelConnectionLog.is_failed</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html#t174">app/models/channel_connection_log.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html#t174"><data value='get_operation_summary'>ChannelConnectionLog.get_operation_summary</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html#t208">app/models/channel_connection_log.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html#t208"><data value='add_metadata'>ChannelConnectionLog.add_metadata</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html#t214">app/models/channel_connection_log.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html#t214"><data value='get_metadata'>ChannelConnectionLog.get_metadata</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html#t221">app/models/channel_connection_log.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html#t221"><data value='create_log'>ChannelConnectionLog.create_log</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html">app/models/channel_connection_log.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>55</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="55 55">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_notification_py.html#t35">app/models/notification.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_notification_py.html#t35"><data value='repr__'>Notification.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_notification_py.html#t38">app/models/notification.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_notification_py.html#t38"><data value='to_dict'>Notification.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_notification_py.html">app/models/notification.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_notification_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html">app/services/__init__.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t15">app/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t15"><data value='init__'>ChannelMonitoringService.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t19">app/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t19"><data value='is_running'>ChannelMonitoringService.is_running</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t28">app/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t28"><data value='start_monitoring'>ChannelMonitoringService.start_monitoring</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t47">app/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t47"><data value='stop_monitoring'>ChannelMonitoringService.stop_monitoring</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t62">app/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t62"><data value='pause_monitoring'>ChannelMonitoringService.pause_monitoring</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t77">app/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t77"><data value='resume_monitoring'>ChannelMonitoringService.resume_monitoring</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t93">app/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t93"><data value='get_status'>ChannelMonitoringService.get_status</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t106">app/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t106"><data value='is_monitoring'>ChannelMonitoringService.is_monitoring</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t123">app/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t123"><data value='init__'>WebSocketManager.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t126">app/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t126"><data value='broadcast'>WebSocketManager.broadcast</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t136">app/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t136"><data value='send_to_channel'>WebSocketManager.send_to_channel</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t151">app/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html#t151"><data value='perform_maintenance'>MaintenanceService.perform_maintenance</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html">app/services/monitoring_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16___init___py.html">app/utils/__init__.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html#t16">app/utils/cookie_manager.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html#t16"><data value='init__'>CookieManager.__init__</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html#t30">app/utils/cookie_manager.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html#t30"><data value='encrypt_cookie'>CookieManager.encrypt_cookie</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html#t49">app/utils/cookie_manager.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html#t49"><data value='decrypt_cookie'>CookieManager.decrypt_cookie</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html#t73">app/utils/cookie_manager.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html#t73"><data value='validate_cookie_format'>CookieManager.validate_cookie_format</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html#t91">app/utils/cookie_manager.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html#t91"><data value='mask_sensitive_data'>CookieManager.mask_sensitive_data</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html#t115">app/utils/cookie_manager.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html#t115"><data value='get_cookie_expiry'>CookieManager.get_cookie_expiry</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html#t127">app/utils/cookie_manager.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html#t127"><data value='is_cookie_expired'>CookieManager.is_cookie_expired</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html">app/utils/cookie_manager.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1361</td>
                <td>693</td>
                <td>0</td>
                <td class="right" data-ratio="668 1361">49%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-04 02:14 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
