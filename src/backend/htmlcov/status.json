{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.1", "globals": "caece341e8ac740805522bc2c07cab3b", "files": {"z_5f5a17c013354698___init___py": {"hash": "b79c5d90888742bc46fd1d6b476ff6ed", "index": {"url": "z_5f5a17c013354698___init___py.html", "file": "app/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cfb6adc3f81c8e3c___init___py": {"hash": "0d4954a4fcffc7c8a57443df56f8048c", "index": {"url": "z_cfb6adc3f81c8e3c___init___py.html", "file": "app/api/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_257b53c25398f6ee___init___py": {"hash": "607c58091fdedb69499296e7b460e7cc", "index": {"url": "z_257b53c25398f6ee___init___py.html", "file": "app/api/v1/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_257b53c25398f6ee_api_py": {"hash": "611cadb2543693b03627d560abb29d82", "index": {"url": "z_257b53c25398f6ee_api_py.html", "file": "app/api/v1/api.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f09dac0431399d___init___py": {"hash": "c74d0db30646e53b58bb961c49339563", "index": {"url": "z_41f09dac0431399d___init___py.html", "file": "app/api/v1/endpoints/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f09dac0431399d_channels_py": {"hash": "343db51ca275b9a482c1c434934a153a", "index": {"url": "z_41f09dac0431399d_channels_py.html", "file": "app/api/v1/endpoints/channels.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 201, "n_excluded": 0, "n_missing": 136, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f09dac0431399d_health_py": {"hash": "3a7d05b000ba971c0a255419fa604b3a", "index": {"url": "z_41f09dac0431399d_health_py.html", "file": "app/api/v1/endpoints/health.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 60, "n_excluded": 0, "n_missing": 42, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f09dac0431399d_system_py": {"hash": "317d0fd455f9e56b2e903fe591742df0", "index": {"url": "z_41f09dac0431399d_system_py.html", "file": "app/api/v1/endpoints/system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 0, "n_missing": 34, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7589b33177cad060___init___py": {"hash": "a78b04692abe09fc900ae6976870144f", "index": {"url": "z_7589b33177cad060___init___py.html", "file": "app/connectors/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7589b33177cad060_douyin_connector_py": {"hash": "ed7e65eafbbdd2f78ebf0709d2650874", "index": {"url": "z_7589b33177cad060_douyin_connector_py.html", "file": "app/connectors/douyin_connector.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 17, "n_excluded": 0, "n_missing": 10, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7589b33177cad060_xianyu_connector_py": {"hash": "de28f40481c407e3f13a5f241eb7d252", "index": {"url": "z_7589b33177cad060_xianyu_connector_py.html", "file": "app/connectors/xianyu_connector.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 17, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417___init___py": {"hash": "cc53b3781f4fd7f933ddc2e817f8b48d", "index": {"url": "z_8f7e1016f2d37417___init___py.html", "file": "app/core/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_config_py": {"hash": "0435f2174a8531ff3c3f1c56ab76906a", "index": {"url": "z_8f7e1016f2d37417_config_py.html", "file": "app/core/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_database_py": {"hash": "dca0e2144dd38b2941a71b7969762ec6", "index": {"url": "z_8f7e1016f2d37417_database_py.html", "file": "app/core/database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 0, "n_missing": 88, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_exceptions_py": {"hash": "e02efe6d835dbe2885838ddbf46f5bcb", "index": {"url": "z_8f7e1016f2d37417_exceptions_py.html", "file": "app/core/exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 49, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_logging_py": {"hash": "b81c059188ff95aab4871eb449926bff", "index": {"url": "z_8f7e1016f2d37417_logging_py.html", "file": "app/core/logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 71, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_middleware_py": {"hash": "73fbf0685fec105124d94931e4339b74", "index": {"url": "z_8f7e1016f2d37417_middleware_py.html", "file": "app/core/middleware.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 100, "n_excluded": 0, "n_missing": 42, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_main_py": {"hash": "6e3e32410a0a3e620893fa9b9deacf44", "index": {"url": "z_5f5a17c013354698_main_py.html", "file": "app/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 79, "n_excluded": 0, "n_missing": 29, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b___init___py": {"hash": "54da4ec051e22b82ac407a7567c036c0", "index": {"url": "z_6c0e4b930745278b___init___py.html", "file": "app/models/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b_base_py": {"hash": "5f6dfa13472662ad6f4051144b0ec193", "index": {"url": "z_6c0e4b930745278b_base_py.html", "file": "app/models/base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 63, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b_channel_py": {"hash": "0dfec8dd986f3aad03a6b1b85574c26c", "index": {"url": "z_6c0e4b930745278b_channel_py.html", "file": "app/models/channel.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 36, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b_channel_connection_log_py": {"hash": "e12cca4e80dcb0e51db675de0d56b42b", "index": {"url": "z_6c0e4b930745278b_channel_connection_log_py.html", "file": "app/models/channel_connection_log.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69___init___py": {"hash": "8e504ddb502dbbea409de680ed1be7a4", "index": {"url": "z_c318f3fa19a49f69___init___py.html", "file": "app/services/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_monitoring_service_py": {"hash": "816d2cc1bc3006e338f9875ab94537b6", "index": {"url": "z_c318f3fa19a49f69_monitoring_service_py.html", "file": "app/services/monitoring_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 47, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_748a0465d46c2a16___init___py": {"hash": "c3ab5af9d9522df458a706670e9ecb76", "index": {"url": "z_748a0465d46c2a16___init___py.html", "file": "app/utils/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_748a0465d46c2a16_cookie_manager_py": {"hash": "722fbb09beafeb27634db80d64f19d29", "index": {"url": "z_748a0465d46c2a16_cookie_manager_py.html", "file": "app/utils/cookie_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 49, "n_excluded": 0, "n_missing": 31, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f09dac0431399d_monitoring_py": {"hash": "d2f67ac33df3aae1b717351cd1eaa5d9", "index": {"url": "z_41f09dac0431399d_monitoring_py.html", "file": "app/api/v1/endpoints/monitoring.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 150, "n_excluded": 0, "n_missing": 106, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f09dac0431399d_notifications_py": {"hash": "a7a3f1943f0403604dd387bcd9b547bc", "index": {"url": "z_41f09dac0431399d_notifications_py.html", "file": "app/api/v1/endpoints/notifications.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b_notification_py": {"hash": "c67fc329df78af01083de299fbe16c68", "index": {"url": "z_6c0e4b930745278b_notification_py.html", "file": "app/models/notification.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}