<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">49%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-04 02:14 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html">app/api/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_257b53c25398f6ee___init___py.html">app/api/v1/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_257b53c25398f6ee_api_py.html">app/api/v1/api.py</a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d___init___py.html">app/api/v1/endpoints/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_channels_py.html">app/api/v1/endpoints/channels.py</a></td>
                <td>201</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="65 201">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html">app/api/v1/endpoints/health.py</a></td>
                <td>60</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="18 60">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_monitoring_py.html">app/api/v1/endpoints/monitoring.py</a></td>
                <td>150</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="44 150">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_notifications_py.html">app/api/v1/endpoints/notifications.py</a></td>
                <td>27</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="14 27">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html">app/api/v1/endpoints/system.py</a></td>
                <td>56</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="22 56">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060___init___py.html">app/connectors/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_douyin_connector_py.html">app/connectors/douyin_connector.py</a></td>
                <td>17</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="7 17">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7589b33177cad060_xianyu_connector_py.html">app/connectors/xianyu_connector.py</a></td>
                <td>17</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="11 17">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html">app/core/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html">app/core/config.py</a></td>
                <td>86</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="79 86">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html">app/core/database.py</a></td>
                <td>117</td>
                <td>88</td>
                <td>0</td>
                <td class="right" data-ratio="29 117">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html">app/core/exceptions.py</a></td>
                <td>78</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="29 78">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html">app/core/logging.py</a></td>
                <td>71</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="46 71">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html">app/core/middleware.py</a></td>
                <td>100</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="58 100">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app/main.py</a></td>
                <td>79</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="50 79">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html">app/models/__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html">app/models/base.py</a></td>
                <td>63</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="45 63">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_channel_py.html">app/models/channel.py</a></td>
                <td>36</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="34 36">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_channel_connection_log_py.html">app/models/channel_connection_log.py</a></td>
                <td>70</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="56 70">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_notification_py.html">app/models/notification.py</a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html">app/services/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_monitoring_service_py.html">app/services/monitoring_service.py</a></td>
                <td>47</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="26 47">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16___init___py.html">app/utils/__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_cookie_manager_py.html">app/utils/cookie_manager.py</a></td>
                <td>49</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="18 49">37%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>1361</td>
                <td>693</td>
                <td>0</td>
                <td class="right" data-ratio="668 1361">49%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-04 02:14 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_748a0465d46c2a16_cookie_manager_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_5f5a17c013354698___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
