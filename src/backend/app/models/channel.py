"""
渠道实例模型

定义渠道实例相关的数据模型，对应设计文档中的 channel_instances 表
"""

from datetime import datetime
from typing import Optional
from enum import Enum

from sqlalchemy import String, Text, Boolean, JSON, Enum as SQLEnum, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column

from .base import BaseModel, TimestampMixin, GUID
import uuid


class ChannelStatus(str, Enum):
    """渠道状态枚举"""
    CONNECTED = "connected"      # 已连接
    DISCONNECTED = "disconnected"  # 已断开
    PAUSED = "paused"           # 已暂停
    ERROR = "error"             # 错误状态
    CONNECTING = "connecting"    # 连接中


class ChannelPlatform(str, Enum):
    """渠道平台枚举"""
    XIANYU = "xianyu"           # 闲鱼
    DOUYIN = "douyin"           # 抖音
    TAOBAO = "taobao"           # 淘宝
    WECHAT = "wechat"           # 微信


class ChannelInstance(BaseModel, TimestampMixin):
    """
    渠道实例模型

    对应设计文档中的 channel_instances 表
    存储用户接入的每一个渠道账号实例
    """

    __tablename__ = "channel_instances"

    # 主键使用字符串ID，如 ci_wx_1a2b3c4d
    id: Mapped[str] = mapped_column(
        String(50),
        primary_key=True,
        comment="渠道实例唯一ID"
    )

    # 所属团队ID (暂时可选，为未来SaaS化预留)
    team_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        GUID(),
        nullable=True,
        comment="所属团队ID"
    )

    # 平台类型
    platform: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        comment="平台类型"
    )

    # 用户设置的别名
    alias: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="用户设置的别名"
    )

    # 加密后的登录凭证
    credentials: Mapped[Optional[dict]] = mapped_column(
        JSON,
        nullable=True,
        comment="加密后的登录凭证"
    )

    # 连接状态
    status: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        comment="连接状态"
    )

    def __repr__(self) -> str:
        return f"<ChannelInstance(id={self.id}, platform={self.platform}, alias={self.alias}, status={self.status})>"

    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.status == "connected"

    @classmethod
    def generate_id(cls, platform: str) -> str:
        """生成渠道实例ID"""
        import uuid
        short_uuid = str(uuid.uuid4())[:8]
        return f"ci_{platform}_{short_uuid}"


# 为了向后兼容，保留 Channel 别名
Channel = ChannelInstance
