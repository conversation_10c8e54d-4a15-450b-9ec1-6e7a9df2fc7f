"""
渠道连接日志模型

记录渠道连接操作的历史记录
"""

import uuid
from datetime import datetime
from typing import Optional
from enum import Enum

from sqlalchemy import String, Text, JSON, ForeignKey, Enum as SQLEnum
from .base import GUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import BaseModel, UUIDMixin, TimestampMixin


class ConnectionLogStatus(str, Enum):
    """连接日志状态枚举"""
    SUCCESS = "success"         # 成功
    FAILED = "failed"          # 失败
    ERROR = "error"            # 错误
    TIMEOUT = "timeout"        # 超时
    CANCELLED = "cancelled"    # 取消
    PENDING = "pending"        # 待处理
    CONNECTED = "connected"    # 已连接
    DISCONNECTED = "disconnected"  # 已断开
    PAUSED = "paused"         # 已暂停
    RESUMED = "resumed"       # 已恢复


class ConnectionLogOperationType(str, Enum):
    """连接日志操作类型枚举"""
    CONNECT = "connect"           # 连接
    DISCONNECT = "disconnect"     # 断开连接
    RECONNECT = "reconnect"       # 重新连接
    PAUSE = "pause"              # 暂停
    RESUME = "resume"            # 恢复
    UPDATE = "update"            # 更新
    DELETE = "delete"            # 删除
    RESTORE = "restore"          # 恢复
    HEALTH_CHECK = "health_check"  # 健康检查
    MAINTENANCE = "maintenance"   # 维护
    CONFIG_UPDATE = "config_update"  # 配置更新


class ChannelConnectionLog(BaseModel, UUIDMixin, TimestampMixin):
    """
    渠道连接日志模型
    
    记录渠道的所有操作历史
    """
    
    __tablename__ = "channel_connection_logs"
    
    # 关联渠道实例
    channel_instance_id: Mapped[str] = mapped_column(
        String(50),
        ForeignKey("channel_instances.id", ondelete="CASCADE"),
        nullable=False,
        comment="关联渠道实例ID"
    )
    
    # 操作信息
    operation_type: Mapped[ConnectionLogOperationType] = mapped_column(
        SQLEnum(ConnectionLogOperationType),
        nullable=False,
        comment="操作类型"
    )
    
    status: Mapped[ConnectionLogStatus] = mapped_column(
        SQLEnum(ConnectionLogStatus),
        nullable=False,
        comment="操作状态"
    )
    
    # 详细信息
    message: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="操作消息"
    )
    
    error_details: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="错误详情"
    )
    
    # 操作者信息
    operator: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="操作者"
    )
    
    operator_type: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        comment="操作者类型（user/system/auto）"
    )
    
    # 操作前后状态
    previous_status: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        comment="操作前状态"
    )
    
    new_status: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        comment="操作后状态"
    )
    
    # 操作耗时
    duration_ms: Mapped[Optional[int]] = mapped_column(
        nullable=True,
        comment="操作耗时（毫秒）"
    )
    
    # 元数据
    metadata_info: Mapped[Optional[dict]] = mapped_column(
        JSON,
        nullable=True,
        comment="操作元数据"
    )
    
    # 请求信息
    request_id: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="请求ID"
    )
    
    ip_address: Mapped[Optional[str]] = mapped_column(
        String(45),  # IPv6最大长度
        nullable=True,
        comment="IP地址"
    )
    
    user_agent: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="用户代理"
    )
    
    # 关联关系
    # channel = relationship("Channel", back_populates="connection_logs")
    
    def __repr__(self) -> str:
        return f"<ChannelConnectionLog(id={self.id}, channel_id={self.channel_id}, operation={self.operation_type}, status={self.status})>"
    
    def is_successful(self) -> bool:
        """检查操作是否成功"""
        return self.status in [
            ConnectionLogStatus.SUCCESS,
            ConnectionLogStatus.CONNECTED,
            ConnectionLogStatus.DISCONNECTED,
            ConnectionLogStatus.PAUSED,
            ConnectionLogStatus.RESUMED
        ]
    
    def is_failed(self) -> bool:
        """检查操作是否失败"""
        return self.status in [
            ConnectionLogStatus.FAILED,
            ConnectionLogStatus.ERROR,
            ConnectionLogStatus.TIMEOUT,
            ConnectionLogStatus.CANCELLED
        ]
    
    def get_operation_summary(self) -> str:
        """获取操作摘要"""
        operation_names = {
            ConnectionLogOperationType.CONNECT: "连接",
            ConnectionLogOperationType.DISCONNECT: "断开连接",
            ConnectionLogOperationType.RECONNECT: "重新连接",
            ConnectionLogOperationType.PAUSE: "暂停",
            ConnectionLogOperationType.RESUME: "恢复",
            ConnectionLogOperationType.UPDATE: "更新",
            ConnectionLogOperationType.DELETE: "删除",
            ConnectionLogOperationType.RESTORE: "恢复",
            ConnectionLogOperationType.HEALTH_CHECK: "健康检查",
            ConnectionLogOperationType.MAINTENANCE: "维护",
            ConnectionLogOperationType.CONFIG_UPDATE: "配置更新"
        }
        
        status_names = {
            ConnectionLogStatus.SUCCESS: "成功",
            ConnectionLogStatus.FAILED: "失败",
            ConnectionLogStatus.ERROR: "错误",
            ConnectionLogStatus.TIMEOUT: "超时",
            ConnectionLogStatus.CANCELLED: "取消",
            ConnectionLogStatus.PENDING: "待处理",
            ConnectionLogStatus.CONNECTED: "已连接",
            ConnectionLogStatus.DISCONNECTED: "已断开",
            ConnectionLogStatus.PAUSED: "已暂停",
            ConnectionLogStatus.RESUMED: "已恢复"
        }
        
        operation_name = operation_names.get(self.operation_type, str(self.operation_type))
        status_name = status_names.get(self.status, str(self.status))
        
        return f"{operation_name} - {status_name}"
    
    def add_metadata(self, key: str, value: any) -> None:
        """添加元数据"""
        if self.metadata_info is None:
            self.metadata_info = {}
        self.metadata_info[key] = value

    def get_metadata(self, key: str, default=None) -> any:
        """获取元数据"""
        if self.metadata_info is None:
            return default
        return self.metadata_info.get(key, default)
    
    @classmethod
    def create_log(
        cls,
        channel_instance_id: str,
        operation_type: ConnectionLogOperationType,
        status: ConnectionLogStatus,
        message: str = None,
        error_details: str = None,
        operator: str = None,
        operator_type: str = "system",
        previous_status: str = None,
        new_status: str = None,
        duration_ms: int = None,
        metadata_info: dict = None,
        request_id: str = None,
        ip_address: str = None,
        user_agent: str = None
    ) -> "ChannelConnectionLog":
        """创建连接日志"""
        return cls(
            channel_instance_id=channel_instance_id,
            operation_type=operation_type,
            status=status,
            message=message,
            error_details=error_details,
            operator=operator,
            operator_type=operator_type,
            previous_status=previous_status,
            new_status=new_status,
            duration_ms=duration_ms,
            metadata_info=metadata_info or {},
            request_id=request_id,
            ip_address=ip_address,
            user_agent=user_agent
        )
