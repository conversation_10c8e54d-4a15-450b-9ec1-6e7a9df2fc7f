"""
柴管家数据模型

包含所有数据库模型定义
"""

from app.models.base import BaseModel, TimestampMixin, UUIDMixin, SoftDeleteMixin, VersionMixin, AuditMixin
from app.models.channel import ChannelInstance, Channel, ChannelStatus, ChannelPlatform
from app.models.channel_connection_log import ChannelConnectionLog, ConnectionLogStatus, ConnectionLogOperationType
# from app.models.notification import Notification  # 暂时注释，等数据库表创建后再启用

__all__ = [
    "BaseModel",
    "TimestampMixin",
    "UUIDMixin",
    "SoftDeleteMixin",
    "VersionMixin",
    "AuditMixin",
    "ChannelInstance",
    "Channel",
    "ChannelStatus",
    "ChannelPlatform",
    "ChannelConnectionLog",
    "ConnectionLogStatus",
    "ConnectionLogOperationType",
    # "Notification",  # 暂时注释
]
