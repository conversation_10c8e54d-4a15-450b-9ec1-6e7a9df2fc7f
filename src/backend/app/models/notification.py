"""
通知模型

定义系统通知的数据结构
"""

from datetime import datetime, timezone
from typing import Optional
from sqlalchemy import Column, String, Text, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Notification(BaseModel):
    """
    通知模型
    
    用于存储系统通知信息，包括Cookie过期、连接异常等
    """
    __tablename__ = "notifications"

    id = Column(String(50), primary_key=True, comment="通知ID")
    type = Column(String(50), nullable=False, comment="通知类型")
    title = Column(String(200), nullable=False, comment="通知标题")
    message = Column(Text, nullable=False, comment="通知内容")
    channel_id = Column(String(50), ForeignKey("channel_instances.id"), nullable=True, comment="关联渠道ID")
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), comment="创建时间")
    is_read = Column(Boolean, default=False, comment="是否已读")
    read_at = Column(DateTime(timezone=True), nullable=True, comment="阅读时间")

    # 关联关系
    channel = relationship("ChannelInstance", back_populates="notifications")

    def __repr__(self):
        return f"<Notification(id={self.id}, type={self.type}, title={self.title})>"

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "type": self.type,
            "title": self.title,
            "message": self.message,
            "channel_id": self.channel_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "is_read": self.is_read,
            "read_at": self.read_at.isoformat() if self.read_at else None
        }
