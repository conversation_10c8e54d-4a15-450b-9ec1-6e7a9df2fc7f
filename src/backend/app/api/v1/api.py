"""
柴管家API v1路由配置

汇总所有v1版本的API路由
"""

from fastapi import APIRouter

from app.api.v1.endpoints import health, system, channels, monitoring, notifications

# 创建API路由器
api_router = APIRouter()

# 注册各模块路由
api_router.include_router(health.router, prefix="/health", tags=["健康检查"])

api_router.include_router(system.router, prefix="/system", tags=["系统信息"])

api_router.include_router(channels.router, prefix="/channels", tags=["渠道管理"])

api_router.include_router(monitoring.router, prefix="/monitoring", tags=["监控管理"])

api_router.include_router(notifications.router, prefix="/notifications", tags=["通知管理"])
#
# api_router.include_router(
#     messages.router,
#     prefix="/messages",
#     tags=["消息管理"]
# )
#
# api_router.include_router(
#     ai_copilot.router,
#     prefix="/ai-copilot",
#     tags=["AI副驾助理"]
# )
#
# api_router.include_router(
#     ai_hosting.router,
#     prefix="/ai-hosting",
#     tags=["AI智能托管"]
# )
