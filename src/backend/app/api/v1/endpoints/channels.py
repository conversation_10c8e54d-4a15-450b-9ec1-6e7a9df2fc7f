"""
渠道管理API端点

提供渠道连接、管理、监控等功能的REST API
"""

import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.models.channel import ChannelInstance, ChannelStatus, ChannelPlatform
from app.models.channel_connection_log import ChannelConnectionLog, ConnectionLogOperationType, ConnectionLogStatus
from app.utils.cookie_manager import CookieManager
from app.connectors.xianyu_connector import XianyuConnector
from app.connectors.douyin_connector import DouyinConnector
from app.services.monitoring_service import ChannelMonitoringService

router = APIRouter()

# 初始化服务
cookie_manager = CookieManager()
monitoring_service = ChannelMonitoringService()


# Pydantic模型
class ChannelConnectRequest(BaseModel):
    """渠道连接请求"""
    platform: ChannelPlatform = Field(..., description="平台类型")
    cookie_config: Dict[str, Any] = Field(..., description="Cookie配置")
    alias: Optional[str] = Field(None, description="渠道别名")


class ChannelResponse(BaseModel):
    """渠道响应"""
    id: str
    platform: str
    platform_account_id: str
    alias: Optional[str]
    status: str
    created_at: datetime
    updated_at: datetime


class ChannelUpdateRequest(BaseModel):
    """渠道更新请求"""
    alias: Optional[str] = Field(None, description="渠道别名")
    status: Optional[ChannelStatus] = Field(None, description="渠道状态")
    config: Optional[Dict[str, Any]] = Field(None, description="渠道配置")


@router.post("/connect", status_code=status.HTTP_201_CREATED)
async def connect_channel(
    request: ChannelConnectRequest,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    连接渠道
    
    Args:
        request: 连接请求
        db: 数据库会话
        
    Returns:
        Dict[str, Any]: 连接结果
    """
    try:
        # 根据平台选择连接器
        if request.platform == ChannelPlatform.XIANYU:
            connector = XianyuConnector()
        elif request.platform == ChannelPlatform.DOUYIN:
            connector = DouyinConnector()
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的平台: {request.platform}"
            )
        
        # 验证Cookie
        if not await connector.validate_cookie(request.cookie_config):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cookie验证失败"
            )
        
        # 获取账号信息
        account_info = await connector.get_account_info(request.cookie_config)
        platform_account_id = account_info.get("account_id")
        
        if not platform_account_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法获取账号ID"
            )
        
        # 生成渠道实例ID
        channel_id = ChannelInstance.generate_id(request.platform.value)

        # 检查是否已存在（基于平台账号ID）
        existing_query = select(ChannelInstance).where(
            ChannelInstance.platform == request.platform.value
        )
        existing_result = await db.execute(existing_query)
        existing_channels = existing_result.scalars().all()

        # 检查是否有相同的账号ID（从credentials中提取）
        for existing in existing_channels:
            if existing.credentials and existing.credentials.get("account_id") == platform_account_id:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="该账号已连接"
                )

        # 加密Cookie
        encrypted_credentials = {
            "account_id": platform_account_id,
            "encrypted_cookie": cookie_manager.encrypt_cookie(request.cookie_config),
            "account_info": account_info
        }

        # 创建渠道实例
        channel = ChannelInstance(
            id=channel_id,
            platform=request.platform.value,
            alias=request.alias or account_info.get("display_name", platform_account_id),
            status=ChannelStatus.CONNECTED.value,
            credentials=encrypted_credentials
        )
        
        db.add(channel)
        await db.flush()
        
        # 创建连接日志
        log = ChannelConnectionLog.create_log(
            channel_instance_id=channel.id,
            operation_type=ConnectionLogOperationType.CONNECT,
            status=ConnectionLogStatus.SUCCESS,
            message="渠道连接成功",
            new_status=ChannelStatus.CONNECTED.value
        )
        
        db.add(log)
        await db.commit()
        
        # 启动监控
        await monitoring_service.start_monitoring(channel.id)
        
        return {
            "message": "渠道连接成功",
            "data": {
                "id": channel.id,
                "platform": channel.platform,
                "account_id": platform_account_id,
                "alias": channel.alias,
                "status": channel.status,
                "created_at": channel.created_at,
                "updated_at": channel.updated_at
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"连接失败: {str(e)}"
        )


@router.get("/{channel_id}/status")
async def get_channel_status(
    channel_id: str,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取渠道状态（包括软删除状态）

    Args:
        channel_id: 渠道ID
        db: 数据库会话

    Returns:
        Dict[str, Any]: 渠道状态信息
    """
    # 查找渠道实例（包括已删除的）
    query = select(ChannelInstance).where(
        ChannelInstance.id == channel_id
    )
    result = await db.execute(query)
    channel = result.scalar_one_or_none()

    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="渠道不存在"
        )

    is_deleted = channel.status == "deleted"
    deleted_at = channel.updated_at if is_deleted else None

    return {
        "data": {
            "id": channel.id,
            "status": channel.status,
            "is_deleted": is_deleted,
            "deleted_at": deleted_at,
            "created_at": channel.created_at,
            "updated_at": channel.updated_at
        }
    }


@router.get("/{channel_id}")
async def get_channel(
    channel_id: str,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取渠道信息
    
    Args:
        channel_id: 渠道ID
        db: 数据库会话
        
    Returns:
        ChannelResponse: 渠道信息
    """
    query = select(ChannelInstance).where(
        ChannelInstance.id == channel_id
    )
    result = await db.execute(query)
    channel = result.scalar_one_or_none()

    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="渠道不存在"
        )

    # 从credentials中提取账号ID
    account_id = ""
    if channel.credentials:
        account_id = channel.credentials.get("account_id", "")

    return {
        "data": {
            "id": channel.id,
            "platform": channel.platform,
            "platform_account_id": account_id,
            "alias": channel.alias,
            "status": channel.status,
            "created_at": channel.created_at,
            "updated_at": channel.updated_at
        }
    }


@router.get("/")
async def list_channels(
    platform: Optional[ChannelPlatform] = None,
    status: Optional[ChannelStatus] = None,
    include_deleted: bool = False,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取渠道列表
    
    Args:
        platform: 平台过滤
        status: 状态过滤
        db: 数据库会话
        
    Returns:
        List[ChannelResponse]: 渠道列表
    """
    query = select(ChannelInstance)

    # 根据include_deleted参数决定是否包含已删除的渠道
    if not include_deleted:
        query = query.where(ChannelInstance.status != "deleted")

    if platform:
        query = query.where(ChannelInstance.platform == platform.value)

    if status:
        query = query.where(ChannelInstance.status == status.value)

    result = await db.execute(query)
    channels = result.scalars().all()

    channels_data = [
        {
            "id": channel.id,
            "platform": channel.platform,
            "platform_account_id": channel.credentials.get("account_id", "") if channel.credentials else "",
            "alias": channel.alias,
            "status": channel.status,
            "is_deleted": channel.status == "deleted",
            "created_at": channel.created_at,
            "updated_at": channel.updated_at
        }
        for channel in channels
    ]

    return {
        "data": channels_data,
        "total": len(channels_data)
    }


@router.put("/{channel_id}")
async def update_channel(
    channel_id: str,
    request: ChannelUpdateRequest,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    更新渠道信息
    
    Args:
        channel_id: 渠道ID
        request: 更新请求
        db: 数据库会话
        
    Returns:
        Dict[str, Any]: 更新结果
    """
    # 查找渠道实例
    query = select(ChannelInstance).where(
        ChannelInstance.id == channel_id
    )
    result = await db.execute(query)
    channel = result.scalar_one_or_none()

    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="渠道不存在"
        )

    # 更新字段
    update_data = {}
    if request.alias is not None:
        update_data["alias"] = request.alias
    if request.status is not None:
        update_data["status"] = request.status.value
    if request.config is not None:
        # 更新credentials中的配置
        if channel.credentials:
            channel.credentials.update(request.config)
            update_data["credentials"] = channel.credentials
        else:
            update_data["credentials"] = request.config

    if update_data:
        update_data["updated_at"] = datetime.now(timezone.utc)

        update_stmt = update(ChannelInstance).where(
            ChannelInstance.id == channel_id
        ).values(**update_data)

        await db.execute(update_stmt)

        # 记录更新日志
        from app.models.channel_connection_log import ChannelConnectionLog, ConnectionLogOperationType, ConnectionLogStatus
        update_log = ChannelConnectionLog(
            channel_instance_id=channel_id,
            operation_type=ConnectionLogOperationType.UPDATE,
            status=ConnectionLogStatus.SUCCESS,
            message="渠道信息更新成功"
        )
        db.add(update_log)

        await db.commit()

        # 重新查询更新后的数据
        result = await db.execute(query)
        updated_channel = result.scalar_one()
    else:
        updated_channel = channel

    # 从credentials中提取账号ID
    account_id = ""
    if updated_channel.credentials:
        account_id = updated_channel.credentials.get("account_id", "")

    return {
        "message": "渠道更新成功",
        "data": {
            "id": updated_channel.id,
            "platform": updated_channel.platform,
            "platform_account_id": account_id,
            "alias": updated_channel.alias,
            "status": updated_channel.status,
            "created_at": updated_channel.created_at,
            "updated_at": updated_channel.updated_at
        }
    }


@router.post("/{channel_id}/pause")
async def pause_channel(
    channel_id: str,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    暂停渠道监控

    Args:
        channel_id: 渠道ID
        db: 数据库会话

    Returns:
        Dict[str, Any]: 暂停结果
    """
    # 查找渠道实例
    query = select(ChannelInstance).where(
        ChannelInstance.id == channel_id
    )
    result = await db.execute(query)
    channel = result.scalar_one_or_none()

    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="渠道不存在"
        )

    # 暂停监控
    await monitoring_service.pause_monitoring(channel_id)

    # 更新渠道状态
    update_stmt = update(ChannelInstance).where(
        ChannelInstance.id == channel_id
    ).values(
        status=ChannelStatus.PAUSED.value,
        updated_at=datetime.now(timezone.utc)
    )

    await db.execute(update_stmt)

    # 记录暂停日志
    from app.models.channel_connection_log import ChannelConnectionLog, ConnectionLogOperationType, ConnectionLogStatus
    pause_log = ChannelConnectionLog(
        channel_instance_id=channel_id,
        operation_type=ConnectionLogOperationType.PAUSE,
        status=ConnectionLogStatus.SUCCESS,
        message="渠道监控已暂停"
    )
    db.add(pause_log)

    await db.commit()

    # 获取更新后的渠道信息
    updated_query = select(ChannelInstance).where(
        ChannelInstance.id == channel_id
    )
    updated_result = await db.execute(updated_query)
    updated_channel = updated_result.scalar_one()

    return {
        "message": "渠道已暂停",
        "data": {
            "id": updated_channel.id,
            "platform": updated_channel.platform,
            "alias": updated_channel.alias,
            "status": updated_channel.status,
            "created_at": updated_channel.created_at,
            "updated_at": updated_channel.updated_at
        }
    }


@router.post("/{channel_id}/resume")
async def resume_channel(
    channel_id: str,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    恢复渠道监控

    Args:
        channel_id: 渠道ID
        db: 数据库会话

    Returns:
        Dict[str, Any]: 恢复结果
    """
    # 查找渠道实例
    query = select(ChannelInstance).where(
        ChannelInstance.id == channel_id
    )
    result = await db.execute(query)
    channel = result.scalar_one_or_none()

    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="渠道不存在"
        )

    # 恢复监控
    await monitoring_service.resume_monitoring(channel_id)

    # 更新渠道状态
    update_stmt = update(ChannelInstance).where(
        ChannelInstance.id == channel_id
    ).values(
        status=ChannelStatus.CONNECTED.value,
        updated_at=datetime.now(timezone.utc)
    )

    await db.execute(update_stmt)

    # 记录恢复日志
    from app.models.channel_connection_log import ChannelConnectionLog, ConnectionLogOperationType, ConnectionLogStatus
    resume_log = ChannelConnectionLog(
        channel_instance_id=channel_id,
        operation_type=ConnectionLogOperationType.RESUME,
        status=ConnectionLogStatus.SUCCESS,
        message="渠道监控已恢复"
    )
    db.add(resume_log)

    await db.commit()

    # 获取更新后的渠道信息
    updated_query = select(ChannelInstance).where(
        ChannelInstance.id == channel_id
    )
    updated_result = await db.execute(updated_query)
    updated_channel = updated_result.scalar_one()

    return {
        "message": "渠道已恢复",
        "data": {
            "id": updated_channel.id,
            "platform": updated_channel.platform,
            "alias": updated_channel.alias,
            "status": updated_channel.status,
            "created_at": updated_channel.created_at,
            "updated_at": updated_channel.updated_at
        }
    }


@router.get("/{channel_id}/logs")
async def get_channel_logs(
    channel_id: str,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取渠道操作日志

    Args:
        channel_id: 渠道ID
        db: 数据库会话

    Returns:
        Dict[str, Any]: 操作日志列表
    """
    # 查找渠道实例
    query = select(ChannelInstance).where(
        ChannelInstance.id == channel_id
    )
    result = await db.execute(query)
    channel = result.scalar_one_or_none()

    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="渠道不存在"
        )

    # 查询操作日志
    from app.models.channel_connection_log import ChannelConnectionLog
    logs_query = select(ChannelConnectionLog).where(
        ChannelConnectionLog.channel_instance_id == channel_id
    ).order_by(ChannelConnectionLog.created_at.desc())

    logs_result = await db.execute(logs_query)
    logs = logs_result.scalars().all()

    # 转换为响应格式
    logs_data = []
    for log in logs:
        logs_data.append({
            "id": log.id,
            "channel_id": log.channel_instance_id,
            "operation_type": log.operation_type,
            "status": log.status,
            "message": log.message,
            "error_details": log.error_details,
            "created_at": log.created_at,
            "updated_at": log.updated_at
        })

    return {
        "data": logs_data,
        "total": len(logs_data)
    }


@router.delete("/{channel_id}")
async def delete_channel(
    channel_id: str,
    permanent: bool = False,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    删除渠道
    
    Args:
        channel_id: 渠道ID
        permanent: 是否永久删除
        db: 数据库会话
        
    Returns:
        Dict[str, Any]: 删除结果
    """
    if permanent:
        # 永久删除
        delete_stmt = delete(ChannelInstance).where(ChannelInstance.id == channel_id)
        result = await db.execute(delete_stmt)

        if result.rowcount == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="渠道不存在"
            )

        await db.commit()
        return {"message": "渠道彻底删除成功"}
    else:
        # 对于渠道实例，我们直接删除（因为设计文档中没有软删除字段）
        # 或者可以将状态设置为 "deleted"
        update_stmt = update(ChannelInstance).where(
            ChannelInstance.id == channel_id
        ).values(
            status="deleted",
            updated_at=datetime.now(timezone.utc)
        )

        result = await db.execute(update_stmt)

        if result.rowcount == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="渠道不存在"
            )

        await db.commit()

        # 停止监控
        await monitoring_service.stop_monitoring(channel_id)

        return {"message": "软删除成功"}



