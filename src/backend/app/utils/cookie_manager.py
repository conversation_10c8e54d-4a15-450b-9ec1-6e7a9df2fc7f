"""
Cookie管理器

负责Cookie的加密、解密和管理
"""

import json
import base64
from typing import Dict, Any, Optional
from cryptography.fernet import Fernet


class CookieManager:
    """Cookie管理器类"""
    
    def __init__(self, encryption_key: Optional[str] = None):
        """
        初始化Cookie管理器
        
        Args:
            encryption_key: 加密密钥，如果为None则生成新密钥
        """
        if encryption_key:
            self.fernet = Fernet(encryption_key.encode())
        else:
            # 生成新的加密密钥
            key = Fernet.generate_key()
            self.fernet = Fernet(key)
    
    def encrypt_cookie(self, cookie_data: Dict[str, Any]) -> str:
        """
        加密Cookie数据
        
        Args:
            cookie_data: Cookie数据字典
            
        Returns:
            str: 加密后的Cookie字符串
        """
        # 将字典转换为JSON字符串
        json_str = json.dumps(cookie_data, ensure_ascii=False)
        
        # 加密JSON字符串
        encrypted_data = self.fernet.encrypt(json_str.encode('utf-8'))
        
        # 返回base64编码的加密数据
        return base64.b64encode(encrypted_data).decode('utf-8')
    
    def decrypt_cookie(self, encrypted_cookie: str) -> Dict[str, Any]:
        """
        解密Cookie数据
        
        Args:
            encrypted_cookie: 加密的Cookie字符串
            
        Returns:
            Dict[str, Any]: 解密后的Cookie数据字典
        """
        try:
            # 解码base64
            encrypted_data = base64.b64decode(encrypted_cookie.encode('utf-8'))
            
            # 解密数据
            decrypted_data = self.fernet.decrypt(encrypted_data)
            
            # 解析JSON
            json_str = decrypted_data.decode('utf-8')
            return json.loads(json_str)
            
        except Exception as e:
            raise ValueError(f"Cookie解密失败: {str(e)}")
    
    def validate_cookie_format(self, cookie_data: Dict[str, Any]) -> bool:
        """
        验证Cookie格式
        
        Args:
            cookie_data: Cookie数据字典
            
        Returns:
            bool: 格式是否有效
        """
        # 基本格式验证
        if not isinstance(cookie_data, dict):
            return False
        
        # 检查必要字段
        required_fields = ["session_id"]
        return all(field in cookie_data for field in required_fields)
    
    def mask_sensitive_data(self, cookie_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        遮蔽敏感数据用于日志记录
        
        Args:
            cookie_data: Cookie数据字典
            
        Returns:
            Dict[str, Any]: 遮蔽敏感信息后的数据
        """
        masked_data = cookie_data.copy()
        
        sensitive_fields = ["session_id", "auth_token", "password", "secret"]
        
        for field in sensitive_fields:
            if field in masked_data:
                value = str(masked_data[field])
                if len(value) > 8:
                    masked_data[field] = value[:4] + "****" + value[-4:]
                else:
                    masked_data[field] = "****"
        
        return masked_data
    
    def get_cookie_expiry(self, cookie_data: Dict[str, Any]) -> Optional[str]:
        """
        获取Cookie过期时间
        
        Args:
            cookie_data: Cookie数据字典
            
        Returns:
            Optional[str]: 过期时间字符串
        """
        return cookie_data.get("expires_at")
    
    def is_cookie_expired(self, cookie_data: Dict[str, Any]) -> bool:
        """
        检查Cookie是否过期
        
        Args:
            cookie_data: Cookie数据字典
            
        Returns:
            bool: 是否过期
        """
        expiry = self.get_cookie_expiry(cookie_data)
        if not expiry:
            return False
        
        try:
            from datetime import datetime
            expiry_time = datetime.fromisoformat(expiry.replace('Z', '+00:00'))
            return datetime.now() > expiry_time
        except:
            return False
