[pytest]
# 测试目录配置
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 测试运行配置
addopts =
    -v
    --strict-markers
    --strict-config
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=70
    --tb=short
    --durations=10
    --asyncio-mode=auto

# 测试标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    slow: 慢速测试
    api: API测试
    database: 数据库测试
    external: 外部依赖测试

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning

# 测试发现配置
minversion = 7.0
