name: 基础CI检查

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'

jobs:
  quick-check:
    name: 快速质量检查
    runs-on: ubuntu-latest

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    # 后端检查
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
        cache-dependency-path: 'src/backend/requirements*.txt'

    - name: 后端依赖安装
      run: |
        cd src/backend
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

    - name: 后端代码格式检查
      run: |
        cd src/backend
        black --check app/ || echo "⚠️ 需要运行 black app/ 格式化代码"
        isort --check-only app/ || echo "⚠️ 需要运行 isort app/ 整理导入"

    - name: 后端基础测试
      run: |
        cd src/backend
        python -m pytest tests/unit/ -v --tb=short --no-cov

    # 前端检查
    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: src/frontend/package-lock.json

    - name: 前端依赖安装
      run: |
        cd src/frontend
        npm ci

    - name: 前端代码检查
      run: |
        cd src/frontend
        npm run lint || echo "⚠️ 需要修复ESLint错误"

    - name: 前端构建测试
      run: |
        cd src/frontend
        npm run build

  # 可选的完整测试（仅在main分支）
  full-test:
    name: 完整测试
    runs-on: ubuntu-latest
    needs: quick-check
    if: github.ref == 'refs/heads/main'

    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: chaiguanjia_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'

    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: src/frontend/package-lock.json

    - name: 安装后端依赖
      run: |
        cd src/backend
        pip install -r requirements-dev.txt

    - name: 安装前端依赖
      run: |
        cd src/frontend
        npm ci

    - name: 运行后端测试（带覆盖率）
      run: |
        cd src/backend
        pytest tests/ --cov=app --cov-report=term-missing
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/chaiguanjia_test

    - name: 运行前端测试
      run: |
        cd src/frontend
        npm run test:run
